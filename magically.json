{"version": "1.0.0", "projectId": "2b237ff5-0e25-49db-b0c2-56f261d0a573", "projectName": "HoofBeat", "fileManifest": [{"path": "App.tsx", "language": "typescript", "hash": "a070cfa6e2587fcdaf72919bfd6f48ba73297c11ff83de715090f371ec014b6b"}, {"path": "navigation/index.tsx", "language": "typescript", "hash": "0a55732b6569d79a5013bca8121ac2e55b857d66a24277e017d9ea5260885bf8"}, {"path": "screens/HomeScreen.tsx", "language": "typescript", "hash": "7106398e99af7d3f268a5c2521a52e3cc827c0bdce9bce36693d703e402f1fe1"}, {"path": "constants/colors.ts", "language": "typescript", "hash": "94d7acc8fbc4f3ad7283e9eca4669738eaccc3fc64e99bcce99d795de4a1a628"}, {"path": "mocks/animals.ts", "language": "typescript", "hash": "cb3744b7651d0217b866dfb68c928584404b607327a44ed095b1fe8f512d5c68"}, {"path": "mocks/vitals.ts", "language": "typescript", "hash": "e2f98d566ef8cbb0fa495e24237f9d751d25a117fac31a761ced6adc01e8407c"}, {"path": "mocks/feeding.ts", "language": "typescript", "hash": "d809d8afc5a1564f48e71682c711dbf27a5befc8f6128d053e6991e047d36588"}, {"path": "mocks/medications.ts", "language": "typescript", "hash": "c6625a9bf7b9d3d19ff4d8ddde637adc9cedc8c98492059abf62cee6428ff21f"}, {"path": "mocks/devices.ts", "language": "typescript", "hash": "599527edd8295f536fb459ccede5e520a7bf55706e311f6e275f8aab11e618a9"}, {"path": "store/animalStore.ts", "language": "typescript", "hash": "57270862f132181c701f4d9f378c623e98f5aa7e1a98f78b7652e9f46298f331"}, {"path": "store/vitalsStore.ts", "language": "typescript", "hash": "3c49874063b69cdee1e72f55d9123d00b46f269302272234c9854ed65ff993ff"}, {"path": "store/feedingStore.ts", "language": "typescript", "hash": "28894ce78b4dc66e9c044fe738bae7d1609f89768f7b8737de7cbc1c85ac34d3"}, {"path": "store/medicationStore.ts", "language": "typescript", "hash": "df0fe539e50b963b0065b71d97090cc6367806041b28bb119b2dc9f7c4bf8440"}, {"path": "store/deviceStore.ts", "language": "typescript", "hash": "39e75f93d4b158d48dec2e196b7d6372a2e6460f19405c4d7824ed3c33ee941f"}, {"path": "components/AnimalCard.tsx", "language": "typescript", "hash": "101fbb138f4b5ccd7885b149ef5e153f1f3d4ecba03ab2a31b9350290e8b0ecc"}, {"path": "components/VitalCard.tsx", "language": "typescript", "hash": "f65b9d3de0170465b0d8dd1c05bea6c4f845a831459ebdcfa7e4e60e143a9aee"}, {"path": "components/DeviceCard.tsx", "language": "typescript", "hash": "d60f24bf28d6c62b8b9cd68da85eac5b39e59c967f27fce2a7e58122b4332032"}, {"path": "components/FeedingScheduleCard.tsx", "language": "typescript", "hash": "c41a185625300935df6a056f9b5995775d259013b4815e8b73876c984daf1637"}, {"path": "components/MedicationCard.tsx", "language": "typescript", "hash": "e78df11d50f52507ba763019a19405b335fe40035e109b7d9ec030c218052e92"}, {"path": "components/Header.tsx", "language": "typescript", "hash": "0bdcc2c0b8d7aec780f9ea55bf8bd541f17b1900775c0069e1139e80ca5b1295"}, {"path": "components/LiveMonitor.tsx", "language": "typescript", "hash": "a60884343f2ec13354efd6484cd6901a056e1d5f80422b36d4e62e82ee00e7d9"}, {"path": "screens/AnimalsScreen.tsx", "language": "typescript", "hash": "412e0ca18ff52c1356b1b4e9c657715089a654e83e35e780c0ed05e71856b8d3"}, {"path": "screens/DevicesScreen.tsx", "language": "typescript", "hash": "8cb5f238d42f9daa590862d14f412ea00dfaf84d7d76db02194b449af6ddcac0"}, {"path": "screens/ProfileScreen.tsx", "language": "typescript", "hash": "252473796652b88eeb56227e0b0346a78d49a7a1aa1784a078e47aef8f370748"}, {"path": "screens/AnimalDetailScreen.tsx", "language": "typescript", "hash": "dd429d14e6f4c3fbf6c64703ae491f8220ec776609a956698af0b132ebd420b1"}, {"path": "components/AlertBanner.tsx", "language": "typescript", "hash": "ef5e169959ffbe83f1e38a80b95c8d2ef353b66979189eb45958d32d56cbddea"}, {"path": "screens/RecordVitalsScreen.tsx", "language": "typescript", "hash": "92ff1eb14d6334677f1fd91fb49a0b3a7e67f6fda6c3af060e90f78ac1a45b5e"}, {"path": "contexts/ThemeContext.tsx", "language": "typescript", "hash": "147060d628de81cabd21e076fe7a20b641219c5faf4271379b016b4936ae39f5"}, {"path": "screens/AddAnimalScreen.tsx", "language": "typescript", "hash": "7d78bbb927a49de96ab53ea3c58942b10f74691f381d1c73b48e9004134b3116"}, {"path": "screens/SubscriptionScreen.tsx", "language": "typescript", "hash": "85ce927e791bd688ea796225f6442a87c8536cd684a6266ae0e187c03199f855"}, {"path": "screens/AdvancedSettingsScreen.tsx", "language": "typescript", "hash": "93b77e7118f0f31e86f079fcc06c97672e7c3a58c24b6a4ccac49a912c29d51a"}, {"path": "screens/EditProfileScreen.tsx", "language": "typescript", "hash": "b68170995001e230774e274593bbb87881a6bcee4e3ddd97d0c65d04f5a64c29"}, {"path": "components/PrintableTable.tsx", "language": "typescript", "hash": "ccf10e455d1bbc0cb3d91f802da8222820933e40df84640ee0b3462f8e9f634a"}, {"path": "screens/PrintScreen.tsx", "language": "typescript", "hash": "77d66ba894e559393fe65a741ac6e94c417654af60e5b1ed3db557114283b8f8"}, {"path": "screens/BarcodeScannerScreen.tsx", "language": "typescript", "hash": "4f6ab3008d17713105104cc4b67f03eec63966ba5149ec87d13d0a69a3122d95"}, {"path": "screens/FeedScheduleScreen.tsx", "language": "typescript", "hash": "f6c46af9e37b1f9f6ce7bdb3d23f9c03639370846c26f13664e4381a0cbb0bc5"}, {"path": "screens/PrivacySettingsScreen.tsx", "language": "typescript", "hash": "581c86a5d8ff8fd0d009706b0e700ac70c79d0d2cf8969072aabf12d4a4cb865"}, {"path": "components/VaccineAlertBanner.tsx", "language": "typescript", "hash": "bcd28028d15550d7f1d19597671bc226fe361be1629e1dde0bf44f26144f3e7a"}, {"path": "components/PremiumBanner.tsx", "language": "typescript", "hash": "397a28343c4ae78bbd336b32362926428c343aa748a645251b5ee925f6ef7586"}, {"path": "supabase/client.ts", "language": "typescript", "hash": "7fdc0f3ae0b59dd755b0d1d194a8826e580f7dcf5a5a5e5401a95a71cfc6085a"}, {"path": "store/vaccinationStore.ts", "language": "typescript", "hash": "224c14a7f9f5ab91e3e5ee2a7487dd7eb6ab98ad7e6d4501fce171da498dc6cc"}, {"path": "store/userStore.ts", "language": "typescript", "hash": "6a16ccb9ae19fbbc939a3be89f156de51a5cf4f8b29ef15025e81ad8b09db93b"}, {"path": "contexts/AuthContext.tsx", "language": "typescript", "hash": "3be7c82c31d9132c68d4cd171b6ae89f8e1893b153059a351320614bd7180eed"}, {"path": "error.md", "language": "markdown", "hash": "61f8311a0dcd13f5253b1afd1612b7e18881989a22ac75d0677dc83efcb30253"}, {"path": "supabase/functions/process-payment.ts", "language": "typescript", "hash": "d72604332de231da98913295abe9170518d7c47d6f937f11ace997b0bce93a61"}, {"path": "screens/LoginScreen.tsx", "language": "typescript", "hash": "64eba38dab3fd5cea6c6b091338e291bd4d839c836e1dad4bfeb0b17514da56d"}, {"path": "supabase/functions/verify-subscription.ts", "language": "typescript", "hash": "465c6f651c664f411495a3718920cbab598f3b5327818d0eabf79340bdfcf1fd"}, {"path": "supabase/deploy.js", "language": "javascript", "hash": "c4b5dcf7694e3b4a5ceda7965a6d388167b0651e572498d06b06f27bbc8cdd4d"}, {"path": "app.json", "language": "json", "hash": "43669084973cc169637e518ff002b2427d7db435d16a10421eafa35560198021"}, {"path": "admin/App.tsx", "language": "typescript", "hash": "b3df80e8f8a96496ef061324efa9d96498da8bd1bfd47a569ecc772b07bf8956"}, {"path": "admin/index.html", "language": "html", "hash": "615c729eed2e7e539441529e1210a141b4e548490ca74fd5f9c0797e5b73669b"}, {"path": "admin/index.js", "language": "javascript", "hash": "83125b7ca368fe1f8ea505b24a398283314c0822220b9e6b72f095e8743ae551"}, {"path": "supabase/functions/handle-auth.ts", "language": "typescript", "hash": "8ffae14c8e7bd13495b00f84ae23ef9d8026561871713eae99d1afb1fed1924c"}, {"path": "supabase/functions/auth-security/index.ts", "language": "typescript", "hash": "3cffc9aa7f925caa92deb1b94c9bbc8295534826f97533991e60ad32d9800754"}, {"path": "supabase/functions/handle-auth/index.ts", "language": "typescript", "hash": "15a588a0fe09c92d8c6ef85c416122b3f3b393ef6244d6ab93955fae1fd642de"}, {"path": "docs/google-auth-setup.md", "language": "markdown", "hash": "a1ef242454a1941b5d98e714269cfcd091d4bb63958701489cd8e51a37943948"}, {"path": "screens/FAQScreen.tsx", "language": "typescript", "hash": "e5fc683735daffc2d23cbbcf52444435540f4a8277ab5fffa13441152a164141"}, {"path": "services/CrispService.ts", "language": "typescript", "hash": "5c98ebfb3b2fac8253a5c99cf70b1fb8d213d60d63f8804239b6d74c29ab9d56"}, {"path": "screens/ContactScreen.tsx", "language": "typescript", "hash": "35be2dc2c21ee5164aa639aff6858487c63d01224f82aa3e7c9f62cdfb63beca"}, {"path": "supabase/functions/send-contact-email/index.ts", "language": "typescript", "hash": "1be7a06f7f0e93e91c3d3c3d05157cca9824690b762ca42f6b1be0150c88ff9a"}, {"path": "screens/LocationScreen.tsx", "language": "typescript", "hash": "8e4d54282c89e291dc7bdae06d5fd9dad7dbed86de58ffc13e25db1b0e960693"}, {"path": "utils/bluetoothHelper.ts", "language": "typescript", "hash": "d550aac9d94a37cf3c7024261e1873fef29447060b6eb62ebbc0645cddeccbc2"}, {"path": "components/ImagePickerModal.tsx", "language": "typescript", "hash": "b0d8e29e442a54b113aec8618481581a402a01440f554b32cd204bd897ae5559"}, {"path": "components/ui/Card.tsx", "language": "typescript", "hash": "19e8804f10238ae85d29031d7bdeb493896fb5ef0caf9a466a59cf78aad6dc16"}, {"path": "screens/AddMedicationScreen.tsx", "language": "typescript", "hash": "0e7b523dba1c9d92282e86c55e7a425449aec3778671c617590d5aca7fce67bf"}, {"path": "package.json", "language": "json", "hash": "be75276645c521218566dc346a8f4c2ab56d89536c9345b2e680b7e7d514d8c2"}, {"path": "components/SpeedMonitor.tsx", "language": "typescript", "hash": "31ff22c96cd1f380565414f433a40e5b325efa83fc84df82ea4cc48e2e681d7e"}, {"path": "components/DataReadinessCheck.tsx", "language": "typescript", "hash": "dce86c3d286f6c5c5dd3f3b8e3c3f33714527a5ad6753bea479447eaab4adc3e"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "fc945b41b97931dc027c7a5a5318c1a4ae5d6aeb78bb977e620e9d6f167b27a5"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "f1f6565d171f001b59f2bffafa98f93b980af5ae7671d0ec6187b6c03dd99607"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "2f1b8299dc6c69f5900ad1dc9de3e4a99cbaece58f9991181e82fb85db26b726"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "87bf8ccca67c9ba500f1eedc1d191ab47798944f331e41a79c62cd33caa91fe4"}, {"path": "components/animalDetail/AnimalDetailHeader.tsx", "language": "typescript", "hash": "dfaeb6a27dc2453f4a19d4a2884765c07c75367f94378a3c60787efb693c6080"}, {"path": "components/animalDetail/AnimalInfoSection.tsx", "language": "typescript", "hash": "2121ca89d88d887591f13f6ab72a9c469a21d1df7ced70119293182a30d92cd1"}, {"path": "components/animalDetail/AnimalSpeedMonitorSection.tsx", "language": "typescript", "hash": "e9510cc94f1733d70911ef68b8dae8da3ab4d59d52fe10c405badf94c4ed52db"}, {"path": "components/animalDetail/AnimalLocationAction.tsx", "language": "typescript", "hash": "18277ae46922d663fee38e502c16961f2819a08a7c747e216f8c8b64cfa47c19"}, {"path": "components/animalDetail/AnimalVitalsSection.tsx", "language": "typescript", "hash": "6ae0a744e5befe9419f804fb6a4fd3e9de0bea5748820b81a7fb8ee2a616597c"}, {"path": "components/animalDetail/AnimalFeedingScheduleSection.tsx", "language": "typescript", "hash": "44ba5233d51fa3a2118378a1a764356461e5b372e762183ad18e402c9e136b36"}, {"path": "components/animalDetail/AnimalMedicationsSection.tsx", "language": "typescript", "hash": "e6a0f051c44e2cd6ed500c1dfed4ad14b799792b743ea87d915236afecf2574e"}, {"path": "components/animalDetail/AnimalVaccinationsSection.tsx", "language": "typescript", "hash": "cff4d7336532adc919d0c37cfc39ec65db0065fe7ef3570dbe13eaab12c5e6fd"}, {"path": "components/animalDetail/AnimalNotesSection.tsx", "language": "typescript", "hash": "61525df355d6af6d4be05a3cacca0ee8b1d8cce697b82292f99b37810434e9b7"}, {"path": "components/auth/LoginHeader.tsx", "language": "typescript", "hash": "0f62066865769b7f896195a8d600b216f815f050cb3dbf7c63de2a059c353c1d"}, {"path": "components/auth/LoginErrorDisplay.tsx", "language": "typescript", "hash": "9b371dbe9e5f998749894c959e72db891e65efff58058b73546c4a0dae737f79"}, {"path": "components/auth/LoginFormInputs.tsx", "language": "typescript", "hash": "ca828e83193cbc57d892aa8ac61d6de5035dde11b51ad4c11dd498a28289b6fa"}, {"path": "components/auth/LoginSecurityWarnings.tsx", "language": "typescript", "hash": "019b4322f36d7745e22d81059c5845ce2628454051958f63fc0e91a6f79b634d"}, {"path": "components/auth/LoginActionButtons.tsx", "language": "typescript", "hash": "407959f963374896d00f3588f286bb388f2e4db8b357741d43e4978057048114"}, {"path": "hooks/useLoginSecurity.ts", "language": "typescript", "hash": "5f7e638d22d74bb6e6e36e6e3a3465850ae6e0439c5205b3e7766cc64147f6b4"}, {"path": "hooks/useLoginValidation.ts", "language": "typescript", "hash": "aca69407fcad86314f5a70a114f89300b2aa17230c324ea426af1102aca73b94"}, {"path": "components/subscription/SubscriptionHeader.tsx", "language": "typescript", "hash": "29d92723d683823c84a7c06487bf893edf3bc72d6577ae9a129f9bcaf069c217"}, {"path": "components/subscription/PricingPlans.tsx", "language": "typescript", "hash": "ed8666cc08ae9f689c425f77386c825fcdd8d42f123bcaba0f37e662f983fbbb"}, {"path": "components/subscription/FeaturesList.tsx", "language": "typescript", "hash": "0bed131c2d8977aaa70daf0459c96347f9e9afc754ac52576063189ea4e04bd0"}, {"path": "components/subscription/PaymentMethodSelector.tsx", "language": "typescript", "hash": "589f0d101e90b87ae49915c7b044421920d5287751bba70b481203f362839969"}, {"path": "components/subscription/PaymentCardForm.tsx", "language": "typescript", "hash": "04f3615cfcd9a87bdaf976c7a93cb9cbd6d66fc9834c463dbadd65744d7030d4"}, {"path": "components/subscription/SubscriptionActions.tsx", "language": "typescript", "hash": "f6eb796edf93ba0cb868d2205eccfc633d2defc0e615300a2260ea6b4c9b5d2b"}, {"path": "hooks/usePaymentProcessing.ts", "language": "typescript", "hash": "e1ad52f3eb9a5a5fbcde9ba880a7be47bff0dd4503d5b72e91326d31ba7ef27f"}, {"path": "hooks/useSubscriptionValidation.ts", "language": "typescript", "hash": "4b2fe7ce133a3d5a396872d131963bb1329e00e9d061d95abb932bc78c57f7e9"}, {"path": "components/subscription/PaymentForm.tsx", "language": "typescript", "hash": "67612786fae5a9759a5e1753bb0acfaf3b56d50cf7a457680e4e51d1c77ae464"}, {"path": "components/ImagePickerWithOptions.tsx", "language": "typescript", "hash": "030f75e369423275bd93718e63654c4c04647ec6ce004ffe90bbed2507bfb620"}, {"path": "components/animalForms/AnimalBasicInfoForm.tsx", "language": "typescript", "hash": "18709f26e13971d25739f5eba368e282aa2e12609cd157fb4f2836ed08134f7c"}, {"path": "components/animalForms/AnimalAdditionalInfoForm.tsx", "language": "typescript", "hash": "05944b7af00231068ca12bc3f8b51fec7db7dd92f496ceea766ca1c885c1ba8a"}, {"path": "components/feedSchedule/FeedScheduleForm.tsx", "language": "typescript", "hash": "28505cbe311651a7ee5a825fdd52b00dc200e3a50e3f206ea85bbe3af16b3fbf"}, {"path": "components/feedSchedule/FeedScheduleListItem.tsx", "language": "typescript", "hash": "2b465e3a7f78f9ba753739c9c058d4d57158e44becc1e2aed12724f89ce24d94"}, {"path": "components/PremiumUpsellBanner.tsx", "language": "typescript", "hash": "677d796a496d1ece5eb9a51d7ed2e5f48ab5267edbce0cea5990ad869034ff73"}, {"path": "components/medication/AddMedicationForm.tsx", "language": "typescript", "hash": "ea47196fa2b4f8585f7d8e336844657f1959b9a13c6e18ffe0b6be2ce405efee"}, {"path": "components/auth/EmailConfirmationBanner.tsx", "language": "typescript", "hash": "983efa9b88e17a56e571f3beb8b6a6a50a5f2c5360eea38f915e0f3222267380"}, {"path": "admin/types.ts", "language": "typescript", "hash": "cbb77720e738761c80ebf29cbf6593b23ff256c35f7f421ccfe2ad56eb6f3965"}, {"path": "admin/components/AdminLoginForm.tsx", "language": "typescript", "hash": "d941ce04cb83f2fe66b6b41ddf86ecbf0874f39563e78ff93d1c8f0ad10095fa"}, {"path": "admin/components/AdminAccessDenied.tsx", "language": "typescript", "hash": "07722a0c17e5ad1c3d1e087ed3a3fe3483681abd45f751942afcb8c60651e661"}, {"path": "admin/components/AdminPanelHeader.tsx", "language": "typescript", "hash": "9cd69f72990d3b3ec76f883f43ff0b136111552ff92ad74b039988ed3c04f8e5"}, {"path": "admin/components/AdminTabs.tsx", "language": "typescript", "hash": "0f4129d17b2818f81c120fdb207c29a47e2551ba3d9c7e84ef9df30c6d0275e2"}, {"path": "admin/components/dashboard/DashboardStatsCard.tsx", "language": "typescript", "hash": "35f8b92c9aa9df7f46e5454f7ecfcfe80996ca1fbbce07d174023e94c1bda902"}, {"path": "admin/components/dashboard/DashboardView.tsx", "language": "typescript", "hash": "5329fb73763e0db7018a2277db26faf658dc9b65aa63a1cabf13ddec075f0811"}, {"path": "admin/components/users/UserListItem.tsx", "language": "typescript", "hash": "726677238d90a7e6cdb61236f02fbf7670aff713ac2f9abcf0ab7bed832898d4"}, {"path": "admin/components/users/UserListView.tsx", "language": "typescript", "hash": "6157b193d756acd4bc475838d5fdb51966cf69245a2de4aeb823bdacec6e434d"}, {"path": "admin/components/animals/AnimalListItem.tsx", "language": "typescript", "hash": "4fccddb425b4322fa5277447e14398f15ced2cc873a94dbd407c743c3ebcc392"}, {"path": "admin/components/animals/AnimalListView.tsx", "language": "typescript", "hash": "daad681a621f7108d70f1990b130867e09d3a658493d0e9a880064fccdfdad76"}, {"path": "hooks/useAuthActions.ts", "language": "typescript", "hash": "69b800d3491572bc20dbf73eabeba89031fd244f1c82f05ebc6c93c9596713f5"}, {"path": "hooks/useShakeAnimation.ts", "language": "typescript", "hash": "c930622e1cfb9618c649cdc9a501ee06388d5fc27ee3caf7cf9d9be5081bc712"}, {"path": "components/auth/AuthModeSwitcher.tsx", "language": "typescript", "hash": "f69816c68fe4eda5d9e5d2c34027c09fb6c28b1e90508838dfd293d1fa5791c0"}, {"path": "components/auth/AuthActionButton.tsx", "language": "typescript", "hash": "c160434c2fac3e95ae90ab7522f583adeb223101c885e177c1991fc6c0b48809"}, {"path": "components/medication/MedicationPrimaryInfoForm.tsx", "language": "typescript", "hash": "6bd0d8070ea562687812cd24b2f7175c304a3a3a6025fc8de4ea586275a832ea"}, {"path": "components/medication/MedicationScheduleForm.tsx", "language": "typescript", "hash": "ee884554c08f53746e2045aabf36fefaf848ea02b4817a6cdada3155609ba2e0"}, {"path": "components/medication/MedicationReminderForm.tsx", "language": "typescript", "hash": "af2fcb2a07fc5a5b689d5b0c1f2aef8e359322d4ca3f5ad83ae890106c9a42c5"}, {"path": "components/medication/MedicationNotesForm.tsx", "language": "typescript", "hash": "acf866d6d168f60acdf5d8e028ddb5ab8255ff94556f2b29fc47e1cb029a4924"}, {"path": "hooks/useDeviceReadinessCheck.ts", "language": "typescript", "hash": "f15e4386adece3a515615409585dc867fd4a87b98e7df5c2ff6028db463c7985"}, {"path": "hooks/useDatabaseReadinessCheck.ts", "language": "typescript", "hash": "0fa1566cd98a25126466730f26396f0c88f8c010767aa2da1228bf20fee3fe22"}, {"path": "hooks/useAnimalDataChecks.ts", "language": "typescript", "hash": "649ff55f75528d31cb71ad45e1cd9a16903f3ef9fcb4da2ff850f919713ee772"}, {"path": "components/CheckListItem.tsx", "language": "typescript", "hash": "87aadd128848da2c83246101edd99aacdb9c79f206c67d1ba59e00fdb44b113f"}, {"path": "components/devices/BluetoothUnavailableView.tsx", "language": "typescript", "hash": "d545e8713a3be882fba68abbd78f29f6c0c0f3be8e7a5cc0f2279d7853c5c8e9"}, {"path": "components/devices/PairedDevicesList.tsx", "language": "typescript", "hash": "0eac4a8ebe17d30280043aecaf77af0f47b3a338bbde30d4516fe4cf22de70bf"}, {"path": "components/devices/DeviceDiscoveryControls.tsx", "language": "typescript", "hash": "3d75efceee19d24585c90411fa0279d82d5ccdee44bd9cc4f006cb57fa8334d1"}, {"path": "components/devices/AvailableDevicesList.tsx", "language": "typescript", "hash": "d27cfe6021ffcc6f3d27a0d83cf286e152f6b86b3b14b6d4aadea7ffa5bf1d75"}, {"path": "components/location/MapViewComponent.tsx", "language": "typescript", "hash": "7b22da3bcd5463bc2f22046d382e526d5cee3a641ec3ac518611df4d33da9466"}, {"path": "components/location/AnimalLocationInfo.tsx", "language": "typescript", "hash": "db7c6ff191bc172df509fd6d2b67ea165e779d5f13efff1159f7654907c2b227"}, {"path": "components/location/LocationActions.tsx", "language": "typescript", "hash": "b3d351a03812e406067cd50aca5d368bab0abcb7c275bb07a5e8ff6d5dbe3757"}, {"path": "services/animalApiService.ts", "language": "typescript", "hash": "d7ba144b61676dbb0ca4af24e0c961a481d9e27587366ad7bb2fdd188f85cd06"}, {"path": "hooks/useAuthForm.ts", "language": "typescript", "hash": "0cacd9a4b09f1506b443c47bc562911a74d99eb418aa57e5c637aa7f216734bf"}, {"path": "hooks/usePrintScreenLogic.ts", "language": "typescript", "hash": "4df62754f35b7bd5f1148fa3b3b4e22871e8d774ba129ccb4af08d6e7770cb64"}, {"path": "components/print/FeedingPrintItem.tsx", "language": "typescript", "hash": "dba4bf19b3fa8e432c5cc48ac6ebb407d28cc4be9c29295e1d67b0524c679d61"}, {"path": "components/print/MedicationPrintItem.tsx", "language": "typescript", "hash": "a0c5085a4134c314759daaa2fa18b770f0341d11d52c79798c2b9c432f2fb0b3"}, {"path": "components/print/PrintPreviewContent.tsx", "language": "typescript", "hash": "1676e6863eba895bed53c2fee99e05b5957a0ee297ac46e9bcbfb6f0cd3a5255"}, {"path": "components/print/PrintActionButton.tsx", "language": "typescript", "hash": "da19dfb1a40caf898e403800948227cbee7d319700d2e85c87408012bdb3c723"}, {"path": "hooks/useLiveVitals.ts", "language": "typescript", "hash": "eb326da16bb92504ed99cdf46498cf11aec666743d5b241cc69279ed12850df0"}, {"path": "components/liveMonitor/LiveMonitorHeader.tsx", "language": "typescript", "hash": "a81176926e898fc8d11f25d688229a3f65c46077807e7807c2cb3f8af5f46d8b"}, {"path": "components/liveMonitor/ConnectionOptionsView.tsx", "language": "typescript", "hash": "93704e7138309f33e7229c5e7fc342300b98f9fb4163d2546be61e21bea6dddc"}, {"path": "components/liveMonitor/VitalDisplayCard.tsx", "language": "typescript", "hash": "69d4825733e5c5ab2d0857f14e64d2cab7bb39f6cb498c5427f62d4446e8f7a1"}, {"path": "components/profile/ProfileMenuItem.tsx", "language": "typescript", "hash": "eba0eafcb24f3c0861130c2a5e1afb27087d9589098f608ab5a1235cb54da7e3"}, {"path": "components/profile/ProfileSection.tsx", "language": "typescript", "hash": "975e227f8b93f257b422fd2d4458749e1c268abad31a41120abb765c6ee1f0ec"}, {"path": "components/profile/ProfileHeader.tsx", "language": "typescript", "hash": "3ec4d427ddcf6affbb87d9093a63741c76d04b76ef9d3d3374a0b225cc3cba5f"}, {"path": "components/profile/SubscriptionInfoCard.tsx", "language": "typescript", "hash": "d34a9706788ae031b775ba3c375b5dcc43a9cb8cf080646f8dd12a5c3ff7a555"}, {"path": "components/settings/SettingToggleItem.tsx", "language": "typescript", "hash": "6610e27b2471c18edd14dd4daf03cc2075975f81022b15136d99210a8d6b0637"}, {"path": "components/settings/SettingTextInputItem.tsx", "language": "typescript", "hash": "f6ff119719154ead38c2c0fc7f67aa521d36476882135c67af440df75891995f"}, {"path": "components/settings/SettingRangeInputItem.tsx", "language": "typescript", "hash": "f7bac7485f30ecb280a5d3b9f69d4ab6f5f8d668458cf489c2932802b6cb7c6d"}, {"path": "components/settings/PrivacyInfoCard.tsx", "language": "typescript", "hash": "f70c551ab2828add1cebc55b3b40ad963cb069d101cf836a4ba205353d4d732f"}, {"path": "components/settings/DeleteAccountSection.tsx", "language": "typescript", "hash": "dabed8d8c64cfc01f6ce9766780d4c6d146704a483c2b126306f17a47e891486"}, {"path": "components/settings/DeleteAccountModal.tsx", "language": "typescript", "hash": "756b927ad64c4bcb074435d863e0f596953ccf5143ee308ba546ebf9130bbac0"}, {"path": "components/settings/ReminderSettingsCard.tsx", "language": "typescript", "hash": "10dcadc9e8a58becceb3fe3cb064530018fd3ac7aa519591c2eb5a46b6f557b8"}, {"path": "components/profile/ProfileImageEditor.tsx", "language": "typescript", "hash": "e943340a5c9f81442f13c1ba9c5ea0dfd2122020ddc7c5a58915f537f2bec3f6"}, {"path": "components/profile/ProfileFormField.tsx", "language": "typescript", "hash": "922b6c7cc72989c4937965a0625f8579537bf46c3136d1e6308fe42e14ccacbd"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "c8e933543f244ca7d323ea021be902d45461790962ea874319a85819d9fd9ad9"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "0ca2e3eff026f7babb249e9353c7d0551b7da160e93833e723fa47963ce95a33"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "9f7d4e95dd9da0b03da9c05f6507a76413bc87da4f4d1b8f0d63a8036a033765"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "ed8ac0acdd3d9ff71de88ab87376a3373d11beca202fe8e7bf1a76ce709cf73e"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "8e532ec13f8c25a49540067a4ba037d49fa8722e2aae1b384288069facd175bf"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "e3d40e49627c45a3a68ac4ed708d327eb199b1b43bdbcff3a1b10ccc5613a0bc"}, {"path": "components/subscription/PlanSelectionView.tsx", "language": "typescript", "hash": "334d13e0e79ede16b6db7a1c7f7bd6ce9cfb2b04292c224c3b9c2970b7ae5362"}, {"path": "components/subscription/PaymentProcessingView.tsx", "language": "typescript", "hash": "2ff4fb783db716137bb12ca4ed8f919401d5b433aac7a9d54a926e61e7fc5387"}, {"path": "components/contact/ContactForm.tsx", "language": "typescript", "hash": "45abe1b87fb700a1c3fd93b705a5c99470b960bae022ff20e78cd85a50153bf6"}, {"path": "components/contact/ContactOptions.tsx", "language": "typescript", "hash": "204497ec6ea749831c9dc8e3532ad69122d7734441e5fa72a4bb551d50a6d766"}, {"path": "components/contact/ContactHeader.tsx", "language": "typescript", "hash": "3baec66fc1fa34a132995accf9a1484fec0e27fcd1a3880fc8c8248b8b0b5d3d"}, {"path": "components/feedSchedule/FeedTypeInput.tsx", "language": "typescript", "hash": "c43a954e0de4020e9d36690ef6aaf6a5b4437802d5ae8468b1c67c0c339dfbf0"}, {"path": "components/feedSchedule/FeedAmountInput.tsx", "language": "typescript", "hash": "58cf253aae875bfd5a96671f0b8a5f72539fa4e3ef0c0e3c50a5c4037ee9d4ee"}, {"path": "components/feedSchedule/FeedTimeInput.tsx", "language": "typescript", "hash": "b82619a2061fe6c6440082f2a6e4d7088dae2f2d7d05a37c308d84c78b1b40ca"}, {"path": "components/feedSchedule/FeedNotesInput.tsx", "language": "typescript", "hash": "6a8f1bc994f512413a51d41cb9cc0d84bbe57648257fe2b5ade1422ad2622dc4"}, {"path": "components/feedSchedule/FeedReminderInput.tsx", "language": "typescript", "hash": "3d8373ca08c26f48ccdc7dfcf73c828fc60a38aa43f35912a0d942aada862eb4"}, {"path": "components/feedSchedule/FeedFormActions.tsx", "language": "typescript", "hash": "e37256bcb979db7eb58e0a41b4b32628c98147b13b1cec7370603e918141f753"}, {"path": "components/medication/MedicationNameInput.tsx", "language": "typescript", "hash": "2286b2264e9070fda490a067f0b4389e9adbf7766e6856d55b11fc8646259bef"}, {"path": "components/medication/MedicationDosageInput.tsx", "language": "typescript", "hash": "2452fe7a93a25d2af7153aeb1227ee6ab80bed0f73c212e9f625132c05776ed6"}, {"path": "components/medication/MedicationUnitSelector.tsx", "language": "typescript", "hash": "3604d3b58de863c4707e14a742ee9708a513e2f396e1078eafa32fb8daed08bf"}, {"path": "components/medication/MedicationFrequencySelector.tsx", "language": "typescript", "hash": "0f5101c741adc27df61de0beaf93a73c05a6386c20ca6c337470c3eb74f16c91"}, {"path": "hooks/useHomeScreenStats.ts", "language": "typescript", "hash": "d98bd3dd26d31b4abecc7b848d46acd5bbd27121a334c126aae0c92e8c893deb"}, {"path": "hooks/useHomeScreenAlerts.ts", "language": "typescript", "hash": "9d89b338224b22323ace4ce81f0db00bf8843f2e961c4d50866481222ed9ad96"}, {"path": "hooks/useAuthCredentials.ts", "language": "typescript", "hash": "bf2f43cdbf8b384802a6719dbb6d073b6e5355aabbd81558dbfdce8467fd5658"}, {"path": "hooks/useAuthMode.ts", "language": "typescript", "hash": "4b90507297ffe4a703b0299d7891076248b0fa6474823645c8c9f41d10fc577b"}, {"path": "hooks/useAuthError.ts", "language": "typescript", "hash": "d12cba8f82841147730b52616b5ae0f94fceb0619cbb09c6bf65e2ba25a48355"}, {"path": "hooks/useDataReadiness.ts", "language": "typescript", "hash": "25499dbbefb547ff3e53bfa5f47a4d1d1bb4367f6834f77a35de28772e501caf"}, {"path": "components/dataReadiness/DataReadinessHeader.tsx", "language": "typescript", "hash": "a8efa010a229800e5cf70295436ee81f690d95b1f6a48cdb2e56fd85b65f1430"}, {"path": "components/dataReadiness/DataReadinessSummary.tsx", "language": "typescript", "hash": "31ca7b2d9ad4976c1ec56b80cc5e79848366afb32f2463f25dd9662f02580cc2"}, {"path": "hooks/useSpeedDisplay.ts", "language": "typescript", "hash": "46a29cc6531382a61448d95b7d0ee4c68dba214cfd794cb3f42f6e4cfd3b508c"}, {"path": "components/speedMonitor/CompactSpeedView.tsx", "language": "typescript", "hash": "64d3499c7a5d0c44e0062b1044c3ab29cedc1b33351cc5ec8bc67b1cefdb7483"}, {"path": "components/speedMonitor/FullSpeedView.tsx", "language": "typescript", "hash": "3a54d37fe548f4f12d23e089bb4bd9f13baccf695cd3c3b64745cb31dcf75c4c"}, {"path": "hooks/useImagePickerActions.ts", "language": "typescript", "hash": "73642d473185694871f453f0d8ff9f639edd81494ba39fc74a036137971df889"}, {"path": "hooks/useDeviceManagement.ts", "language": "typescript", "hash": "abf40b1ab07e5961150675014cd085e2ba53db43c8aad54176e13b98949a3f82"}, {"path": "hooks/useAnimalDetail.ts", "language": "typescript", "hash": "0ed8187ec9cd875365d9c0ec3df643721acf6a0a229c25e8a7d62f2a2a3b942e"}, {"path": "hooks/useAnimalVitalsHistory.ts", "language": "typescript", "hash": "c397ac3ef3f9382e145361860c69d19b6a70febd1f593e1d73bb0322b8fb64a3"}, {"path": "hooks/useLocationManagement.ts", "language": "typescript", "hash": "bdc1e19fc6712bbe3d1fc640979bbec339d2c37da6cb40390bba3e2722bfb6eb"}, {"path": "supabase/functions/secure-map-proxy/index.ts", "language": "typescript", "hash": "2ea6ffa4a82eaf59a474361e662c757783f8a1b2f5c0f269a62c49a9076f43e7"}, {"path": "migrations/004_implement_rls_medications_vitals.sql", "language": "text", "hash": "1931877e0909ca1829850882099efc6e50569d60a9f61a38e5c7fe97e299f093"}, {"path": "migrations/005_implement_rls_vaccinations.sql", "language": "text", "hash": "7b4cc920b8ee27b752ae470685e387ef75f84afd573feb85af25b2c521e946e1"}, {"path": "migrations/006_implement_rls_animals_feeding_schedules.sql", "language": "text", "hash": "936fecba3d2fddeff904c2a8a35a1b4741335ea9efd1536713548217c839ba4d"}, {"path": "migrations/007_implement_rls_devices.sql", "language": "text", "hash": "f5b3a60523347f102afd1df0529fd8d9687b44c4389a014587d253c94f08aaca"}, {"path": "utils/validation.ts", "language": "typescript", "hash": "464bbb56d98c295e1bd75e0b1ff947d7ba6de1cfa055592b5ac648688d650911"}, {"path": "hooks/useFormValidation.ts", "language": "typescript", "hash": "aca0ac3b7ce4c54a04eabc4682a6a5f7d8ac8ec964d95f5c02a3df0d7bc2bb62"}, {"path": "components/ui/ValidatedInput.tsx", "language": "typescript", "hash": "6a311d86d4f5eb0332a302cb197bd77474a08ba21cd8735245fa01afdeceb908"}, {"path": "supabase/functions/setup-storage/index.ts", "language": "typescript", "hash": "3a45033e37a379463aea1eee6baff055e58f7379e05af7c6ad571d5659b24beb"}, {"path": "utils/setupStorage.ts", "language": "typescript", "hash": "aadad6a23dd195562225af7877ab2981ae2bcece494525aee0da8ba1798aec24"}, {"path": "migrations/009_create_devices_table.sql", "language": "text", "hash": "494e40645379a61d92aa9a56945077cba059584543914c5e7c503c135cf940ad"}, {"path": "components/devices/DeviceScanningAnimation.tsx", "language": "typescript", "hash": "ffefba167476da732212bb1cbf4cf8a3c87c4205f220360071895a3b235fb80a"}, {"path": "assets/translations/index.ts", "language": "typescript", "hash": "db4cd138662e34ed4e77c40b29fcbd533de68c5992c8c7d161f9c1f2ae826a3e"}, {"path": "contexts/LanguageContext.tsx", "language": "typescript", "hash": "cd327df6143aa9773cf890480b61c75db0c7a5aec46da92c04c70c002b49da0f"}, {"path": "components/settings/LanguageSelector.tsx", "language": "typescript", "hash": "12b86729199074cc872805390692c17fe5f43de21f01bf140c3370de11312f36"}, {"path": "components/settings/LanguageSelectionModal.tsx", "language": "typescript", "hash": "cee8b0fa16f31124c5038226a8fb287c7fb6856435b820db57aee35f5ce65fce"}, {"path": "supabase/functions/get-assistant-trainer-analysis/index.ts", "language": "typescript", "hash": "e2b729e7f570ebe812d71df76b6c700411f86233153f85ff07f86bdffd14fc42"}, {"path": "store/aiStore.ts", "language": "typescript", "hash": "32232059012111737416e7b7c0af2cd8328fa7f3c6473497e12470feb2f148c2"}, {"path": "screens/LogTrainingSessionScreen.tsx", "language": "typescript", "hash": "431c7198a3500851ea2b542bfbe2c9d34857ab971ad88be3c8fccc080850aaae"}, {"path": "screens/AIAssistantScreen.tsx", "language": "typescript", "hash": "e2015b5c333efc68f63d8da319c6b0c173d63016bed7f705afa04f04b8149310"}, {"path": "components/animalDetail/AnimalAIInsightsSection.tsx", "language": "typescript", "hash": "1755ccb0907411669c257e12f3fa586c47cc187bfd772e2e38ddcaab93caf6b1"}, {"path": "components/animalDetail/AnimalTrainingSection.tsx", "language": "typescript", "hash": "72df6348286598393ca1be52225f0c92594b0d1d04f10ee0aaa52a3db2beebb8"}, {"path": "components/AIAssistantWidget.tsx", "language": "typescript", "hash": "2f6d65cca7851e4659d9891453a0550acc0d8d9b38ca9d5822c31b317d9413fa"}, {"path": "components/AIOnboardingBanner.tsx", "language": "typescript", "hash": "c7ee4dfe35c763156e8d10c677cfd3eb25317ce50a1e103e2153e1a505899dcf"}, {"path": "supabase/functions/get-realtime-animal-analysis/index.ts", "language": "typescript", "hash": "35deef482f2a04a3534da0513a6fecee5dc7040c74c97f9315cbd71251f0ba8c"}, {"path": "assets/translations/en.ts", "language": "typescript", "hash": "5a2cdc2b0003a3b3a0ebd6c22b70a226cc06d5eeaeca13cc12fdf02e4eb2c64d"}, {"path": "assets/translations/ar.ts", "language": "typescript", "hash": "24a9ae0fb521a60142e0d96291f1bb3cc46c3b6869a1a307df9265630ca9bbe0"}, {"path": "assets/translations/newIndex.ts", "language": "typescript", "hash": "e5683cb255b28994f0cb5494505b40f1d51267095e0beb516d8218ae246cebd6"}, {"path": "store/slices/deviceApiSlice.ts", "language": "typescript", "hash": "2f9d82e6a4d182a75f48e187969fe1b0e06f56bceb3437d5c2e97f9efdc12d67"}, {"path": "store/slices/deviceScanSlice.ts", "language": "typescript", "hash": "10fa88f168c7961669aa01b2556d2e4c8f7f838c44f59075a3c7364f03f34ad4"}, {"path": "store/slices/devicePairingSlice.ts", "language": "typescript", "hash": "6d59dad346677086b0a2083180092ccaf0f97329f521d7582ed580ef50052082"}, {"path": "store/deviceStoreRefactored.ts", "language": "typescript", "hash": "7728661bdd3a26b6998550a581c9450a0834bbe61af0f9e1376dc0cb42c6aede"}, {"path": "components/aiAssistant/AIAnimalInfoCard.tsx", "language": "typescript", "hash": "671054f44079762e12e6f7377ecaeaec2fa7f425317b54aceb32db02105b71c2"}, {"path": "components/aiAssistant/AIReadinessScoreCard.tsx", "language": "typescript", "hash": "04dcf4ec165b69a2820ecaaa95af54c303c120419a5a79212525104ff3cdde70"}, {"path": "components/aiAssistant/AIHealthAssessmentCard.tsx", "language": "typescript", "hash": "4a72768c3ed489eea32094aa5feb6fd055c56367ede55de79fc9b8062fc39111"}, {"path": "components/aiAssistant/AICoachingTipsCard.tsx", "language": "typescript", "hash": "49cf6e4d5199285cc4f2172e3e9ad28f62da09c2aad85795216a527fb0a04ca7"}, {"path": "screens/AIAssistantScreenRefactored.tsx", "language": "typescript", "hash": "26210aa447902700c605ebc4cc7d81326336b78fe135ed350d9d9c27f6743fdf"}, {"path": "components/aiAssistant/AIWidgetExpandedContent.tsx", "language": "typescript", "hash": "f0833ac0f8cd1ae02536a528e6f459f1b9fb311bae3d1e59def8bc8a19dba191"}, {"path": "components/aiAssistant/AIWidgetButton.tsx", "language": "typescript", "hash": "0e1a5c554269369e3208fd67ab5056c3fa051a7625e0145de5af1d0dc957f9fc"}, {"path": "components/AIAssistantWidgetRefactored.tsx", "language": "typescript", "hash": "641d2c505ab22eb8234d4d92cb3559d2735f79d933e273cd398efed166d4aa8c"}, {"path": "components/devices/animations/ScanningCenterAnimation.tsx", "language": "typescript", "hash": "cca0b6e2a51f33634c669b82f8841bd194088dc6777ec2e37d826f1fa9030503"}, {"path": "components/devices/animations/FloatingIconsAnimation.tsx", "language": "typescript", "hash": "81df712e5455bbcca4884bf8a5938cd6ad82e129ba9d612b398bb57101c4f89f"}, {"path": "components/devices/animations/ScanningProgressBar.tsx", "language": "typescript", "hash": "65f49bdf009bec2f4ea7c3e3148df9ae3ba6b00dc473c550b7adae231856a39c"}, {"path": "components/devices/animations/ScanningStatusText.tsx", "language": "typescript", "hash": "d383c494e435d34d9b29000267641c1fc0e1ca752dce4b7362f26cff7fc2eb9d"}, {"path": "components/devices/DeviceScanningAnimationRefactored.tsx", "language": "typescript", "hash": "8469c3a27025642fb0a2f2183baa11c2aaf75d982038f050e7ff2aef533cd663"}, {"path": "components/training/TrainingSessionForm.tsx", "language": "typescript", "hash": "f4d31075752199c10ba370e725fc9f1970c50fb18a5dbb3a82abc9b903c754aa"}, {"path": "components/training/IntensitySelector.tsx", "language": "typescript", "hash": "d8acab1b8cd9f4d7113a5acd2ed97928de07ab466819ed6489b085cabf832d39"}, {"path": "screens/LogTrainingSessionScreenRefactored.tsx", "language": "typescript", "hash": "b24915453b194fa69b65d41579f2a73b82ffc5ebe8cdfcda3851c9794d84bd0f"}, {"path": "store/slices/healthAssessmentSlice.ts", "language": "typescript", "hash": "c2ab1242e1f79251d12ca5852ecc53055250b709b3c9cfa5eb1e0ff500a54527"}, {"path": "store/slices/trainingPlanSlice.ts", "language": "typescript", "hash": "1b5349f092e4a39cf89111490386790c830996234579f81ab5c23bb36b67f6aa"}, {"path": "store/slices/readinessScoreSlice.ts", "language": "typescript", "hash": "a9fe07ce7b74a7748cab9ba5c87b5a4a533379fbdb8b8504781e83ea9eccdd70"}, {"path": "store/slices/coachingTipSlice.ts", "language": "typescript", "hash": "b4146e82aba15cc5a1d0917e230fbfb8fc8baeca93dff5fbfca326421a897cf0"}, {"path": "store/slices/aiAnalysisSlice.ts", "language": "typescript", "hash": "683c31f3120d2c0026726e56eaebfe83c7563f349eb8592565333048eeed8ee6"}, {"path": "store/aiStoreRefactored.ts", "language": "typescript", "hash": "dac2bbaf7766dc07cbad8d99a6cacd786ccac5f8d8fd5fe20f658b3be2b91155"}, {"path": "migrations/010_create_device_ordering_tables.sql", "language": "text", "hash": "d821fbef65d353970e142a9bde9bfc58754316c242640d19531bffd47d5abba2"}, {"path": "supabase/functions/create-device-order/index.ts", "language": "typescript", "hash": "2b232e3ba7e33dd7ae7184160df3aa6103007d2b90bfedae258d4038c23fa7f1"}, {"path": "store/productStore.ts", "language": "typescript", "hash": "b9c61940f0749fcfd96369876d500601023aae7912315569de104f8fe125fa2b"}, {"path": "store/orderStore.ts", "language": "typescript", "hash": "0607987d4e6ed1a160915f2634709f7f893c81488f9083429af41eda00fcd60f"}, {"path": "screens/OrderDeviceScreen.tsx", "language": "typescript", "hash": "74adc87f98d7bb62b7c6ad284bac1662bd5846274fc5d17053d284562781a4aa"}, {"path": "screens/CheckoutScreen.tsx", "language": "typescript", "hash": "5a62545c5424436225af7b065ddf8dadc454d508dcca98c232898a37c06f2b97"}, {"path": "screens/OrderHistoryScreen.tsx", "language": "typescript", "hash": "a8df5e4e9f23f27d8c7abafe5911552f6d849c748d135f7ab62e6b5a26862ed4"}, {"path": "migrations/011_create_dehydration_logs_table.sql", "language": "text", "hash": "04a69b03934e2296ce689c819d574b9f2544aaeb92ea87880c65466bbea00409"}, {"path": "store/dehydrationStore.ts", "language": "typescript", "hash": "8f38696837d90a8b22a74c0b186f75a561501864682c49f46d94f37cdb08cbbe"}, {"path": "components/animalDetail/AnimalDehydrationSection.tsx", "language": "typescript", "hash": "5254144761e493f1a40b1731f09dc4f1d556ef69a8b5e7bc42474661d47721a8"}, {"path": "migrations/012_enhance_training_sessions_table.sql", "language": "text", "hash": "91e8035d4b3bb505c20a925238414bd0e9fab0596694a46d981b60a3cef02f7d"}, {"path": "hooks/useAutomatedTrainingLogger.ts", "language": "typescript", "hash": "00602015576f25394405730d6cfac8783b6347d9a37252404e6aaa0a36eae2d4"}, {"path": "supabase/functions/get-dehydration-analysis/index.ts", "language": "typescript", "hash": "f530514eaad8e9baef9974ad442f10e63128644a882aa44bb06ced66ef14e202"}, {"path": "components/chat/ChatMessage.tsx", "language": "typescript", "hash": "e790c92918658934c2fa329b50e96683de29a8cec7302db503fa4ee1451c99db"}, {"path": "components/chat/MessageList.tsx", "language": "typescript", "hash": "2c45aebb72599099b41a6af564ab4a4e63de86f1229f08bf1bb6b0242e66947b"}, {"path": "components/chat/ChatInput.tsx", "language": "typescript", "hash": "bd24f347e816ab515fb3032a5224c4c3dbee55a1a90e8dc36cc5e452f30ed95c"}, {"path": "store/chatStore.ts", "language": "typescript", "hash": "17d80e1dc4ee8458c86bee0a09ec08d803210a9fb8ea8b9f57c7ea689f36e1a2"}, {"path": "supabase/functions/handle-ai-chat/index.ts", "language": "typescript", "hash": "217ff109f0f3c3f5d8c73d8ab11a530b294f85ceed5a29ba77c0f6f5b6482561"}, {"path": "migrations/013_create_ai_chat_tables.sql", "language": "text", "hash": "2998815b455a323d59b94d821bfbe34765917c68fdd7768b02fec0b2f47a3399"}, {"path": "screens/AIChatScreen.tsx", "language": "typescript", "hash": "a670193dcfe4281e2821e61f387ab8712c3ac9624688ffbb15800b8dca92f636"}, {"path": "components/ai/AIGuidanceComponent.tsx", "language": "typescript", "hash": "8f756f9429a70ad9615816df0d85c7edf4d4527e8ae49340094a311e3470a05c"}, {"path": "hooks/useAIDataDetection.ts", "language": "typescript", "hash": "de9827f154b2c658053743cee487e5053f4aff22bc605f80835e24183b1ad682"}, {"path": "hooks/useBiometricAuth.ts", "language": "typescript", "hash": "a7cb2247dac6583d39265edf2d76ba7a9b17044c0db3b1d6c38fbaeffef14314"}, {"path": "components/auth/BiometricLoginButton.tsx", "language": "typescript", "hash": "a2f3be578e4289df223b65aecfda25acf7d7e7d3ae483a3b0a2685ff6f2cadfb"}, {"path": "components/auth/BiometricSettings.tsx", "language": "typescript", "hash": "98d77b91423eb9c299c7c297b2e47b3ab44bb827329ad2a6acce69838e970b0e"}, {"path": "components/animalDetail/dehydration/DehydrationHeader.tsx", "language": "typescript", "hash": "cbac2931c5a50a61a34f88db02d8beb5ba6c7fb9fa0ad6aaf7b8d11d261fafda"}, {"path": "components/animalDetail/dehydration/DehydrationStatus.tsx", "language": "typescript", "hash": "a75b38e1f468b868294883fde8106fa3eb2f71c78cc9f0af5aa7f3822544a62b"}, {"path": "components/animalDetail/dehydration/DehydrationLatestReading.tsx", "language": "typescript", "hash": "7681e6672e49e8d33b9d9ef80a4a635277933396efc9a736facf555dd2706e3c"}, {"path": "components/animalDetail/dehydration/DehydrationAlerts.tsx", "language": "typescript", "hash": "12b70ebbe31e4bb4e88c7f02ae900abe643ff1d7334f3d5bf6589805371fceb0"}, {"path": "components/animalDetail/dehydration/DehydrationControls.tsx", "language": "typescript", "hash": "8ea4546a4f89cdf1113d57c2d338eb5dbabd39005adf2be737fdd1a6c3bd3edf"}, {"path": "components/animalDetail/dehydration/DehydrationAIInsights.tsx", "language": "typescript", "hash": "370975ba266848ce84226934488dd2b57ef71fcf92801d98b64cb2d8739902b0"}, {"path": "components/animalDetail/dehydration/DehydrationRecentReadings.tsx", "language": "typescript", "hash": "5491cea8facda810f91d220b9588e882c483103adfe20e27f7ccd07066da1795"}, {"path": "migrations/014_create_ai_health_analysis_tables.sql", "language": "text", "hash": "70866927100a659d00bb3aa14ea03f2512a30fa04876d9db506029ebc37ad1c4"}, {"path": "store/aiHealthStore.ts", "language": "typescript", "hash": "90bbcf483ce66ec7e08433bc8eac92a488a896bf4b95cf88a56be562d05a162f"}, {"path": "supabase/functions/calculate-daily-health-score/index.ts", "language": "typescript", "hash": "d9152a54b091e069837287f352fc35fa31658b95aa49c623f9c05371b7eb2394"}, {"path": "components/healthScore/HealthScoreCard.tsx", "language": "typescript", "hash": "cc607390fcf06221c7050788cd07ec3ffc9d09c9cffdef1da3d57efca775a89b"}, {"path": "supabase/functions/analyze-disease-risk/index.ts", "language": "typescript", "hash": "c58bf84ac7b6a46d3c667318512a55510a4b7b2e1e3d94b651d99398ec6b4451"}, {"path": "supabase/functions/generate-smart-alerts/index.ts", "language": "typescript", "hash": "5707a5596dec391d59729ff3f2356674ef429ace2261fa8683113b252fda631a"}, {"path": "supabase/functions/process-health-trends/index.ts", "language": "typescript", "hash": "5112ae63f3808d4d107665dd64360609b865e5fc6cbb34ecc9c0f029d49dc8cb"}, {"path": "screens/AIHealthDashboardScreen.tsx", "language": "typescript", "hash": "6eaab4d0e5c3bdc38d1aa7931e45cee39f29501f255c99e1373d75cb5bef004c"}, {"path": "components/smartAlerts/AlertCard.tsx", "language": "typescript", "hash": "f8d3eb82fc50c9c4c65536309f49f159c3cd392be144c97b8324d9e0ee185ac4"}, {"path": "components/diseaseRisk/RiskAssessmentCard.tsx", "language": "typescript", "hash": "0296b209d383cb6b3c8e2e6bc058a21fdc621b668fb741496ed73b0a0aa4d08c"}, {"path": "screens/HealthScoreDetailScreen.tsx", "language": "typescript", "hash": "31ee271637a7e6580e0602a6dddd6f8dbfd8434a11ead41230a23accc6aae18a"}, {"path": "screens/DiseaseRiskScreen.tsx", "language": "typescript", "hash": "30a36e4cb905bd572a6656387fac11ef3cdd78f1699d86a5818e4423b0118ce4"}, {"path": "screens/HealthTrendsScreen.tsx", "language": "typescript", "hash": "716919b3b40044c4d9c511fb4d063499b0415eea71ddb1b7bef0f7188eeea0dc"}, {"path": "migrations/015_create_behavioral_analysis_tables.sql", "language": "text", "hash": "5979a8de9a5b8cac7a95cfa895b7c966d04852e8feabb7ec1ede7d6b8097f59a"}, {"path": "supabase/functions/analyze-stress-levels/index.ts", "language": "typescript", "hash": "e75d477131e90e5b4c2c2c9d29b4f6a8602cc59bd87cde2203aac9cf829c6500"}, {"path": "supabase/functions/analyze-sleep-patterns/index.ts", "language": "typescript", "hash": "c8acb35e3404de4d0f0a5f1de60c6fbebaf6ff3b7dff21a4fa7508027abb3056"}, {"path": "store/behavioralStore.ts", "language": "typescript", "hash": "be796c6968d8acc69a2be943e2dadc01a686999205a761033bcb4a6744c1fbe3"}, {"path": "screens/StressAnalysisScreen.tsx", "language": "typescript", "hash": "8274d4647c4bf3c1c985e03e0b4c1a7939a51ff7cd66d27da92cdfbc9d26308c"}, {"path": "screens/SleepMonitoringScreen.tsx", "language": "typescript", "hash": "d1d0d82540f65c075b7b43ae4363ffdf29d3598f2c375c932a00ecec77ac5c1b"}, {"path": "components/charts/StressLevelChart.tsx", "language": "typescript", "hash": "5bae29dc256acb1c3eb38472aa8ea8134e2632c22e09c12f47ad082bb4fbe426"}, {"path": "components/charts/SleepQualityChart.tsx", "language": "typescript", "hash": "84c34cebc89cf3900b4a81ebf09c7caa5ba0728d4733bd5ea146ed045728de04"}, {"path": "components/behavioral/StressAnalysisHeader.tsx", "language": "typescript", "hash": "d3edf5f9414c760bca96f5ec0e1a59aa1e2ceaf67658751989026fee5c01caff"}, {"path": "components/behavioral/SleepAnalysisHeader.tsx", "language": "typescript", "hash": "61af90a95474b13ad42c70e4389332efd03dad811966b4d9eacb46b73226a1d9"}, {"path": "migrations/016_create_environmental_analysis_tables.sql", "language": "text", "hash": "b05607c4aeca0faa4aab8826a42cc358bd7a56e34c25e912b349f0c3417f49b1"}, {"path": "supabase/functions/analyze-environmental-impact/index.ts", "language": "typescript", "hash": "c5b359ff28a1752afb75e0b02be16feb650c6ba4c4cf3464067c59327765349d"}, {"path": "supabase/functions/generate-predictive-insights/index.ts", "language": "typescript", "hash": "fab8a68d46b240f5f3d9f53dab284824a2b1cc29164d1b6c4d04859abbef4951"}, {"path": "store/environmentalStore.ts", "language": "typescript", "hash": "2a64f16157f0391789ac6d7fb61d51d88602f6714d2543ab720e82ff87655a79"}, {"path": "store/predictiveStore.ts", "language": "typescript", "hash": "08bbbdb9418f690b217227a6cc17655a3fa770d00ad46fd7ecbeb223d5cc8036"}, {"path": "screens/EnvironmentalAnalysisScreen.tsx", "language": "typescript", "hash": "b8635c46731ffffa93f9f4f3bac819d6fd7fc9af6ca9b4b4365e407ba7022bf3"}, {"path": "screens/PredictiveInsightsScreen.tsx", "language": "typescript", "hash": "964ba3d9866641609b631a358026bff7968fe9ff0b5264a2d12e7a582e47d3ae"}, {"path": "migrations/017_create_platform_optimization_tables.sql", "language": "text", "hash": "899f58422729146dd29a8a97d5e73e0acf1cec0ece16555d0cc7ad9352641217"}, {"path": "supabase/functions/process-realtime-data/index.ts", "language": "typescript", "hash": "6c5113a406ac367b6fb585833c0585fe80145f3d9bb53f36df74d29bf560f36e"}, {"path": "supabase/functions/export-data/index.ts", "language": "typescript", "hash": "d9b3da139e9ea7ae8d3ebab7372ea8d2a2299127a41e03e989c30b785635bdf3"}, {"path": "store/platformStore.ts", "language": "typescript", "hash": "cf2ecb734baec5f2652701e85ae63512756e8c92048d626e5f83489166299ba2"}, {"path": "migrations/018_create_ai_ml_tables.sql", "language": "text", "hash": "efcca63b2cc08b542898de90bc3bc953d0cd822644383c2f813a693b06872aaa"}, {"path": "supabase/functions/analyze-image-health/index.ts", "language": "typescript", "hash": "8e4750cf559a0343335d485ebad0e6f77abd0a834b6245bb3927fbe0b2ddfe12"}, {"path": "supabase/functions/advanced-ai-chat/index.ts", "language": "typescript", "hash": "469ef965fba535cff58ce90f1733120bcccbd927d5aacdb49d0ecf5ef22a0662"}, {"path": "store/aiMLStore.ts", "language": "typescript", "hash": "d53cd0c49b57d35d3a0b7213013fd96cdea7c304c086e988bba6cf4cdd51622a"}, {"path": "migrations/019_create_analytics_dashboard_tables.sql", "language": "text", "hash": "69b8a72b60c5288d2f561e648755772a1554cd73fafaacf4b6747552ac34cf77"}, {"path": "supabase/functions/generate-analytics-dashboard/index.ts", "language": "typescript", "hash": "2e5c388af6adbc41955b87a7744e861f78665f46f56682c309d5f7991d3b4714"}, {"path": "supabase/functions/generate-analytics-report/index.ts", "language": "typescript", "hash": "f8f06a43159352c4aca68e6306c66f82dbe03fee4cb019b7487d60e1abf0c00f"}, {"path": "store/analyticsStore.ts", "language": "typescript", "hash": "5d51324566c3fee80a74f232459ac31467889c1e561d31f8bbf9b96e6c25139e"}, {"path": "components/training/TrainingSessionHeader.tsx", "language": "typescript", "hash": "a8c601b30fe1ea8a272b9993264a36b34762b648fcc6df1288d703ef2f4089ed"}, {"path": "components/training/TrainingMetricsDisplay.tsx", "language": "typescript", "hash": "adeba3dbef41fa3c2490a4dd56c460eecd0ae58b322edb6997f61d8b3874efca"}, {"path": "components/training/TrainingNotesSection.tsx", "language": "typescript", "hash": "3dea7a8a2c534151d2fc0bc6c7e447c277906e0ffbf4901bde464e8782fd9543"}, {"path": "components/training/AutomatedTrainingLogger.tsx", "language": "typescript", "hash": "c57f35a5553b51c9e555baa4e4ffac5507b68bf427fe394ce8e2713932418bb1"}, {"path": "components/aiAssistant/AIAssistantHeader.tsx", "language": "typescript", "hash": "e52d6808107a56327041683220f1fde3db7c0cfa580d978f64ded708d1d71ac5"}, {"path": "components/aiAssistant/AIDataReadinessCard.tsx", "language": "typescript", "hash": "f9edb4087f37a75ce9039da8b43621a6262daf9ac74d332e84773fd75e66b500"}, {"path": "components/aiAssistant/AIInsightsList.tsx", "language": "typescript", "hash": "28989d494d93f96c3acbebc7a64f84b8f4d3f37beb16c376dd7f540e8061a59f"}, {"path": "components/animal/AnimalProfileHeader.tsx", "language": "typescript", "hash": "25187796a3951dbb82a21d550e2a97c767087c1832ad3b87ce348464b75af68d"}, {"path": "components/animal/AnimalHealthOverview.tsx", "language": "typescript", "hash": "683d4a0486d6aa18c159314350ea46617883642d33044bbb7ea81ad22c9af386"}, {"path": "components/animal/AnimalQuickActions.tsx", "language": "typescript", "hash": "53725f6c1cb9251655beb76fa0f3e5670642084a4c5dc8448b96ec3dd06ed3c9"}, {"path": "screens/AnimalDetailScreenRefactored.tsx", "language": "typescript", "hash": "535844f8ed38f09e4cddf4c155a19356ce5bb4d50cadc283a235ffae7dd5681e"}, {"path": "store/slices/deviceConnectionSlice.ts", "language": "typescript", "hash": "a60c7e4909a349024532827a19a30bb9bf11ca0bec0ed235d435e8dc25fd613e"}, {"path": "store/slices/deviceScanningSlice.ts", "language": "typescript", "hash": "dcd58451e03c34c6f93f10a1e448fb7cc2c6a24ff5eaf06b6b29f90ef1b39f5d"}, {"path": "store/slices/deviceManagementSlice.ts", "language": "typescript", "hash": "5eda17a0ca2971dd06b1f212ae5bed602661033e38c4e88059f0207e8fe6fb71"}, {"path": "navigation/stacks/HomeStack.tsx", "language": "typescript", "hash": "cb5e6d6276cc87972c842bdb33f9d5501fd6672e6480276a9f7b535aa8ec46a3"}, {"path": "navigation/stacks/AnimalsStack.tsx", "language": "typescript", "hash": "1858d49293e105b85ed6e30516f2af8d197f2471620f6bb766efcc3f3c194c82"}, {"path": "navigation/stacks/DevicesStack.tsx", "language": "typescript", "hash": "b037091bac39263c651e08a7e31393acf43cb40a0e8b089ce922b29ec8346cc9"}, {"path": "navigation/stacks/ProfileStack.tsx", "language": "typescript", "hash": "2ccbc2cbc50e3271fef6ae7bec8b49d84f6fcceb24ae5be32d096f6a0129814d"}, {"path": "navigation/RootNavigator.tsx", "language": "typescript", "hash": "ddab62e0dc3c3235ed02156bb56290e35f00f12136deeb460ca34f36267e0fd7"}, {"path": "PHASE_7_SUMMARY.md", "language": "markdown", "hash": "e6e8b8a7eb2aff276b24da64e732a8b1294426446b4636f0d6b8c0deab4cbcef"}, {"path": "assets/translations/fr.ts", "language": "typescript", "hash": "38a77c16219e6993b70d271a8714ecf0e7162b58b81f1433f2bbc2fca996b421"}, {"path": "assets/translations/ja.ts", "language": "typescript", "hash": "7f1a839b9a5029ec1fee2e552407639b4266ee96d6f0c90495b43a22531d6b3c"}, {"path": "assets/translations/it.ts", "language": "typescript", "hash": "d615c4c49e48cd5931755118b04bc53c319d8d2fb0fbb01d11838a3f156549c4"}, {"path": "assets/translations/tr.ts", "language": "typescript", "hash": "67dd293443b3ee3cba4597c716978407af31b4fe6959da6f9a20536bff70f2ac"}, {"path": "assets/translations/nl.ts", "language": "typescript", "hash": "89183929140a9b6fdc7e1763e3d6685bba19baba914b94e28ebb7cfeddcef9f1"}, {"path": "assets/translations/es.ts", "language": "typescript", "hash": "7c61e1b1c661d5b761bb712c4723d4cbb8ec82d18d59b36add08b907dfca5494"}, {"path": "components/training/AutoPauseManager.tsx", "language": "typescript", "hash": "18f5bbe6ece7df9b0c7dde961f36bd90167df906a76d3c98b0a40e0f847a959b"}, {"path": "components/training/AutoPauseSettings.tsx", "language": "typescript", "hash": "c1c4203f9ec3811def19badccb954ff6acb56479389acd427493d1f8f064286d"}, {"path": "supabase/functions/mfa-enroll/index.ts", "language": "typescript", "hash": "47faa876d010cfe411c34dd8445d53aad7c1f46ec4038696f11641c012ef9186"}, {"path": "supabase/functions/mfa-challenge/index.ts", "language": "typescript", "hash": "72ac26cdd06fe47f93beac55673e07a8f163a8e6dd60ae8c50be5296b5d99f1f"}, {"path": "supabase/functions/mfa-verify/index.ts", "language": "typescript", "hash": "9c72c9c774359d1a231c2e8ea041ae1bf8d50a40d93a563293110de0f9935a03"}, {"path": "migrations/020_create_mfa_tables.sql", "language": "text", "hash": "49c188a1eda004efad6d63a5ffc0c9dff3c57cb10864b680249e2ef02fadff69"}, {"path": "screens/MfaScreen.tsx", "language": "typescript", "hash": "456c955ee738372c860fdbfacfdcac9ba7fe72d945c8022d2834305584c22d38"}, {"path": "screens/VerifyMfaScreen.tsx", "language": "typescript", "hash": "0e016405eeb2b86d11b2ce44c0ae7c30398cf32c582ea07c47f37011e23682b4"}, {"path": "navigation/stacks/AuthStack.tsx", "language": "typescript", "hash": "1b2938ece2596b7bc1aa220e27ecfd21144eae1044ce25070694384396814b37"}, {"path": "navigation/stacks/HealthStack.tsx", "language": "typescript", "hash": "e7497be8a564e97e0949191cc3bb0f0dd5f958391d763e6bc550c90b87965509"}, {"path": "assets/translations/en/common.ts", "language": "typescript", "hash": "bbbfedf960894d80bd96dcc9af8401955e3ede704da74c5911804ad05d8b276b"}, {"path": "assets/translations/en/navigation.ts", "language": "typescript", "hash": "9e700f9d35c030def37e1ec8c28bb027c8d8c61095336bb0c70d7453239b3661"}, {"path": "assets/translations/en/auth.ts", "language": "typescript", "hash": "e86dc69be76b6639f041b10f7a3cf3b103990d8866b8768dbfba088652eb0977"}, {"path": "assets/translations/en/animals.ts", "language": "typescript", "hash": "c468d73376a6d59a95ee6aeb10e262038c6432a3a17cc755a782458663e3e360"}, {"path": "assets/translations/en/ai.ts", "language": "typescript", "hash": "44acd19a1ec6aa601d8d9e9556fc52c4d891661d98d1c8fe4edef58be30be4a2"}, {"path": "assets/translations/en/index.ts", "language": "typescript", "hash": "2504bc14b9129df216011a7703a6f3e1ba314e692dbcea86ec94fcbe62f9dbbf"}, {"path": "assets/translations/ar/common.ts", "language": "typescript", "hash": "c6468ccb4577d1179ee0d6199af325e639cdc564ab5a7888344800762c72bd03"}, {"path": "assets/translations/ar/navigation.ts", "language": "typescript", "hash": "bd0e4dbc8fe652c238bec779ad73c1ff5f5e6b7dd34a9d4173e078bc280d6afd"}, {"path": "assets/translations/ar/auth.ts", "language": "typescript", "hash": "85a9fd8cdf4e080d2bba10e87feedbbc997615d03c789d0b308462714cbfd3c7"}, {"path": "assets/translations/ar/animals.ts", "language": "typescript", "hash": "e03a22eed9bbf30a9358b25c4c6862e7b23188e65286fa4f806691bb9f075a6c"}, {"path": "assets/translations/ar/ai.ts", "language": "typescript", "hash": "d9c7b366990552970a45a28d7b9c9045c1d6bfaf9a899b457c59c167a9ac52ac"}, {"path": "assets/translations/ar/index.ts", "language": "typescript", "hash": "8b4b96123efc6c736eead25224cf26cf76a24e9c3645da86182da32cc390160b"}, {"path": "assets/translations/fr/common.ts", "language": "typescript", "hash": "59cfe63a73f673346862322fb1b18927b0dbb2e97f0b5050cbea45c2dd9ef5d0"}, {"path": "assets/translations/fr/navigation.ts", "language": "typescript", "hash": "4a969d5687fb9c6620ec585421ceaeba0fa53266b0d97ae748b8ce3f9773bfd8"}, {"path": "assets/translations/fr/auth.ts", "language": "typescript", "hash": "81d0471294d00f051bf58c23b8adae1543f7fb317baba25678957b431d8f730a"}, {"path": "assets/translations/fr/animals.ts", "language": "typescript", "hash": "9f4f925c850ee8beca0a4cfb604153a755638a50662a26388dbc47ccc4fbae4f"}, {"path": "assets/translations/fr/ai.ts", "language": "typescript", "hash": "4c6f32cfa4517e8af5cc388dd679c247ada89917bdf630aa79433ead4a478bf1"}, {"path": "assets/translations/fr/index.ts", "language": "typescript", "hash": "be376188c2adb1c176dd3ec1503cbe0bc3318ca0df72153cd9a1e180972857b3"}, {"path": "assets/translations/ja/common.ts", "language": "typescript", "hash": "940e10bd9944f805aa6fc1aed2f33baa932a9fb04ef16369df64678f57fa21f2"}, {"path": "assets/translations/ja/navigation.ts", "language": "typescript", "hash": "dada6e5f5a29acd76e73ed4bebaaea78f7baacace1f990f0846e8fd9453b0a21"}, {"path": "assets/translations/ja/auth.ts", "language": "typescript", "hash": "3ad44a47c669d297efd8f59ddda03a28585f7619632d61bf7c7eba7cd979d0d0"}, {"path": "assets/translations/ja/animals.ts", "language": "typescript", "hash": "3a849793558aca6234b29c65940b128817c5b2774b16e98af952fe88147ec3f9"}, {"path": "assets/translations/ja/ai.ts", "language": "typescript", "hash": "45b1e510ba26fea24d7e1f71bb4ffe927beba060481009254031cc95beec360b"}, {"path": "assets/translations/ja/index.ts", "language": "typescript", "hash": "eec11642eb3d085776e7bc70776a886271bc0833e4976b2685286f3b94d30bec"}, {"path": "assets/translations/it/index.ts", "language": "typescript", "hash": "a090719a9189a37d123b49b644b09536b7616754249c9f0fb3f3feca706a1970"}, {"path": "assets/translations/tr/index.ts", "language": "typescript", "hash": "a9601ff9325f155a7ed496f47c6ff576972ce6e9e7d9d5d6394ea8cdb8b1af6f"}, {"path": "assets/translations/nl/index.ts", "language": "typescript", "hash": "945a9943e6900eeae4c9f2f41796b18915b62e7178dfb1bad35f50101b6ec10e"}, {"path": "assets/translations/es/index.ts", "language": "typescript", "hash": "bcd29af0c611ceaf11705bb9e938cb2336ee69c34217f4e8175859555e1b512e"}, {"path": "components/training/TrainingModeSelector.tsx", "language": "typescript", "hash": "7c5a9ea3451f03f9adad2bd8b9ffadd054bc55c479cf98f905182fb3a24d4535"}, {"path": "components/training/AutomatedRecordingSection.tsx", "language": "typescript", "hash": "d88447993be87caa10124a8ffe0bd59641f170dcc26e7c134a9e656dec4b7691"}, {"path": "hooks/useLemonSqueezyCheckout.ts", "language": "typescript", "hash": "3db2001af5dbbb1af001f4338e49c73492cf878f391840fb8d177f7c960ab76d"}, {"path": "hooks/useOrderManagement.ts", "language": "typescript", "hash": "5db69d9f6d295c9db14aa4db2aa767a2fd448c917c75d40eff66669bece6bd3c"}, {"path": "supabase/functions/create-lemonsqueezy-checkout/index.ts", "language": "typescript", "hash": "a648bc438b1a75227b1a68741ad7a6e79580066e903336deb9e6d2cb595d35d6"}, {"path": "supabase/functions/lemonsqueezy-webhook/index.ts", "language": "typescript", "hash": "998edd142838d2d9bf61e5ffc538432e831a4ddfaed1efd3408a01b5e3713816"}, {"path": "components/aiHealth/AIHealthDashboardHeader.tsx", "language": "typescript", "hash": "0cbb51b93946f7d5626ec18bfdf8c56e64abd722f9b137f97e52af470eeaebd5"}, {"path": "components/aiHealth/AIHealthMetricsGrid.tsx", "language": "typescript", "hash": "5bef6b86e8258c49a3d7ce8b5809fdfed22cb7b5cd7b0aa136c848ba276b3e86"}, {"path": "components/aiHealth/AIHealthInsightsList.tsx", "language": "typescript", "hash": "1952f6653bac143a0308d564300a4d8df817f6a7e506f1262c2c2a80c05289f6"}, {"path": "components/training/TrainingSessionTimer.tsx", "language": "typescript", "hash": "4e2c2209c1f9f1904683964255df4450cf589cb6b2959344e8cbbad24a588b96"}, {"path": "components/training/LiveMetricsDisplay.tsx", "language": "typescript", "hash": "85a2be16e7feaf1010795b169ea226be009304a49e754a0149c4250626f8621c"}, {"path": "migrations/021_create_payment_logs_table.sql", "language": "text", "hash": "82a1dfcced7d1ff0d04ac3b3541720b27dc6aa23790d75022ae1697b7b7feb1d"}, {"path": "store/slices/chatMessagesSlice.ts", "language": "typescript", "hash": "4ae2e5ace9b3c2241e2b227a767f5940dce62c3715995c0efc1566f5ad44ca01"}, {"path": "store/slices/chatConversationsSlice.ts", "language": "typescript", "hash": "29acc64da6345b12f387e5f3a047c82395bab68a69cf9e2033a40797f71a7886"}, {"path": "components/auth/QRCodeDisplay.tsx", "language": "typescript", "hash": "55b1fec4e3fd2be88a803a0d0f7bf25897d02dc81a5857538640eb2e00248492"}, {"path": "hooks/useApplePayPayment.ts", "language": "typescript", "hash": "769d1204c02a1d231f1a788ed4d3c4e433f84218828b57b8cd569163f686f377"}, {"path": "hooks/useGooglePayPayment.ts", "language": "typescript", "hash": "877087198e1caf57cddf53d0bdb9714d01b038c2321c8b4965da59d1e4945604"}, {"path": "supabase/functions/process-apple-pay/index.ts", "language": "typescript", "hash": "f688634232be12261b3329c4dd62472b55c95622cce2270b85eb9312ff7077ed"}, {"path": "supabase/functions/process-google-pay/index.ts", "language": "typescript", "hash": "30aaa23a60f36b3f560b0333277f8c5abc8f35d0c03074364ca8a3086bcc01a0"}, {"path": "components/aiHealth/AnimalHealthCard.tsx", "language": "typescript", "hash": "26cf1c27f6a8b85c12827d3b271c958814b87b29251cc1bc54cb4ee4e2d48f00"}, {"path": "components/aiHealth/QuickActionsGrid.tsx", "language": "typescript", "hash": "0b2a3ccf6cf88d238d1d89ade2a221acc34eca1dec342ed54ce42e78188570f9"}, {"path": "components/aiHealth/PriorityAlertsCard.tsx", "language": "typescript", "hash": "80d7a8a079ebaecd443de2bf12053104c87f31d51b46c92a24caa75f10a6dd41"}, {"path": "components/aiHealth/RecentActivityCard.tsx", "language": "typescript", "hash": "1448c086e88954e214f4febd9ab7f6737ef6eefaae64a8235dc6fe5e5d7d1ddb"}, {"path": "components/aiHealth/HealthInsightsCard.tsx", "language": "typescript", "hash": "31c639e18eb75c4cf31b27f17418495e850480d5fa1d2adea7c059c2414cfe01"}, {"path": "screens/HealthDashboardScreen.tsx", "language": "typescript", "hash": "be1bc2a37f2b3377cfac72d204243f95ffb809b7657d0904bf0c65d636cb7635"}, {"path": "components/aiHealth/AnimalHealthSummaryCard.tsx", "language": "typescript", "hash": "4dd2f508faae6fbe5db4c6feb605ea2d51244cb857f20ea1bfe756d41c75da37"}, {"path": "scripts/translation-utils.js", "language": "javascript", "hash": "d2f7642ba8ca1a89c1dca2d13ad6e3d07372c048a982992ee8d50bd90c08cd6a"}, {"path": "scripts/README.md", "language": "markdown", "hash": "3154cc81bd85be302b810df64f7fe8dd54e9e8a637ba3451ee5a48fb8828dc2d"}, {"path": "scripts/package-scripts.json", "language": "json", "hash": "e7a0a3392ef84e76d98f72cca46fd28606689f386bbd574eb8b391fb20306757"}, {"path": "scripts/test-translations.js", "language": "javascript", "hash": "de8890216819910001a779d0be4cc1ce9cbcd781ab147599a9924e323e473c23"}, {"path": ".github/workflows/translation-validation.yml", "language": "yaml", "hash": "04afc426f586b62aea23990018297ce3b53d56f368d55af13ebfef38a3ca5889"}, {"path": "scripts/pre-commit-translations.sh", "language": "shell", "hash": "31c499240affc2f5aa8d0e629cf5a9d272104675578eec52e2b6bdbf7e91541d"}, {"path": "scripts/ci-translation-check.js", "language": "javascript", "hash": "f21b1ef3bcd64fd792fd8a7e4ab13e51299927e28d6fccdb9486ef8f771879a8"}, {"path": "scripts/setup-git-hooks.sh", "language": "shell", "hash": "f6abab6dfb42d875d7b63c49ccdc82826e922db64a3facf98233dae97ea90781"}, {"path": "scripts/ci-config.env", "language": "text", "hash": "5c6931a2705ac12eb3170bfe57de6dfd75284d255116653644d1f49e06030e92"}, {"path": "docs/ci-cd-translation-integration.md", "language": "markdown", "hash": "3295752f74c8386ce47e3890ee65f02eaa7753ce6d456dbc3622f31acc84728e"}, {"path": "services/translationServices.ts", "language": "typescript", "hash": "b0f5646b982c61be6ecd63603212b7995bad1f13c5468467694102557b28aba2"}, {"path": "scripts/auto-translate.js", "language": "javascript", "hash": "baf1af75f0f8d6ec588653a86926859554932affb4adebcf5abe04e489678b15"}, {"path": "supabase/functions/manage-translation-keys/index.ts", "language": "typescript", "hash": "2ea37446ed16bd63777eb3fe854904afa7a5607341464b80ebb16cb73501b883"}, {"path": "migrations/022_create_translation_system_tables.sql", "language": "text", "hash": "d775378845312ae807e40d06246aaf65987c8297a147c8b4b1fd5d5932422499"}, {"path": "docs/automated-translation-setup.md", "language": "markdown", "hash": "b81399d4553323b859239e3cb417ac3cbeb68e21b3536c9c9a2a21fc4a816d8a"}, {"path": "components/training/AutomatedDataDisplay.tsx", "language": "typescript", "hash": "bf3f7ac596309aa5e6e64840535e07f7b8da11965a1932f9aa8f46f7e3d1b2b4"}, {"path": "components/training/TrainingSessionActions.tsx", "language": "typescript", "hash": "098686d5a143f9bb2ac59f4e0a6da2a6133b7662d666279557b169ad32d20608"}, {"path": "store/slices/aiHealthAssessmentSlice.ts", "language": "typescript", "hash": "30918587948b47d9131eb477fe509ae2fcdd4a96dfae20e3f6d96fa141c49155"}, {"path": "store/slices/aiTrainingPlanSlice.ts", "language": "typescript", "hash": "e9e00935a5874d5fd2566ec65a6dcc25cf4b2aee96e49486dd4342e5c44e2c19"}, {"path": "store/slices/aiReadinessScoreSlice.ts", "language": "typescript", "hash": "a5bde6f7d27ded03ed56935fd629b8fc7feb3ddcf4de157bde1a6bcc5fd73581"}, {"path": "store/slices/aiCoachingTipSlice.ts", "language": "typescript", "hash": "ce8d80197c1e04888901863f8bb467d2f1adc1dfef054edd2f0d4f53f4de438b"}, {"path": "hooks/auth/useGoogleAuth.ts", "language": "typescript", "hash": "20d718689b7ae053ffee8532b402ccef8b1d72a3c84319c5fae3a8c0f9c3d8cf"}, {"path": "hooks/auth/useSignIn.ts", "language": "typescript", "hash": "aecdfc492580360453b6fc73445617dd435f8d87051e3bf7787d86a714763185"}, {"path": "hooks/auth/useSignUp.ts", "language": "typescript", "hash": "fbffc941eeaab4f272f8d31aa9f610614eca79f65e4b22c19563f534e15a6509"}, {"path": "hooks/auth/usePasswordReset.ts", "language": "typescript", "hash": "db092381dc4cc1d1093337b8315ffde5e0cf34d0a91f234b9b29541de0e0932b"}, {"path": "hooks/useAuthActionsRefactored.ts", "language": "typescript", "hash": "5e8f9213955d6e0a8f231cae52becaba540b542cd9815aa74600c592dab93af9"}, {"path": "store/slices/userProfileSlice.ts", "language": "typescript", "hash": "62a0dec9b4e2677f9c86175e26333995548ef6fd956cbea1ed4512af391c04a8"}, {"path": "store/slices/userSubscriptionSlice.ts", "language": "typescript", "hash": "e0019f42f1500d3b0b3a58008844ea39682462545a55c5f7c57f7236552271f5"}, {"path": "store/slices/userImageSlice.ts", "language": "typescript", "hash": "5adb553385e0609c419d868d640b849709c9d1b18e40a5140eb4c5e8330838f6"}, {"path": "store/slices/userMfaSlice.ts", "language": "typescript", "hash": "29e80f2bea8179fd4d595836840959ffc7874ba1812f4e0c1481b1810042fcb2"}, {"path": "store/userStoreRefactored.ts", "language": "typescript", "hash": "8d61af55439928d0a86a13316074aee06a93357147ca85c9ec85e69674d76cfe"}, {"path": "assets/translations/en/devices.ts", "language": "typescript", "hash": "787ddbca2e0ce2cb0507b5b59375f8a457848c5ec4465545cf3032a8f8b65002"}, {"path": "assets/translations/fr/devices.ts", "language": "typescript", "hash": "ae3b98e1364e2b2eaa9214f3b8de7435e338a4269975723408f0461218b0ea69"}, {"path": "assets/translations/es/devices.ts", "language": "typescript", "hash": "a604fcccbf2254d9a43cf6d37b02ad3fc0a7452d895b05ca7ab8bba76f6ef34b"}, {"path": "assets/translations/it/devices.ts", "language": "typescript", "hash": "a07ad4df49066bfe899e936749e797d758a3526189d56b01b0f6c976c2a7d3b7"}, {"path": "assets/translations/tr/devices.ts", "language": "typescript", "hash": "45bada6fa54a158376a3b451490ed965adb8ee2d4f91f177f13c1e87dc97a4c3"}, {"path": "assets/translations/nl/devices.ts", "language": "typescript", "hash": "ced1d467a5cfa7682640f81820fbc434c531c4919ce74a0ef558abd1a5598cbc"}, {"path": "assets/translations/ja/devices.ts", "language": "typescript", "hash": "2099ed396486ff14d63c811807fc2eeb6165f467752fbda44ae94d9be303df99"}, {"path": "assets/translations/ar/devices.ts", "language": "typescript", "hash": "f542583a760aba9a5d9f362e2b245be3f2d07a81a906e93fb6f452df791b704c"}, {"path": "components/animal/RecentActivityCard.tsx", "language": "typescript", "hash": "58282c5124eeaff197bd36de3d723f64f2f3e155785f0b540524b833eed18faf"}, {"path": "components/animal/UpcomingEventsCard.tsx", "language": "typescript", "hash": "302364b6fb8df5787b207c08a9bfe51d0f962c5abae589b15c8ca1f1b544c2f0"}, {"path": "hooks/useImagePicker.ts", "language": "typescript", "hash": "3e74c1777b471e18eab7252715701abfd90dd093aedb826a4a0a4cea9024910e"}, {"path": "components/environmental/EnvironmentalInsightsCard.tsx", "language": "typescript", "hash": "6555fa28d58a47abdc5b741eafff06f52498f34c49062f8570bf9b47b5411cca"}, {"path": "components/environmental/PriorityActionsCard.tsx", "language": "typescript", "hash": "cf41118987be6573b35e811070e19cd39701a8146fdc90ce4c6cddc194797073"}, {"path": "components/environmental/PersonalizedRecommendationsCard.tsx", "language": "typescript", "hash": "f584d771f3beb8280fedb82bcd69a85cab837d54a6ac7f9dd66aaa0708abd169"}, {"path": "components/diseaseRisk/DiseaseInsightsCard.tsx", "language": "typescript", "hash": "9a3a4f32475b0e2c99e071e0d75446671eb22b34368f681cbd8af06ad87dc014"}, {"path": "components/diseaseRisk/PreventiveActionsCard.tsx", "language": "typescript", "hash": "c765bbbb3beebbe728fb4a733c8f372ba19abcd14f33578478ec981827db0315"}, {"path": "migrations/023_create_safe_conversation_update_function.sql", "language": "text", "hash": "c75b84d4b6b394ad3d0127e242106c74ae0d389ac82bc264c06b6c784b3f1309"}, {"path": "admin/components/orders/OrderListView.tsx", "language": "typescript", "hash": "78c3712c92237a4c5a7dde20f3118dea24dce0bb2222f9225c4432dddf9101cd"}, {"path": "admin/components/orders/OrderListItem.tsx", "language": "typescript", "hash": "8b0c19dfbad945d1028e810fe13481b4ce4cee51a9c3d5bff6df0b9473394161"}, {"path": "admin/components/orders/EditOrderModal.tsx", "language": "typescript", "hash": "bfc4802cc1ea8b2c404d0972cac618dd727fe3b6a5e1240ed7e684de226d4e98"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "94002716e973a420abadb445edad96614075d7781777b1a77cf26472255423fc"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "438257e89ff7640a7caa293794e7200cdb376051ce018e75e366aae901482965"}, {"path": "services/NotificationService.ts", "language": "typescript", "hash": "c1bb15175f98b68c250c4e622ddd91d465b3d79d5133efea497893710539ed4d"}, {"path": "supabase/functions/send-push-notification/index.ts", "language": "typescript", "hash": "aedd79f841d9a291396bbc330241e65f0b25e051ebc416fbdf5656e3823c10ba"}, {"path": "migrations/024_create_notification_logs_table.sql", "language": "text", "hash": "f2a8e5bee7d47b1f42dce8deda7b2cc24d3dd11833f022d686afd61f710b3ad0"}, {"path": "supabase/migrations/020_fix_notification_analytics_security.sql", "language": "text", "hash": "43e286aa40e6ff31b6bef9db3b6fa3a16f50e0d0b4eb0d2ca2a69e39e4c8ce96"}, {"path": "supabase/migrations/022_fix_notification_analytics_security.sql", "language": "text", "hash": "49c085c4df56c6687b08262add91fe6526b6e9615d97861d2ad5f92f7856ccb0"}, {"path": "supabase/migrations/023_remove_security_definer_notification_analytics.sql", "language": "text", "hash": "ca98125d460bd64bcd3af8b6e4715345c34d6d054342d74c95f39e1640317d13"}, {"path": "supabase/migrations/024_secure_admin_view_permissions.sql", "language": "text", "hash": "2fdcc86077914aa903c00c7d7cca4b1e749fba2b35d59225c071cafc3e5caae8"}, {"path": "supabase/migrations/025_fix_search_path_security_functions.sql", "language": "text", "hash": "8e9d6eaa5158709f9955df97eed731cb015cf6afd10ebbdf33cef919eb70b1b4"}, {"path": "supabase/migrations/026_fix_remaining_search_path_functions.sql", "language": "text", "hash": "045b72f420149c6aa92a4fc2c9971889c942355ab036cf3e9fcac50c68182a52"}, {"path": "supabase/migrations/027_complete_search_path_security_fix.sql", "language": "text", "hash": "f3bfc0613449d7435fc6c6ef64a20dda4f5ed885260d2e883d209fc4d8975da4"}, {"path": "supabase/migrations/028_fix_all_remaining_search_path_functions.sql", "language": "text", "hash": "f6b4cd5749022395f5d6ebb2a36cd25292fb5c20021b50cc65879c987adfe660"}, {"path": "supabase/migrations/029_final_search_path_security_fixes.sql", "language": "text", "hash": "120385fc18b949606e9c06a11c25c5832ba650a421fd1d3eec302084c977731c"}, {"path": "supabase/migrations/030_fix_get_translation_analytics_search_path.sql", "language": "text", "hash": "e7d7c2a10a7c57dec0a5127af67e50d08c26e7ecde94e89a08f7a7a2567d07f0"}, {"path": "admin/components/devices/DeviceManagementView.tsx", "language": "typescript", "hash": "836b92df1c6d7b7de1c9b0bdd394e88b6a48203253aaa709cb3ab580c1368630"}, {"path": "admin/components/devices/DeviceAlertsPanel.tsx", "language": "typescript", "hash": "e8e30a3d7e577915581d3cd6fccde42887852f8f59749cd2534f1b869dc863d6"}, {"path": "supabase/migrations/031_fix_get_translation_analytics_search_path.sql", "language": "text", "hash": "2802180b91457a127bf9b7505a0ad28cfc7efd5a555277f2aa6935832638175b"}, {"path": "components/home/<USER>", "language": "typescript", "hash": "29c5fed7b63c5b53113a060c33e16b1b8c4603766266fd8c30cb24223f748645"}]}