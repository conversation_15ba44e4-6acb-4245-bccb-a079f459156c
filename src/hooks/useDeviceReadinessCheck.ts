import { useState, useEffect } from 'react';
import { useAnimalStore } from '../store/animalStore';

interface DeviceReadinessResult {
  isReady: boolean;
  message: string;
  deviceName?: string;
}

export const useDeviceReadinessCheck = (animalId: string): DeviceReadinessResult => {
  const { getAnimalById } = useAnimalStore();
  const [result, setResult] = useState<DeviceReadinessResult>({
    isReady: false,
    message: 'Checking device...'
  });
  
  useEffect(() => {
    const checkDeviceReadiness = (): DeviceReadinessResult => {
      const animal = getAnimalById(animalId);
      
      if (!animal) {
        return { isReady: false, message: "Animal not found" };
      }
      
      if (!animal.deviceId || !animal.deviceName) {
        return { isReady: false, message: "No device connected" };
      }
      
      if (animal.deviceStatus !== 'connected') {
        return { 
          isReady: false, 
          message: `Device ${animal.deviceStatus}`,
          deviceName: animal.deviceName
        };
      }
      
      // Check last sync time - if more than 1 hour, consider stale
      if (animal.lastSyncTime && Date.now() - animal.lastSyncTime > 3600000) {
        return { 
          isReady: true, 
          message: "Device data may be stale",
          deviceName: animal.deviceName
        };
      }
      
      return { 
        isReady: true, 
        message: "Device ready",
        deviceName: animal.deviceName
      };
    };
    
    setResult(checkDeviceReadiness());
  }, [animalId, getAnimalById]);
  
  return result;
};