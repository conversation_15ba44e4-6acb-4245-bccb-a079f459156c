import { useState } from 'react';

export interface AuthMode {
  isSignUp: boolean;
  isResetPassword: boolean;
  needsEmailConfirmation: boolean;
}

export interface AuthModeActions {
  setIsSignUp: (isSignUp: boolean) => void;
  setIsResetPassword: (isResetPassword: boolean) => void;
  setNeedsEmailConfirmation: (needs: boolean) => void;
  switchToSignUp: () => void;
  switchToSignIn: () => void;
  switchToResetPassword: () => void;
  backToSignIn: () => void;
}

/**
 * @magic_description Hook for managing authentication mode state and transitions
 * Handles switching between login, signup, and password reset modes
 */
export const useAuthMode = (
  resetCredentials?: () => void,
  clearError?: () => void
) => {
  const [isSignUp, setIsSignUp] = useState(false);
  const [isResetPassword, setIsResetPassword] = useState(false);
  const [needsEmailConfirmation, setNeedsEmailConfirmation] = useState(false);

  const switchToSignUp = () => {
    setIsSignUp(true);
    setIsResetPassword(false);
    setNeedsEmailConfirmation(false);
    resetCredentials?.();
    clearError?.();
  };

  const switchToSignIn = () => {
    setIsSignUp(false);
    setIsResetPassword(false);
    setNeedsEmailConfirmation(false);
    resetCredentials?.();
    clearError?.();
  };

  const switchToResetPassword = () => {
    setIsSignUp(false);
    setIsResetPassword(true);
    setNeedsEmailConfirmation(false);
    resetCredentials?.();
    clearError?.();
  };

  const backToSignIn = () => {
    setIsSignUp(false);
    setIsResetPassword(false);
    setNeedsEmailConfirmation(false);
    clearError?.();
  };

  return {
    // State
    isSignUp,
    isResetPassword,
    needsEmailConfirmation,
    // Actions
    setIsSignUp,
    setIsResetPassword,
    setNeedsEmailConfirmation,
    switchToSignUp,
    switchToSignIn,
    switchToResetPassword,
    backToSignIn,
  };
};

export default useAuthMode;