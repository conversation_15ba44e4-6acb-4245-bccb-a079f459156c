import { useMemo } from 'react';
import { useVitalsStore } from '../store/vitalsStore';
import { useFeedingStore } from '../store/feedingStore';
import { useMedicationStore } from '../store/medicationStore';
import { useAIStore } from '../store/aiStore';
import { supabase } from '../supabase/client';

export const useAIDataDetection = () => {
  const { vitals } = useVitalsStore();
  const { feedingEntries } = useFeedingStore();
  const { medications } = useMedicationStore();
  const { healthAssessments, trainingPlans, readinessScores, coachingTips } = useAIStore();

  return useMemo(() => {
    // Ensure all data arrays are defined with fallbacks
    const safeVitals = vitals || [];
    const safeFeedingEntries = feedingEntries || [];
    const safeMedications = medications || [];
    const safeHealthAssessments = healthAssessments || [];
    const safeTrainingPlans = trainingPlans || [];
    const safeReadinessScores = readinessScores || [];
    const safeCoachingTips = coachingTips || [];

    // Calculate data availability
    const hasVitalsData = safeVitals.length > 0;
    const hasFeedingData = safeFeedingEntries.length > 0;
    const hasMedicationData = safeMedications.length > 0;
    const hasAIData = safeHealthAssessments.length > 0 || safeTrainingPlans.length > 0;

    // Calculate recent data (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentVitals = safeVitals.filter(vital => {
      try {
        return new Date(vital.recorded_at) > sevenDaysAgo;
      } catch {
        return false;
      }
    });

    const recentFeedings = safeFeedingEntries.filter(feeding => {
      try {
        return new Date(feeding.feeding_time || feeding.created_at) > sevenDaysAgo;
      } catch {
        return false;
      }
    });

    const recentMedications = safeMedications.filter(medication => {
      try {
        return new Date(medication.created_at) > sevenDaysAgo;
      } catch {
        return false;
      }
    });

    // Calculate data quality score
    let dataQualityScore = 0;
    let maxScore = 4;

    if (hasVitalsData) dataQualityScore += 1;
    if (hasFeedingData) dataQualityScore += 1;
    if (hasMedicationData) dataQualityScore += 1;
    if (hasAIData) dataQualityScore += 1;

    const dataQualityPercentage = Math.round((dataQualityScore / maxScore) * 100);

    // Determine if AI analysis is ready
    const isReadyForAI = hasVitalsData && hasFeedingData;
    const hasMinimalData = safeVitals.length >= 3 || safeFeedingEntries.length >= 5;

    // Generate recommendations
    const recommendations = [];
    
    if (!hasVitalsData) {
      recommendations.push('Record vital signs to enable health analysis');
    }
    
    if (!hasFeedingData) {
      recommendations.push('Log feeding schedules for nutrition insights');
    }
    
    if (recentVitals.length === 0 && hasVitalsData) {
      recommendations.push('Update recent vital signs for current health status');
    }
    
    if (!hasAIData && isReadyForAI) {
      recommendations.push('Generate AI health assessment with available data');
    }

    return {
      hasVitalsData,
      hasFeedingData,
      hasMedicationData,
      hasAIData,
      isReadyForAI,
      hasMinimalData,
      dataQualityScore: dataQualityPercentage,
      recentDataCounts: {
        vitals: recentVitals.length,
        feedings: recentFeedings.length,
        medications: recentMedications.length
      },
      totalDataCounts: {
        vitals: safeVitals.length,
        feedings: safeFeedingEntries.length,
        medications: safeMedications.length,
        assessments: safeHealthAssessments.length
      },
      recommendations
    };
  }, [vitals, feedingEntries, medications, healthAssessments, trainingPlans, readinessScores, coachingTips]);
};