import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '../supabase/client';
import { toast } from 'sonner-native';
import { useAnimalStore } from '../store/animalStore';

export interface AutomatedTrainingData {
  distance: number; // km
  avg_speed: number; // km/h
  max_speed: number; // km/h
  avg_intensity: number; // 0-100
  max_intensity: number; // 0-100
  heart_rate_avg?: number; // BPM
  heart_rate_max?: number; // BPM
  elevation_gain?: number; // meters
  calories_burned?: number;
  gps_track: GPSPoint[];
  weather_conditions?: WeatherData;
  session_quality: 'excellent' | 'good' | 'fair' | 'poor';
}

export interface GPSPoint {
  lat: number;
  lng: number;
  timestamp: number;
  elevation?: number;
  speed?: number;
}

export interface WeatherData {
  temperature: number; // Celsius
  humidity: number; // percentage
  wind_speed: number; // km/h
  conditions: 'clear' | 'partly_cloudy' | 'overcast' | 'light_rain' | 'rain' | 'snow';
}

export interface TrainingSession {
  id?: string;
  animal_id: string;
  user_id: string;
  session_timestamp: string;
  duration: string; // PostgreSQL interval
  duration_seconds: number;
  intensity_label: 'light' | 'moderate' | 'intense';
  notes?: string;
  location?: string;
  
  // Automated data fields
  distance?: number;
  avg_speed?: number;
  max_speed?: number;
  avg_intensity?: number;
  max_intensity?: number;
  calories_burned?: number;
  heart_rate_avg?: number;
  heart_rate_max?: number;
  elevation_gain?: number;
  device_id?: string;
  is_automated: boolean;
  session_data?: any;
  gps_track?: GPSPoint[];
  weather_conditions?: WeatherData;
  session_quality?: string;
  auto_paused_time?: number;
  manual_adjustments?: any;
}

interface AutomatedTrainingState {
  isRecording: boolean;
  isPaused: boolean;
  isAutoPaused: boolean;
  startTime: Date | null;
  pauseTime: Date | null;
  resumeTime: Date | null;
  currentData: Partial<AutomatedTrainingData>;
  sessionId: string | null;
  error: string | null;
  inactivityStartTime: Date | null;
  lastActivityTime: Date | null;
  autoPauseSettings: {
    enabled: boolean;
    inactivityThreshold: number; // minutes
    speedThreshold: number; // km/h
  };
}

export const useAutomatedTrainingLogger = (animalId: string) => {
  const [state, setState] = useState<AutomatedTrainingState>({
    isRecording: false,
    isPaused: false,
    isAutoPaused: false,
    startTime: null,
    pauseTime: null,
    resumeTime: null,
    currentData: {},
    sessionId: null,
    error: null,
    inactivityStartTime: null,
    lastActivityTime: null,
    autoPauseSettings: {
      enabled: true,
      inactivityThreshold: 5, // 5 minutes
      speedThreshold: 0.5, // 0.5 km/h
    },
  });
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const gpsTrackRef = useRef<GPSPoint[]>([]);
  const metricsRef = useRef({
    totalDistance: 0,
    speeds: [] as number[],
    intensities: [] as number[],
    heartRates: [] as number[],
    elevations: [] as number[]
  });
  
  // Simulate GPS and sensor data collection
  const collectDataPoint = useCallback(() => {
    if (!state.isRecording || state.isPaused) return;
    
    const now = Date.now();
    const currentSpeed = 8 + Math.random() * 15; // 8-23 km/h
    const currentIntensity = 40 + Math.random() * 50; // 40-90%
    const currentHeartRate = 120 + Math.random() * 80; // 120-200 BPM
    const currentElevation = 100 + Math.random() * 50; // 100-150m
    
    // Auto-pause detection logic
    if (state.autoPauseSettings.enabled) {
      const isInactive = currentSpeed < state.autoPauseSettings.speedThreshold;
      
      if (isInactive) {
        if (!state.inactivityStartTime) {
          // Start tracking inactivity
          setState(prev => ({
            ...prev,
            inactivityStartTime: new Date(),
          }));
        } else {
          // Check if inactivity threshold is reached
          const inactivityDuration = (now - state.inactivityStartTime.getTime()) / (1000 * 60); // minutes
          if (inactivityDuration >= state.autoPauseSettings.inactivityThreshold) {
            // Trigger auto-pause
            autoPauseSession();
            return;
          }
        }
      } else {
        // Reset inactivity tracking if animal is active
        if (state.inactivityStartTime) {
          setState(prev => ({
            ...prev,
            inactivityStartTime: null,
            lastActivityTime: new Date(),
          }));
        }
      }
    }
    
    // Add GPS point
    const gpsPoint: GPSPoint = {
      lat: 40.7128 + (Math.random() - 0.5) * 0.01,
      lng: -74.0060 + (Math.random() - 0.5) * 0.01,
      timestamp: now,
      elevation: currentElevation,
      speed: currentSpeed
    };
    
    gpsTrackRef.current.push(gpsPoint);
    
    // Update metrics
    const metrics = metricsRef.current;
    metrics.speeds.push(currentSpeed);
    metrics.intensities.push(currentIntensity);
    metrics.heartRates.push(currentHeartRate);
    metrics.elevations.push(currentElevation);
    
    // Calculate distance (simplified)
    if (gpsTrackRef.current.length > 1) {
      const prevPoint = gpsTrackRef.current[gpsTrackRef.current.length - 2];
      const distance = calculateDistance(prevPoint, gpsPoint);
      metrics.totalDistance += distance;
    }
    
    // Update current data
    const avgSpeed = metrics.speeds.reduce((a, b) => a + b, 0) / metrics.speeds.length;
    const maxSpeed = Math.max(...metrics.speeds);
    const avgIntensity = metrics.intensities.reduce((a, b) => a + b, 0) / metrics.intensities.length;
    const maxIntensity = Math.max(...metrics.intensities);
    const avgHeartRate = metrics.heartRates.reduce((a, b) => a + b, 0) / metrics.heartRates.length;
    const maxHeartRate = Math.max(...metrics.heartRates);
    const elevationGain = Math.max(...metrics.elevations) - Math.min(...metrics.elevations);
    
    setState(prev => ({
      ...prev,
      currentData: {
        distance: metrics.totalDistance,
        avg_speed: avgSpeed,
        max_speed: maxSpeed,
        avg_intensity: avgIntensity,
        max_intensity: maxIntensity,
        heart_rate_avg: Math.round(avgHeartRate),
        heart_rate_max: Math.round(maxHeartRate),
        elevation_gain: elevationGain,
        gps_track: [...gpsTrackRef.current],
        session_quality: getSessionQuality(avgIntensity, metrics.totalDistance)
      }
    }));
  }, [state.isRecording, state.isPaused, autoPauseSession]);
  
  // Calculate distance between two GPS points (Haversine formula)
  const calculateDistance = (point1: GPSPoint, point2: GPSPoint): number => {
    const R = 6371; // Earth's radius in km
    const dLat = (point2.lat - point1.lat) * Math.PI / 180;
    const dLng = (point2.lng - point1.lng) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };
  
  // Determine session quality based on metrics
  const getSessionQuality = (avgIntensity: number, distance: number): 'excellent' | 'good' | 'fair' | 'poor' => {
    if (avgIntensity >= 80 && distance >= 15) return 'excellent';
    if (avgIntensity >= 60 && distance >= 10) return 'good';
    if (avgIntensity >= 40 || distance >= 5) return 'fair';
    return 'poor';
  };
  
  // Get current weather (simulated)
  const getCurrentWeather = (): WeatherData => {
    const conditions = ['clear', 'partly_cloudy', 'overcast', 'light_rain'] as const;
    return {
      temperature: 15 + Math.random() * 20,
      humidity: 40 + Math.random() * 40,
      wind_speed: Math.random() * 15,
      conditions: conditions[Math.floor(Math.random() * conditions.length)]
    };
  };
  
  // Start recording session
  const startRecording = useCallback(async () => {
    try {
      setState(prev => ({
        ...prev,
        isRecording: true,
        isPaused: false,
        startTime: new Date(),
        currentData: {},
        error: null
      }));
      
      // Reset tracking data
      gpsTrackRef.current = [];
      metricsRef.current = {
        totalDistance: 0,
        speeds: [],
        intensities: [],
        heartRates: [],
        elevations: []
      };
      
      // Start data collection interval (every 5 seconds)
      intervalRef.current = setInterval(collectDataPoint, 5000);
      
      toast.success('Started automated training session');
      
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message }));
      toast.error('Failed to start recording');
    }
  }, [collectDataPoint]);
  
  // Pause recording
  const pauseRecording = useCallback(() => {
    setState(prev => ({ ...prev, isPaused: true }));
    toast.success('Training session paused');
  }, []);
  
  // Resume recording
  const resumeRecording = useCallback(() => {
    setState(prev => ({
      ...prev,
      isPaused: false,
      isAutoPaused: false,
      resumeTime: new Date(),
      inactivityStartTime: null,
    }));
    
    const message = state.isAutoPaused ? 'Auto-paused session resumed' : 'Training session resumed';
    toast.success(message);
  }, [state.isAutoPaused]);
  
  // Stop recording and save session
  const stopRecording = useCallback(async (): Promise<TrainingSession | null> => {
    try {
      if (!state.startTime || !state.isRecording) {
        throw new Error('No active recording session');
      }
      
      const endTime = new Date();
      const durationSeconds = Math.floor((endTime.getTime() - state.startTime.getTime()) / 1000);
      const durationInterval = `${Math.floor(durationSeconds / 3600)}:${Math.floor((durationSeconds % 3600) / 60)}:${durationSeconds % 60}`;
      
      // Clear interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      
      // Get user ID
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');
      
      // Prepare session data
      const sessionData: Omit<TrainingSession, 'id'> = {
        animal_id: animalId,
        user_id: user.id,
        session_timestamp: state.startTime.toISOString(),
        duration: durationInterval,
        duration_seconds: durationSeconds,
        intensity_label: state.currentData.avg_intensity && state.currentData.avg_intensity >= 70 ? 'intense' : 
                        state.currentData.avg_intensity && state.currentData.avg_intensity >= 50 ? 'moderate' : 'light',
        distance: state.currentData.distance,
        avg_speed: state.currentData.avg_speed,
        max_speed: state.currentData.max_speed,
        avg_intensity: state.currentData.avg_intensity,
        max_intensity: state.currentData.max_intensity,
        heart_rate_avg: state.currentData.heart_rate_avg,
        heart_rate_max: state.currentData.heart_rate_max,
        elevation_gain: state.currentData.elevation_gain,
        device_id: `AUTO-TRACKER-${Date.now()}`,
        is_automated: true,
        session_quality: state.currentData.session_quality,
        gps_track: state.currentData.gps_track,
        weather_conditions: getCurrentWeather(),
        auto_paused_time: 0, // TODO: Track actual pause time
        session_data: {
          raw_metrics: metricsRef.current,
          data_points: gpsTrackRef.current.length,
          recording_start: state.startTime.toISOString(),
          recording_end: endTime.toISOString()
        }
      };
      
      // Save to database
      const { data, error } = await supabase
        .from('training_sessions')
        .insert([sessionData])
        .select()
        .single();
      
      if (error) throw error;
      
      // Reset state
      setState({
        isRecording: false,
        isPaused: false,
        isAutoPaused: false,
        startTime: null,
        pauseTime: null,
        resumeTime: null,
        currentData: {},
        sessionId: null,
        error: null,
        inactivityStartTime: null,
        lastActivityTime: null,
        autoPauseSettings: state.autoPauseSettings, // Preserve settings
      });
      
      toast.success(`Training session saved!\nDuration: ${Math.floor(durationSeconds / 60)} min\nDistance: ${state.currentData.distance?.toFixed(1) || 0} km`);
      
      return data;
      
    } catch (error: any) {
      setState(prev => ({ ...prev, error: error.message }));
      toast.error('Failed to save training session');
      return null;
    }
  }, [state.startTime, state.isRecording, state.currentData, animalId]);
  
  // Get current session duration
  const getCurrentDuration = useCallback((): number => {
    if (!state.startTime || !state.isRecording) return 0;
    return Math.floor((Date.now() - state.startTime.getTime()) / 1000);
  }, [state.startTime, state.isRecording]);
  
  // Auto-pause function
  const autoPauseSession = useCallback(() => {
    if (!state.isRecording || state.isPaused) {
      return;
    }

    setState(prev => ({
      ...prev,
      isPaused: true,
      isAutoPaused: true,
      pauseTime: new Date(),
      inactivityStartTime: null,
    }));

    toast('Session auto-paused due to inactivity', {
      description: `No movement detected for ${state.autoPauseSettings.inactivityThreshold} minutes`,
      duration: 10000,
    });
  }, [state.isRecording, state.isPaused, state.autoPauseSettings.inactivityThreshold]);

  // Update auto-pause settings
  const updateAutoPauseSettings = useCallback((settings: typeof state.autoPauseSettings) => {
    setState(prev => ({
      ...prev,
      autoPauseSettings: settings,
    }));
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);
  
  return {
    // State
    isRecording: state.isRecording,
    isPaused: state.isPaused,
    isAutoPaused: state.isAutoPaused,
    startTime: state.startTime,
    pauseTime: state.pauseTime,
    resumeTime: state.resumeTime,
    currentData: state.currentData,
    error: state.error,
    autoPauseSettings: state.autoPauseSettings,
    inactivityStartTime: state.inactivityStartTime,
    lastActivityTime: state.lastActivityTime,
    
    // Actions
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    autoPauseSession,
    updateAutoPauseSettings,
    
    // Utilities
    getCurrentDuration,
    
    // Computed values
    canStart: !state.isRecording,
    canPause: state.isRecording && !state.isPaused,
    canResume: state.isRecording && state.isPaused,
    canStop: state.isRecording
  };
};