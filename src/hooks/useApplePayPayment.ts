import { useState } from 'react';
import { Platform, Alert } from 'react-native';
import { toast } from 'sonner-native';
import { useUserStore } from '../store/userStore';
import { useOrderManagement } from './useOrderManagement';
import { supabase } from '../supabase/client';

export interface ApplePayPaymentRequest {
  planType: 'monthly' | 'yearly';
  productId: string;
  amount: number;
  currency: string;
}

export const useApplePayPayment = (onSuccess?: () => void) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAvailable, setIsAvailable] = useState(false);
  const { user } = useUserStore();
  const { createOrder } = useOrderManagement(onSuccess);

  // Check if Apple Pay is available
  const checkApplePayAvailability = async (): Promise<boolean> => {
    if (Platform.OS !== 'ios') {
      return false;
    }

    try {
      // In a real implementation, you would use:
      // import { ApplePay } from '@stripe/stripe-react-native';
      // const isAvailable = await ApplePay.isApplePaySupported();
      
      // For now, simulate availability check
      const available = Platform.OS === 'ios' && Platform.Version >= '13.0';
      setIsAvailable(available);
      return available;
    } catch (error) {
      console.error('Error checking Apple Pay availability:', error);
      setIsAvailable(false);
      return false;
    }
  };

  const processApplePayPayment = async (paymentRequest: ApplePayPaymentRequest) => {
    if (!user) {
      toast.error('Please log in to continue');
      return;
    }

    if (Platform.OS !== 'ios') {
      Alert.alert('Apple Pay', 'Apple Pay is only available on iOS devices.');
      return;
    }

    const available = await checkApplePayAvailability();
    if (!available) {
      Alert.alert('Apple Pay', 'Apple Pay is not available on this device.');
      return;
    }

    setIsProcessing(true);

    try {
      // Step 1: Create order in our database
      const orderId = await createOrder({
        user_id: user.id,
        product_id: paymentRequest.productId,
        quantity: 1,
        total_amount: paymentRequest.amount,
        currency: paymentRequest.currency,
        status: 'pending',
        plan_type: paymentRequest.planType,
        payment_method: 'apple_pay'
      });

      if (!orderId) {
        throw new Error('Failed to create order');
      }

      // Step 2: Process Apple Pay payment
      const { data, error } = await supabase.functions.invoke('process-apple-pay', {
        body: {
          order_id: orderId,
          amount: paymentRequest.amount,
          currency: paymentRequest.currency,
          plan_type: paymentRequest.planType,
          product_id: paymentRequest.productId
        }
      });

      if (error) {
        console.error('Apple Pay processing error:', error);
        throw new Error('Failed to process Apple Pay payment');
      }

      if (data.success) {
        // In a real implementation, you would:
        // 1. Present Apple Pay sheet
        // 2. Handle user authorization
        // 3. Process payment with payment processor
        // 4. Confirm payment on backend
        
        // For now, simulate successful payment
        toast.success('Apple Pay payment successful!');
        onSuccess?.();
      } else {
        throw new Error(data.error || 'Apple Pay payment failed');
      }
    } catch (error: any) {
      console.error('Apple Pay payment error:', error);
      toast.error(error.message || 'Apple Pay payment failed');
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    isProcessing,
    isAvailable,
    checkApplePayAvailability,
    processApplePayPayment
  };
};