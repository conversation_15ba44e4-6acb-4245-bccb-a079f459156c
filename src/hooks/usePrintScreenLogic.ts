import { useState } from 'react';
import { Share } from 'react-native';
import { useAnimalStore } from '../store/animalStore';
import { useFeedingStore } from '../store/feedingStore';
import { useMedicationStore } from '../store/medicationStore';
import { toast } from 'sonner-native';

export type PrintType = 'feeding' | 'medication' | 'vaccination';

export interface UsePrintScreenLogicProps {
  type: PrintType;
  animalId: string;
}

export interface PrintScreenState {
  isLoading: boolean;
  isPrinted: boolean;
  isShared: boolean;
  isDownloaded: boolean;
}

export interface PrintScreenData {
  animal: any;
  data: any[];
  title: string;
}

export interface PrintScreenActions {
  handlePrint: () => Promise<void>;
  handleShare: () => Promise<void>;
  handleDownload: () => Promise<void>;
}

export interface UsePrintScreenLogicReturn extends PrintScreenState, PrintScreenData, PrintScreenActions {}

export const usePrintScreenLogic = ({ type, animalId }: UsePrintScreenLogicProps): UsePrintScreenLogicReturn => {
  // State
  const [isLoading, setIsLoading] = useState(false);
  const [isPrinted, setIsPrinted] = useState(false);
  const [isShared, setIsShared] = useState(false);
  const [isDownloaded, setIsDownloaded] = useState(false);
  
  // Store hooks
  const { getAnimalById } = useAnimalStore();
  const { getTodayFeedings } = useFeedingStore();
  const { getTodayMedications } = useMedicationStore();
  
  // Get animal data
  const animal = getAnimalById(animalId);
  
  // Get the data based on the type
  const getData = () => {
    if (!animal) return [];
    
    switch (type) {
      case 'feeding':
        return getTodayFeedings(animalId);
      case 'medication':
        return getTodayMedications(animalId);
      case 'vaccination':
        // In a real app, we would have a getVaccinations function
        return [];
      default:
        return [];
    }
  };
  
  const data = getData();
  
  // Get title based on type
  const getTitle = () => {
    switch (type) {
      case 'feeding':
        return 'Feeding Schedule';
      case 'medication':
        return 'Medication Schedule';
      case 'vaccination':
        return 'Vaccination Records';
      default:
        return 'Schedule';
    }
  };
  
  const title = getTitle();
  
  // Action handlers
  const handlePrint = async () => {
    setIsLoading(true);
    
    try {
      // In a real app, this would use a printing library
      // For now, we'll just simulate printing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsPrinted(true);
      toast.success('Document sent to printer');
    } catch (error) {
      console.error('Error printing:', error);
      toast.error('Failed to print document');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleShare = async () => {
    setIsLoading(true);
    
    try {
      // Generate a text representation of the data
      const shareTitle = `${title} for ${animal?.name}`;
      let message = `${shareTitle}\nDate: ${new Date().toLocaleDateString()}\n\n`;
      
      if (type === 'feeding') {
        data.forEach((item: any, index: number) => {
          message += `${index + 1}. ${item.feedType} - ${item.amount} at ${item.time}\n`;
          if (item.notes) message += `   Notes: ${item.notes}\n`;
          message += '\n';
        });
      } else if (type === 'medication') {
        data.forEach((item: any, index: number) => {
          message += `${index + 1}. ${item.medicationName} - ${item.dosage}${item.dosageUnit} at ${item.time}\n`;
          if (item.notes) message += `   Notes: ${item.notes}\n`;
          message += '\n';
        });
      }
      
      await Share.share({
        title: shareTitle,
        message,
      });
      
      setIsShared(true);
    } catch (error) {
      console.error('Error sharing:', error);
      toast.error('Failed to share document');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleDownload = async () => {
    setIsLoading(true);
    
    try {
      // In a real app, this would generate a PDF and save it
      // For now, we'll just simulate downloading
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsDownloaded(true);
      toast.success('Document downloaded');
    } catch (error) {
      console.error('Error downloading:', error);
      toast.error('Failed to download document');
    } finally {
      setIsLoading(false);
    }
  };
  
  return {
    // State
    isLoading,
    isPrinted,
    isShared,
    isDownloaded,
    
    // Data
    animal,
    data,
    title,
    
    // Actions
    handlePrint,
    handleShare,
    handleDownload
  };
};