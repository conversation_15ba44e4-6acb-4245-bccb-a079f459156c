import { useState } from 'react';

export interface AuthErrorState {
  errorMessage: string;
}

export interface AuthErrorActions {
  setErrorMessage: (message: string) => void;
  clearError: () => void;
}

/**
 * @magic_description Hook for managing authentication error state
 * Handles error messages and clearing errors
 */
export const useAuthError = () => {
  const [errorMessage, setErrorMessage] = useState('');

  const clearError = () => {
    setErrorMessage('');
  };

  return {
    // State
    errorMessage,
    // Actions
    setErrorMessage,
    clearError,
  };
};

export default useAuthError;