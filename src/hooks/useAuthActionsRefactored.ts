import { useGoogleAuth } from './auth/useGoogleAuth';
import { useSignIn } from './auth/useSignIn';
import { useSignUp } from './auth/useSignUp';
import { usePasswordReset } from './auth/usePasswordReset';
import { AuthCredentials } from './useAuthCredentials';
import { AuthMode } from './useAuthMode';
import { AuthErrorActions } from './useAuthError';

interface UseAuthActionsRefactoredProps {
  credentials: AuthCredentials;
  mode: AuthMode;
  errorManager: AuthErrorActions;
  security: {
    recordFailedAttempt: (email: string) => Promise<void>;
    resetLoginAttempts: (email: string) => Promise<void>;
    isAccountLocked: boolean;
  };
  validation: {
    validateForm: (email: string, password: string, confirmPassword: string, isSignUp: boolean, isResetPassword: boolean) => boolean;
  };
  modeActions: {
    setNeedsEmailConfirmation: (needs: boolean) => void;
    setIsSignUp: (isSignUp: boolean) => void;
    setIsResetPassword: (isReset: boolean) => void;
  };
  onShakeAnimation?: () => void;
}

export const useAuthActionsRefactored = (props: UseAuthActionsRefactoredProps) => {
  const {
    credentials,
    mode,
    errorManager,
    security,
    validation,
    modeActions,
    onShakeAnimation
  } = props;

  // Google Authentication
  const googleAuth = useGoogleAuth(errorManager, onShakeAnimation);

  // Sign In
  const signIn = useSignIn({
    credentials,
    security,
    validation,
    errorManager,
    modeActions,
    onShakeAnimation
  });

  // Sign Up
  const signUp = useSignUp({
    credentials,
    validation,
    errorManager,
    modeActions,
    onShakeAnimation
  });

  // Password Reset
  const passwordReset = usePasswordReset({
    credentials,
    errorManager,
    modeActions,
    onShakeAnimation
  });

  // Combine loading states
  const isLoading = signIn.isLoading || signUp.isLoading || passwordReset.isLoading;

  return {
    // Loading states
    isLoading,
    isGoogleLoading: googleAuth.isGoogleLoading,
    isGoogleModuleLoading: googleAuth.isGoogleModuleLoading,
    googleModuleReady: googleAuth.googleModuleReady,
    googleModuleLoadFailed: googleAuth.googleModuleLoadFailed,
    
    // Actions
    handleSignIn: signIn.handleSignIn,
    handleSignUp: signUp.handleSignUp,
    handleForgotPassword: passwordReset.handleForgotPassword,
    handleResendConfirmation: passwordReset.handleResendConfirmation,
    handleGoogleSignIn: googleAuth.handleGoogleSignIn,
  };
};