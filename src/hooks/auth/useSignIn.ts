import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';
import { RootStackParamList } from '../../navigation';

interface SignInProps {
  credentials: { email: string; password: string };
  security: {
    isAccountLocked: boolean;
    recordFailedAttempt: (email: string) => Promise<void>;
    resetLoginAttempts: (email: string) => Promise<void>;
  };
  validation: {
    validateForm: (email: string, password: string, confirmPassword: string, isSignUp: boolean, isResetPassword: boolean) => boolean;
  };
  errorManager: {
    setErrorMessage: (message: string) => void;
    clearError: () => void;
  };
  modeActions: {
    setNeedsEmailConfirmation: (needs: boolean) => void;
  };
  onShakeAnimation?: () => void;
}

export const useSignIn = ({
  credentials,
  security,
  validation,
  errorManager,
  modeActions,
  onShakeAnimation
}: SignInProps) => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const [isLoading, setIsLoading] = useState(false);

  const handleSignIn = async () => {
    if (security.isAccountLocked) {
      errorManager.setErrorMessage('Your account is locked. Please try again later.');
      onShakeAnimation?.();
      return { success: false };
    }

    if (!validation.validateForm(
      credentials.email,
      credentials.password,
      '',
      false,
      false
    )) {
      onShakeAnimation?.();
      return { success: false };
    }

    setIsLoading(true);
    errorManager.clearError();

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        // Check for MFA challenge
        if (error.message.includes('MFA challenge required') || error.message.includes('mfa_challenge_required')) {
          try {
            const { data: challengeData, error: challengeError } = await supabase.functions.invoke('mfa-challenge', {
              body: { email: credentials.email }
            });
            
            if (challengeError) throw challengeError;
            
            // Navigate to MFA verification screen
            navigation.navigate('VerifyMfaScreen', {
              email: credentials.email,
              challengeId: challengeData.challengeId
            });
            
            return { success: false, requiresMfa: true };
          } catch (mfaError) {
            console.error('MFA challenge error:', mfaError);
            errorManager.setErrorMessage('MFA verification required but failed to initiate. Please try again.');
            onShakeAnimation?.();
            return { success: false };
          }
        } else if (error.message.includes('Email not confirmed')) {
          modeActions.setNeedsEmailConfirmation(true);
          errorManager.setErrorMessage('Please check your email to confirm your account before logging in.');
          onShakeAnimation?.();
          return { success: false, needsConfirmation: true };
        } else if (error.message.includes('Invalid login credentials')) {
          errorManager.setErrorMessage('Invalid email or password');
          await security.recordFailedAttempt(credentials.email);
          onShakeAnimation?.();
          return { success: false };
        } else {
          errorManager.setErrorMessage(error.message);
          await security.recordFailedAttempt(credentials.email);
          onShakeAnimation?.();
          return { success: false };
        }
      }

      await security.resetLoginAttempts(credentials.email);
      toast.success('Signed in successfully');
      return { success: true, data };
    } catch (err) {
      errorManager.setErrorMessage('An error occurred. Please try again.');
      onShakeAnimation?.();
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    handleSignIn,
  };
};