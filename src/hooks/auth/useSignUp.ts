import { useState } from 'react';
import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

interface SignUpProps {
  credentials: { email: string; password: string; confirmPassword: string };
  validation: {
    validateForm: (email: string, password: string, confirmPassword: string, isSignUp: boolean, isResetPassword: boolean) => boolean;
  };
  errorManager: {
    setErrorMessage: (message: string) => void;
    clearError: () => void;
  };
  modeActions: {
    setNeedsEmailConfirmation: (needs: boolean) => void;
    setIsSignUp: (isSignUp: boolean) => void;
  };
  onShakeAnimation?: () => void;
}

export const useSignUp = ({
  credentials,
  validation,
  errorManager,
  modeActions,
  onShakeAnimation
}: SignUpProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleSignUp = async () => {
    if (!validation.validateForm(
      credentials.email,
      credentials.password,
      credentials.confirmPassword,
      true,
      false
    )) {
      onShakeAnimation?.();
      return { success: false };
    }

    setIsLoading(true);
    errorManager.clearError();

    try {
      const { data, error } = await supabase.auth.signUp({
        email: credentials.email,
        password: credentials.password,
        options: {
          emailRedirectTo: 'https://hoofbeat.app/confirm-email'
        }
      });

      if (error) {
        errorManager.setErrorMessage(error.message);
        onShakeAnimation?.();
        return { success: false };
      }

      if (data?.user?.identities?.length === 0) {
        errorManager.setErrorMessage('This email is already registered. Please sign in instead.');
        onShakeAnimation?.();
        return { success: false };
      } else if (!data?.user?.email_confirmed_at) {
        modeActions.setNeedsEmailConfirmation(true);
        toast.success('Please check your email to confirm your account');
        modeActions.setIsSignUp(false);
        return { success: true, needsConfirmation: true, data };
      } else {
        toast.success('Account created successfully');
        modeActions.setIsSignUp(false);
        return { success: true, data };
      }
    } catch (err) {
      errorManager.setErrorMessage('An error occurred. Please try again.');
      onShakeAnimation?.();
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    handleSignUp,
  };
};