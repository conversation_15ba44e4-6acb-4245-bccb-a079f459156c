import { useState } from 'react';
import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

interface PasswordResetProps {
  credentials: { email: string };
  errorManager: {
    setErrorMessage: (message: string) => void;
    clearError: () => void;
  };
  modeActions: {
    setIsResetPassword: (isReset: boolean) => void;
  };
  onShakeAnimation?: () => void;
}

export const usePasswordReset = ({
  credentials,
  errorManager,
  modeActions,
  onShakeAnimation
}: PasswordResetProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleForgotPassword = async () => {
    if (!credentials.email) {
      errorManager.setErrorMessage('Please enter your email');
      onShakeAnimation?.();
      return { success: false };
    }

    setIsLoading(true);
    errorManager.clearError();

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(credentials.email, {
        redirectTo: 'https://hoofbeat.app/reset-password',
      });

      if (error) {
        errorManager.setErrorMessage(error.message);
        onShakeAnimation?.();
        return { success: false };
      }

      toast.success('Password reset link sent to your email');
      modeActions.setIsResetPassword(false);
      return { success: true };
    } catch (err) {
      errorManager.setErrorMessage('An error occurred. Please try again.');
      onShakeAnimation?.();
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendConfirmation = async () => {
    if (!credentials.email) {
      errorManager.setErrorMessage('Please enter your email');
      onShakeAnimation?.();
      return { success: false };
    }

    setIsLoading(true);
    errorManager.clearError();

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: credentials.email,
        options: {
          emailRedirectTo: 'https://hoofbeat.app/confirm-email'
        }
      });

      if (error) {
        errorManager.setErrorMessage(error.message);
        onShakeAnimation?.();
        return { success: false };
      }

      toast.success('Confirmation email resent. Please check your inbox.');
      return { success: true };
    } catch (err) {
      errorManager.setErrorMessage('An error occurred. Please try again.');
      onShakeAnimation?.();
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    handleForgotPassword,
    handleResendConfirmation,
  };
};