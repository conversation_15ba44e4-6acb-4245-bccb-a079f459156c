import { useState, useEffect, useCallback } from 'react';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

interface GoogleAuthState {
  isGoogleLoading: boolean;
  isGoogleModuleLoading: boolean;
  googleModuleReady: boolean;
  googleModuleLoadFailed: boolean;
}

interface GoogleAuthActions {
  handleGoogleSignIn: () => Promise<{ success: boolean; [key: string]: any }>;
}

export const useGoogleAuth = (errorManager: any, onShakeAnimation?: () => void): GoogleAuthState & GoogleAuthActions => {
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [googleAuthModule, setGoogleAuthModule] = useState<any>(null);
  const [isGoogleModuleLoading, setIsGoogleModuleLoading] = useState(false);
  const [googleModuleLoadFailed, setGoogleModuleLoadFailed] = useState(false);

  // Conditionally load Google Auth module on native platforms
  useEffect(() => {
    const loadGoogleAuthModule = async () => {
      console.log('🔍 [Google Auth Debug] Starting module loading process...');
      console.log('🔍 [Google Auth Debug] Platform:', Platform.OS);
      
      // Only attempt to load on native platforms
      if (Platform.OS === 'android' || Platform.OS === 'ios') {
        console.log('🔍 [Google Auth Debug] Native platform detected, proceeding with module loading');
        setIsGoogleModuleLoading(true);
        
        try {
          console.log('🔍 [Google Auth Debug] Attempting primary import method...');
          // Primary import method
          const [GoogleModule, WebBrowserModule] = await Promise.all([
            import('expo-auth-session/providers/google'),
            import('expo-web-browser')
          ]);
          
          console.log('🔍 [Google Auth Debug] Primary import successful!');
          
          // Complete auth session setup
          WebBrowserModule.maybeCompleteAuthSession();
          console.log('🔍 [Google Auth Debug] maybeCompleteAuthSession called');
          
          setGoogleAuthModule({
            Google: GoogleModule,
            WebBrowser: WebBrowserModule
          });
          console.log('🔍 [Google Auth Debug] ✅ Google auth module loaded successfully via primary method');
        } catch (primaryError) {
          console.warn('🔍 [Google Auth Debug] ⚠️ Primary Google module import failed:', primaryError);
          
          try {
            console.log('🔍 [Google Auth Debug] Attempting alternative import method...');
            // Alternative import method as fallback
            const [AuthSession, WebBrowserModule] = await Promise.all([
              import('expo-auth-session'),
              import('expo-web-browser')
            ]);
            
            console.log('🔍 [Google Auth Debug] Alternative imports successful');
            
            if (!AuthSession.providers || !AuthSession.providers.Google) {
              throw new Error('Google provider not found on AuthSession.providers');
            }
            
            // Complete auth session setup
            WebBrowserModule.maybeCompleteAuthSession();
            console.log('🔍 [Google Auth Debug] maybeCompleteAuthSession called (alternative)');
            
            setGoogleAuthModule({
              Google: { default: AuthSession.providers.Google },
              WebBrowser: WebBrowserModule
            });
            console.log('🔍 [Google Auth Debug] ✅ Google auth module loaded successfully via alternative method');
          } catch (alternativeError) {
            console.error('🔍 [Google Auth Debug] ❌ Both primary and alternative Google auth module loading failed');
            setGoogleModuleLoadFailed(true);
            console.log('🔍 [Google Auth Debug] googleModuleLoadFailed set to true');
          }
        } finally {
          setIsGoogleModuleLoading(false);
          console.log('🔍 [Google Auth Debug] Module loading process completed');
        }
      } else {
        console.log('🔍 [Google Auth Debug] Web platform detected, skipping module loading');
      }
    };

    loadGoogleAuthModule();
  }, []);

  // Configure Google Auth Request
  const googleWebClientId = Constants.expoConfig?.extra?.googleWebClientId;
  
  console.log('🔍 [Google Auth Debug] Configuring useAuthRequest...');
  console.log('🔍 [Google Auth Debug] googleAuthModule available:', !!googleAuthModule);
  
  // Configure Google Auth Request (must be at top level for hook rules)
  const [request, response, promptAsyncInternal] = googleAuthModule?.Google?.useAuthRequest ? 
    googleAuthModule.Google.useAuthRequest({
      expoClientId: '148731138249-dneh8a5gagnd9n7rinfk3ecos7c0gect.apps.googleusercontent.com',
      iosClientId: '148731138249-dneh8a5gagnd9n7rinfk3ecos7c0gect.apps.googleusercontent.com',
      webClientId: googleWebClientId || '148731138249-t2arajrujdmq01vjubr8omuj97qocfd6.apps.googleusercontent.com',
    }) : [null, null, () => {
      console.log('🔍 [Google Auth Debug] Using dummy promptAsync (module not loaded)');
      if (Platform.OS !== 'web') {
        toast.error('Google Sign-In is initializing or unavailable. Please try again.');
      } else {
        toast.info('Google Sign-In is available in the mobile app. Please use email/password on web.');
      }
      return Promise.resolve(null);
    }];
    
  console.log('🔍 [Google Auth Debug] useAuthRequest configured:', {
    hasRequest: !!request,
    hasResponse: !!response,
    hasPromptAsync: typeof promptAsyncInternal === 'function'
  });

  const handleGoogleSignIn = useCallback(async () => {
    setIsGoogleLoading(true);
    errorManager.clearError();

    try {
      // Check if we're on web platform
      if (Platform.OS === 'web') {
        toast.info('Google Sign-In is available in the mobile app. Please use email/password on web.');
        return { success: false, webNotSupported: true };
      }

      // Check if Google module is ready
      if (!googleAuthModule || typeof promptAsyncInternal !== 'function') {
        if (isGoogleModuleLoading) {
          toast.error('Google Sign-In is still loading. Please try again in a moment.');
        } else {
          toast.error('Google Sign-In module failed to load. Please try another sign-in method.');
        }
        return { success: false, moduleNotReady: true };
      }

      // Check if request is configured
      if (!request) {
        toast.error('Google Sign-In is not properly configured.');
        return { success: false, notConfigured: true };
      }

      const authResponse = await promptAsyncInternal();
      
      if (authResponse?.type === 'success') {
        const { id_token } = authResponse.params;
        
        if (!id_token) {
          throw new Error('No ID token received from Google');
        }

        // Sign in with Supabase using Google ID token
        const { data, error } = await supabase.auth.signInWithIdToken({
          provider: 'google',
          token: id_token,
        });

        if (error) {
          throw error;
        }

        toast.success('Signed in with Google successfully');
        return { success: true, data };
      } else if (authResponse?.type === 'cancel' || authResponse?.type === 'dismiss') {
        toast.info('Google Sign-In cancelled.');
        return { success: false, cancelled: true };
      } else if (authResponse?.type === 'error') {
        const errorMessage = authResponse.error?.message || 'Unknown Google Sign-In error';
        toast.error(`Google Sign-In error: ${errorMessage}`);
        return { success: false, error: errorMessage };
      } else {
        throw new Error('Unexpected Google Sign-In response');
      }
    } catch (err: any) {
      console.error('Google Sign-In error:', err);
      
      const errorMessage = err.message || 'Google Sign-In failed. Please try again.';
      errorManager.setErrorMessage(errorMessage);
      onShakeAnimation?.();
      return { success: false, error: errorMessage };
    } finally {
      setIsGoogleLoading(false);
    }
  }, [googleAuthModule, promptAsyncInternal, request, isGoogleModuleLoading, errorManager, onShakeAnimation]);

  return {
    isGoogleLoading,
    isGoogleModuleLoading,
    googleModuleReady: !!googleAuthModule && !googleModuleLoadFailed,
    googleModuleLoadFailed,
    handleGoogleSignIn,
  };
};