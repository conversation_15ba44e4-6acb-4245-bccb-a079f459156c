import { useMemo } from 'react';
import { Animal } from '../mocks/animals';

export interface SpeedDisplayData {
  formattedSpeed: string;
  speedUnit: string;
  speedColor: string;
  speedLevel: 'low' | 'medium' | 'high';
  lastUpdated: string;
}

/**
 * @magic_description Hook for processing animal speed data for display
 * Handles speed formatting, color coding, and timestamp formatting
 */
export const useSpeedDisplay = (
  animal: Animal,
  colors: any
): SpeedDisplayData => {
  return useMemo(() => {
    const speed = animal.speed || 0;
    const speedUnit = animal.speedUnit || 'km/h';
    const speedUpdatedAt = animal.speedUpdatedAt;

    // Determine speed level based on thresholds
    const getSpeedLevel = (speed: number): 'low' | 'medium' | 'high' => {
      if (speed < 5) return 'low';
      if (speed < 15) return 'medium';
      return 'high';
    };

    // Get color based on speed level
    const getSpeedColor = (level: 'low' | 'medium' | 'high'): string => {
      switch (level) {
        case 'low':
          return colors.success;
        case 'medium':
          return colors.warning;
        case 'high':
          return colors.error;
        default:
          return colors.textSecondary;
      }
    };

    // Format speed to one decimal place
    const formattedSpeed = speed.toFixed(1);

    // Format last updated timestamp
    const getLastUpdated = (): string => {
      if (!speedUpdatedAt) return 'Never';
      
      try {
        const date = new Date(speedUpdatedAt);
        const now = new Date();
        const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
        
        const diffInHours = Math.floor(diffInMinutes / 60);
        if (diffInHours < 24) return `${diffInHours}h ago`;
        
        const diffInDays = Math.floor(diffInHours / 24);
        return `${diffInDays}d ago`;
      } catch (error) {
        return 'Unknown';
      }
    };

    const speedLevel = getSpeedLevel(speed);
    const speedColor = getSpeedColor(speedLevel);
    const lastUpdated = getLastUpdated();

    return {
      formattedSpeed,
      speedUnit,
      speedColor,
      speedLevel,
      lastUpdated,
    };
  }, [animal.speed, animal.speedUnit, animal.speedUpdatedAt, colors]);
};

export default useSpeedDisplay;