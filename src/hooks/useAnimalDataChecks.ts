import { useState, useEffect } from 'react';
import { useAnimalStore } from '../store/animalStore';
import { useDeviceReadinessCheck } from './useDeviceReadinessCheck';

interface AnimalDataChecksResult {
  vaccineReady: boolean;
  vaccineMessage: string;
  vaccineMicrochipId?: string;
  gpsReady: boolean;
  gpsMessage: string;
  speedReady: boolean;
  speedMessage: string;
}

export const useAnimalDataChecks = (animalId: string): AnimalDataChecksResult => {
  const { getAnimalById } = useAnimalStore();
  const deviceReadiness = useDeviceReadinessCheck(animalId);
  
  const [result, setResult] = useState<AnimalDataChecksResult>({
    vaccineReady: false,
    vaccineMessage: 'Checking...',
    gpsReady: false,
    gpsMessage: 'Checking...',
    speedReady: false,
    speedMessage: 'Checking...'
  });
  
  useEffect(() => {
    const checkVaccineDataReadiness = (animalId: string): {
      isReady: boolean;
      message: string;
      microchipId?: string;
    } => {
      const animal = getAnimalById(animalId);
      
      if (!animal) {
        return { isReady: false, message: "Animal not found" };
      }
      
      if (!animal.microchipId) {
        return { isReady: false, message: "No microchip ID" };
      }
      
      // Validate microchip format (example: should be alphanumeric and certain length)
      const isValidFormat = /^[A-Z0-9]{8,12}$/.test(animal.microchipId);
      if (!isValidFormat) {
        return { 
          isReady: false, 
          message: "Invalid microchip format",
          microchipId: animal.microchipId
        };
      }
      
      return { 
        isReady: true, 
        message: "Ready for vaccine data",
        microchipId: animal.microchipId
      };
    };
    
    const checkGPSReadiness = (animalId: string): {
      isReady: boolean;
      message: string;
    } => {
      const animal = getAnimalById(animalId);
      
      if (!animal) {
        return { isReady: false, message: "Animal not found" };
      }
      
      // Check if device is connected and supports GPS
      if (!deviceReadiness.isReady) {
        return { isReady: false, message: "Device not ready" };
      }
      
      return { 
        isReady: true, 
        message: "GPS ready" 
      };
    };
    
    const checkSpeedTrackingReadiness = (animalId: string): {
      isReady: boolean;
      message: string;
    } => {
      // Speed tracking depends on GPS, so check that first
      const gpsReadiness = checkGPSReadiness(animalId);
      if (!gpsReadiness.isReady) {
        return { isReady: false, message: "GPS not ready" };
      }
      
      return { 
        isReady: true, 
        message: "Speed tracking ready"
      };
    };
    
    // Perform all checks
    const vaccineCheck = checkVaccineDataReadiness(animalId);
    const gpsCheck = checkGPSReadiness(animalId);
    const speedCheck = checkSpeedTrackingReadiness(animalId);
    
    setResult({
      vaccineReady: vaccineCheck.isReady,
      vaccineMessage: vaccineCheck.message,
      vaccineMicrochipId: vaccineCheck.microchipId,
      gpsReady: gpsCheck.isReady,
      gpsMessage: gpsCheck.message,
      speedReady: speedCheck.isReady,
      speedMessage: speedCheck.message
    });
  }, [animalId, getAnimalById, deviceReadiness.isReady]);
  
  return result;
};