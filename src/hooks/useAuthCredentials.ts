import { useState } from 'react';

export interface AuthCredentials {
  email: string;
  password: string;
  confirmPassword: string;
  showPassword: boolean;
}

export interface AuthCredentialsActions {
  setEmail: (email: string) => void;
  setPassword: (password: string) => void;
  setConfirmPassword: (password: string) => void;
  setShowPassword: (show: boolean) => void;
  resetCredentials: () => void;
}

/**
 * @magic_description Hook for managing authentication form input state
 * Handles email, password, confirm password, and password visibility
 */
export const useAuthCredentials = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const resetCredentials = () => {
    setEmail('');
    setPassword('');
    setConfirmPassword('');
    setShowPassword(false);
  };

  return {
    // State
    email,
    password,
    confirmPassword,
    showPassword,
    // Actions
    setEmail,
    setPassword,
    setConfirmPassword,
    setShowPassword,
    resetCredentials,
  };
};

export default useAuthCredentials;