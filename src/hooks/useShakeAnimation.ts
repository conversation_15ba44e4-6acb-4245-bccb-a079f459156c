import { useRef } from 'react';
import { Animated } from 'react-native';

export const useShakeAnimation = () => {
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  
  const startShake = () => {
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();
  };
  
  return {
    shakeAnimation,
    startShake
  };
};