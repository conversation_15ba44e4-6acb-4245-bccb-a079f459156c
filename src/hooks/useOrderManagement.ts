import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import { toast } from 'sonner-native';
import { useNavigation } from '@react-navigation/native';
import { useUserStore } from '../store/userStore';
import { useOrderStore } from '../store/orderStore';

type PlanType = 'monthly' | 'yearly';
type OrderStatus = 'pending' | 'paid' | 'failed' | 'cancelled' | 'refunded';

interface OrderManagementState {
  isProcessingOrder: boolean;
  currentOrderId: string | null;
}

interface OrderManagementActions {
  createOrder: (planType: PlanType, productId: string, checkoutUrl: string) => Promise<string | null>;
  updateOrderStatus: (orderId: string, status: OrderStatus) => Promise<void>;
  handlePaymentSuccess: (orderId: string, planType: PlanType) => Promise<void>;
  handlePaymentFailure: (orderId: string, error: string) => Promise<void>;
  checkOrderStatus: (orderId: string) => Promise<OrderStatus | null>;
}

type OrderManagementHook = OrderManagementState & OrderManagementActions;

/**
 * Custom hook for managing order lifecycle and status updates
 * Handles order creation, status tracking, and subscription activation
 */
export const useOrderManagement = (onSuccess?: () => void): OrderManagementHook => {
  const [isProcessingOrder, setIsProcessingOrder] = useState(false);
  const [currentOrderId, setCurrentOrderId] = useState<string | null>(null);
  const { user, updateSubscription } = useUserStore();
  const { createOrder: createOrderInStore, updateOrderStatus: updateOrderInStore } = useOrderStore();
  const navigation = useNavigation();
  
  const getPlanPrice = (planType: PlanType) => {
    return planType === 'monthly' ? '$14.99' : '$143.90';
  };
  
  const showSuccessAlert = (planType: PlanType) => {
    if (onSuccess) {
      // Use custom success callback if provided
      toast.success(`Your ${planType} subscription is now active!`);
      onSuccess();
    } else {
      // Default behavior
      Alert.alert(
        "Payment Successful!",
        `Your ${planType} subscription has been activated.`,
        [
          {
            text: "OK",
            onPress: () => {
              toast.success(`Your ${planType} subscription is now active!`);
              navigation.goBack();
            }
          }
        ]
      );
    }
  };
  
  const createOrder = async (planType: PlanType, productId: string, checkoutUrl: string): Promise<string | null> => {
    if (!user) {
      toast.error('User not found. Please log in.');
      return null;
    }
    
    setIsProcessingOrder(true);
    
    try {
      const orderData = {
        user_id: user.id,
        product_id: productId,
        quantity: 1,
        total_amount: planType === 'monthly' ? 14.99 : 143.90,
        currency: 'USD',
        status: 'pending' as const,
        checkout_url: checkoutUrl,
        plan_type: planType,
        payment_method: 'lemonsqueezy'
      };
      
      const orderId = await createOrderInStore(orderData);
      
      if (orderId) {
        setCurrentOrderId(orderId);
        console.log('Order created successfully:', orderId);
        return orderId;
      }
      
      throw new Error('Failed to create order');
    } catch (error: any) {
      console.error('Order creation error:', error);
      toast.error(`Failed to create order: ${error.message}`);
      return null;
    } finally {
      setIsProcessingOrder(false);
    }
  };
  
  const updateOrderStatus = async (orderId: string, status: OrderStatus): Promise<void> => {
    try {
      await updateOrderInStore(orderId, { status });
      console.log(`Order ${orderId} status updated to: ${status}`);
    } catch (error: any) {
      console.error('Error updating order status:', error);
      toast.error('Failed to update order status');
    }
  };
  
  const handlePaymentSuccess = async (orderId: string, planType: PlanType): Promise<void> => {
    setIsProcessingOrder(true);
    
    try {
      // Update order status to paid
      await updateOrderStatus(orderId, 'paid');
      
      // Update user subscription status
      await updateSubscription(true, planType);
      
      // Show success message
      showSuccessAlert(planType);
      
      console.log('Payment success handled for order:', orderId);
    } catch (error: any) {
      console.error('Error handling payment success:', error);
      toast.error('Payment succeeded but failed to activate subscription. Please contact support.');
    } finally {
      setIsProcessingOrder(false);
    }
  };
  
  const handlePaymentFailure = async (orderId: string, error: string): Promise<void> => {
    try {
      // Update order status to failed
      await updateOrderStatus(orderId, 'failed');
      
      // Show error message
      toast.error(`Payment failed: ${error}`);
      
      console.log('Payment failure handled for order:', orderId);
    } catch (err: any) {
      console.error('Error handling payment failure:', err);
      toast.error('Payment failed and unable to update order status');
    }
  };
  
  const checkOrderStatus = async (orderId: string): Promise<OrderStatus | null> => {
    try {
      // This would typically call an API to check the current order status
      // For now, we'll rely on the webhook to update status
      console.log('Checking order status for:', orderId);
      return null; // Status will be updated via webhook
    } catch (error: any) {
      console.error('Error checking order status:', error);
      return null;
    }
  };
  
  return {
    isProcessingOrder,
    currentOrderId,
    createOrder,
    updateOrderStatus,
    handlePaymentSuccess,
    handlePaymentFailure,
    checkOrderStatus
  };
};