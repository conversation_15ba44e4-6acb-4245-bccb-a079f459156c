import { useState } from 'react';

interface ValidationState {
  errorMessage: string;
}

interface ValidationActions {
  validateForm: (email: string, password: string, confirmPassword: string, isSignUp: boolean, isResetPassword: boolean) => boolean;
  setErrorMessage: (message: string) => void;
  clearError: () => void;
}

/**
 * @magic_description Custom hook for login form validation logic
 * Handles email, password, and confirm password validation with appropriate error messages
 */
export const useLoginValidation = (): ValidationState & ValidationActions => {
  const [errorMessage, setErrorMessage] = useState('');
  
  const validateForm = (
    email: string, 
    password: string, 
    confirmPassword: string, 
    isSignUp: boolean, 
    isResetPassword: boolean
  ): boolean => {
    setErrorMessage('');
    
    if (!email) {
      setErrorMessage('Please enter your email');
      return false;
    }
    
    if (!email.includes('@')) {
      setErrorMessage('Please enter a valid email');
      return false;
    }
    
    if (isResetPassword) {
      if (!password) {
        setErrorMessage('Please enter a new password');
        return false;
      }
      
      if (password.length < 6) {
        setErrorMessage('Password must be at least 6 characters');
        return false;
      }
      
      if (password !== confirmPassword) {
        setErrorMessage('Passwords do not match');
        return false;
      }
      
      return true;
    }
    
    if (!isSignUp && !password) {
      setErrorMessage('Please enter your password');
      return false;
    }
    
    if (isSignUp) {
      if (!password) {
        setErrorMessage('Please enter a password');
        return false;
      }
      
      if (password.length < 6) {
        setErrorMessage('Password must be at least 6 characters');
        return false;
      }
      
      if (password !== confirmPassword) {
        setErrorMessage('Passwords do not match');
        return false;
      }
    }
    
    return true;
  };
  
  const clearError = () => {
    setErrorMessage('');
  };
  
  return {
    errorMessage,
    validateForm,
    setErrorMessage,
    clearError
  };
};