import { useState, useEffect, useCallback } from 'react';
import { useAnimalStore } from '../store/animalStore';
import { useVitalsStore } from '../store/vitalsStore';
import { useUserStore } from '../store/userStore';
import { toast } from 'sonner-native';
import { Animal } from '../mocks/animals';
import { VitalRecord } from '../mocks/vitals';

interface UseAnimalVitalsHistoryReturn {
  // Data
  animal: Animal | undefined;
  vitalsHistory: VitalRecord[];
  isLoading: boolean;
  isRefreshing: boolean;
  isPremium: boolean;
  isLimitedView: boolean;
  error: string | null;
  
  // Actions
  refreshVitalsHistory: () => Promise<void>;
}

// Premium users see unlimited history, non-premium users see only last 3 records
const FREE_TIER_LIMIT = 3;

/**
 * @magic_description Hook for managing animal vital signs history
 * Handles data fetching with premium feature limitations
 */
export const useAnimalVitalsHistory = (animalId: string): UseAnimalVitalsHistoryReturn => {
  // Store hooks
  const { getAnimalById } = useAnimalStore();
  const { vitals, fetchVitals, isLoading: vitalsLoading } = useVitalsStore();
  const { user } = useUserStore();
  
  // Local state
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Get animal data
  const animal = getAnimalById(animalId);
  
  // Get user premium status
  const isPremium = user?.is_premium || false;
  
  // Filter and sort vitals for this animal
  const animalVitals = vitals
    .filter(vital => vital.animalId === animalId)
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  
  // Apply premium limitations
  const isLimitedView = !isPremium && animalVitals.length > FREE_TIER_LIMIT;
  const vitalsHistory = isPremium ? animalVitals : animalVitals.slice(0, FREE_TIER_LIMIT);
  
  // Initial data fetch
  useEffect(() => {
    const initializeVitalsHistory = async () => {
      if (!animal) {
        setError('Animal not found');
        return;
      }
      
      try {
        setError(null);
        await fetchVitals();
      } catch (err) {
        console.error('Error fetching vitals history:', err);
        setError('Failed to load vital signs history');
        toast.error('Failed to load vital signs history');
      }
    };
    
    initializeVitalsHistory();
  }, [animal, animalId, fetchVitals]);
  
  // Refresh vitals history
  const refreshVitalsHistory = useCallback(async () => {
    if (!animal) {
      toast.error('Animal not found');
      return;
    }
    
    try {
      setIsRefreshing(true);
      setError(null);
      
      await fetchVitals();
      
      toast.success('Vital signs history refreshed');
    } catch (err) {
      console.error('Error refreshing vitals history:', err);
      setError('Failed to refresh vital signs history');
      toast.error('Failed to refresh vital signs history');
    } finally {
      setIsRefreshing(false);
    }
  }, [animal, fetchVitals]);
  
  return {
    // Data
    animal,
    vitalsHistory,
    isLoading: vitalsLoading,
    isRefreshing,
    isPremium,
    isLimitedView,
    error,
    
    // Actions
    refreshVitalsHistory,
  };
};

export default useAnimalVitalsHistory;