import { useState, useEffect } from 'react';
import { supabase } from '../supabase/client';

interface LoginSecurityState {
  loginAttempts: number;
  isAccountLocked: boolean;
  lockUntil: Date | null;
}

interface LoginSecurityActions {
  checkLoginStatus: (email: string) => Promise<void>;
  recordFailedAttempt: (email: string) => Promise<void>;
  resetLoginAttempts: (email: string) => Promise<void>;
}

/**
 * @magic_description Custom hook for managing login security including failed attempts and account locking
 * Handles checking login status, recording failed attempts, and resetting attempts on successful login
 */
export const useLoginSecurity = (): LoginSecurityState & LoginSecurityActions => {
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isAccountLocked, setIsAccountLocked] = useState(false);
  const [lockUntil, setLockUntil] = useState<Date | null>(null);
  
  const checkLoginStatus = async (email: string) => {
    if (!email) return;
    
    try {
      const { data, error } = await supabase.functions.invoke('auth-security', {
        body: { action: 'check_status', email }
      });
      
      if (error) throw error;
      
      if (data.locked) {
        setIsAccountLocked(true);
        setLockUntil(new Date(data.lockedUntil));
        setLoginAttempts(data.attempts);
      } else {
        setIsAccountLocked(false);
        setLoginAttempts(data.attempts);
      }
    } catch (err) {
      console.error('Error checking login status:', err);
    }
  };
  
  const recordFailedAttempt = async (email: string) => {
    if (!email) return;
    
    try {
      const { data, error } = await supabase.functions.invoke('auth-security', {
        body: { action: 'failed_attempt', email }
      });
      
      if (error) throw error;
      
      setLoginAttempts(data.attempts);
      
      if (data.locked) {
        setIsAccountLocked(true);
        setLockUntil(data.lockedUntil ? new Date(data.lockedUntil) : null);
      }
    } catch (err) {
      console.error('Error recording failed attempt:', err);
    }
  };
  
  const resetLoginAttempts = async (email: string) => {
    if (!email) return;
    
    try {
      await supabase.functions.invoke('auth-security', {
        body: { action: 'reset_attempts', email }
      });
      
      setLoginAttempts(0);
      setIsAccountLocked(false);
      setLockUntil(null);
    } catch (err) {
      console.error('Error resetting login attempts:', err);
    }
  };
  
  return {
    loginAttempts,
    isAccountLocked,
    lockUntil,
    checkLoginStatus,
    recordFailedAttempt,
    resetLoginAttempts
  };
};