import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAnimalStore } from '../store/animalStore';
import { useVitalsStore } from '../store/vitalsStore';
import { useFeedingStore } from '../store/feedingStore';
import { useMedicationStore } from '../store/medicationStore';
import { useVaccinationStore } from '../store/vaccinationStore';
import { toast } from 'sonner-native';
import { Animal } from '../mocks/animals';
import { HomeStackParamList } from '../navigation';

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

export interface UseAnimalDetailReturn {
  // Data
  animal: Animal | undefined;
  animalVitals: any[];
  todayFeedings: any[];
  medications: any[];
  vaccinations: any[];
  isRefreshingSpeed: boolean;
  isLoading: boolean;
  
  // Actions
  handleDelete: () => void;
  handleEdit: () => void;
  handleAddVitals: () => void;
  handleAddMedication: () => void;
  handleAddVaccination: () => void;
  handleViewLocation: () => void;
  handleViewFeedSchedule: () => void;
  handlePrint: (type: 'feeding' | 'medication' | 'vaccination') => void;
  handleRefreshSpeed: () => Promise<void>;
}

/**
 * @magic_description Hook for managing animal detail screen data and actions
 * Handles data fetching, filtering, and all animal-related operations
 */
export const useAnimalDetail = (animalId: string): UseAnimalDetailReturn => {
  const navigation = useNavigation<NavigationProp>();
  
  // Store hooks
  const { getAnimalById, deleteAnimal, updateAnimalSpeed } = useAnimalStore();
  const { vitals } = useVitalsStore();
  const { getTodayFeedings } = useFeedingStore();
  const { getMedicationsByAnimalId } = useMedicationStore();
  const { getVaccinationsByAnimalId } = useVaccinationStore();
  
  // Local state
  const [animalVitals, setAnimalVitals] = useState<any[]>([]);
  const [todayFeedings, setTodayFeedings] = useState<any[]>([]);
  const [medications, setMedications] = useState<any[]>([]);
  const [vaccinations, setVaccinations] = useState<any[]>([]);
  const [isRefreshingSpeed, setIsRefreshingSpeed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  // Get animal data
  const animal = getAnimalById(animalId);
  
  // Fetch and filter data when animal or store data changes
  useEffect(() => {
    const fetchAnimalData = async () => {
      if (!animal) {
        setIsLoading(false);
        return;
      }
      
      try {
        setIsLoading(true);
        
        // Filter vitals for this animal
        const filteredVitals = vitals.filter(vital => vital.animalId === animalId);
        setAnimalVitals(filteredVitals);
        
        // Get today's feedings
        const feedings = getTodayFeedings(animalId);
        setTodayFeedings(feedings);
        
        // Get medications
        const animalMedications = getMedicationsByAnimalId(animalId);
        setMedications(animalMedications);
        
        // Get vaccinations
        const animalVaccinations = getVaccinationsByAnimalId(animalId);
        setVaccinations(animalVaccinations);
        
      } catch (error) {
        console.error('Error fetching animal data:', error);
        toast.error('Failed to load animal data');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchAnimalData();
  }, [animal, animalId, vitals, getTodayFeedings, getMedicationsByAnimalId, getVaccinationsByAnimalId]);
  
  // Handle animal deletion
  const handleDelete = useCallback(() => {
    if (!animal) return;
    
    Alert.alert(
      'Delete Animal',
      `Are you sure you want to delete ${animal.name}? This action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            try {
              deleteAnimal(animalId);
              toast.success(`${animal.name} has been deleted`);
              navigation.goBack();
            } catch (error) {
              console.error('Error deleting animal:', error);
              toast.error('Failed to delete animal');
            }
          },
        },
      ]
    );
  }, [animal, animalId, deleteAnimal, navigation]);
  
  // Handle animal editing
  const handleEdit = useCallback(() => {
    if (!animal) return;
    
    navigation.navigate('AddAnimal', {
      animalId: animalId,
      isEditing: true,
    });
  }, [animal, animalId, navigation]);
  
  // Handle adding vitals
  const handleAddVitals = useCallback(() => {
    toast.info('Vitals can only be recorded by connected devices');
  }, []);
  
  // Handle adding medication
  const handleAddMedication = useCallback(() => {
    if (!animal) return;
    
    navigation.navigate('AddMedication', {
      animalId: animalId,
    });
  }, [animal, animalId, navigation]);
  
  // Handle adding vaccination
  const handleAddVaccination = useCallback(() => {
    if (!animal) return;
    
    if (!animal.microchipId) {
      toast.error('Microchip ID is required for vaccination records');
      return;
    }
    
    // Simulate fetching vaccination data
    toast.info('Fetching vaccination data from veterinary database...');
    
    setTimeout(() => {
      toast.success('Vaccination data updated');
    }, 2000);
  }, [animal]);
  
  // Handle viewing location
  const handleViewLocation = useCallback(() => {
    if (!animal) return;
    
    navigation.navigate('Location', {
      animalId: animalId,
    });
  }, [animal, animalId, navigation]);
  
  // Handle viewing feed schedule
  const handleViewFeedSchedule = useCallback(() => {
    if (!animal) return;
    
    navigation.navigate('FeedSchedule', {
      animalId: animalId,
    });
  }, [animal, animalId, navigation]);
  
  // Handle printing reports
  const handlePrint = useCallback((type: 'feeding' | 'medication' | 'vaccination') => {
    if (!animal) return;
    
    navigation.navigate('Print', {
      animalId: animalId,
      printType: type,
    });
  }, [animal, animalId, navigation]);
  
  // Handle speed refresh
  const handleRefreshSpeed = useCallback(async () => {
    if (!animal) return;
    
    try {
      setIsRefreshingSpeed(true);
      
      // Simulate speed update
      const newSpeed = Math.random() * 30; // Random speed between 0-30 km/h
      
      await updateAnimalSpeed(animalId, newSpeed);
      
      toast.success('Speed updated successfully');
    } catch (error) {
      console.error('Error refreshing speed:', error);
      toast.error('Failed to refresh speed');
    } finally {
      setIsRefreshingSpeed(false);
    }
  }, [animal, animalId, updateAnimalSpeed]);
  
  return {
    // Data
    animal,
    animalVitals,
    todayFeedings,
    medications,
    vaccinations,
    isRefreshingSpeed,
    isLoading,
    
    // Actions
    handleDelete,
    handleEdit,
    handleAddVitals,
    handleAddMedication,
    handleAddVaccination,
    handleViewLocation,
    handleViewFeedSchedule,
    handlePrint,
    handleRefreshSpeed,
  };
};

export default useAnimalDetail;