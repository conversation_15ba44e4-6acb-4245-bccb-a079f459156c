import { useState, useEffect, useCallback } from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { toast } from 'sonner-native';
import { useDeviceStore } from '../store/deviceStore';
import { useAnimalStore } from '../store/animalStore';
import bluetoothHelper from '../utils/bluetoothHelper';

type NavigationProp = NativeStackNavigationProp<any>;

export interface UseDeviceManagementReturn {
  // State
  isScanning: boolean;
  isLoading: boolean;
  pairedDevices: any[];
  availableDevices: any[];
  bluetoothAvailable: boolean;
  barcodeInput: string;
  showBarcodeInput: boolean;
  
  // Enhanced error and loading states
  isLoadingBluetoothStatus: boolean;
  isLoadingDevices: boolean;
  bluetoothError: string | null;
  devicesError: string | null;
  
  // Actions
  handleStartScan: () => Promise<void>;
  handleStopScan: () => void;
  handleAddDevice: (deviceData: any) => Promise<any>;
  handlePairDevice: (deviceId: string) => Promise<void>;
  handleUnpairDevice: (deviceId: string) => Promise<void>;
  handleScanBarcode: () => void;
  handleAddDeviceByBarcode: (barcode: string) => Promise<void>;
  setBarcodeInput: (input: string) => void;
  setShowBarcodeInput: (show: boolean) => void;
  retryBluetoothCheck: () => Promise<void>;
  retryDevicesFetch: () => Promise<void>;
}

/**
 * @magic_description Hook for managing device operations
 * Handles scanning, pairing, unpairing, and barcode operations
 */
export const useDeviceManagement = (): UseDeviceManagementReturn => {
  const navigation = useNavigation<NavigationProp>();
  
  // Store hooks
  const {
    pairedDevices,
    availableDevices,
    isScanning,
    isLoading,
    startScan,
    stopScan,
    addDevice,
    pairDevice,
    unpairDevice,
    fetchDevices,
    error: storeDevicesError
  } = useDeviceStore();
  
  const { animals } = useAnimalStore();
  
  // Enhanced local state with defensive defaults
  const [bluetoothAvailable, setBluetoothAvailable] = useState(false); // Default to false for safety
  const [isLoadingBluetoothStatus, setIsLoadingBluetoothStatus] = useState(true);
  const [bluetoothError, setBluetoothError] = useState<string | null>(null);
  const [barcodeInput, setBarcodeInput] = useState('');
  const [showBarcodeInput, setShowBarcodeInput] = useState(false);
  
  // Enhanced Bluetooth availability check with robust error handling
  const checkBluetoothStatus = useCallback(async () => {
    setIsLoadingBluetoothStatus(true);
    setBluetoothError(null);
    
    try {
      // Verify bluetoothHelper has the required function
      if (!bluetoothHelper || typeof bluetoothHelper.isBluetoothAvailable !== 'function') {
        throw new Error('Bluetooth helper not properly initialized');
      }
      
      const isAvailable = await bluetoothHelper.isBluetoothAvailable();
      
      // Defensive check for return value
      const safeIsAvailable = Boolean(isAvailable);
      
      setBluetoothAvailable(safeIsAvailable);
      setBluetoothError(null);
      
      console.log(`Bluetooth availability check: ${safeIsAvailable}`);
      
    } catch (error: any) {
      console.error('Error checking Bluetooth status:', error);
      const errorMessage = error?.message || 'Failed to check Bluetooth availability';
      setBluetoothError(errorMessage);
      setBluetoothAvailable(false);
    } finally {
      setIsLoadingBluetoothStatus(false);
    }
  }, []);
  
  // Check Bluetooth status on mount
  useEffect(() => {
    checkBluetoothStatus();
  }, [checkBluetoothStatus]);
  
  // Fetch devices on mount with error handling
  useEffect(() => {
    const initializeDevices = async () => {
      try {
        await fetchDevices();
      } catch (error) {
        console.error('Error initializing devices:', error);
        // Error is handled by the store
      }
    };
    
    initializeDevices();
  }, [fetchDevices]);
  
  // Handle starting device scan
  const handleStartScan = useCallback(async () => {
    if (!bluetoothAvailable) {
      toast.error('Bluetooth is not available');
      return;
    }
    
    try {
      startScan();
      toast.success('Scanning for devices...');
      
      // Start the simulated scan
      await bluetoothHelper.startScan();
      
      // Stop scanning after completion
      stopScan();
    } catch (error) {
      console.error('Error starting scan:', error);
      toast.error('Failed to start device scan');
      stopScan();
    }
  }, [bluetoothAvailable, startScan, stopScan]);
  
  // Handle stopping device scan
  const handleStopScan = useCallback(() => {
    try {
      stopScan();
      bluetoothHelper.stopScan();
      toast.success('Scan stopped');
    } catch (error) {
      console.error('Error stopping scan:', error);
      toast.error('Failed to stop scan');
    }
  }, [stopScan]);
  
  // Handle adding a scanned device to the user's device list
  const handleAddDevice = useCallback(async (deviceData: any) => {
    try {
      const deviceToAdd = {
        name: deviceData.name,
        type: deviceData.type,
        battery_level: deviceData.battery_level || deviceData.batteryLevel,
        status: 'disconnected',
        // Include the original scan ID for removal from availableDevices
        id: deviceData.id,
      };
      
      const result = await addDevice(deviceToAdd);
      
      if (result) {
        // Success toast is handled by the store
        return result;
      } else {
        toast.error('Failed to add device');
        return null;
      }
    } catch (error) {
      console.error('Error adding device:', error);
      toast.error('Failed to add device');
      return null;
    }
  }, [addDevice]);
  
  // Handle pairing a device (updated for async)
  const handlePairDevice = useCallback(async (deviceId: string) => {
    try {
      if (!animals || animals.length === 0) {
        toast.error('No animals available. Please add an animal first.');
        return;
      }
      
      // For now, pair with the first animal
      // In a real app, this would show an animal selection dialog
      const animalId = animals[0].id;
      
      // Simulate Bluetooth pairing process
      const bluetoothSuccess = await bluetoothHelper.pairDevice(deviceId);
      
      if (!bluetoothSuccess) {
        toast.error('Bluetooth pairing failed');
        return;
      }
      
      // Update device status in Supabase
      await pairDevice(deviceId, animalId);
      // Success toast is handled by the store
      
    } catch (error) {
      console.error('Error pairing device:', error);
      // Error toast is handled by the store
    }
  }, [animals, pairDevice]);
  
  // Handle unpairing a device (updated for async)
  const handleUnpairDevice = useCallback(async (deviceId: string) => {
    try {
      await unpairDevice(deviceId);
      // Success toast is handled by the store
    } catch (error) {
      console.error('Error unpairing device:', error);
      // Error toast is handled by the store
    }
  }, [unpairDevice]);
  
  // Handle barcode scanning navigation
  const handleScanBarcode = useCallback(() => {
    navigation.navigate('BarcodeScannerScreen', {
      onScan: handleAddDeviceByBarcode,
    });
  }, [navigation]);
  
  // Handle adding device by barcode
  const handleAddDeviceByBarcode = useCallback(async (barcode: string) => {
    if (!barcode || !barcode.trim()) {
      toast.error('Invalid barcode');
      return;
    }
    
    try {
      // Simulate finding device by barcode
      toast.success('Looking up device...');
      
      // Simulate device lookup delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Simulate device discovery based on barcode
      const deviceData = {
        name: `Device ${barcode.slice(-4)}`,
        type: 'Collar',
        battery_level: Math.floor(Math.random() * 100),
        status: 'disconnected' as const,
      };
      
      // Add the device to Supabase
      const result = await handleAddDevice(deviceData);
      
      if (result) {
        toast.success(`Device "${result.name}" added from barcode!`);
      }
      
    } catch (error) {
      console.error('Error adding device by barcode:', error);
      toast.error('Failed to add device from barcode');
    }
  }, [handleAddDevice]);
  
  // Retry functions for error recovery
  const retryBluetoothCheck = useCallback(async () => {
    await checkBluetoothStatus();
  }, [checkBluetoothStatus]);
  
  const retryDevicesFetch = useCallback(async () => {
    try {
      await fetchDevices();
    } catch (error) {
      console.error('Error retrying devices fetch:', error);
    }
  }, [fetchDevices]);
  
  // Defensive data validation with proper type checking
  const safePairedDevices = Array.isArray(pairedDevices) ? 
    pairedDevices.filter(device => device && typeof device === 'object' && device.id) : [];
  const safeAvailableDevices = Array.isArray(availableDevices) ? 
    availableDevices.filter(device => device && typeof device === 'object' && device.id) : [];
  
  return {
    // State with defensive defaults - avoid over-conversion
    isScanning: isScanning || false,
    isLoading: isLoading || false,
    pairedDevices: safePairedDevices,
    availableDevices: safeAvailableDevices,
    bluetoothAvailable: bluetoothAvailable || false,
    barcodeInput: barcodeInput || '',
    showBarcodeInput: showBarcodeInput || false,
    
    // Enhanced error and loading states
    isLoadingBluetoothStatus: isLoadingBluetoothStatus || false,
    isLoadingDevices: isLoading || false,
    bluetoothError: bluetoothError || null,
    devicesError: storeDevicesError || null,
    
    // Actions
    handleStartScan,
    handleStopScan,
    handleAddDevice,
    handlePairDevice,
    handleUnpairDevice,
    handleScanBarcode,
    handleAddDeviceByBarcode,
    setBarcodeInput,
    setShowBarcodeInput,
    retryBluetoothCheck,
    retryDevicesFetch,
  };
};

export default useDeviceManagement;