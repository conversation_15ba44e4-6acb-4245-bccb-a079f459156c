import { useState, useEffect } from 'react';
import { useVitalsStore } from '../store/vitalsStore';
import { toast } from 'sonner-native';

export type ConnectionType = 'bluetooth' | 'wifi' | null;

export interface LiveVitalsState {
  heartRate: number;
  temperature: number;
  respirationRate: number;
  elapsed: number;
  isConnected: boolean;
  connectionType: ConnectionType;
  isConnecting: boolean;
}

export interface LiveVitalsActions {
  connectDevice: (type: 'bluetooth' | 'wifi') => void;
  formatTime: (seconds: number) => string;
  isAbnormal: (type: 'temperature' | 'heartRate' | 'respirationRate', value: number) => boolean;
}

export interface UseLiveVitalsProps {
  animalId: string;
}

export interface UseLiveVitalsReturn extends LiveVitalsState, LiveVitalsActions {}

export const useLiveVitals = ({ animalId }: UseLiveVitalsProps): UseLiveVitalsReturn => {
  const { addLiveVitalRecord } = useVitalsStore();
  
  // State
  const [heartRate, setHeartRate] = useState<number>(42);
  const [temperature, setTemperature] = useState<number>(37.8);
  const [respirationRate, setRespirationRate] = useState<number>(12);
  const [elapsed, setElapsed] = useState<number>(0);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [connectionType, setConnectionType] = useState<ConnectionType>(null);
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  
  // Simulate connecting to device
  const connectDevice = (type: 'bluetooth' | 'wifi') => {
    setIsConnecting(true);
    setConnectionType(type);
    
    // Simulate connection delay
    setTimeout(() => {
      setIsConnected(true);
      setIsConnecting(false);
      toast.success(`Connected via ${type === 'bluetooth' ? 'Bluetooth' : 'WiFi'}`);
    }, 2000);
  };
  
  // Simulate changing vitals
  useEffect(() => {
    if (!isConnected) return;
    
    const interval = setInterval(() => {
      // Simulate slight variations in vitals
      setHeartRate(prev => {
        const variation = Math.random() * 2 - 1; // -1 to +1
        return Math.max(30, Math.min(60, prev + variation));
      });
      
      setTemperature(prev => {
        const variation = (Math.random() * 0.2 - 0.1) / 10; // Small temperature variations
        return Math.max(37.2, Math.min(38.5, prev + variation));
      });
      
      setRespirationRate(prev => {
        const variation = Math.random() > 0.7 ? (Math.random() > 0.5 ? 1 : -1) : 0; // Occasional changes
        return Math.max(8, Math.min(20, prev + variation));
      });
      
      setElapsed(prev => prev + 1);
      
      // Record data every 15 seconds
      if (elapsed % 15 === 0 && elapsed > 0) {
        addLiveVitalRecord(animalId, {
          temperature,
          heartRate: Math.round(heartRate),
          respirationRate: Math.round(respirationRate),
        });
      }
    }, 1000);
    
    return () => clearInterval(interval);
  }, [animalId, elapsed, heartRate, temperature, respirationRate, addLiveVitalRecord, isConnected]);
  
  // Helper functions
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  const isAbnormal = (type: 'temperature' | 'heartRate' | 'respirationRate', value: number) => {
    switch (type) {
      case 'temperature':
        return value < 37.2 || value > 38.5;
      case 'heartRate':
        return value < 30 || value > 50;
      case 'respirationRate':
        return value < 8 || value > 20;
      default:
        return false;
    }
  };
  
  return {
    // State
    heartRate,
    temperature,
    respirationRate,
    elapsed,
    isConnected,
    connectionType,
    isConnecting,
    
    // Actions
    connectDevice,
    formatTime,
    isAbnormal
  };
};