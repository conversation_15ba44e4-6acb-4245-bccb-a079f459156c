import { useState, useCallback, useMemo } from 'react';
import { ValidationResult, validateFormFields } from '../utils/validation';

// Generic form validation hook for consistent validation patterns
export interface FormValidationState {
  errors: Record<string, string>;
  isValid: boolean;
  isSubmitting: boolean;
  hasBeenSubmitted: boolean;
}

export interface FormValidationActions {
  validateField: (fieldName: string, validation: ValidationResult) => void;
  validateAllFields: (validations: Record<string, ValidationResult>) => boolean;
  clearFieldError: (fieldName: string) => void;
  clearAllErrors: () => void;
  setSubmitting: (isSubmitting: boolean) => void;
  resetForm: () => void;
}

export const useFormValidation = (): FormValidationState & FormValidationActions => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasBeenSubmitted, setHasBeenSubmitted] = useState(false);

  // Calculate if form is valid based on current errors
  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0;
  }, [errors]);

  // Validate a single field and update errors state
  const validateField = useCallback((fieldName: string, validation: ValidationResult) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      
      if (!validation.isValid && validation.error) {
        newErrors[fieldName] = validation.error;
      } else {
        delete newErrors[fieldName];
      }
      
      return newErrors;
    });
  }, []);

  // Validate all fields at once (typically used on form submission)
  const validateAllFields = useCallback((validations: Record<string, ValidationResult>): boolean => {
    setHasBeenSubmitted(true);
    
    const result = validateFormFields(validations);
    setErrors(result.errors);
    
    return result.isValid;
  }, []);

  // Clear error for a specific field
  const clearFieldError = useCallback((fieldName: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
  }, []);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Set submitting state
  const setSubmitting = useCallback((submitting: boolean) => {
    setIsSubmitting(submitting);
  }, []);

  // Reset entire form state
  const resetForm = useCallback(() => {
    setErrors({});
    setIsSubmitting(false);
    setHasBeenSubmitted(false);
  }, []);

  return {
    // State
    errors,
    isValid,
    isSubmitting,
    hasBeenSubmitted,
    
    // Actions
    validateField,
    validateAllFields,
    clearFieldError,
    clearAllErrors,
    setSubmitting,
    resetForm
  };
};

// Specialized hook for real-time field validation
export const useFieldValidation = (fieldName: string, validation: () => ValidationResult) => {
  const [error, setError] = useState<string>('');
  const [hasBeenTouched, setHasBeenTouched] = useState(false);

  const validateField = useCallback(() => {
    const result = validation();
    setError(result.isValid ? '' : (result.error || ''));
    return result.isValid;
  }, [validation]);

  const onBlur = useCallback(() => {
    setHasBeenTouched(true);
    validateField();
  }, [validateField]);

  const onChange = useCallback(() => {
    // Only show errors after field has been touched
    if (hasBeenTouched) {
      validateField();
    }
  }, [hasBeenTouched, validateField]);

  const clearError = useCallback(() => {
    setError('');
  }, []);

  return {
    error,
    hasError: !!error,
    hasBeenTouched,
    validateField,
    onBlur,
    onChange,
    clearError
  };
};