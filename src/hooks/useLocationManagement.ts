import { useState, useEffect, useCallback } from 'react';
import { Platform, Linking } from 'react-native';
import { useAnimalStore } from '../store/animalStore';
import { toast } from 'sonner-native';
import { Animal } from '../mocks/animals';

interface LocationData {
  latitude: number;
  longitude: number;
  timestamp: number;
}

interface UseLocationManagementReturn {
  // Data
  animal: Animal | undefined;
  isLoading: boolean;
  isUpdating: boolean;
  connectedDevice: string | null;
  lastUpdated: string;
  
  // Actions
  handleUpdateLocation: () => Promise<void>;
  handleOpenInMaps: () => void;
  handleConnectDevice: () => void;
}

/**
 * @magic_description Hook for managing animal location functionality
 * Handles location updates, device connections, and external map integration
 */
export const useLocationManagement = (animalId: string): UseLocationManagementReturn => {
  // Store hooks
  const { getAnimalById, updateAnimal } = useAnimalStore();
  
  // Get animal data
  const animal = getAnimalById(animalId);
  
  // Local state
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [connectedDevice, setConnectedDevice] = useState<string | null>(animal?.deviceName || null);
  const [lastUpdated, setLastUpdated] = useState<string>(
    animal?.location?.timestamp 
      ? new Date(animal.location.timestamp).toLocaleString() 
      : 'Never'
  );
  
  // Update last updated time when animal location changes
  useEffect(() => {
    if (animal?.location) {
      setLastUpdated(new Date(animal.location.timestamp).toLocaleString());
    }
  }, [animal?.location]);
  
  // Update connected device when animal device changes
  useEffect(() => {
    if (animal?.deviceName) {
      setConnectedDevice(animal.deviceName);
    }
  }, [animal?.deviceName]);
  
  // Handle location update
  const handleUpdateLocation = useCallback(async () => {
    if (!animal) {
      toast.error('Animal not found');
      return;
    }
    
    try {
      setIsUpdating(true);
      
      // In a real app, this would use the device's GPS or get data from a connected device
      // For now, we'll simulate it with random coordinates near the current location
      const currentLat = animal.location?.latitude || 40.7128; // Default to NYC if no location
      const currentLng = animal.location?.longitude || -74.0060;
      
      // Generate new coordinates within a small radius (about 500m)
      const newLocation: LocationData = {
        latitude: currentLat + (Math.random() * 0.005 - 0.0025),
        longitude: currentLng + (Math.random() * 0.005 - 0.0025),
        timestamp: Date.now()
      };
      
      // Update animal with new location data
      await updateAnimal(animalId, { 
        location: newLocation,
        locationLatitude: newLocation.latitude,
        locationLongitude: newLocation.longitude,
        locationTimestamp: new Date(newLocation.timestamp).toISOString()
      });
      
      setLastUpdated(new Date().toLocaleString());
      
      toast.success(`${animal.name}'s location has been updated`);
      
    } catch (error) {
      console.error('Error updating location:', error);
      toast.error('Failed to update location');
    } finally {
      setIsUpdating(false);
    }
  }, [animal, animalId, updateAnimal]);
  
  // Handle opening location in external maps
  const handleOpenInMaps = useCallback(() => {
    if (!animal?.location) {
      toast.error('No location data available');
      return;
    }
    
    const { latitude, longitude } = animal.location;
    const label = encodeURIComponent(animal.name);
    
    let url: string;
    if (Platform.OS === 'ios') {
      url = `maps:0,0?q=${label}@${latitude},${longitude}`;
    } else if (Platform.OS === 'android') {
      url = `geo:0,0?q=${latitude},${longitude}(${label})`;
    } else {
      // For web or other platforms
      url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    }
    
    Linking.openURL(url).catch(() => {
      // Fallback to Google Maps in browser
      const fallbackUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
      Linking.openURL(fallbackUrl).catch(() => {
        toast.error('Unable to open maps application');
      });
    });
  }, [animal]);
  
  // Handle device connection simulation
  const handleConnectDevice = useCallback(() => {
    if (!animal) {
      toast.error('Animal not found');
      return;
    }
    
    setIsLoading(true);
    
    // Simulate connecting to a device
    setTimeout(() => {
      const deviceNames = ['HoofTracker Pro', 'EquiGPS 3000', 'LivestockLocator', 'AnimalTrack Elite'];
      const randomDevice = deviceNames[Math.floor(Math.random() * deviceNames.length)];
      
      setConnectedDevice(randomDevice);
      
      // Update animal with connected device name
      updateAnimal(animalId, { deviceName: randomDevice }).catch((error) => {
        console.error('Error updating device name:', error);
      });
      
      toast.success(`Connected to ${randomDevice}`);
      setIsLoading(false);
    }, 2000);
  }, [animal, animalId, updateAnimal]);
  
  return {
    // Data
    animal,
    isLoading,
    isUpdating,
    connectedDevice,
    lastUpdated,
    
    // Actions
    handleUpdateLocation,
    handleOpenInMaps,
    handleConnectDevice,
  };
};

export default useLocationManagement;