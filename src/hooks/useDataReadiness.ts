import { useState, useEffect, useCallback } from 'react';
import { Heart, Shield, MapPin, Gauge, Database } from 'lucide-react-native';
import { useDeviceReadinessCheck } from './useDeviceReadinessCheck';
import { useDatabaseReadinessCheck } from './useDatabaseReadinessCheck';
import { useAnimalDataChecks } from './useAnimalDataChecks';

export interface DataReadinessCheckItem {
  id: string;
  icon: any; // LucideIcon component
  title: string;
  message: string;
  isReady: boolean;
}

export interface UseDataReadinessReturn {
  isLoading: boolean;
  isRefreshing: boolean;
  allReady: boolean;
  checkItems: DataReadinessCheckItem[];
  handleRefresh: () => Promise<void>;
}

/**
 * @magic_description Hook for managing data readiness checks and state
 * Consolidates device, database, and animal-specific data checks
 */
export const useDataReadiness = (
  animalId: string,
  onRefresh?: () => void
): UseDataReadinessReturn => {
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Individual readiness checks
  const deviceReadiness = useDeviceReadinessCheck(animalId);
  const databaseReadiness = useDatabaseReadinessCheck();
  const animalDataChecks = useAnimalDataChecks(animalId);

  // Initialize checks on mount
  const initializeChecks = useCallback(async () => {
    setIsLoading(true);
    try {
      await databaseReadiness.checkDb();
    } catch (error) {
      console.error('Error during data readiness initialization:', error);
    } finally {
      setIsLoading(false);
    }
  }, [databaseReadiness.checkDb]);

  // Handle manual refresh
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await databaseReadiness.checkDb();
      onRefresh?.();
    } catch (error) {
      console.error('Error during data readiness refresh:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [databaseReadiness.checkDb, onRefresh]);

  // Initialize checks on mount or when dependencies change
  useEffect(() => {
    initializeChecks();
  }, [animalId, initializeChecks]);

  // Calculate overall readiness status
  const allReady = 
    deviceReadiness.isReady &&
    databaseReadiness.isReady &&
    animalDataChecks.vaccineReady &&
    animalDataChecks.gpsReady &&
    animalDataChecks.speedReady;

  // Construct check items array for rendering
  const checkItems: DataReadinessCheckItem[] = [
    {
      id: 'device',
      icon: Heart,
      title: 'Vitals Device',
      message: deviceReadiness.message,
      isReady: deviceReadiness.isReady,
    },
    {
      id: 'database',
      icon: Database,
      title: 'Vitals Database',
      message: databaseReadiness.message,
      isReady: databaseReadiness.isReady,
    },
    {
      id: 'vaccination',
      icon: Shield,
      title: 'Vaccination Data',
      message: animalDataChecks.vaccineMessage,
      isReady: animalDataChecks.vaccineReady,
    },
    {
      id: 'gps',
      icon: MapPin,
      title: 'GPS Tracking',
      message: animalDataChecks.gpsMessage,
      isReady: animalDataChecks.gpsReady,
    },
    {
      id: 'speed',
      icon: Gauge,
      title: 'Speed Tracking',
      message: animalDataChecks.speedMessage,
      isReady: animalDataChecks.speedReady,
    },
  ];

  return {
    isLoading,
    isRefreshing,
    allReady,
    checkItems,
    handleRefresh,
  };
};

export default useDataReadiness;