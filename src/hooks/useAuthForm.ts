import { useEffect } from 'react';
import { useAuthCredentials } from './useAuthCredentials';
import { useAuthMode } from './useAuthMode';
import { useAuthError } from './useAuthError';
import { useAuthActions } from './useAuthActions';
import { useLoginSecurity } from './useLoginSecurity';
import { useLoginValidation } from './useLoginValidation';

export interface AuthFormState {
  // Form state
  email: string;
  password: string;
  confirmPassword: string;
  showPassword: boolean;
  
  // UI state
  isLoading: boolean;
  isGoogleLoading: boolean;
  isGoogleModuleLoading: boolean;
  googleModuleReady: boolean;
  googleModuleLoadFailed: boolean;
  isSignUp: boolean;
  isResetPassword: boolean;
  needsEmailConfirmation: boolean;
  
  // Security state (from useLoginSecurity)
  loginAttempts: number;
  isAccountLocked: boolean;
  lockUntil: Date | null;
  
  // Validation state (from useLoginValidation)
  errorMessage: string;
}

export interface AuthFormActions {
  // Form setters
  setEmail: (email: string) => void;
  setPassword: (password: string) => void;
  setConfirmPassword: (confirmPassword: string) => void;
  setShowPassword: (show: boolean) => void;
  
  // UI setters
  setIsSignUp: (isSignUp: boolean) => void;
  setIsResetPassword: (isResetPassword: boolean) => void;
  setNeedsEmailConfirmation: (needs: boolean) => void;
  
  // Error handling
  setErrorMessage: (message: string) => void;
  clearError: () => void;
  
  // Authentication handlers
  handleSignIn: () => Promise<void>;
  handleSignUp: () => Promise<void>;
  handleForgotPassword: () => Promise<void>;
  handleResendConfirmation: () => Promise<void>;
  handleGoogleSignIn: () => Promise<void>;
  
  // Utility functions
  resetForm: () => void;
  switchToSignUp: () => void;
  switchToSignIn: () => void;
  switchToResetPassword: () => void;
  backToSignIn: () => void;
}

export interface UseAuthFormReturn extends AuthFormState, AuthFormActions {}

/**
 * @magic_description Orchestrator hook for authentication form management
 * Coordinates multiple specialized hooks for a complete auth solution
 */
export const useAuthForm = (onShakeAnimation?: () => void): UseAuthFormReturn => {
  // Initialize specialized hooks
  const credentials = useAuthCredentials();
  const errorManager = useAuthError();
  const mode = useAuthMode(credentials.resetCredentials, errorManager.clearError);
  
  const security = useLoginSecurity();
  const validation = useLoginValidation();
  
  const actions = useAuthActions({
    credentials,
    mode,
    errorManager,
    security: {
      recordFailedAttempt: security.recordFailedAttempt,
      resetLoginAttempts: security.resetLoginAttempts,
      isAccountLocked: security.isAccountLocked
    },
    validation: {
      validateForm: validation.validateForm
    },
    modeActions: {
      setNeedsEmailConfirmation: mode.setNeedsEmailConfirmation,
      setIsSignUp: mode.setIsSignUp,
      setIsResetPassword: mode.setIsResetPassword
    },
    onShakeAnimation
  });
  
  // Check login attempts status when email changes
  useEffect(() => {
    if (credentials.email) {
      security.checkLoginStatus(credentials.email);
    }
  }, [credentials.email, security.checkLoginStatus]);
  
  // Utility functions that combine multiple hook actions
  const resetForm = () => {
    credentials.resetCredentials();
    errorManager.clearError();
    mode.setNeedsEmailConfirmation(false);
  };
  
  return {
    // State from credentials hook
    email: credentials.email,
    password: credentials.password,
    confirmPassword: credentials.confirmPassword,
    showPassword: credentials.showPassword,
    
    // State from mode hook
    isSignUp: mode.isSignUp,
    isResetPassword: mode.isResetPassword,
    needsEmailConfirmation: mode.needsEmailConfirmation,
    
    // State from actions hook
    isLoading: actions.isLoading,
    isGoogleLoading: actions.isGoogleLoading,
    isGoogleModuleLoading: actions.isGoogleModuleLoading,
    googleModuleReady: actions.googleModuleReady,
    googleModuleLoadFailed: actions.googleModuleLoadFailed,
    
    // State from security hook
    loginAttempts: security.loginAttempts,
    isAccountLocked: security.isAccountLocked,
    lockUntil: security.lockUntil,
    
    // State from error hook
    errorMessage: errorManager.errorMessage,
    
    // Setters from credentials hook
    setEmail: credentials.setEmail,
    setPassword: credentials.setPassword,
    setConfirmPassword: credentials.setConfirmPassword,
    setShowPassword: credentials.setShowPassword,
    
    // Setters from mode hook
    setIsSignUp: mode.setIsSignUp,
    setIsResetPassword: mode.setIsResetPassword,
    setNeedsEmailConfirmation: mode.setNeedsEmailConfirmation,
    
    // Error management
    setErrorMessage: errorManager.setErrorMessage,
    clearError: errorManager.clearError,
    
    // Authentication handlers from actions hook
    handleSignIn: actions.handleSignIn,
    handleSignUp: actions.handleSignUp,
    handleForgotPassword: actions.handleForgotPassword,
    handleResendConfirmation: actions.handleResendConfirmation,
    handleGoogleSignIn: actions.handleGoogleSignIn,
    
    // Mode switching utilities
    switchToSignUp: mode.switchToSignUp,
    switchToSignIn: mode.switchToSignIn,
    switchToResetPassword: mode.switchToResetPassword,
    backToSignIn: mode.backToSignIn,
    
    // Combined utility
    resetForm
  };
};