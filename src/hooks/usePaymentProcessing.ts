import { useState } from 'react';
import { Alert } from 'react-native';
import { toast } from 'sonner-native';
import { useNavigation } from '@react-navigation/native';
import { useUserStore } from '../store/userStore';
import { useLemonSqueezyCheckout } from './useLemonSqueezyCheckout';
import { useOrderManagement } from './useOrderManagement';

type PaymentMethod = 'card' | 'apple' | 'google' | 'paypal';
type PlanType = 'monthly' | 'yearly';

interface PaymentProcessingState {
  isProcessing: boolean;
}

interface PaymentProcessingActions {
  processCardPayment: (planType: PlanType) => Promise<void>;
  processApplePay: (planType: PlanType) => Promise<void>;
  processGooglePay: (planType: PlanType) => Promise<void>;
  processPayPal: (planType: PlanType) => Promise<void>;
}

/**
 * @magic_description Legacy payment processing hook - now uses new modular hooks
 * @deprecated Use useLemonSqueezyCheckout and useOrderManagement instead
 * Maintained for backward compatibility
 */
export const usePaymentProcessing = (onSuccess?: () => void): PaymentProcessingState & PaymentProcessingActions => {
  const [isProcessing, setIsProcessing] = useState(false);
  const { user } = useUserStore();
  const navigation = useNavigation();
  
  // Use new modular hooks
  const { createCheckoutSession, openCheckout } = useLemonSqueezyCheckout();
  const { createOrder, handlePaymentSuccess } = useOrderManagement(onSuccess);
  
  const getPlanPrice = (planType: PlanType) => {
    return planType === 'monthly' ? '$14.99' : '$143.90';
  };
  
  const processCardPayment = async (planType: PlanType) => {
    if (!user) {
      toast.error('User not found');
      return;
    }
    
    setIsProcessing(true);
    
    try {
      // Use new Lemon Squeezy checkout flow
      const productId = planType === 'monthly' ? 'monthly_plan_id' : 'yearly_plan_id';
      const url = await createCheckoutSession(planType, productId);
      
      if (!url) {
        throw new Error('Failed to create checkout session');
      }
      
      // Create order
      const orderId = await createOrder({
        user_id: user.id,
        product_id: productId,
        quantity: 1,
        total_amount: planType === 'monthly' ? 14.99 : 143.90,
        currency: 'USD',
        status: 'pending',
        checkout_url: url,
        plan_type: planType,
        payment_method: 'lemonsqueezy'
      });
      
      if (orderId) {
        await openCheckout(url);
        toast.info('Complete your payment in the browser.');
      }
    } catch (error: any) {
      console.error('Payment processing error:', error);
      toast.error(`Payment failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };
  
  const processApplePay = async (planType: PlanType) => {
    Alert.alert(
      "Apple Pay",
      "Apple Pay integration coming soon! Please use the web checkout option.",
      [{ text: "OK" }]
    );
  };
  
  const processGooglePay = async (planType: PlanType) => {
    Alert.alert(
      "Google Pay",
      "Google Pay integration coming soon! Please use the web checkout option.",
      [{ text: "OK" }]
    );
  };
  
  const processPayPal = async (planType: PlanType) => {
    // PayPal is handled through Lemon Squeezy checkout
    await processCardPayment(planType);
  };
  
  return {
    isProcessing,
    processCardPayment,
    processApplePay,
    processGooglePay,
    processPayPal
  };
};