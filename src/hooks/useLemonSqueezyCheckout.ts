import { useState } from 'react';
import { Alert } from 'react-native';
import { toast } from 'sonner-native';
import { useUserStore } from '../store/userStore';
import { supabase } from '../supabase/client';
import * as WebBrowser from 'expo-web-browser';

type PlanType = 'monthly' | 'yearly';

interface LemonSqueezyCheckoutState {
  isCreatingCheckout: boolean;
  checkoutUrl: string | null;
}

interface LemonSqueezyCheckoutActions {
  createCheckoutSession: (planType: PlanType, productId: string) => Promise<string | null>;
  openCheckout: (checkoutUrl: string) => Promise<void>;
  clearCheckout: () => void;
}

type LemonSqueezyCheckoutHook = LemonSqueezyCheckoutState & LemonSqueezyCheckoutActions;

/**
 * Custom hook for handling Lemon Squeezy checkout creation and management
 * Manages secure checkout URL generation and browser-based payment flow
 */
export const useLemonSqueezyCheckout = (): LemonSqueezyCheckoutHook => {
  const [isCreatingCheckout, setIsCreatingCheckout] = useState(false);
  const [checkoutUrl, setCheckoutUrl] = useState<string | null>(null);
  const { user } = useUserStore();
  
  const createCheckoutSession = async (planType: PlanType, productId: string): Promise<string | null> => {
    if (!user) {
      toast.error('User not found. Please log in.');
      return null;
    }
    
    setIsCreatingCheckout(true);
    
    try {
      // Get current session for authentication
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      // Call Supabase Edge Function to create Lemon Squeezy checkout
      const response = await fetch(
        `${supabase.supabaseUrl}/functions/v1/create-lemonsqueezy-checkout`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ 
            planType,
            productId,
            userId: user.id,
            userEmail: user.email,
            userName: user.name || user.email?.split('@')[0] || 'User'
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const result = await response.json();
      const url = result.checkoutUrl;
      
      if (!url) {
        throw new Error('No checkout URL received');
      }
      
      setCheckoutUrl(url);
      console.log('Checkout session created successfully:', url);
      
      return url;
    } catch (error: any) {
      console.error('Checkout creation error:', error);
      toast.error(`Failed to create checkout: ${error.message}`);
      return null;
    } finally {
      setIsCreatingCheckout(false);
    }
  };
  
  const openCheckout = async (url: string): Promise<void> => {
    try {
      // Open checkout URL in browser
      const result = await WebBrowser.openBrowserAsync(url, {
        presentationStyle: WebBrowser.WebBrowserPresentationStyle.FORM_SHEET,
        controlsColor: '#3B82F6',
      });
      
      console.log('Browser result:', result);
      
      // Note: We'll rely on the webhook to update order status
      // The user will return to the app after payment completion
      if (result.type === 'dismiss') {
        toast.info('Payment window closed. Check your email for confirmation.');
      }
    } catch (error: any) {
      console.error('Error opening checkout:', error);
      toast.error('Failed to open payment page');
    }
  };
  
  const clearCheckout = () => {
    setCheckoutUrl(null);
  };
  
  return {
    isCreatingCheckout,
    checkoutUrl,
    createCheckoutSession,
    openCheckout,
    clearCheckout
  };
};