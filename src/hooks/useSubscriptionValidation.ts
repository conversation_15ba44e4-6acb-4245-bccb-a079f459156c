import { toast } from 'sonner-native';
import { CardData } from '../components/subscription/PaymentCardForm';

type PaymentMethod = 'card' | 'apple' | 'google' | 'paypal';

interface SubscriptionValidationActions {
  validatePayment: (paymentMethod: PaymentMethod, cardData?: CardData) => boolean;
}

/**
 * @magic_description Custom hook for subscription and payment validation
 * Handles validation of payment forms and subscription data
 */
export const useSubscriptionValidation = (): SubscriptionValidationActions => {
  
  const validateCardData = (cardData: CardData): boolean => {
    const cleanedCardNumber = cardData.cardNumber.replace(/\s/g, '');
    
    if (!cleanedCardNumber || cleanedCardNumber.length < 16) {
      toast.error('Please enter a valid card number');
      return false;
    }
    
    if (!cardData.expiryDate.trim() || !cardData.expiryDate.includes('/')) {
      toast.error('Please enter a valid expiry date (MM/YY)');
      return false;
    }
    
    if (!cardData.cvv.trim() || cardData.cvv.length < 3) {
      toast.error('Please enter a valid CVV');
      return false;
    }
    
    if (!cardData.cardholderName.trim()) {
      toast.error('Please enter the cardholder name');
      return false;
    }
    
    return true;
  };
  
  const validatePayment = (paymentMethod: PaymentMethod, cardData?: CardData): boolean => {
    if (paymentMethod === 'card') {
      if (!cardData) {
        toast.error('Card data is required');
        return false;
      }
      return validateCardData(cardData);
    }
    
    // For other payment methods (Apple Pay, Google Pay, PayPal), no additional validation needed
    // as they handle their own validation flows
    return true;
  };
  
  return {
    validatePayment
  };
};