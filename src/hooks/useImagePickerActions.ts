import { useCallback } from 'react';
import { Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { toast } from 'sonner-native';

export interface UseImagePickerActionsProps {
  onImageChange: (uri: string) => void;
  onCloseOptions: () => void;
}

export interface UseImagePickerActionsReturn {
  pickFromGalleryHandler: () => Promise<void>;
  handleImageFromUrlHandler: () => void;
  takePhotoHandler: () => Promise<void>;
}

/**
 * @magic_description Hook for handling image picker actions
 * Manages gallery selection, URL input, and camera capture with proper permissions
 */
export const useImagePickerActions = ({
  onImageChange,
  onCloseOptions,
}: UseImagePickerActionsProps): UseImagePickerActionsReturn => {
  
  // Handle picking image from gallery
  const pickFromGalleryHandler = useCallback(async () => {
    try {
      // Request media library permissions
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (!permissionResult.granted) {
        toast.error('Permission to access media library is required!');
        onCloseOptions();
        return;
      }

      // Launch image library
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        onImageChange(result.assets[0].uri);
        toast.success('Image selected successfully!');
      }
      
      onCloseOptions();
    } catch (error) {
      console.error('Error picking image from gallery:', error);
      toast.error('Failed to pick image from gallery');
      onCloseOptions();
    }
  }, [onImageChange, onCloseOptions]);

  // Handle taking photo with camera
  const takePhotoHandler = useCallback(async () => {
    try {
      // Request camera permissions
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      
      if (!permissionResult.granted) {
        toast.error('Permission to access camera is required!');
        onCloseOptions();
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        onImageChange(result.assets[0].uri);
        toast.success('Photo captured successfully!');
      }
      
      onCloseOptions();
    } catch (error) {
      console.error('Error taking photo:', error);
      toast.error('Failed to take photo');
      onCloseOptions();
    }
  }, [onImageChange, onCloseOptions]);

  // Handle entering image URL
  const handleImageFromUrlHandler = useCallback(() => {
    Alert.prompt(
      'Enter Image URL',
      'Please enter a valid image URL:',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => onCloseOptions(),
        },
        {
          text: 'OK',
          onPress: (url) => {
            if (url && url.trim()) {
              // Basic URL validation
              const urlPattern = /^(https?:\/\/)([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
              const imageExtensions = /\.(jpg|jpeg|png|gif|bmp|webp)$/i;
              
              if (urlPattern.test(url.trim()) || imageExtensions.test(url.trim())) {
                onImageChange(url.trim());
                toast.success('Image URL set successfully!');
              } else {
                toast.error('Please enter a valid image URL');
              }
            } else {
              toast.error('Please enter a valid URL');
            }
            onCloseOptions();
          },
        },
      ],
      'plain-text',
      '',
      'url'
    );
  }, [onImageChange, onCloseOptions]);

  return {
    pickFromGalleryHandler,
    handleImageFromUrlHandler,
    takePhotoHandler,
  };
};

export default useImagePickerActions;