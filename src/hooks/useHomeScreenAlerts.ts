import { useMemo } from 'react';
import { Animal } from '../mocks/animals';
import { VitalRecord } from '../mocks/vitals';
import { addDays, isAfter, isBefore, format } from 'date-fns';

interface Alert {
  id: string;
  type: 'health' | 'location' | 'vaccine';
  severity: 'low' | 'medium' | 'high';
  title: string;
  message: string;
  animalId: string;
  timestamp: string;
}

/**
 * @magic_description Custom hook for HomeScreen alerts generation
 * Handles health alerts, location alerts, and vaccine alerts
 */
export const useHomeScreenAlerts = (animals: Animal[], abnormalVitals: VitalRecord[]) => {
  const alerts = useMemo(() => {
    const healthAndLocationAlerts: Alert[] = [];
    const vaccineAlerts: Alert[] = [];

    // Generate health alerts from abnormal vitals
    abnormalVitals.forEach(vital => {
      const animal = animals.find(a => a.id === vital.animalId);
      if (!animal) return;

      let alertMessage = '';
      let severity: 'low' | 'medium' | 'high' = 'medium';

      if (vital.temperature > 39.5 || vital.temperature < 37.5) {
        alertMessage += `Temperature: ${vital.temperature}°C (Normal: 37.5-39.5°C). `;
        severity = vital.temperature > 40 || vital.temperature < 37 ? 'high' : 'medium';
      }
      
      if (vital.heartRate > 100 || vital.heartRate < 40) {
        alertMessage += `Heart Rate: ${vital.heartRate} bpm (Normal: 40-100 bpm). `;
        if (vital.heartRate > 120 || vital.heartRate < 30) severity = 'high';
      }
      
      if (vital.respiration > 30 || vital.respiration < 10) {
        alertMessage += `Respiration: ${vital.respiration} breaths/min (Normal: 10-30). `;
        if (vital.respiration > 40 || vital.respiration < 8) severity = 'high';
      }

      if (alertMessage) {
        healthAndLocationAlerts.push({
          id: `health-${vital.id}`,
          type: 'health',
          severity,
          title: `Health Alert: ${animal.name}`,
          message: alertMessage.trim(),
          animalId: animal.id,
          timestamp: vital.timestamp
        });
      }
    });

    // Generate location alerts for animals with outdated location data
    const now = new Date();
    const locationThreshold = addDays(now, -1); // 24 hours ago

    animals.forEach(animal => {
      if (animal.location && animal.location.lastUpdated) {
        const lastUpdate = new Date(animal.location.lastUpdated);
        if (isBefore(lastUpdate, locationThreshold)) {
          const hoursAgo = Math.floor((now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60));
          
          healthAndLocationAlerts.push({
            id: `location-${animal.id}`,
            type: 'location',
            severity: hoursAgo > 48 ? 'high' : 'medium',
            title: `Location Alert: ${animal.name}`,
            message: `Location data is ${hoursAgo} hours old. Last known location: ${animal.location.address || 'Unknown'}.`,
            animalId: animal.id,
            timestamp: animal.location.lastUpdated
          });
        }
      }
    });

    // Generate vaccine alerts (mock data for demonstration)
    // TODO: Replace with real vaccination data when available
    animals.forEach(animal => {
      // Mock vaccine data - in real app, this would come from a vaccination store
      const mockVaccines = [
        {
          id: `vaccine-1-${animal.id}`,
          name: 'Annual Vaccination',
          dueDate: addDays(now, Math.floor(Math.random() * 120) - 30), // Random date within ±30-90 days
          type: 'routine'
        },
        {
          id: `vaccine-2-${animal.id}`,
          name: 'Deworming',
          dueDate: addDays(now, Math.floor(Math.random() * 90) - 15), // Random date within ±15-75 days
          type: 'preventive'
        }
      ];

      mockVaccines.forEach(vaccine => {
        const daysUntilDue = Math.ceil((vaccine.dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        
        // Show alerts for vaccines due within 60 days
        if (daysUntilDue <= 60 && daysUntilDue >= -7) { // Include 7 days overdue
          let severity: 'low' | 'medium' | 'high' = 'low';
          let message = '';
          
          if (daysUntilDue < 0) {
            severity = 'high';
            message = `${vaccine.name} is ${Math.abs(daysUntilDue)} days overdue.`;
          } else if (daysUntilDue <= 7) {
            severity = 'medium';
            message = `${vaccine.name} is due in ${daysUntilDue} days.`;
          } else {
            severity = 'low';
            message = `${vaccine.name} is due on ${format(vaccine.dueDate, 'MMM dd, yyyy')}.`;
          }

          vaccineAlerts.push({
            id: vaccine.id,
            type: 'vaccine',
            severity,
            title: `Vaccine Alert: ${animal.name}`,
            message,
            animalId: animal.id,
            timestamp: new Date().toISOString()
          });
        }
      });
    });

    return {
      healthAndLocationAlerts: healthAndLocationAlerts.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      ),
      vaccineAlerts: vaccineAlerts.sort((a, b) => {
        // Sort by severity (high first) then by due date
        const severityOrder = { high: 3, medium: 2, low: 1 };
        const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
        if (severityDiff !== 0) return severityDiff;
        return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
      })
    };
  }, [animals, abnormalVitals]);

  return alerts;
};

export default useHomeScreenAlerts;