import { useState } from 'react';
import { Platform, Alert } from 'react-native';
import { toast } from 'sonner-native';
import { useUserStore } from '../store/userStore';
import { useOrderManagement } from './useOrderManagement';
import { supabase } from '../supabase/client';

export interface GooglePayPaymentRequest {
  planType: 'monthly' | 'yearly';
  productId: string;
  amount: number;
  currency: string;
}

export const useGooglePayPayment = (onSuccess?: () => void) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAvailable, setIsAvailable] = useState(false);
  const { user } = useUserStore();
  const { createOrder } = useOrderManagement(onSuccess);

  // Check if Google Pay is available
  const checkGooglePayAvailability = async (): Promise<boolean> => {
    if (Platform.OS !== 'android') {
      return false;
    }

    try {
      // In a real implementation, you would use:
      // import { GooglePay } from '@stripe/stripe-react-native';
      // const isAvailable = await GooglePay.isGooglePaySupported();
      
      // For now, simulate availability check
      const available = Platform.OS === 'android' && Platform.Version >= 21;
      setIsAvailable(available);
      return available;
    } catch (error) {
      console.error('Error checking Google Pay availability:', error);
      setIsAvailable(false);
      return false;
    }
  };

  const processGooglePayPayment = async (paymentRequest: GooglePayPaymentRequest) => {
    if (!user) {
      toast.error('Please log in to continue');
      return;
    }

    if (Platform.OS !== 'android') {
      Alert.alert('Google Pay', 'Google Pay is only available on Android devices.');
      return;
    }

    const available = await checkGooglePayAvailability();
    if (!available) {
      Alert.alert('Google Pay', 'Google Pay is not available on this device.');
      return;
    }

    setIsProcessing(true);

    try {
      // Step 1: Create order in our database
      const orderId = await createOrder({
        user_id: user.id,
        product_id: paymentRequest.productId,
        quantity: 1,
        total_amount: paymentRequest.amount,
        currency: paymentRequest.currency,
        status: 'pending',
        plan_type: paymentRequest.planType,
        payment_method: 'google_pay'
      });

      if (!orderId) {
        throw new Error('Failed to create order');
      }

      // Step 2: Process Google Pay payment
      const { data, error } = await supabase.functions.invoke('process-google-pay', {
        body: {
          order_id: orderId,
          amount: paymentRequest.amount,
          currency: paymentRequest.currency,
          plan_type: paymentRequest.planType,
          product_id: paymentRequest.productId
        }
      });

      if (error) {
        console.error('Google Pay processing error:', error);
        throw new Error('Failed to process Google Pay payment');
      }

      if (data.success) {
        // In a real implementation, you would:
        // 1. Present Google Pay sheet
        // 2. Handle user authorization
        // 3. Process payment with payment processor
        // 4. Confirm payment on backend
        
        // For now, simulate successful payment
        toast.success('Google Pay payment successful!');
        onSuccess?.();
      } else {
        throw new Error(data.error || 'Google Pay payment failed');
      }
    } catch (error: any) {
      console.error('Google Pay payment error:', error);
      toast.error(error.message || 'Google Pay payment failed');
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    isProcessing,
    isAvailable,
    checkGooglePayAvailability,
    processGooglePayPayment
  };
};