import { useState, useEffect, useCallback } from 'react';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { supabase } from '../supabase/client';
import { toast } from 'sonner-native';
import { AuthCredentials } from './useAuthCredentials';
import { AuthMode } from './useAuthMode';
import { AuthErrorActions } from './useAuthError';
import { RootStackParamList } from '../navigation';

interface UseAuthActionsProps {
  credentials: AuthCredentials;
  mode: AuthMode;
  errorManager: AuthErrorActions;
  security: {
    recordFailedAttempt: (email: string) => Promise<void>;
    resetLoginAttempts: (email: string) => Promise<void>;
    isAccountLocked: boolean;
  };
  validation: {
    validateForm: (email: string, password: string, confirmPassword: string, isSignUp: boolean, isResetPassword: boolean) => boolean;
  };
  modeActions: {
    setNeedsEmailConfirmation: (needs: boolean) => void;
    setIsSignUp: (isSignUp: boolean) => void;
    setIsResetPassword: (isReset: boolean) => void;
  };
  onShakeAnimation?: () => void;
}

/**
 * @magic_description Hook for handling authentication actions with Supabase
 * Manages sign in, sign up, password reset, and email confirmation
 */
export const useAuthActions = ({
  credentials,
  mode,
  errorManager,
  security,
  validation,
  modeActions,
  onShakeAnimation
}: UseAuthActionsProps) => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [googleAuthModule, setGoogleAuthModule] = useState<any>(null);
  const [isGoogleModuleLoading, setIsGoogleModuleLoading] = useState(false);
  const [googleModuleLoadFailed, setGoogleModuleLoadFailed] = useState(false);

  // Conditionally load Google Auth module on native platforms
  useEffect(() => {
    const loadGoogleAuthModule = async () => {
      console.log('🔍 [Google Auth Debug] Starting module loading process...');
      console.log('🔍 [Google Auth Debug] Platform:', Platform.OS);
      
      // Only attempt to load on native platforms
      if (Platform.OS === 'android' || Platform.OS === 'ios') {
        console.log('🔍 [Google Auth Debug] Native platform detected, proceeding with module loading');
        setIsGoogleModuleLoading(true);
        
        try {
          console.log('🔍 [Google Auth Debug] Attempting primary import method...');
          // Primary import method
          const [GoogleModule, WebBrowserModule] = await Promise.all([
            import('expo-auth-session/providers/google'),
            import('expo-web-browser')
          ]);
          
          console.log('🔍 [Google Auth Debug] Primary import successful!');
          console.log('🔍 [Google Auth Debug] GoogleModule:', !!GoogleModule);
          console.log('🔍 [Google Auth Debug] WebBrowserModule:', !!WebBrowserModule);
          
          // Complete auth session setup
          WebBrowserModule.maybeCompleteAuthSession();
          console.log('🔍 [Google Auth Debug] maybeCompleteAuthSession called');
          
          setGoogleAuthModule({
            Google: GoogleModule,
            WebBrowser: WebBrowserModule
          });
          console.log('🔍 [Google Auth Debug] ✅ Google auth module loaded successfully via primary method');
        } catch (primaryError) {
          console.warn('🔍 [Google Auth Debug] ⚠️ Primary Google module import failed:', primaryError);
          console.warn('🔍 [Google Auth Debug] Error details:', {
            name: primaryError?.name,
            message: primaryError?.message,
            stack: primaryError?.stack?.substring(0, 200)
          });
          
          try {
            console.log('🔍 [Google Auth Debug] Attempting alternative import method...');
            // Alternative import method as fallback
            const [AuthSession, WebBrowserModule] = await Promise.all([
              import('expo-auth-session'),
              import('expo-web-browser')
            ]);
            
            console.log('🔍 [Google Auth Debug] Alternative imports successful');
            console.log('🔍 [Google Auth Debug] AuthSession:', !!AuthSession);
            console.log('🔍 [Google Auth Debug] AuthSession.providers:', !!AuthSession.providers);
            console.log('🔍 [Google Auth Debug] AuthSession.providers.Google:', !!AuthSession.providers?.Google);
            
            if (!AuthSession.providers || !AuthSession.providers.Google) {
              throw new Error('Google provider not found on AuthSession.providers');
            }
            
            // Complete auth session setup
            WebBrowserModule.maybeCompleteAuthSession();
            console.log('🔍 [Google Auth Debug] maybeCompleteAuthSession called (alternative)');
            
            setGoogleAuthModule({
              Google: { default: AuthSession.providers.Google },
              WebBrowser: WebBrowserModule
            });
            console.log('🔍 [Google Auth Debug] ✅ Google auth module loaded successfully via alternative method');
          } catch (alternativeError) {
            console.error('🔍 [Google Auth Debug] ❌ Both primary and alternative Google auth module loading failed');
            console.error('🔍 [Google Auth Debug] Primary error:', {
              name: primaryError?.name,
              message: primaryError?.message
            });
            console.error('🔍 [Google Auth Debug] Alternative error:', {
              name: alternativeError?.name,
              message: alternativeError?.message
            });
            setGoogleModuleLoadFailed(true);
            console.log('🔍 [Google Auth Debug] googleModuleLoadFailed set to true');
            // Don't show toast here as it might be called before UI is ready
          }
        } finally {
          setIsGoogleModuleLoading(false);
          console.log('🔍 [Google Auth Debug] Module loading process completed');
        }
      } else {
        console.log('🔍 [Google Auth Debug] Web platform detected, skipping module loading');
      }
    };

    loadGoogleAuthModule();
  }, []);

  // Configure Google Auth Request (must be at top level for hook rules)
  // Note: For production iOS builds, replace iosClientId with actual iOS client ID from GoogleService-Info.plist
  // Current iosClientId is using expoClientId as placeholder for Expo Go development
  
  // Get Google Web Client ID from app.json configuration
  const googleWebClientId = Constants.expoConfig?.extra?.googleWebClientId;
  
  console.log('🔍 [Google Auth Debug] Configuring useAuthRequest...');
  console.log('🔍 [Google Auth Debug] googleAuthModule available:', !!googleAuthModule);
  console.log('🔍 [Google Auth Debug] googleAuthModule.Google available:', !!googleAuthModule?.Google);
  console.log('🔍 [Google Auth Debug] googleWebClientId from config:', googleWebClientId);
  
  // Configure Google Auth Request (must be at top level for hook rules)
  // Use dynamic configuration from app.json
  const [request, response, promptAsyncInternal] = googleAuthModule?.Google?.useAuthRequest ? 
    googleAuthModule.Google.useAuthRequest({
      expoClientId: '148731138249-dneh8a5gagnd9n7rinfk3ecos7c0gect.apps.googleusercontent.com',
      iosClientId: '148731138249-dneh8a5gagnd9n7rinfk3ecos7c0gect.apps.googleusercontent.com', // TODO: Replace with iOS client ID for production
      webClientId: googleWebClientId || '148731138249-t2arajrujdmq01vjubr8omuj97qocfd6.apps.googleusercontent.com',
    }) : [null, null, () => {
      // Dummy promptAsync if module not loaded
      console.log('🔍 [Google Auth Debug] Using dummy promptAsync (module not loaded)');
      if (Platform.OS !== 'web') {
        toast.error('Google Sign-In is initializing or unavailable. Please try again.');
      } else {
        toast.info('Google Sign-In is available in the mobile app. Please use email/password on web.');
      }
      return Promise.resolve(null);
    }];
    
  console.log('🔍 [Google Auth Debug] useAuthRequest configured:', {
    hasRequest: !!request,
    hasResponse: !!response,
    hasPromptAsync: typeof promptAsyncInternal === 'function'
  });

  const handleSignIn = async () => {
    if (security.isAccountLocked) {
      errorManager.setErrorMessage('Your account is locked. Please try again later.');
      onShakeAnimation?.();
      return { success: false };
    }

    if (!validation.validateForm(
      credentials.email,
      credentials.password,
      credentials.confirmPassword,
      mode.isSignUp,
      mode.isResetPassword
    )) {
      onShakeAnimation?.();
      return { success: false };
    }

    setIsLoading(true);
    errorManager.clearError();

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        // Check for MFA challenge
        if (error.message.includes('MFA challenge required') || error.message.includes('mfa_challenge_required')) {
          // Extract challenge ID from error or make MFA challenge request
          try {
            const { data: challengeData, error: challengeError } = await supabase.functions.invoke('mfa-challenge', {
              body: { email: credentials.email }
            });
            
            if (challengeError) throw challengeError;
            
            // Navigate to MFA verification screen
            navigation.navigate('VerifyMfaScreen', {
              email: credentials.email,
              challengeId: challengeData.challengeId
            });
            
            return { success: false, requiresMfa: true };
          } catch (mfaError) {
            console.error('MFA challenge error:', mfaError);
            errorManager.setErrorMessage('MFA verification required but failed to initiate. Please try again.');
            onShakeAnimation?.();
            return { success: false };
          }
        } else if (error.message.includes('Email not confirmed')) {
          modeActions.setNeedsEmailConfirmation(true);
          errorManager.setErrorMessage('Please check your email to confirm your account before logging in.');
          onShakeAnimation?.();
          return { success: false, needsConfirmation: true };
        } else if (error.message.includes('Invalid login credentials')) {
          errorManager.setErrorMessage('Invalid email or password');
          await security.recordFailedAttempt(credentials.email);
          onShakeAnimation?.();
          return { success: false };
        } else {
          errorManager.setErrorMessage(error.message);
          await security.recordFailedAttempt(credentials.email);
          onShakeAnimation?.();
          return { success: false };
        }
      }

      await security.resetLoginAttempts(credentials.email);
      toast.success('Signed in successfully');
      return { success: true, data };
    } catch (err) {
      errorManager.setErrorMessage('An error occurred. Please try again.');
      onShakeAnimation?.();
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignUp = async () => {
    if (!validation.validateForm(
      credentials.email,
      credentials.password,
      credentials.confirmPassword,
      mode.isSignUp,
      mode.isResetPassword
    )) {
      onShakeAnimation?.();
      return { success: false };
    }

    setIsLoading(true);
    errorManager.clearError();

    try {
      const { data, error } = await supabase.auth.signUp({
        email: credentials.email,
        password: credentials.password,
        options: {
          emailRedirectTo: 'https://hoofbeat.app/confirm-email'
        }
      });

      if (error) {
        errorManager.setErrorMessage(error.message);
        onShakeAnimation?.();
        return { success: false };
      }

      if (data?.user?.identities?.length === 0) {
        errorManager.setErrorMessage('This email is already registered. Please sign in instead.');
        onShakeAnimation?.();
        return { success: false };
      } else if (!data?.user?.email_confirmed_at) {
        modeActions.setNeedsEmailConfirmation(true);
        toast.success('Please check your email to confirm your account');
        modeActions.setIsSignUp(false);
        return { success: true, needsConfirmation: true, data };
      } else {
        toast.success('Account created successfully');
        modeActions.setIsSignUp(false);
        return { success: true, data };
      }
    } catch (err) {
      errorManager.setErrorMessage('An error occurred. Please try again.');
      onShakeAnimation?.();
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!credentials.email) {
      errorManager.setErrorMessage('Please enter your email');
      onShakeAnimation?.();
      return { success: false };
    }

    setIsLoading(true);
    errorManager.clearError();

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(credentials.email, {
        redirectTo: 'https://hoofbeat.app/reset-password',
      });

      if (error) {
        errorManager.setErrorMessage(error.message);
        onShakeAnimation?.();
        return { success: false };
      }

      toast.success('Password reset link sent to your email');
      modeActions.setIsResetPassword(false);
      return { success: true };
    } catch (err) {
      errorManager.setErrorMessage('An error occurred. Please try again.');
      onShakeAnimation?.();
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendConfirmation = async () => {
    if (!credentials.email) {
      errorManager.setErrorMessage('Please enter your email');
      onShakeAnimation?.();
      return { success: false };
    }

    setIsLoading(true);
    errorManager.clearError();

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: credentials.email,
        options: {
          emailRedirectTo: 'https://hoofbeat.app/confirm-email'
        }
      });

      if (error) {
        errorManager.setErrorMessage(error.message);
        onShakeAnimation?.();
        return { success: false };
      }

      toast.success('Confirmation email resent. Please check your inbox.');
      return { success: true };
    } catch (err) {
      errorManager.setErrorMessage('An error occurred. Please try again.');
      onShakeAnimation?.();
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleGoogleSignIn = useCallback(async () => {
    setIsGoogleLoading(true);
    errorManager.clearError();

    try {
      // Check if we're on web platform
      if (Platform.OS === 'web') {
        toast.info('Google Sign-In is available in the mobile app. Please use email/password on web.');
        return { success: false, webNotSupported: true };
      }

      // Check if Google module is ready
      if (!googleAuthModule || typeof promptAsyncInternal !== 'function') {
        if (isGoogleModuleLoading) {
          toast.error('Google Sign-In is still loading. Please try again in a moment.');
        } else {
          toast.error('Google Sign-In module failed to load. Please try another sign-in method.');
        }
        return { success: false, moduleNotReady: true };
      }

      // Check if request is configured
      if (!request) {
        toast.error('Google Sign-In is not properly configured.');
        return { success: false, notConfigured: true };
      }

      const authResponse = await promptAsyncInternal();
      
      if (authResponse?.type === 'success') {
        const { id_token } = authResponse.params;
        
        if (!id_token) {
          throw new Error('No ID token received from Google');
        }

        // Sign in with Supabase using Google ID token
        const { data, error } = await supabase.auth.signInWithIdToken({
          provider: 'google',
          token: id_token,
        });

        if (error) {
          throw error;
        }

        toast.success('Signed in with Google successfully');
        return { success: true, data };
      } else if (authResponse?.type === 'cancel' || authResponse?.type === 'dismiss') {
        toast.info('Google Sign-In cancelled.');
        return { success: false, cancelled: true };
      } else if (authResponse?.type === 'error') {
        const errorMessage = authResponse.error?.message || 'Unknown Google Sign-In error';
        toast.error(`Google Sign-In error: ${errorMessage}`);
        return { success: false, error: errorMessage };
      } else {
        throw new Error('Unexpected Google Sign-In response');
      }
    } catch (err: any) {
      console.error('Google Sign-In error:', err);
      
      const errorMessage = err.message || 'Google Sign-In failed. Please try again.';
      errorManager.setErrorMessage(errorMessage);
      onShakeAnimation?.();
      return { success: false, error: errorMessage };
    } finally {
      setIsGoogleLoading(false);
    }
  }, [googleAuthModule, promptAsyncInternal, request, isGoogleModuleLoading, errorManager, onShakeAnimation]);

  return {
    isLoading,
    isGoogleLoading,
    isGoogleModuleLoading,
    googleModuleReady: !!googleAuthModule && !googleModuleLoadFailed,
    googleModuleLoadFailed,
    handleSignIn,
    handleSignUp,
    handleForgotPassword,
    handleResendConfirmation,
    handleGoogleSignIn,
  };
};