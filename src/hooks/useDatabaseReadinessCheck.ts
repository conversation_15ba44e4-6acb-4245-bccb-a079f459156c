import { useState, useCallback } from 'react';
import { supabase } from '../supabase/client';

interface DatabaseReadinessResult {
  isReady: boolean;
  message: string;
}

export const useDatabaseReadinessCheck = () => {
  const [result, setResult] = useState<DatabaseReadinessResult>({
    isReady: false,
    message: 'Not checked'
  });
  const [isChecking, setIsChecking] = useState(false);
  
  const checkDb = useCallback(async (): Promise<void> => {
    setIsChecking(true);
    
    try {
      // Ping the database with a simple query
      const { data, error } = await supabase
        .from('vitals')
        .select('id')
        .limit(1);
        
      if (error) {
        setResult({ isReady: false, message: `Database error` });
      } else {
        setResult({ isReady: true, message: "Database ready" });
      }
    } catch (error) {
      setResult({ isReady: false, message: `Connection error` });
    } finally {
      setIsChecking(false);
    }
  }, []);
  
  return {
    ...result,
    isChecking,
    checkDb
  };
};