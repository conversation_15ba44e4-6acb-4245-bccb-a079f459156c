import { useState, useEffect, useCallback } from 'react';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { toast } from 'sonner-native';
import { supabase } from '../supabase/client';

// Type definitions for biometric authentication
interface BiometricAuthResult {
  success: boolean;
  error?: string;
  cancelled?: boolean;
}

interface BiometricCapabilities {
  isAvailable: boolean;
  hasHardware: boolean;
  isEnrolled: boolean;
  supportedTypes: string[];
}

interface UseBiometricAuthReturn {
  isAvailable: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  capabilities: BiometricCapabilities;
  enableBiometric: (email: string, password: string) => Promise<boolean>;
  disableBiometric: () => Promise<void>;
  authenticateWithBiometric: () => Promise<BiometricAuthResult>;
  checkBiometricCapabilities: () => Promise<BiometricCapabilities>;
}

// Storage keys
const BIOMETRIC_ENABLED_KEY = 'hoofbeat-biometric-enabled';
const BIOMETRIC_USER_KEY = 'hoofbeat-biometric-user';

export const useBiometricAuth = (): UseBiometricAuthReturn => {
  const [isAvailable, setIsAvailable] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [capabilities, setCapabilities] = useState<BiometricCapabilities>({
    isAvailable: false,
    hasHardware: false,
    isEnrolled: false,
    supportedTypes: []
  });

  // Check biometric capabilities
  const checkBiometricCapabilities = useCallback(async (): Promise<BiometricCapabilities> => {
    try {
      // For web platform, biometrics are not supported
      if (Platform.OS === 'web') {
        const webCapabilities = {
          isAvailable: false,
          hasHardware: false,
          isEnrolled: false,
          supportedTypes: []
        };
        setCapabilities(webCapabilities);
        return webCapabilities;
      }

      // Try to dynamically import expo-local-authentication
      try {
        const LocalAuthentication = await import('expo-local-authentication');
        
        // Check if hardware is available
        const hasHardware = await LocalAuthentication.hasHardwareAsync();
        
        if (!hasHardware) {
          const noHardwareCapabilities = {
            isAvailable: false,
            hasHardware: false,
            isEnrolled: false,
            supportedTypes: []
          };
          setCapabilities(noHardwareCapabilities);
          return noHardwareCapabilities;
        }

        // Check if biometrics are enrolled
        const isEnrolled = await LocalAuthentication.isEnrolledAsync();
        
        // Get supported authentication types
        const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
        const supportedTypeNames = supportedTypes.map(type => {
          switch (type) {
            case LocalAuthentication.AuthenticationType.FINGERPRINT:
              return 'Fingerprint';
            case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
              return 'Face ID';
            case LocalAuthentication.AuthenticationType.IRIS:
              return 'Iris';
            default:
              return 'Biometric';
          }
        });

        const biometricCapabilities = {
          isAvailable: hasHardware && isEnrolled,
          hasHardware,
          isEnrolled,
          supportedTypes: supportedTypeNames
        };

        setCapabilities(biometricCapabilities);
        setIsAvailable(biometricCapabilities.isAvailable);
        
        return biometricCapabilities;
      } catch (importError) {
        console.log('expo-local-authentication not available:', importError);
        const unavailableCapabilities = {
          isAvailable: false,
          hasHardware: false,
          isEnrolled: false,
          supportedTypes: []
        };
        setCapabilities(unavailableCapabilities);
        return unavailableCapabilities;
      }
    } catch (error) {
      console.error('Error checking biometric capabilities:', error);
      const errorCapabilities = {
        isAvailable: false,
        hasHardware: false,
        isEnrolled: false,
        supportedTypes: []
      };
      setCapabilities(errorCapabilities);
      return errorCapabilities;
    }
  }, []);

  // Check if biometric is enabled for current user
  const checkBiometricEnabled = useCallback(async () => {
    try {
      const enabled = await AsyncStorage.getItem(BIOMETRIC_ENABLED_KEY);
      setIsEnabled(enabled === 'true');
    } catch (error) {
      console.error('Error checking biometric enabled status:', error);
      setIsEnabled(false);
    }
  }, []);

  // Enable biometric authentication
  const enableBiometric = useCallback(async (email: string, password: string): Promise<boolean> => {
    if (!isAvailable) {
      toast.error('Biometric authentication is not available on this device');
      return false;
    }

    setIsLoading(true);
    
    try {
      // First, verify the credentials work
      const { error: authError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (authError) {
        toast.error('Please verify your credentials before enabling biometric login');
        return false;
      }

      // Prompt for biometric authentication to confirm setup
      const LocalAuthentication = await import('expo-local-authentication');
      
      const biometricResult = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Enable biometric login',
        subPromptMessage: 'Use your biometric to confirm setup',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Password',
        disableDeviceFallback: false,
      });

      if (biometricResult.success) {
        // Store biometric enabled flag and user email
        await AsyncStorage.setItem(BIOMETRIC_ENABLED_KEY, 'true');
        await AsyncStorage.setItem(BIOMETRIC_USER_KEY, email);
        
        setIsEnabled(true);
        toast.success('Biometric login enabled successfully!');
        return true;
      } else {
        toast.error('Biometric setup cancelled');
        return false;
      }
    } catch (error: any) {
      console.error('Error enabling biometric:', error);
      toast.error('Failed to enable biometric login');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isAvailable]);

  // Disable biometric authentication
  const disableBiometric = useCallback(async (): Promise<void> => {
    try {
      await AsyncStorage.removeItem(BIOMETRIC_ENABLED_KEY);
      await AsyncStorage.removeItem(BIOMETRIC_USER_KEY);
      setIsEnabled(false);
      toast.success('Biometric login disabled');
    } catch (error) {
      console.error('Error disabling biometric:', error);
      toast.error('Failed to disable biometric login');
    }
  }, []);

  // Authenticate with biometric
  const authenticateWithBiometric = useCallback(async (): Promise<BiometricAuthResult> => {
    if (!isAvailable || !isEnabled) {
      return {
        success: false,
        error: 'Biometric authentication is not available or enabled'
      };
    }

    setIsLoading(true);

    try {
      const LocalAuthentication = await import('expo-local-authentication');
      
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Sign in to HoofBeat',
        subPromptMessage: 'Use your biometric to access your account',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Password',
        disableDeviceFallback: false,
      });

      if (result.success) {
        // Get stored user email
        const storedEmail = await AsyncStorage.getItem(BIOMETRIC_USER_KEY);
        
        if (!storedEmail) {
          return {
            success: false,
            error: 'No user found for biometric login. Please sign in with password first.'
          };
        }

        // For security, we'll use Supabase's session refresh instead of storing passwords
        // Check if there's an existing session that can be refreshed
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (session && !sessionError) {
          // Session exists and is valid
          return { success: true };
        } else {
          // No valid session, user needs to sign in with password first
          return {
            success: false,
            error: 'Please sign in with your password to refresh your session, then enable biometric login again.'
          };
        }
      } else if (result.error === 'UserCancel' || result.error === 'UserFallback') {
        return {
          success: false,
          cancelled: true
        };
      } else {
        return {
          success: false,
          error: result.error || 'Biometric authentication failed'
        };
      }
    } catch (error: any) {
      console.error('Error during biometric authentication:', error);
      return {
        success: false,
        error: 'Biometric authentication failed'
      };
    } finally {
      setIsLoading(false);
    }
  }, [isAvailable, isEnabled]);

  // Initialize on mount
  useEffect(() => {
    const initialize = async () => {
      await checkBiometricCapabilities();
      await checkBiometricEnabled();
    };
    
    initialize();
  }, [checkBiometricCapabilities, checkBiometricEnabled]);

  return {
    isAvailable,
    isEnabled,
    isLoading,
    capabilities,
    enableBiometric,
    disableBiometric,
    authenticateWithBiometric,
    checkBiometricCapabilities,
  };
};

export default useBiometricAuth;