import { useMemo } from 'react';
import { useAnimalStore } from '../store/animalStore';
import { useVitalsStore } from '../store/vitalsStore';
import { useFeedingStore } from '../store/feedingStore';
import { useMedicationStore } from '../store/medicationStore';
import { format } from 'date-fns';

/**
 * @magic_description Custom hook for HomeScreen statistics and data processing
 * Handles all data aggregation, calculations, and derivations for the home dashboard
 */
export const useHomeScreenStats = () => {
  const { animals } = useAnimalStore();
  const { vitals } = useVitalsStore();
  const { getTodayFeedings } = useFeedingStore();
  const { getTodayMedications } = useMedicationStore();

  const stats = useMemo(() => {
    // Calculate basic stats
    const totalAnimals = animals.length;
    const activeDevices = animals.filter(animal => animal.deviceConnected).length;
    
    // Calculate total feedings and medications across all animals
    const todayFeedings = animals.reduce((total, animal) => 
      total + getTodayFeedings(animal.id).length, 0
    );
    const todayMedications = animals.reduce((total, animal) => 
      total + getTodayMedications(animal.id).length, 0
    );

    // Get latest vitals for each animal
    const latestVitals = animals.map(animal => {
      const animalVitals = vitals.filter(vital => vital.animalId === animal.id);
      if (animalVitals.length === 0) return null;
      
      return animalVitals.reduce((latest, current) => {
        return new Date(current.timestamp) > new Date(latest.timestamp) ? current : latest;
      });
    }).filter(Boolean);

    // Filter for abnormal vitals
    const abnormalVitals = latestVitals.filter(vital => {
      if (!vital) return false;
      
      const tempHigh = vital.temperature > 39.5;
      const tempLow = vital.temperature < 37.5;
      const heartRateHigh = vital.heartRate > 100;
      const heartRateLow = vital.heartRate < 40;
      const respirationHigh = vital.respiration > 30;
      const respirationLow = vital.respiration < 10;
      
      return tempHigh || tempLow || heartRateHigh || heartRateLow || respirationHigh || respirationLow;
    });

    // Generate upcoming tasks
    const upcomingTasks = animals.flatMap(animal => {
      const feedings = getTodayFeedings(animal.id).map(feeding => ({
        id: `feeding-${feeding.id}`,
        type: 'feeding' as const,
        animalId: animal.id,
        time: feeding.time,
        title: `Feed ${animal.name}`,
        subtitle: `${feeding.feedType} - ${feeding.amount}`,
        data: feeding
      }));
      
      const medications = getTodayMedications(animal.id).map(medication => ({
        id: `medication-${medication.id}`,
        type: 'medication' as const,
        animalId: animal.id,
        time: medication.time,
        title: `Medication for ${animal.name}`,
        subtitle: `${medication.medicationName} - ${medication.dosage}${medication.dosageUnit}`,
        data: medication
      }));
      
      return [...feedings, ...medications];
    }).sort((a, b) => {
      const timeA = parseInt(a.time.replace(':', ''));
      const timeB = parseInt(b.time.replace(':', ''));
      return timeA - timeB;
    });

    return {
      totalAnimals,
      activeDevices,
      todayFeedings,
      todayMedications,
      latestVitals,
      abnormalVitals,
      upcomingTasks
    };
  }, [animals, vitals, getTodayFeedings, getTodayMedications]);

  return stats;
};

export default useHomeScreenStats;