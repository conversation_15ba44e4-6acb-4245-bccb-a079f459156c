-- Fix notification_analytics view security issue
-- The original view exposed data across all users without proper filtering
-- This migration fixes the security definer issue and adds user-specific filtering

-- Drop the existing insecure views
DROP VIEW IF EXISTS public.notification_analytics_admin;
DROP VIEW IF EXISTS public.notification_analytics;

-- Create a secure view that only shows data for the authenticated user
CREATE VIEW public.notification_analytics AS
SELECT 
  date_trunc('day', nl.created_at) AS date,
  nl.priority,
  nl.status,
  nl.alert_type,
  count(*) AS count
FROM notification_logs nl
WHERE 
  nl.created_at >= (now() - interval '30 days')
  AND nl.user_id = auth.uid()  -- CRITICAL: Only show current user's data
GROUP BY 
  date_trunc('day', nl.created_at), 
  nl.priority, 
  nl.status, 
  nl.alert_type
ORDER BY 
  date_trunc('day', nl.created_at) DESC;

-- Add a comment explaining the security fix
COMMENT ON VIEW public.notification_analytics IS 
'Secure analytics view that shows notification statistics only for the authenticated user. Fixed security issue where previous view exposed data across all users.';

-- Create an admin-only view for system-wide analytics (if needed for admin panel)
CREATE VIEW public.notification_analytics_admin AS
SELECT 
  date_trunc('day', nl.created_at) AS date,
  nl.priority,
  nl.status,
  nl.alert_type,
  nl.user_id,  -- Include user_id for admin context
  count(*) AS count
FROM notification_logs nl
WHERE nl.created_at >= (now() - interval '30 days')
GROUP BY 
  date_trunc('day', nl.created_at), 
  nl.priority, 
  nl.status, 
  nl.alert_type,
  nl.user_id
ORDER BY 
  date_trunc('day', nl.created_at) DESC;

-- Restrict admin view to service role only
GRANT SELECT ON public.notification_analytics_admin TO service_role;
REVOKE SELECT ON public.notification_analytics_admin FROM authenticated;
REVOKE SELECT ON public.notification_analytics_admin FROM anon;

COMMENT ON VIEW public.notification_analytics_admin IS 
'Admin-only view for system-wide notification analytics. Only accessible by service role for administrative purposes.';