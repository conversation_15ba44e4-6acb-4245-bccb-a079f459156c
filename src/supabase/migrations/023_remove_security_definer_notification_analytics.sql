-- Remove SECURITY DEFINER property from notification_analytics view
-- This ensures the view runs with the querying user's permissions, not the creator's

-- Drop the existing view completely
DROP VIEW IF EXISTS public.notification_analytics CASCADE;

-- Recreate the view explicitly WITHOUT SECURITY DEFINER
CREATE VIEW public.notification_analytics 
WITH (security_invoker = true) AS
SELECT 
  date_trunc('day', nl.created_at) AS date,
  nl.priority,
  nl.status,
  nl.alert_type,
  count(*) AS count
FROM notification_logs nl
WHERE 
  nl.created_at >= (now() - interval '30 days')
  AND nl.user_id = auth.uid()  -- Ensures user can only see their own data
GROUP BY 
  date_trunc('day', nl.created_at), 
  nl.priority, 
  nl.status, 
  nl.alert_type
ORDER BY 
  date_trunc('day', nl.created_at) DESC;

-- Explicitly grant permissions to authenticated users
GRANT SELECT ON public.notification_analytics TO authenticated;

-- Add comment explaining the security model
COMMENT ON VIEW public.notification_analytics IS 
'User-specific notification analytics view. Runs with SECURITY INVOKER to respect user permissions and RLS policies. Each user can only see their own notification data.';

-- Verify the view is created without SECURITY DEFINER
-- The view will now inherit the querying user's permissions and respect RLS