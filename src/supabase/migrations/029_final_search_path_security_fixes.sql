-- FINAL SEARCH_PATH SECURITY FIXES
-- Fix the last remaining functions with correct signatures

-- Fix utility functions with correct signatures
ALTER FUNCTION public.check_translation_budget(uuid, numeric) SET search_path = public, pg_temp;
ALTER FUNCTION public.interval_to_seconds(interval) SET search_path = public, pg_temp;
ALTER FUNCTION public.update_animal_speed() SET search_path = public, pg_temp;

-- Add security comments
COMMENT ON FUNCTION public.check_translation_budget IS 
'Checks translation budget limits and updates spending. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.interval_to_seconds IS 
'Converts interval to seconds for duration calculations. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.update_animal_speed IS 
'Trigger function to update animal speed from speed records. SECURITY DEFINER with fixed search_path to prevent injection attacks.';