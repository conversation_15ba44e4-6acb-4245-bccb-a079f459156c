-- Secure the admin notification analytics view permissions
-- Remove excessive permissions and restrict to service role only

-- Revoke all permissions from public roles
REVOKE ALL ON public.notification_analytics_admin FROM anon;
REVOKE ALL ON public.notification_analytics_admin FROM authenticated;
REVOKE ALL ON public.notification_analytics_admin FROM public;

-- Grant only SELECT permission to service_role (for admin access)
GRANT SELECT ON public.notification_analytics_admin TO service_role;

-- Add comment explaining the restricted access
COMMENT ON VIEW public.notification_analytics_admin IS 
'Admin-only notification analytics view with system-wide data. Restricted to service_role only for administrative dashboards and reporting.';