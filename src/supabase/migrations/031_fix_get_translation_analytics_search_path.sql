-- FIX: get_translation_analytics function search_path vulnerability
-- Secure the get_translation_analytics function to prevent search_path injection attacks

-- Fix the get_translation_analytics function with correct signature
ALTER FUNCTION public.get_translation_analytics(uuid, integer) SET search_path = public, pg_temp;

-- Add security comment
COMMENT ON FUNCTION public.get_translation_analytics IS 
'Gets translation analytics data for a user over specified days. SECURITY DEFINER with fixed search_path to prevent injection attacks.';