-- Complete search_path security fix for all remaining SECURITY DEFINER functions
-- This migration adds SET search_path to functions that don't have it yet

-- Fix remaining authentication and MFA functions
ALTER FUNCTION public.cleanup_expired_mfa_challenges() SET search_path = public, pg_temp;
ALTER FUNCTION public.cleanup_expired_mfa_session_tokens() SET search_path = public, pg_temp;
ALTER FUNCTION public.confirm_user_email(text) SET search_path = public, pg_temp;
ALTER FUNCTION public.is_email_confirmed(text) SET search_path = public, pg_temp;
ALTER FUNCTION public.make_user_admin(text) SET search_path = public, pg_temp;
ALTER FUNCTION public.handle_new_user() SET search_path = public, pg_temp;
ALTER FUNCTION public.set_user_id_on_animal_insert() SET search_path = public, pg_temp;

-- Fix notification functions
ALTER FUNCTION public.get_user_notification_stats(uuid) SET search_path = public, pg_temp;
ALTER FUNCTION public.get_recent_notifications(uuid, integer) SET search_path = public, pg_temp;
ALTER FUNCTION public.get_order_notification_history(uuid) SET search_path = public, pg_temp;

-- Fix translation functions
ALTER FUNCTION public.get_translation_secrets() SET search_path = public, pg_temp;

-- Add security comments to document the fix
COMMENT ON FUNCTION public.cleanup_expired_mfa_challenges IS 
'Cleans up expired MFA challenges. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.cleanup_expired_mfa_session_tokens IS 
'Cleans up expired MFA session tokens. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.confirm_user_email IS 
'Confirms user email address. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.is_email_confirmed IS 
'Checks if user email is confirmed. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.make_user_admin IS 
'Makes user an admin. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.handle_new_user IS 
'Handles new user creation. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.set_user_id_on_animal_insert IS 
'Sets user ID on animal insert. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.get_user_notification_stats IS 
'Gets user notification statistics. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.get_recent_notifications IS 
'Gets recent notifications for user. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.get_order_notification_history IS 
'Gets order notification history. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.get_translation_secrets IS 
'Gets translation API secrets. SECURITY DEFINER with fixed search_path to prevent injection attacks.';