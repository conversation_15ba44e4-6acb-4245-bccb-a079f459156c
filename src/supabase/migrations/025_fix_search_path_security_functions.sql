-- Fix search_path security vulnerability in functions
-- Set explicit search_path to prevent search_path injection attacks

-- Fix update_conversation_with_message function
CREATE OR REPLACE FUNCTION public.update_conversation_with_message(
  p_conversation_id uuid, 
  p_user_id uuid, 
  p_last_message text, 
  p_message_count_increment integer DEFAULT 2
)
RETURNS bool
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- Fix: Set explicit search_path
AS $$
BEGIN
  -- Update conversation with new message info
  UPDATE ai_chat_conversations 
  SET 
    last_message_at = NOW(),
    total_messages = COALESCE(total_messages, 0) + p_message_count_increment,
    updated_at = NOW(),
    conversation_summary = CASE 
      WHEN LENGTH(p_last_message) > 200 
      THEN LEFT(p_last_message, 200) || '...'
      ELSE p_last_message
    END
  WHERE 
    id = p_conversation_id 
    AND user_id = p_user_id;
  
  -- Return true if update was successful
  RETURN FOUND;
END;
$$;

-- Fix insert_chat_message function
CREATE OR R<PERSON>LACE FUNCTION public.insert_chat_message(
  p_conversation_id uuid, 
  p_message_type character varying, 
  p_message_content text, 
  p_message_metadata jsonb DEFAULT NULL::jsonb
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- Fix: Set explicit search_path
AS $$
DECLARE
  v_message_id UUID;
BEGIN
  -- Insert the message
  INSERT INTO ai_chat_messages (
    conversation_id,
    message_type,
    message_content,
    message_metadata,
    created_at
  )
  VALUES (
    p_conversation_id,
    p_message_type,
    p_message_content,
    p_message_metadata,
    NOW()
  )
  RETURNING id INTO v_message_id;
  
  RETURN v_message_id;
END;
$$;

-- Fix handle_chat_message_flow function
CREATE OR REPLACE FUNCTION public.handle_chat_message_flow(
  p_conversation_id uuid, 
  p_user_id uuid, 
  p_user_message text, 
  p_ai_response text, 
  p_user_metadata jsonb DEFAULT NULL::jsonb, 
  p_ai_metadata jsonb DEFAULT NULL::jsonb
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- Fix: Set explicit search_path
AS $$
DECLARE
  v_user_message_id UUID;
  v_ai_message_id UUID;
  v_conversation_updated BOOLEAN;
  v_result JSONB;
BEGIN
  -- Start transaction
  BEGIN
    -- Insert user message
    SELECT insert_chat_message(
      p_conversation_id,
      'user',
      p_user_message,
      p_user_metadata
    ) INTO v_user_message_id;
    
    -- Insert AI response
    SELECT insert_chat_message(
      p_conversation_id,
      'assistant',
      p_ai_response,
      p_ai_metadata
    ) INTO v_ai_message_id;
    
    -- Update conversation metadata
    SELECT update_conversation_with_message(
      p_conversation_id,
      p_user_id,
      p_ai_response,
      2  -- 2 messages added (user + AI)
    ) INTO v_conversation_updated;
    
    -- Build result
    v_result := jsonb_build_object(
      'success', true,
      'user_message_id', v_user_message_id,
      'ai_message_id', v_ai_message_id,
      'conversation_updated', v_conversation_updated
    );
    
    RETURN v_result;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- Return error info
      v_result := jsonb_build_object(
        'success', false,
        'error', SQLERRM,
        'error_code', SQLSTATE
      );
      
      RETURN v_result;
  END;
END;
$$;

-- Add comments explaining the security fix
COMMENT ON FUNCTION public.update_conversation_with_message IS 
'Updates conversation metadata with new message info. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.insert_chat_message IS 
'Inserts a new chat message. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.handle_chat_message_flow IS 
'Handles complete chat message flow. SECURITY DEFINER with fixed search_path to prevent injection attacks.';