-- COMPREHENSIVE FIX: All remaining functions with mutable search_path vulnerabilities
-- This migration secures ALL database functions to prevent search_path injection attacks

-- Fix the specifically mentioned functions
ALTER FUNCTION public.auto_calculate_training_metrics() SET search_path = public, pg_temp;
ALTER FUNCTION public.update_ai_file_uploads_updated_at() SET search_path = public, pg_temp;
ALTER FUNCTION public.update_ai_file_analyses_updated_at() SET search_path = public, pg_temp;

-- Fix all calculation and utility functions
ALTER FUNCTION public.calculate_hydration_level(numeric) SET search_path = public, pg_temp;
ALTER FUNCTION public.calculate_hydration_status(numeric) SET search_path = public, pg_temp;
ALTER FUNCTION public.calculate_training_metrics(numeric, integer, numeric, numeric, numeric) SET search_path = public, pg_temp;

-- Fix all trigger functions for updated_at columns
ALTER FUNCTION public.handle_devices_updated_at() SET search_path = public, pg_temp;
ALTER FUNCTION public.handle_training_sessions_updated_at() SET search_path = public, pg_temp;
ALTER FUNCTION public.update_dehydration_logs_updated_at() SET search_path = public, pg_temp;
ALTER FUNCTION public.update_notification_logs_updated_at() SET search_path = public, pg_temp;
ALTER FUNCTION public.update_payment_logs_updated_at() SET search_path = public, pg_temp;
ALTER FUNCTION public.update_updated_at_column() SET search_path = public, pg_temp;

-- Fix translation and analytics functions
ALTER FUNCTION public.update_translation_usage_stats(uuid, character varying, integer, numeric, numeric) SET search_path = public, pg_temp;

-- Add security comments for all fixed functions
COMMENT ON FUNCTION public.auto_calculate_training_metrics IS 
'Trigger function to auto-calculate training metrics. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.update_ai_file_uploads_updated_at IS 
'Trigger function for AI file uploads timestamps. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.update_ai_file_analyses_updated_at IS 
'Trigger function for AI file analyses timestamps. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.calculate_hydration_level IS 
'Calculates hydration level from bioimpedance. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.calculate_hydration_status IS 
'Determines hydration status from bioimpedance. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.calculate_training_metrics IS 
'Calculates comprehensive training metrics. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.handle_devices_updated_at IS 
'Trigger function for devices timestamps. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.handle_training_sessions_updated_at IS 
'Trigger function for training sessions timestamps. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.update_dehydration_logs_updated_at IS 
'Trigger function for dehydration logs timestamps. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.update_notification_logs_updated_at IS 
'Trigger function for notification logs timestamps. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.update_payment_logs_updated_at IS 
'Trigger function for payment logs timestamps. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.update_updated_at_column IS 
'Generic trigger function for updated_at columns. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.update_translation_usage_stats IS 
'Updates translation usage statistics. SECURITY DEFINER with fixed search_path to prevent injection attacks.';