-- Fix search_path for remaining functions that need it
-- Drop and recreate functions with proper return types and search_path

-- Drop and recreate search_conversations function with proper return type
DROP FUNCTION IF EXISTS public.search_conversations(text);

CREATE FUNCTION public.search_conversations(search_term text)
RETURNS TABLE(
  id uuid,
  title text,
  animal_name text,
  last_message text,
  last_message_at timestamp with time zone,
  relevance numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- Fix: Set explicit search_path
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    c.title,
    a.name as animal_name,
    c.last_message,
    c.last_message_at,
    (
      CASE WHEN c.title ILIKE '%' || search_term || '%' THEN 1.0 ELSE 0.0 END +
      CASE WHEN c.last_message ILIKE '%' || search_term || '%' THEN 0.8 ELSE 0.0 END +
      CASE WHEN a.name ILIKE '%' || search_term || '%' THEN 0.6 ELSE 0.0 END
    ) as relevance
  FROM ai_conversations c
  LEFT JOIN animals a ON c.animal_id = a.id
  WHERE c.user_id = auth.uid()
    AND (
      c.title ILIKE '%' || search_term || '%' OR
      c.last_message ILIKE '%' || search_term || '%' OR
      a.name ILIKE '%' || search_term || '%'
    )
  ORDER BY relevance DESC, c.last_message_at DESC;
END;
$$;

-- Drop and recreate get_conversation_summary function with proper return type
DROP FUNCTION IF EXISTS public.get_conversation_summary(uuid);

CREATE FUNCTION public.get_conversation_summary(conversation_uuid uuid)
RETURNS TABLE(
  id uuid,
  title text,
  animal_name text,
  message_count bigint,
  last_message text,
  last_message_at timestamp with time zone,
  created_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- Fix: Set explicit search_path
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    c.title,
    a.name as animal_name,
    COUNT(m.id) as message_count,
    c.last_message,
    c.last_message_at,
    c.created_at
  FROM ai_conversations c
  LEFT JOIN animals a ON c.animal_id = a.id
  LEFT JOIN ai_messages m ON c.id = m.conversation_id
  WHERE c.id = conversation_uuid
    AND c.user_id = auth.uid()
  GROUP BY c.id, c.title, a.name, c.last_message, c.last_message_at, c.created_at;
END;
$$;

-- Fix cleanup functions with search_path
CREATE OR REPLACE FUNCTION public.cleanup_old_ai_conversations()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- Fix: Set explicit search_path
AS $$
BEGIN
  -- Delete conversations older than 6 months with no recent activity
  DELETE FROM ai_conversations
  WHERE last_message_at < NOW() - INTERVAL '6 months'
    AND message_count < 5;
  
  -- Delete orphaned messages (shouldn't happen with CASCADE, but just in case)
  DELETE FROM ai_messages
  WHERE conversation_id NOT IN (SELECT id FROM ai_conversations);
END;
$$;

CREATE OR REPLACE FUNCTION public.cleanup_old_notification_logs()
RETURNS int4
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- Fix: Set explicit search_path
AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM notification_logs 
  WHERE created_at < NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$;

-- Fix trigger functions with search_path
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- Fix: Set explicit search_path
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.update_ai_conversations_updated_at()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- Fix: Set explicit search_path
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.update_modified_column()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp  -- Fix: Set explicit search_path
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Add comments explaining the security fixes
COMMENT ON FUNCTION public.search_conversations IS 
'Searches user conversations with relevance scoring. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.get_conversation_summary IS 
'Gets conversation summary with message count. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.cleanup_old_ai_conversations IS 
'Cleans up old conversations. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.cleanup_old_notification_logs IS 
'Cleans up old notification logs. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.handle_updated_at IS 
'Trigger function to update timestamps. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.update_ai_conversations_updated_at IS 
'Trigger function for AI conversations timestamps. SECURITY DEFINER with fixed search_path to prevent injection attacks.';

COMMENT ON FUNCTION public.update_modified_column IS 
'Trigger function for modified column updates. SECURITY DEFINER with fixed search_path to prevent injection attacks.';