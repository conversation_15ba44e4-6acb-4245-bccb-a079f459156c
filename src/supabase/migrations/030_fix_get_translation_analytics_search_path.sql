-- FIX: get_translation_analytics function search_path vulnerability
-- This function was missed in previous security audits

-- Fix the get_translation_analytics function with correct signature
ALTER FUNCTION public.get_translation_analytics(uuid, integer) SET search_path = public, pg_temp;

-- Add security comment
COMMENT ON FUNCTION public.get_translation_analytics IS 
'Gets translation analytics data for a user over specified days. SECURITY DEFINER with fixed search_path to prevent injection attacks.';