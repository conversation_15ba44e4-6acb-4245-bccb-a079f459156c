
// This file is used to deploy Supabase Edge Functions
// Run with: node deploy.js

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get the Supabase project ID from the environment
const projectId = 'hfqhqymuenbuzndkdcqf';

// Path to the functions directory
const functionsDir = path.join(__dirname, 'functions');

// Get all function directories
const functions = fs.readdirSync(functionsDir)
  .filter(file => fs.statSync(path.join(functionsDir, file)).isDirectory());

console.log(`Found ${functions.length} functions to deploy:`);
console.log(functions.join(', '));

// Deploy each function
functions.forEach(functionName => {
  console.log(`Deploying function: ${functionName}`);
  try {
    execSync(`supabase functions deploy ${functionName} --project-ref ${projectId}`, {
      stdio: 'inherit',
    });
    console.log(`Successfully deployed function: ${functionName}`);
  } catch (error) {
    console.error(`Failed to deploy function: ${functionName}`);
    console.error(error);
  }
});

console.log('Deployment complete!');
