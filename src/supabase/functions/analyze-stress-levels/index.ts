import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface StressAnalysisRequest {
  animalId: string;
  analysisHours?: number; // Default 24 hours
}

interface HRVMetrics {
  rmssd: number; // Root Mean Square of Successive Differences
  pnn50: number; // Percentage of successive RR intervals that differ by more than 50ms
  heartRateVariability: number;
  averageHeartRate: number;
  stressIndicator: number; // 0-100 scale
}

interface ActivityIndicators {
  restlessness: number; // 0-100 scale
  movementPatterns: string; // 'normal', 'agitated', 'lethargic'
  activityLevel: number; // 0-100 scale
  unusualBehaviors: string[];
}

interface EnvironmentalFactors {
  temperature: number;
  humidity: number;
  noiseLevel: number;
  locationChanges: number;
  weatherConditions: string;
  timeOfDay: string;
}

interface StressTrigger {
  type: string;
  confidence: number;
  description: string;
  timestamp: string;
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    // Get user from JWT
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();
    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { animalId, analysisHours = 24 }: StressAnalysisRequest = await req.json();

    if (!animalId) {
      return new Response(JSON.stringify({ error: 'Animal ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Verify animal ownership
    const { data: animal, error: animalError } = await supabaseClient
      .from('animals')
      .select('id, name')
      .eq('id', animalId)
      .eq('user_id', user.id)
      .single();

    if (animalError || !animal) {
      return new Response(JSON.stringify({ error: 'Animal not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const analysisStartTime = new Date(Date.now() - (analysisHours * 60 * 60 * 1000)).toISOString();
    const analysisEndTime = new Date().toISOString();

    // Fetch vitals data for HRV analysis
    const { data: vitalsData } = await supabaseClient
      .from('vitals')
      .select('*')
      .eq('animal_id', animalId)
      .gte('recorded_at', analysisStartTime)
      .lte('recorded_at', analysisEndTime)
      .order('recorded_at', { ascending: true });

    // Fetch activity/movement data
    const { data: activityData } = await supabaseClient
      .from('training_sessions')
      .select('*')
      .eq('animal_id', animalId)
      .gte('session_date', analysisStartTime)
      .lte('session_date', analysisEndTime)
      .order('session_date', { ascending: true });

    // Fetch feeding data for behavioral analysis
    const { data: feedingData } = await supabaseClient
      .from('feeding_schedules')
      .select('*')
      .eq('animal_id', animalId)
      .gte('created_at', analysisStartTime)
      .lte('created_at', analysisEndTime);

    // Fetch medication data (stress from medical procedures)
    const { data: medicationData } = await supabaseClient
      .from('medications')
      .select('*')
      .eq('animal_id', animalId)
      .gte('created_at', analysisStartTime)
      .lte('created_at', analysisEndTime);

    // Analyze Heart Rate Variability
    const hrvMetrics = analyzeHRV(vitalsData || []);
    
    // Analyze Activity Patterns
    const activityIndicators = analyzeActivityPatterns(activityData || [], feedingData || []);
    
    // Analyze Environmental Factors
    const environmentalFactors = analyzeEnvironmentalFactors(vitalsData || []);
    
    // Identify Stress Triggers
    const stressTriggers = identifyStressTriggers(
      vitalsData || [],
      activityData || [],
      medicationData || []
    );
    
    // Calculate Overall Stress Score
    const stressScore = calculateStressScore(hrvMetrics, activityIndicators, environmentalFactors);
    
    // Determine Stress Level
    const stressLevel = determineStressLevel(stressScore);
    
    // Calculate Confidence Level
    const confidenceLevel = calculateConfidenceLevel(
      vitalsData?.length || 0,
      activityData?.length || 0,
      analysisHours
    );
    
    // Generate Recommendations
    const recommendations = generateStressRecommendations(
      stressLevel,
      stressTriggers,
      environmentalFactors
    );

    // Calculate data quality score
    const dataQualityScore = calculateDataQuality(
      vitalsData?.length || 0,
      activityData?.length || 0,
      analysisHours
    );

    // Store stress analysis results
    const { data: stressAnalysis, error: insertError } = await supabaseClient
      .from('stress_analysis')
      .insert({
        animal_id: animalId,
        user_id: user.id,
        stress_level: stressLevel,
        stress_score: stressScore,
        hrv_metrics: hrvMetrics,
        activity_indicators: activityIndicators,
        environmental_factors: environmentalFactors,
        stress_triggers: stressTriggers,
        confidence_level: confidenceLevel,
        analysis_duration_minutes: analysisHours * 60,
        data_quality_score: dataQualityScore,
        recommendations: recommendations
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting stress analysis:', insertError);
      return new Response(JSON.stringify({ error: 'Failed to save analysis' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Store individual stress events if any critical triggers found
    const criticalTriggers = stressTriggers.filter(trigger => trigger.confidence > 0.8);
    for (const trigger of criticalTriggers) {
      await supabaseClient
        .from('stress_events')
        .insert({
          animal_id: animalId,
          user_id: user.id,
          event_type: trigger.type,
          severity_level: stressScore > 80 ? 'critical' : stressScore > 60 ? 'severe' : 'moderate',
          trigger_identified: true,
          trigger_description: trigger.description,
          physiological_response: {
            heart_rate_change: hrvMetrics.stressIndicator,
            hrv_change: hrvMetrics.heartRateVariability
          },
          behavioral_response: {
            activity_change: activityIndicators.activityLevel,
            movement_pattern: activityIndicators.movementPatterns
          }
        });
    }

    return new Response(JSON.stringify({
      success: true,
      analysis: stressAnalysis,
      summary: {
        stress_level: stressLevel,
        stress_score: stressScore,
        confidence: confidenceLevel,
        data_quality: dataQualityScore,
        triggers_found: stressTriggers.length,
        critical_triggers: criticalTriggers.length
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Stress analysis error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
});

// Helper Functions

function analyzeHRV(vitalsData: any[]): HRVMetrics {
  if (vitalsData.length < 2) {
    return {
      rmssd: 0,
      pnn50: 0,
      heartRateVariability: 0,
      averageHeartRate: 0,
      stressIndicator: 50 // Neutral when no data
    };
  }

  const heartRates = vitalsData
    .filter(v => v.heart_rate && v.heart_rate > 0)
    .map(v => v.heart_rate);

  if (heartRates.length < 2) {
    return {
      rmssd: 0,
      pnn50: 0,
      heartRateVariability: 0,
      averageHeartRate: 0,
      stressIndicator: 50
    };
  }

  const averageHeartRate = heartRates.reduce((sum, hr) => sum + hr, 0) / heartRates.length;
  
  // Calculate successive differences
  const successiveDifferences = [];
  for (let i = 1; i < heartRates.length; i++) {
    successiveDifferences.push(Math.abs(heartRates[i] - heartRates[i - 1]));
  }

  // Calculate RMSSD (Root Mean Square of Successive Differences)
  const squaredDifferences = successiveDifferences.map(diff => diff * diff);
  const meanSquaredDiff = squaredDifferences.reduce((sum, sq) => sum + sq, 0) / squaredDifferences.length;
  const rmssd = Math.sqrt(meanSquaredDiff);

  // Calculate pNN50 (percentage of successive RR intervals that differ by more than 50ms)
  // Note: Converting heart rate differences to approximate RR interval differences
  const significantDifferences = successiveDifferences.filter(diff => diff > 5); // Approximate 50ms in HR terms
  const pnn50 = (significantDifferences.length / successiveDifferences.length) * 100;

  // Calculate overall HRV
  const heartRateVariability = rmssd;

  // Calculate stress indicator (inverse relationship with HRV)
  // Higher HRV = Lower stress, Lower HRV = Higher stress
  const normalizedHRV = Math.min(rmssd / 10, 1); // Normalize to 0-1
  const stressIndicator = Math.max(0, Math.min(100, (1 - normalizedHRV) * 100));

  return {
    rmssd: Math.round(rmssd * 100) / 100,
    pnn50: Math.round(pnn50 * 100) / 100,
    heartRateVariability: Math.round(heartRateVariability * 100) / 100,
    averageHeartRate: Math.round(averageHeartRate * 100) / 100,
    stressIndicator: Math.round(stressIndicator * 100) / 100
  };
}

function analyzeActivityPatterns(activityData: any[], feedingData: any[]): ActivityIndicators {
  if (activityData.length === 0) {
    return {
      restlessness: 50,
      movementPatterns: 'normal',
      activityLevel: 50,
      unusualBehaviors: []
    };
  }

  // Analyze training session intensity and frequency
  const intensityLevels = activityData.map(session => {
    const intensity = session.intensity_level || 'medium';
    switch (intensity) {
      case 'low': return 25;
      case 'medium': return 50;
      case 'high': return 75;
      case 'very_high': return 100;
      default: return 50;
    }
  });

  const averageIntensity = intensityLevels.reduce((sum, i) => sum + i, 0) / intensityLevels.length;
  
  // Calculate restlessness based on session frequency and intensity
  const sessionsPerDay = activityData.length / 1; // Assuming 1-day analysis
  const restlessness = Math.min(100, (sessionsPerDay * 20) + (averageIntensity * 0.3));

  // Determine movement patterns
  let movementPatterns = 'normal';
  if (averageIntensity > 80) {
    movementPatterns = 'agitated';
  } else if (averageIntensity < 30) {
    movementPatterns = 'lethargic';
  }

  // Activity level based on session count and intensity
  const activityLevel = Math.min(100, (sessionsPerDay * 25) + (averageIntensity * 0.5));

  // Identify unusual behaviors
  const unusualBehaviors = [];
  if (sessionsPerDay > 5) {
    unusualBehaviors.push('excessive_activity');
  }
  if (sessionsPerDay < 0.5 && feedingData.length > 0) {
    unusualBehaviors.push('reduced_activity');
  }
  if (averageIntensity > 90) {
    unusualBehaviors.push('hyperactivity');
  }

  return {
    restlessness: Math.round(restlessness),
    movementPatterns,
    activityLevel: Math.round(activityLevel),
    unusualBehaviors
  };
}

function analyzeEnvironmentalFactors(vitalsData: any[]): EnvironmentalFactors {
  // Extract temperature data from vitals
  const temperatures = vitalsData
    .filter(v => v.temperature && v.temperature > 0)
    .map(v => v.temperature);

  const averageTemperature = temperatures.length > 0 
    ? temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length
    : 20; // Default temperature

  // Simulate other environmental factors (in real implementation, these would come from sensors)
  const currentHour = new Date().getHours();
  const timeOfDay = currentHour < 6 ? 'night' : 
                   currentHour < 12 ? 'morning' : 
                   currentHour < 18 ? 'afternoon' : 'evening';

  return {
    temperature: Math.round(averageTemperature * 100) / 100,
    humidity: 60, // Simulated
    noiseLevel: 40, // Simulated
    locationChanges: 0, // Would be calculated from GPS data
    weatherConditions: 'clear', // Would come from weather API
    timeOfDay
  };
}

function identifyStressTriggers(
  vitalsData: any[],
  activityData: any[],
  medicationData: any[]
): StressTrigger[] {
  const triggers: StressTrigger[] = [];

  // Check for medication-related stress
  if (medicationData.length > 0) {
    triggers.push({
      type: 'medical_intervention',
      confidence: 0.7,
      description: 'Recent medication administration may cause stress',
      timestamp: medicationData[medicationData.length - 1].created_at
    });
  }

  // Check for sudden heart rate spikes
  if (vitalsData.length > 1) {
    const heartRates = vitalsData.filter(v => v.heart_rate).map(v => v.heart_rate);
    if (heartRates.length > 1) {
      const maxHR = Math.max(...heartRates);
      const avgHR = heartRates.reduce((sum, hr) => sum + hr, 0) / heartRates.length;
      
      if (maxHR > avgHR * 1.3) {
        triggers.push({
          type: 'physiological_stress',
          confidence: 0.8,
          description: 'Significant heart rate elevation detected',
          timestamp: vitalsData.find(v => v.heart_rate === maxHR)?.recorded_at || new Date().toISOString()
        });
      }
    }
  }

  // Check for excessive activity
  if (activityData.length > 3) {
    triggers.push({
      type: 'behavioral_stress',
      confidence: 0.6,
      description: 'Unusual increase in activity levels',
      timestamp: activityData[activityData.length - 1].session_date
    });
  }

  return triggers;
}

function calculateStressScore(
  hrvMetrics: HRVMetrics,
  activityIndicators: ActivityIndicators,
  environmentalFactors: EnvironmentalFactors
): number {
  // Weight different factors
  const hrvWeight = 0.4;
  const activityWeight = 0.3;
  const environmentalWeight = 0.3;

  // HRV stress contribution (higher HRV = lower stress)
  const hrvStress = hrvMetrics.stressIndicator;

  // Activity stress contribution
  const activityStress = (activityIndicators.restlessness * 0.6) + 
                        (activityIndicators.activityLevel > 80 ? 30 : 0);

  // Environmental stress contribution
  const tempStress = Math.abs(environmentalFactors.temperature - 20) * 2; // Optimal temp around 20°C
  const environmentalStress = Math.min(100, tempStress + (environmentalFactors.noiseLevel * 0.5));

  // Calculate weighted stress score
  const totalStress = (hrvStress * hrvWeight) + 
                     (Math.min(100, activityStress) * activityWeight) + 
                     (environmentalStress * environmentalWeight);

  return Math.max(0, Math.min(100, Math.round(totalStress)));
}

function determineStressLevel(stressScore: number): string {
  if (stressScore >= 80) return 'very_high';
  if (stressScore >= 60) return 'high';
  if (stressScore >= 40) return 'moderate';
  if (stressScore >= 20) return 'low';
  return 'very_low';
}

function calculateConfidenceLevel(
  vitalsCount: number,
  activityCount: number,
  analysisHours: number
): number {
  // Minimum data points needed for reliable analysis
  const minVitals = analysisHours * 2; // 2 vitals per hour
  const minActivity = 1; // At least 1 activity session

  const vitalsConfidence = Math.min(1, vitalsCount / minVitals);
  const activityConfidence = Math.min(1, activityCount / minActivity);

  // Overall confidence is the average of data confidence factors
  const overallConfidence = (vitalsConfidence + activityConfidence) / 2;

  return Math.round(overallConfidence * 100) / 100;
}

function generateStressRecommendations(
  stressLevel: string,
  stressTriggers: StressTrigger[],
  environmentalFactors: EnvironmentalFactors
): string {
  const recommendations = [];

  // Base recommendations by stress level
  switch (stressLevel) {
    case 'very_high':
    case 'high':
      recommendations.push('Consider immediate intervention to reduce stress levels');
      recommendations.push('Monitor closely for the next 24 hours');
      recommendations.push('Consult with a veterinarian if stress persists');
      break;
    case 'moderate':
      recommendations.push('Monitor stress levels and identify potential triggers');
      recommendations.push('Consider environmental modifications');
      break;
    case 'low':
    case 'very_low':
      recommendations.push('Stress levels are within normal range');
      recommendations.push('Continue current care routine');
      break;
  }

  // Trigger-specific recommendations
  if (stressTriggers.some(t => t.type === 'medical_intervention')) {
    recommendations.push('Provide extra comfort and monitoring after medical procedures');
  }
  if (stressTriggers.some(t => t.type === 'behavioral_stress')) {
    recommendations.push('Review recent changes in routine or environment');
  }

  // Environmental recommendations
  if (Math.abs(environmentalFactors.temperature - 20) > 5) {
    recommendations.push('Adjust environmental temperature for optimal comfort');
  }
  if (environmentalFactors.noiseLevel > 60) {
    recommendations.push('Reduce noise levels in the animal\'s environment');
  }

  return recommendations.join('. ');
}

function calculateDataQuality(
  vitalsCount: number,
  activityCount: number,
  analysisHours: number
): number {
  const expectedVitals = analysisHours * 2;
  const expectedActivity = 1;

  const vitalsQuality = Math.min(100, (vitalsCount / expectedVitals) * 100);
  const activityQuality = Math.min(100, (activityCount / expectedActivity) * 100);

  return Math.round((vitalsQuality + activityQuality) / 2);
}