// @verify_jwt: true
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface HealthTrendsRequest {
  animal_id: string;
  analysis_period?: number; // Days to analyze (default: 30)
  trend_types?: string[]; // Specific trend types to analyze
}

interface HealthTrendsResponse {
  success: boolean;
  trends_processed?: number;
  trends?: any[];
  error?: string;
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response('ok', {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          'Access-Control-Allow-Methods': 'POST, OPTIONS'
        }
      });
    }

    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        { status: 405, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user from JWT
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ success: false, error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Parse request body
    const { animal_id, analysis_period = 30, trend_types }: HealthTrendsRequest = await req.json();
    
    if (!animal_id) {
      return new Response(
        JSON.stringify({ success: false, error: 'Animal ID is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Verify animal ownership
    const { data: animal, error: animalError } = await supabase
      .from('animals')
      .select('id, name, species, breed, age')
      .eq('id', animal_id)
      .eq('user_id', user.id)
      .single();

    if (animalError || !animal) {
      return new Response(
        JSON.stringify({ success: false, error: 'Animal not found or access denied' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Processing health trends for animal ${animal_id} over ${analysis_period} days`);

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - analysis_period);

    // Fetch comprehensive historical data
    const [vitalsData, healthScoresData, feedingData, medicationData, dehydrationData] = await Promise.all([
      // Vitals data
      supabase
        .from('vitals')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('recorded_at', startDate.toISOString())
        .order('recorded_at', { ascending: true }),
      
      // Health scores data
      supabase
        .from('ai_health_scores')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('score_date', startDate.toISOString().split('T')[0])
        .order('score_date', { ascending: true }),
      
      // Feeding data
      supabase
        .from('feeding_schedules')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true }),
      
      // Medication data
      supabase
        .from('medications')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true }),
      
      // Dehydration data
      supabase
        .from('dehydration_logs')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('recorded_at', startDate.toISOString())
        .order('recorded_at', { ascending: true })
    ]);

    const data = {
      vitals: vitalsData.data || [],
      healthScores: healthScoresData.data || [],
      feeding: feedingData.data || [],
      medications: medicationData.data || [],
      dehydration: dehydrationData.data || []
    };

    // Process different types of trends
    const processedTrends = [];
    
    if (!trend_types || trend_types.includes('health_score')) {
      const healthScoreTrends = analyzeHealthScoreTrends(data.healthScores, analysis_period);
      processedTrends.push(...healthScoreTrends);
    }
    
    if (!trend_types || trend_types.includes('vital_signs')) {
      const vitalsTrends = analyzeVitalSignsTrends(data.vitals, analysis_period);
      processedTrends.push(...vitalsTrends);
    }
    
    if (!trend_types || trend_types.includes('weight')) {
      const weightTrends = analyzeWeightTrends(data.vitals, analysis_period);
      processedTrends.push(...weightTrends);
    }
    
    if (!trend_types || trend_types.includes('activity')) {
      const activityTrends = analyzeActivityTrends(data.vitals, analysis_period);
      processedTrends.push(...activityTrends);
    }
    
    if (!trend_types || trend_types.includes('feeding')) {
      const feedingTrends = analyzeFeedingTrends(data.feeding, analysis_period);
      processedTrends.push(...feedingTrends);
    }
    
    if (!trend_types || trend_types.includes('hydration')) {
      const hydrationTrends = analyzeHydrationTrends(data.dehydration, analysis_period);
      processedTrends.push(...hydrationTrends);
    }

    // Save trends to database
    const savedTrends = [];
    for (const trend of processedTrends) {
      // Deactivate old trends of the same type
      await supabase
        .from('health_trends')
        .update({ is_active: false })
        .eq('animal_id', animal_id)
        .eq('trend_type', trend.trend_type)
        .eq('is_active', true);
      
      // Insert new trend
      const { data, error } = await supabase
        .from('health_trends')
        .insert({
          animal_id,
          user_id: user.id,
          trend_type: trend.trend_type,
          trend_direction: trend.trend_direction,
          trend_strength: trend.trend_strength,
          start_date: trend.start_date,
          end_date: trend.end_date,
          trend_data: trend.trend_data,
          analysis_summary: trend.analysis_summary
        })
        .select()
        .single();
      
      if (!error && data) {
        savedTrends.push(data);
      }
    }

    console.log(`Processed ${processedTrends.length} health trends`);

    const response: HealthTrendsResponse = {
      success: true,
      trends_processed: processedTrends.length,
      trends: savedTrends
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    );

  } catch (error) {
    console.error('Error in process-health-trends:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    );
  }
});

// Trend analysis functions
function analyzeHealthScoreTrends(healthScores: any[], analysisPeriod: number) {
  if (healthScores.length < 3) return [];
  
  const trends = [];
  const scores = healthScores.map(hs => hs.overall_score);
  const dates = healthScores.map(hs => hs.score_date);
  
  // Calculate linear regression for overall trend
  const { slope, correlation } = calculateLinearRegression(
    scores.map((_, i) => i),
    scores
  );
  
  let trendDirection: string;
  let trendStrength = Math.abs(correlation);
  
  if (slope > 2 && correlation > 0.3) {
    trendDirection = 'improving';
  } else if (slope < -2 && correlation < -0.3) {
    trendDirection = 'declining';
  } else if (slope < -5 && correlation < -0.5) {
    trendDirection = 'concerning';
  } else {
    trendDirection = 'stable';
  }
  
  const latestScore = scores[scores.length - 1];
  const firstScore = scores[0];
  const totalChange = latestScore - firstScore;
  
  trends.push({
    trend_type: 'health_score_overall',
    trend_direction: trendDirection,
    trend_strength: trendStrength,
    start_date: dates[0],
    end_date: dates[dates.length - 1],
    trend_data: {
      scores,
      dates,
      slope,
      correlation,
      total_change: totalChange,
      latest_score: latestScore,
      first_score: firstScore
    },
    analysis_summary: generateHealthScoreTrendSummary(trendDirection, totalChange, latestScore, trendStrength)
  });
  
  return trends;
}

function analyzeVitalSignsTrends(vitals: any[], analysisPeriod: number) {
  if (vitals.length < 5) return [];
  
  const trends = [];
  
  // Heart rate trend
  const heartRates = vitals.map(v => v.heart_rate).filter(hr => hr);
  if (heartRates.length >= 5) {
    const hrTrend = analyzeSingleMetricTrend(heartRates, 'heart_rate');
    if (hrTrend) {
      hrTrend.start_date = vitals[0].recorded_at.split('T')[0];
      hrTrend.end_date = vitals[vitals.length - 1].recorded_at.split('T')[0];
      trends.push(hrTrend);
    }
  }
  
  // Temperature trend
  const temperatures = vitals.map(v => v.temperature).filter(t => t);
  if (temperatures.length >= 5) {
    const tempTrend = analyzeSingleMetricTrend(temperatures, 'temperature');
    if (tempTrend) {
      tempTrend.start_date = vitals[0].recorded_at.split('T')[0];
      tempTrend.end_date = vitals[vitals.length - 1].recorded_at.split('T')[0];
      trends.push(tempTrend);
    }
  }
  
  return trends;
}

function analyzeWeightTrends(vitals: any[], analysisPeriod: number) {
  const weights = vitals.map(v => v.weight).filter(w => w);
  if (weights.length < 3) return [];
  
  const weightTrend = analyzeSingleMetricTrend(weights, 'weight');
  if (weightTrend) {
    weightTrend.start_date = vitals[0].recorded_at.split('T')[0];
    weightTrend.end_date = vitals[vitals.length - 1].recorded_at.split('T')[0];
    
    // Add weight-specific analysis
    const firstWeight = weights[0];
    const lastWeight = weights[weights.length - 1];
    const weightChangePercent = ((lastWeight - firstWeight) / firstWeight) * 100;
    
    weightTrend.trend_data.weight_change_percent = weightChangePercent;
    weightTrend.analysis_summary += ` Weight changed by ${weightChangePercent.toFixed(1)}% over the analysis period.`;
    
    return [weightTrend];
  }
  
  return [];
}

function analyzeActivityTrends(vitals: any[], analysisPeriod: number) {
  const heartRates = vitals.map(v => v.heart_rate).filter(hr => hr);
  if (heartRates.length < 5) return [];
  
  // Use heart rate variability as activity proxy
  const dailyVariability = [];
  const dates = [];
  
  // Group by day and calculate daily HR variability
  const dailyGroups = groupVitalsByDay(vitals);
  
  for (const [date, dayVitals] of Object.entries(dailyGroups)) {
    const dayHeartRates = dayVitals.map((v: any) => v.heart_rate).filter((hr: any) => hr);
    if (dayHeartRates.length >= 2) {
      const variance = calculateVariance(dayHeartRates);
      dailyVariability.push(variance);
      dates.push(date);
    }
  }
  
  if (dailyVariability.length < 3) return [];
  
  const { slope, correlation } = calculateLinearRegression(
    dailyVariability.map((_, i) => i),
    dailyVariability
  );
  
  let trendDirection: string;
  if (slope > 5 && correlation > 0.3) {
    trendDirection = 'improving'; // Increasing variability = more activity
  } else if (slope < -5 && correlation < -0.3) {
    trendDirection = 'declining'; // Decreasing variability = less activity
  } else {
    trendDirection = 'stable';
  }
  
  return [{
    trend_type: 'activity_level',
    trend_direction: trendDirection,
    trend_strength: Math.abs(correlation),
    start_date: dates[0],
    end_date: dates[dates.length - 1],
    trend_data: {
      daily_variability: dailyVariability,
      dates,
      slope,
      correlation
    },
    analysis_summary: `Activity level trend shows ${trendDirection} pattern based on heart rate variability analysis.`
  }];
}

function analyzeFeedingTrends(feeding: any[], analysisPeriod: number) {
  if (feeding.length < 3) return [];
  
  // Group feeding by day
  const dailyFeeding = groupFeedingByDay(feeding);
  const dates = Object.keys(dailyFeeding).sort();
  const dailyCounts = dates.map(date => dailyFeeding[date].length);
  
  if (dailyCounts.length < 3) return [];
  
  const { slope, correlation } = calculateLinearRegression(
    dailyCounts.map((_, i) => i),
    dailyCounts
  );
  
  let trendDirection: string;
  if (slope > 0.1 && correlation > 0.3) {
    trendDirection = 'improving';
  } else if (slope < -0.1 && correlation < -0.3) {
    trendDirection = 'declining';
  } else {
    trendDirection = 'stable';
  }
  
  const avgDailyFeeding = dailyCounts.reduce((sum, count) => sum + count, 0) / dailyCounts.length;
  
  return [{
    trend_type: 'feeding_frequency',
    trend_direction: trendDirection,
    trend_strength: Math.abs(correlation),
    start_date: dates[0],
    end_date: dates[dates.length - 1],
    trend_data: {
      daily_counts: dailyCounts,
      dates,
      slope,
      correlation,
      avg_daily_feeding: avgDailyFeeding
    },
    analysis_summary: `Feeding frequency shows ${trendDirection} trend with average ${avgDailyFeeding.toFixed(1)} feedings per day.`
  }];
}

function analyzeHydrationTrends(dehydration: any[], analysisPeriod: number) {
  if (dehydration.length < 3) return [];
  
  const hydrationLevels = dehydration.map(d => d.hydration_level);
  const dates = dehydration.map(d => d.recorded_at.split('T')[0]);
  
  const { slope, correlation } = calculateLinearRegression(
    hydrationLevels.map((_, i) => i),
    hydrationLevels
  );
  
  let trendDirection: string;
  if (slope > 1 && correlation > 0.3) {
    trendDirection = 'improving';
  } else if (slope < -1 && correlation < -0.3) {
    trendDirection = 'declining';
  } else if (slope < -2 && correlation < -0.5) {
    trendDirection = 'concerning';
  } else {
    trendDirection = 'stable';
  }
  
  const avgHydration = hydrationLevels.reduce((sum, level) => sum + level, 0) / hydrationLevels.length;
  const latestHydration = hydrationLevels[hydrationLevels.length - 1];
  
  return [{
    trend_type: 'hydration_level',
    trend_direction: trendDirection,
    trend_strength: Math.abs(correlation),
    start_date: dates[0],
    end_date: dates[dates.length - 1],
    trend_data: {
      hydration_levels: hydrationLevels,
      dates,
      slope,
      correlation,
      avg_hydration: avgHydration,
      latest_hydration: latestHydration
    },
    analysis_summary: `Hydration levels show ${trendDirection} trend. Average: ${avgHydration.toFixed(1)}%, Latest: ${latestHydration}%.`
  }];
}

// Helper functions
function analyzeSingleMetricTrend(values: number[], metricType: string) {
  const { slope, correlation } = calculateLinearRegression(
    values.map((_, i) => i),
    values
  );
  
  let trendDirection: string;
  let thresholds = getMetricThresholds(metricType);
  
  if (slope > thresholds.improving && correlation > 0.3) {
    trendDirection = 'improving';
  } else if (slope < thresholds.declining && correlation < -0.3) {
    trendDirection = 'declining';
  } else if (slope < thresholds.concerning && correlation < -0.5) {
    trendDirection = 'concerning';
  } else {
    trendDirection = 'stable';
  }
  
  const avgValue = values.reduce((sum, val) => sum + val, 0) / values.length;
  const latestValue = values[values.length - 1];
  const firstValue = values[0];
  
  return {
    trend_type: metricType,
    trend_direction: trendDirection,
    trend_strength: Math.abs(correlation),
    trend_data: {
      values,
      slope,
      correlation,
      avg_value: avgValue,
      latest_value: latestValue,
      first_value: firstValue,
      total_change: latestValue - firstValue
    },
    analysis_summary: `${metricType.replace('_', ' ')} shows ${trendDirection} trend. Average: ${avgValue.toFixed(1)}, Latest: ${latestValue}.`
  };
}

function calculateLinearRegression(x: number[], y: number[]) {
  const n = x.length;
  const sumX = x.reduce((sum, val) => sum + val, 0);
  const sumY = y.reduce((sum, val) => sum + val, 0);
  const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
  const sumXX = x.reduce((sum, val) => sum + val * val, 0);
  const sumYY = y.reduce((sum, val) => sum + val * val, 0);
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  const correlation = (n * sumXY - sumX * sumY) / Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));
  
  return { slope, correlation };
}

function calculateVariance(values: number[]) {
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  return variance;
}

function groupVitalsByDay(vitals: any[]) {
  const groups: { [key: string]: any[] } = {};
  
  vitals.forEach(vital => {
    const date = vital.recorded_at.split('T')[0];
    if (!groups[date]) groups[date] = [];
    groups[date].push(vital);
  });
  
  return groups;
}

function groupFeedingByDay(feeding: any[]) {
  const groups: { [key: string]: any[] } = {};
  
  feeding.forEach(feed => {
    const date = (feed.feeding_time || feed.created_at).split('T')[0];
    if (!groups[date]) groups[date] = [];
    groups[date].push(feed);
  });
  
  return groups;
}

function getMetricThresholds(metricType: string) {
  const thresholds: { [key: string]: any } = {
    heart_rate: { improving: -1, declining: 2, concerning: 5 },
    temperature: { improving: -0.1, declining: 0.1, concerning: 0.2 },
    weight: { improving: 0.1, declining: -0.1, concerning: -0.5 }
  };
  
  return thresholds[metricType] || { improving: 1, declining: -1, concerning: -2 };
}

function generateHealthScoreTrendSummary(direction: string, totalChange: number, latestScore: number, strength: number) {
  let summary = `Health score trend is ${direction}`;
  
  if (direction === 'improving') {
    summary += ` with a ${totalChange.toFixed(0)} point improvement. Current score: ${latestScore}/100.`;
  } else if (direction === 'declining' || direction === 'concerning') {
    summary += ` with a ${Math.abs(totalChange).toFixed(0)} point decline. Current score: ${latestScore}/100.`;
  } else {
    summary += ` with minimal change. Current score: ${latestScore}/100.`;
  }
  
  if (strength > 0.7) {
    summary += ' This trend is highly reliable.';
  } else if (strength > 0.5) {
    summary += ' This trend is moderately reliable.';
  }
  
  return summary;
}