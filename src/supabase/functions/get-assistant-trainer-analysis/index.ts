// @verify_jwt: true
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface AnimalProfile {
  name: string;
  age: number;
  breed: string;
  gender: string;
  healthFlags?: string[];
}

interface VitalsData {
  heartRate: number;
  temperature: number;
  respirationRate: number;
  weight: number;
}

interface TrainingSession {
  distance: number;
  speed: number;
  duration: string;
  restTime: string;
  intensityLabel: string;
}

interface FastAPIRequest {
  animal: AnimalProfile;
  vitals: VitalsData;
  session: TrainingSession;
}

interface FastAPIResponse {
  healthAssessment: {
    text: string;
    severity: string;
  };
  trainingPlan: {
    text: string;
    type?: string;
    data?: any;
  };
  readinessScore: {
    value: number;
    category?: string;
  };
  tips: Array<{
    text: string;
    category?: string;
    priority?: string;
  }>;
}

serve(async (req) => {
  // Set CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify JWT and get user
    const jwt = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(jwt);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Parse request body
    const { animalId } = await req.json();
    
    if (!animalId) {
      return new Response(
        JSON.stringify({ error: 'animalId is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Fetching AI analysis for animal ${animalId} for user ${user.id}`);

    // Fetch animal profile
    const { data: animal, error: animalError } = await supabase
      .from('animals')
      .select('*')
      .eq('id', animalId)
      .eq('user_id', user.id)
      .single();

    if (animalError || !animal) {
      console.error('Animal fetch error:', animalError);
      return new Response(
        JSON.stringify({ error: 'Animal not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Fetch latest vitals
    const { data: vitals, error: vitalsError } = await supabase
      .from('vitals')
      .select('*')
      .eq('animal_id', animalId)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (vitalsError || !vitals) {
      console.error('Vitals fetch error:', vitalsError);
      return new Response(
        JSON.stringify({ error: 'No vitals data found for this animal' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Fetch latest training session with enhanced automated data
    const { data: session, error: sessionError } = await supabase
      .from('training_sessions')
      .select(`
        *,
        duration_seconds,
        avg_speed,
        max_speed,
        avg_intensity,
        max_intensity,
        calories_burned,
        heart_rate_avg,
        heart_rate_max,
        elevation_gain,
        is_automated,
        session_quality,
        gps_track,
        weather_conditions,
        session_data
      `)
      .eq('animal_id', animalId)
      .eq('user_id', user.id)
      .order('session_timestamp', { ascending: false })
      .limit(1)
      .single();

    if (sessionError || !session) {
      console.error('Training session fetch error:', sessionError);
      return new Response(
        JSON.stringify({ error: 'No training session data found for this animal' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Construct FastAPI request payload
    const fastApiPayload: FastAPIRequest = {
      animal: {
        name: animal.name,
        age: animal.age,
        breed: animal.breed,
        gender: animal.gender,
        healthFlags: [] // You can extend this based on your animal data
      },
      vitals: {
        heartRate: vitals.heart_rate,
        temperature: vitals.temperature,
        respirationRate: vitals.respiration_rate,
        weight: vitals.weight
      },
      session: {
        distance: session.distance,
        speed: session.speed,
        duration: session.duration,
        restTime: session.rest_time,
        intensityLabel: session.intensity_label
      }
    };

    console.log('Sending payload to FastAPI:', JSON.stringify(fastApiPayload, null, 2));

    // Get FastAPI server URL from secrets (you'll need to add this)
    const fastApiUrl = Deno.env.get('FASTAPI_SERVER_URL');
    const fastApiKey = Deno.env.get('FASTAPI_API_KEY');

    if (!fastApiUrl) {
      return new Response(
        JSON.stringify({ error: 'FastAPI server URL not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Call FastAPI server
    const fastApiHeaders: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    
    if (fastApiKey) {
      fastApiHeaders['Authorization'] = `Bearer ${fastApiKey}`;
    }

    const fastApiResponse = await fetch(fastApiUrl, {
      method: 'POST',
      headers: fastApiHeaders,
      body: JSON.stringify(fastApiPayload)
    });

    if (!fastApiResponse.ok) {
      const errorText = await fastApiResponse.text();
      console.error('FastAPI error:', errorText);
      return new Response(
        JSON.stringify({ error: 'Failed to get AI analysis', details: errorText }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const aiResponse: FastAPIResponse = await fastApiResponse.json();
    console.log('Received AI response:', JSON.stringify(aiResponse, null, 2));

    // Store AI outputs in Supabase
    const now = new Date().toISOString();

    // Store health assessment
    const { data: healthAssessment, error: healthError } = await supabase
      .from('ai_health_assessments')
      .insert({
        animal_id: animalId,
        user_id: user.id,
        assessment_text: aiResponse.healthAssessment.text,
        severity_level: aiResponse.healthAssessment.severity,
        generated_at: now
      })
      .select()
      .single();

    if (healthError) {
      console.error('Health assessment insert error:', healthError);
    }

    // Store training plan
    const { data: trainingPlan, error: planError } = await supabase
      .from('ai_training_plans')
      .insert({
        animal_id: animalId,
        user_id: user.id,
        plan_text: aiResponse.trainingPlan.text,
        plan_type: aiResponse.trainingPlan.type || 'general',
        plan_data: aiResponse.trainingPlan.data || null,
        generated_at: now
      })
      .select()
      .single();

    if (planError) {
      console.error('Training plan insert error:', planError);
    }

    // Store readiness score
    const { data: readinessScore, error: scoreError } = await supabase
      .from('ai_readiness_scores')
      .insert({
        animal_id: animalId,
        user_id: user.id,
        score_value: aiResponse.readinessScore.value,
        score_category: aiResponse.readinessScore.category || 'general',
        generated_at: now
      })
      .select()
      .single();

    if (scoreError) {
      console.error('Readiness score insert error:', scoreError);
    }

    // Store coaching tips
    const tipInserts = aiResponse.tips.map(tip => ({
      animal_id: animalId,
      user_id: user.id,
      tip_text: tip.text,
      category: tip.category || 'general',
      priority: tip.priority || 'normal',
      generated_at: now
    }));

    const { data: coachingTips, error: tipsError } = await supabase
      .from('ai_coaching_tips')
      .insert(tipInserts)
      .select();

    if (tipsError) {
      console.error('Coaching tips insert error:', tipsError);
    }

    // Return success response with created record IDs
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          healthAssessmentId: healthAssessment?.assessment_id,
          trainingPlanId: trainingPlan?.plan_id,
          readinessScoreId: readinessScore?.score_id,
          coachingTipIds: coachingTips?.map(tip => tip.tip_id) || [],
          analysis: aiResponse
        }
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});