// Function ID: secure-map-proxy-001
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// CORS headers for client access
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
};

/**
 * @magic_description Secure Google Maps API proxy
 * Protects API key by handling Google Maps requests server-side
 */
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        {
          status: 405,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Get Google Maps API key from environment
    const googleMapsApiKey = Deno.env.get('GOOGLE_MAPS_API_KEY');
    if (!googleMapsApiKey) {
      console.error('GOOGLE_MAPS_API_KEY not found in environment');
      return new Response(
        JSON.stringify({ error: 'API key not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Parse URL parameters
    const url = new URL(req.url);
    const latitude = url.searchParams.get('latitude');
    const longitude = url.searchParams.get('longitude');
    const zoom = url.searchParams.get('zoom') || '15';
    const width = url.searchParams.get('width') || '400';
    const height = url.searchParams.get('height') || '300';
    const maptype = url.searchParams.get('maptype') || 'roadmap';
    const animalName = url.searchParams.get('animalName') || 'Animal';

    // Validate required parameters
    if (!latitude || !longitude) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: latitude, longitude' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate coordinate ranges
    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);
    if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return new Response(
        JSON.stringify({ error: 'Invalid coordinate values' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate dimensions
    const w = parseInt(width);
    const h = parseInt(height);
    if (isNaN(w) || isNaN(h) || w <= 0 || h <= 0 || w > 2048 || h > 2048) {
      return new Response(
        JSON.stringify({ error: 'Invalid dimensions (must be 1-2048)' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Validate zoom level
    const z = parseInt(zoom);
    if (isNaN(z) || z < 1 || z > 20) {
      return new Response(
        JSON.stringify({ error: 'Invalid zoom level (must be 1-20)' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Construct Google Maps Static API URL
    const size = `${width}x${height}`;
    const center = `${latitude},${longitude}`;
    const markerLabel = encodeURIComponent(animalName.charAt(0).toUpperCase());
    const markers = `color:red%7Clabel:${markerLabel}%7C${latitude},${longitude}`;
    
    // Handle dark mode styling for roadmap
    let mapTypeParam = maptype;
    if (maptype.includes('dark') || maptype.includes('night')) {
      mapTypeParam = 'roadmap&style=feature:all|element:geometry|color:0x212121&style=feature:all|element:labels.icon|visibility:off&style=feature:all|element:labels.text.fill|color:0x757575&style=feature:all|element:labels.text.stroke|color:0x212121&style=feature:landscape|element:geometry|color:0x212121&style=feature:road|element:geometry.fill|color:0x2c2c2c&style=feature:road|element:labels.text.fill|color:0x8a8a8a&style=feature:road.arterial|element:geometry|color:0x373737&style=feature:road.highway|element:geometry|color:0x3c3c3c&style=feature:road.highway.controlled_access|element:geometry|color:0x4e4e4e&style=feature:road.local|element:geometry|color:0x2c2c2c&style=feature:transit|element:geometry|color:0x2f3948&style=feature:water|element:geometry|color:0x17263c';
    }

    const googleMapsUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${center}&zoom=${zoom}&size=${size}&maptype=${mapTypeParam}&markers=${markers}&key=${googleMapsApiKey}`;

    console.log('Fetching map from Google Maps API:', {
      center,
      zoom,
      size,
      maptype: mapTypeParam,
      animalName
    });

    // Fetch map image from Google Maps API
    const googleResponse = await fetch(googleMapsUrl);
    
    if (!googleResponse.ok) {
      console.error('Google Maps API error:', googleResponse.status, googleResponse.statusText);
      return new Response(
        JSON.stringify({ 
          error: 'Failed to fetch map from Google Maps API',
          status: googleResponse.status 
        }),
        {
          status: 502,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    // Get the image data
    const imageData = await googleResponse.arrayBuffer();
    const contentType = googleResponse.headers.get('Content-Type') || 'image/png';

    console.log('Successfully proxied map image:', {
      size: imageData.byteLength,
      contentType
    });

    // Return the image with proper headers
    return new Response(imageData, {
      headers: {
        ...corsHeaders,
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'Content-Length': imageData.byteLength.toString(),
      },
      status: 200,
    });

  } catch (error) {
    console.error('Edge function error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});