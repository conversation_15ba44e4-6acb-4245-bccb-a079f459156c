import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { encode as base32Encode } from "https://deno.land/std@0.168.0/encoding/base32.ts";
import { encode as base64Encode } from "https://deno.land/std@0.168.0/encoding/base64.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

interface MfaEnrollRequest {
  action: 'generate_secret' | 'verify_setup';
  verification_code?: string;
}

interface MfaEnrollResponse {
  success: boolean;
  secret?: string;
  qr_code_url?: string;
  backup_codes?: string[];
  error?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ success: false, error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify JWT and get user
    const jwt = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(jwt);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return new Response(
        JSON.stringify({ success: false, error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Parse request body
    const { action, verification_code }: MfaEnrollRequest = await req.json();
    
    if (!action) {
      return new Response(
        JSON.stringify({ success: false, error: 'Action is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`MFA enrollment action: ${action} for user ${user.id}`);

    if (action === 'generate_secret') {
      // Generate a new TOTP secret
      const secret = generateTOTPSecret();
      const appName = 'Animal Tracker';
      const userEmail = user.email || '<EMAIL>';
      
      // Create QR code URL for authenticator apps
      const qrCodeUrl = `otpauth://totp/${encodeURIComponent(appName)}:${encodeURIComponent(userEmail)}?secret=${secret}&issuer=${encodeURIComponent(appName)}`;
      
      // Generate backup codes
      const backupCodes = generateBackupCodes();
      
      // Store the secret temporarily (not yet enabled)
      const { error: upsertError } = await supabase
        .from('user_mfa_settings')
        .upsert({
          user_id: user.id,
          totp_secret: secret,
          backup_codes: backupCodes,
          is_enabled: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (upsertError) {
        console.error('Error storing MFA secret:', upsertError);
        return new Response(
          JSON.stringify({ success: false, error: 'Failed to store MFA secret' }),
          { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      const response: MfaEnrollResponse = {
        success: true,
        secret,
        qr_code_url: qrCodeUrl,
        backup_codes: backupCodes
      };

      return new Response(
        JSON.stringify(response),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );

    } else if (action === 'verify_setup') {
      if (!verification_code) {
        return new Response(
          JSON.stringify({ success: false, error: 'Verification code is required' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      // Get the stored secret
      const { data: mfaSettings, error: fetchError } = await supabase
        .from('user_mfa_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (fetchError || !mfaSettings) {
        return new Response(
          JSON.stringify({ success: false, error: 'MFA setup not found. Please generate a new secret.' }),
          { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      // Verify the TOTP code
      const isValidCode = await verifyTOTPCode(mfaSettings.totp_secret, verification_code);
      
      if (!isValidCode) {
        return new Response(
          JSON.stringify({ success: false, error: 'Invalid verification code. Please try again.' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      // Enable MFA for the user
      const { error: enableError } = await supabase
        .from('user_mfa_settings')
        .update({
          is_enabled: true,
          enabled_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id);

      if (enableError) {
        console.error('Error enabling MFA:', enableError);
        return new Response(
          JSON.stringify({ success: false, error: 'Failed to enable MFA' }),
          { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      // Update user metadata to indicate MFA is enabled
      const { error: updateUserError } = await supabase.auth.admin.updateUserById(
        user.id,
        {
          user_metadata: {
            ...user.user_metadata,
            mfa_enabled: true
          }
        }
      );

      if (updateUserError) {
        console.error('Error updating user metadata:', updateUserError);
      }

      console.log(`MFA successfully enabled for user ${user.id}`);

      const response: MfaEnrollResponse = {
        success: true
      };

      return new Response(
        JSON.stringify(response),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    return new Response(
      JSON.stringify({ success: false, error: 'Invalid action' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('MFA enrollment error:', error);
    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

// Helper Functions

function generateTOTPSecret(): string {
  // Generate a 32-byte random secret
  const bytes = new Uint8Array(32);
  crypto.getRandomValues(bytes);
  
  // Encode as base32 (required for TOTP)
  return base32Encode(bytes).replace(/=/g, ''); // Remove padding
}

function generateBackupCodes(): string[] {
  const codes: string[] = [];
  
  for (let i = 0; i < 10; i++) {
    // Generate 8-digit backup codes
    const code = Math.random().toString().slice(2, 10);
    codes.push(code);
  }
  
  return codes;
}

async function verifyTOTPCode(secret: string, code: string): Promise<boolean> {
  try {
    // Get current time in 30-second intervals (TOTP standard)
    const timeStep = Math.floor(Date.now() / 1000 / 30);
    
    // Check current time step and previous/next for clock drift tolerance
    for (let i = -1; i <= 1; i++) {
      const testTimeStep = timeStep + i;
      const expectedCode = await generateTOTPCode(secret, testTimeStep);
      
      if (expectedCode === code) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error verifying TOTP code:', error);
    return false;
  }
}

async function generateTOTPCode(secret: string, timeStep: number): Promise<string> {
  // Decode base32 secret
  const secretBytes = base32Decode(secret);
  
  // Convert time step to 8-byte big-endian
  const timeBytes = new ArrayBuffer(8);
  const timeView = new DataView(timeBytes);
  timeView.setUint32(4, timeStep, false); // Big-endian
  
  // Import secret as HMAC key
  const key = await crypto.subtle.importKey(
    'raw',
    secretBytes,
    { name: 'HMAC', hash: 'SHA-1' },
    false,
    ['sign']
  );
  
  // Generate HMAC
  const hmac = await crypto.subtle.sign('HMAC', key, timeBytes);
  const hmacBytes = new Uint8Array(hmac);
  
  // Dynamic truncation (RFC 4226)
  const offset = hmacBytes[19] & 0x0f;
  const code = (
    ((hmacBytes[offset] & 0x7f) << 24) |
    ((hmacBytes[offset + 1] & 0xff) << 16) |
    ((hmacBytes[offset + 2] & 0xff) << 8) |
    (hmacBytes[offset + 3] & 0xff)
  ) % 1000000;
  
  // Pad with leading zeros to ensure 6 digits
  return code.toString().padStart(6, '0');
}

function base32Decode(encoded: string): Uint8Array {
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
  const cleanInput = encoded.toUpperCase().replace(/[^A-Z2-7]/g, '');
  
  let bits = '';
  for (const char of cleanInput) {
    const index = alphabet.indexOf(char);
    if (index === -1) continue;
    bits += index.toString(2).padStart(5, '0');
  }
  
  const bytes = [];
  for (let i = 0; i < bits.length; i += 8) {
    const byte = bits.slice(i, i + 8);
    if (byte.length === 8) {
      bytes.push(parseInt(byte, 2));
    }
  }
  
  return new Uint8Array(bytes);
}