import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface ImageAnalysisRequest {
  animalId: string;
  imageUrl: string;
  imageType: string;
  analysisTypes: string[];
}

interface ImageAnalysisResult {
  bodyConditionScore?: number;
  healthIndicators?: any;
  behavioralMarkers?: any;
  symptomDetection?: any;
  facialFeatures?: any;
  emotionAnalysis?: any;
  identityConfidence?: number;
  imageQualityScore?: number;
  confidenceScore?: number;
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user ID from JWT
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Authorization header required');
    }

    const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
    if (!user) {
      throw new Error('Invalid authentication');
    }

    // Get request data
    const { animalId, imageUrl, imageType, analysisTypes }: ImageAnalysisRequest = await req.json();

    console.log(`Starting AI image analysis for animal ${animalId}, type: ${imageType}`);

    // Validate animal exists and belongs to user
    const { data: animal, error: animalError } = await supabase
      .from('animals')
      .select('id, name, species, breed')
      .eq('id', animalId)
      .eq('user_id', user.id)
      .single();

    if (animalError || !animal) {
      throw new Error(`Animal not found or access denied: ${animalError?.message}`);
    }

    // Create analysis record
    const { data: analysisRecord, error: recordError } = await supabase
      .from('ai_image_analysis')
      .insert({
        animal_id: animalId,
        user_id: user.id,
        image_url: imageUrl,
        image_type: imageType,
        analysis_status: 'processing',
        image_metadata: {
          analysis_types: analysisTypes,
          animal_info: {
            species: animal.species,
            breed: animal.breed
          }
        }
      })
      .select()
      .single();

    if (recordError) {
      throw new Error(`Failed to create analysis record: ${recordError.message}`);
    }

    const startTime = Date.now();

    try {
      // Perform AI image analysis
      const analysisResult = await performImageAnalysis(imageUrl, imageType, analysisTypes, animal);
      
      const processingTime = Date.now() - startTime;

      // Update analysis record with results
      const { data: completedAnalysis, error: updateError } = await supabase
        .from('ai_image_analysis')
        .update({
          analysis_status: 'completed',
          confidence_score: analysisResult.confidenceScore,
          body_condition_score: analysisResult.bodyConditionScore,
          health_indicators: analysisResult.healthIndicators,
          behavioral_markers: analysisResult.behavioralMarkers,
          symptom_detection: analysisResult.symptomDetection,
          facial_features: analysisResult.facialFeatures,
          emotion_analysis: analysisResult.emotionAnalysis,
          identity_confidence: analysisResult.identityConfidence,
          image_quality_score: analysisResult.imageQualityScore,
          model_version: 'v2.1.0',
          processing_time_ms: processingTime,
          lighting_conditions: analysisResult.lightingConditions || 'unknown',
          image_clarity: analysisResult.imageClarity || 'unknown'
        })
        .eq('id', analysisRecord.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating analysis record:', updateError);
      }

      // Generate AI insights based on analysis
      if (analysisResult.healthIndicators || analysisResult.symptomDetection) {
        await generateHealthInsights(supabase, animalId, user.id, analysisResult, imageType);
      }

      // Check for urgent health concerns
      const urgentConcerns = await checkForUrgentConcerns(analysisResult);
      if (urgentConcerns.length > 0) {
        await createUrgentAlerts(supabase, animalId, user.id, urgentConcerns);
      }

      console.log(`AI image analysis completed for animal ${animalId} in ${processingTime}ms`);

      return new Response(
        JSON.stringify({
          success: true,
          analysisId: analysisRecord.id,
          results: analysisResult,
          processingTimeMs: processingTime,
          urgentConcerns: urgentConcerns,
          recommendations: await generateRecommendations(analysisResult, imageType)
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );

    } catch (analysisError) {
      // Update record with error status
      await supabase
        .from('ai_image_analysis')
        .update({
          analysis_status: 'failed'
        })
        .eq('id', analysisRecord.id);

      throw analysisError;
    }

  } catch (error) {
    console.error('AI image analysis error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'AI image analysis failed'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});

async function performImageAnalysis(imageUrl: string, imageType: string, analysisTypes: string[], animal: any): Promise<ImageAnalysisResult> {
  const result: ImageAnalysisResult = {};
  
  // Simulate AI image analysis (in production, this would call actual AI services)
  console.log(`Analyzing image: ${imageUrl} for types: ${analysisTypes.join(', ')}`);
  
  // Image quality assessment
  result.imageQualityScore = await assessImageQuality(imageUrl);
  
  // Body condition scoring
  if (analysisTypes.includes('body_condition') || imageType === 'body_condition') {
    result.bodyConditionScore = await analyzeBodyCondition(imageUrl, animal.species);
  }
  
  // Health indicator detection
  if (analysisTypes.includes('health_indicators') || imageType === 'health_check') {
    result.healthIndicators = await detectHealthIndicators(imageUrl, animal);
  }
  
  // Behavioral analysis
  if (analysisTypes.includes('behavior') || imageType === 'behavior') {
    result.behavioralMarkers = await analyzeBehavior(imageUrl, animal.species);
  }
  
  // Symptom detection
  if (analysisTypes.includes('symptoms') || imageType === 'symptom') {
    result.symptomDetection = await detectSymptoms(imageUrl, animal);
  }
  
  // Facial recognition and emotion analysis
  if (analysisTypes.includes('facial_recognition')) {
    const facialAnalysis = await analyzeFacialFeatures(imageUrl, animal.species);
    result.facialFeatures = facialAnalysis.features;
    result.emotionAnalysis = facialAnalysis.emotions;
    result.identityConfidence = facialAnalysis.identityConfidence;
  }
  
  // Calculate overall confidence score
  result.confidenceScore = calculateOverallConfidence(result);
  
  return result;
}

async function assessImageQuality(imageUrl: string): Promise<number> {
  // Simulate image quality assessment
  // In production, this would analyze resolution, lighting, blur, etc.
  
  const qualityFactors = {
    resolution: Math.random() * 0.3 + 0.7, // 0.7-1.0
    lighting: Math.random() * 0.2 + 0.8,   // 0.8-1.0
    clarity: Math.random() * 0.25 + 0.75,  // 0.75-1.0
    composition: Math.random() * 0.2 + 0.8  // 0.8-1.0
  };
  
  const overallQuality = (qualityFactors.resolution + qualityFactors.lighting + 
                         qualityFactors.clarity + qualityFactors.composition) / 4;
  
  return Math.round(overallQuality * 100) / 100;
}

async function analyzeBodyCondition(imageUrl: string, species: string): Promise<number> {
  // Simulate body condition scoring (1-9 scale)
  // In production, this would use computer vision to assess body fat, muscle tone, etc.
  
  const baseScore = Math.random() * 3 + 4; // 4-7 range (normal)
  
  // Adjust based on species
  let speciesAdjustment = 0;
  if (species === 'dog') {
    speciesAdjustment = Math.random() * 0.5 - 0.25;
  } else if (species === 'cat') {
    speciesAdjustment = Math.random() * 0.3 - 0.15;
  }
  
  const finalScore = Math.max(1, Math.min(9, baseScore + speciesAdjustment));
  return Math.round(finalScore * 10) / 10;
}

async function detectHealthIndicators(imageUrl: string, animal: any): Promise<any> {
  // Simulate health indicator detection
  const indicators = {
    coat_condition: {
      quality: ['excellent', 'good', 'fair', 'poor'][Math.floor(Math.random() * 4)],
      shine: Math.random() > 0.3,
      thickness: ['normal', 'thin', 'thick'][Math.floor(Math.random() * 3)],
      shedding: ['minimal', 'normal', 'excessive'][Math.floor(Math.random() * 3)]
    },
    eye_health: {
      clarity: Math.random() > 0.2,
      discharge: Math.random() < 0.1,
      redness: Math.random() < 0.15,
      alertness: Math.random() > 0.1
    },
    nose_condition: {
      moisture: Math.random() > 0.2,
      discharge: Math.random() < 0.1,
      color: ['normal', 'pale', 'dark'][Math.floor(Math.random() * 3)]
    },
    posture: {
      stance: ['normal', 'hunched', 'stiff', 'favoring_limb'][Math.floor(Math.random() * 4)],
      head_position: ['normal', 'lowered', 'tilted'][Math.floor(Math.random() * 3)],
      tail_position: ['normal', 'lowered', 'tucked'][Math.floor(Math.random() * 3)]
    }
  };
  
  return indicators;
}

async function analyzeBehavior(imageUrl: string, species: string): Promise<any> {
  // Simulate behavioral analysis
  const behaviors = {
    activity_level: ['low', 'moderate', 'high'][Math.floor(Math.random() * 3)],
    alertness: ['low', 'normal', 'high'][Math.floor(Math.random() * 3)],
    social_behavior: ['withdrawn', 'normal', 'seeking_attention'][Math.floor(Math.random() * 3)],
    stress_indicators: {
      panting: Math.random() < 0.2,
      pacing: Math.random() < 0.1,
      hiding: Math.random() < 0.15,
      excessive_grooming: Math.random() < 0.1
    },
    comfort_level: Math.random() * 10,
    interaction_willingness: Math.random() > 0.2
  };
  
  return behaviors;
}

async function detectSymptoms(imageUrl: string, animal: any): Promise<any> {
  // Simulate symptom detection
  const symptoms = {
    visible_symptoms: [],
    severity_assessment: {},
    confidence_scores: {}
  };
  
  const possibleSymptoms = [
    'lethargy', 'limping', 'skin_irritation', 'eye_discharge',
    'nasal_discharge', 'labored_breathing', 'unusual_posture',
    'weight_loss', 'weight_gain', 'coat_changes'
  ];
  
  // Randomly detect some symptoms with low probability
  for (const symptom of possibleSymptoms) {
    if (Math.random() < 0.1) { // 10% chance of detecting each symptom
      symptoms.visible_symptoms.push(symptom);
      symptoms.severity_assessment[symptom] = ['mild', 'moderate', 'severe'][Math.floor(Math.random() * 3)];
      symptoms.confidence_scores[symptom] = Math.random() * 0.3 + 0.7; // 0.7-1.0
    }
  }
  
  return symptoms;
}

async function analyzeFacialFeatures(imageUrl: string, species: string): Promise<any> {
  // Simulate facial recognition and emotion analysis
  const analysis = {
    features: {
      eye_shape: 'normal',
      ear_position: 'alert',
      mouth_expression: 'neutral',
      facial_symmetry: Math.random() > 0.1
    },
    emotions: {
      happiness: Math.random() * 0.4 + 0.3,
      alertness: Math.random() * 0.5 + 0.5,
      stress: Math.random() * 0.3,
      comfort: Math.random() * 0.4 + 0.4,
      pain_indicators: Math.random() * 0.2
    },
    identityConfidence: Math.random() * 0.2 + 0.8 // 0.8-1.0
  };
  
  return analysis;
}

function calculateOverallConfidence(result: ImageAnalysisResult): number {
  let totalConfidence = 0;
  let factorCount = 0;
  
  if (result.imageQualityScore) {
    totalConfidence += result.imageQualityScore;
    factorCount++;
  }
  
  if (result.identityConfidence) {
    totalConfidence += result.identityConfidence;
    factorCount++;
  }
  
  // Add confidence from symptom detection
  if (result.symptomDetection?.confidence_scores) {
    const symptomConfidences = Object.values(result.symptomDetection.confidence_scores) as number[];
    if (symptomConfidences.length > 0) {
      totalConfidence += symptomConfidences.reduce((a, b) => a + b, 0) / symptomConfidences.length;
      factorCount++;
    }
  }
  
  // Base confidence for other analyses
  if (result.bodyConditionScore) {
    totalConfidence += 0.85; // High confidence for body condition
    factorCount++;
  }
  
  if (result.healthIndicators) {
    totalConfidence += 0.8; // Good confidence for health indicators
    factorCount++;
  }
  
  return factorCount > 0 ? totalConfidence / factorCount : 0.5;
}

async function generateHealthInsights(supabase: any, animalId: string, userId: string, analysisResult: ImageAnalysisResult, imageType: string): Promise<void> {
  const insights = [];
  
  // Body condition insights
  if (analysisResult.bodyConditionScore) {
    const score = analysisResult.bodyConditionScore;
    let insight = null;
    
    if (score < 3) {
      insight = {
        insight_type: 'health_concern',
        insight_category: 'body_condition',
        insight_title: 'Underweight Condition Detected',
        insight_description: `Body condition score of ${score}/9 indicates your pet may be underweight. Consider consulting with a veterinarian about nutrition and feeding plans.`,
        confidence_level: analysisResult.confidenceScore,
        importance_score: 0.9,
        recommended_actions: [
          'Schedule veterinary consultation',
          'Review current diet and feeding schedule',
          'Monitor weight changes',
          'Consider nutritional supplements'
        ]
      };
    } else if (score > 7) {
      insight = {
        insight_type: 'health_concern',
        insight_category: 'body_condition',
        insight_title: 'Overweight Condition Detected',
        insight_description: `Body condition score of ${score}/9 indicates your pet may be overweight. Weight management may help improve overall health and mobility.`,
        confidence_level: analysisResult.confidenceScore,
        importance_score: 0.8,
        recommended_actions: [
          'Consult veterinarian about weight management',
          'Adjust feeding portions',
          'Increase exercise activity',
          'Monitor progress with regular weigh-ins'
        ]
      };
    }
    
    if (insight) {
      insights.push(insight);
    }
  }
  
  // Symptom-based insights
  if (analysisResult.symptomDetection?.visible_symptoms?.length > 0) {
    const symptoms = analysisResult.symptomDetection.visible_symptoms;
    const severeSymptoms = Object.entries(analysisResult.symptomDetection.severity_assessment || {})
      .filter(([_, severity]) => severity === 'severe')
      .map(([symptom, _]) => symptom);
    
    if (severeSymptoms.length > 0) {
      insights.push({
        insight_type: 'health_alert',
        insight_category: 'symptoms',
        insight_title: 'Severe Symptoms Detected',
        insight_description: `Severe symptoms detected: ${severeSymptoms.join(', ')}. Immediate veterinary attention may be required.`,
        confidence_level: analysisResult.confidenceScore,
        importance_score: 1.0,
        recommended_actions: [
          'Contact veterinarian immediately',
          'Monitor symptoms closely',
          'Document any changes',
          'Prepare for potential emergency visit'
        ]
      });
    }
  }
  
  // Save insights to database
  for (const insight of insights) {
    await supabase
      .from('ai_insights')
      .insert({
        animal_id: animalId,
        user_id: userId,
        ...insight,
        supporting_data: {
          analysis_result: analysisResult,
          image_type: imageType
        },
        evidence_strength: insight.confidence_level > 0.8 ? 'strong' : 'moderate'
      });
  }
}

async function checkForUrgentConcerns(analysisResult: ImageAnalysisResult): Promise<any[]> {
  const urgentConcerns = [];
  
  // Check for severe symptoms
  if (analysisResult.symptomDetection?.visible_symptoms) {
    const severeSymptoms = Object.entries(analysisResult.symptomDetection.severity_assessment || {})
      .filter(([_, severity]) => severity === 'severe');
    
    if (severeSymptoms.length > 0) {
      urgentConcerns.push({
        type: 'severe_symptoms',
        severity: 'high',
        message: `Severe symptoms detected: ${severeSymptoms.map(([symptom, _]) => symptom).join(', ')}`,
        recommendation: 'Seek immediate veterinary attention'
      });
    }
  }
  
  // Check for extreme body condition
  if (analysisResult.bodyConditionScore) {
    if (analysisResult.bodyConditionScore < 2 || analysisResult.bodyConditionScore > 8) {
      urgentConcerns.push({
        type: 'extreme_body_condition',
        severity: 'medium',
        message: `Extreme body condition score: ${analysisResult.bodyConditionScore}/9`,
        recommendation: 'Schedule veterinary consultation for nutrition assessment'
      });
    }
  }
  
  // Check for pain indicators
  if (analysisResult.emotionAnalysis?.pain_indicators && analysisResult.emotionAnalysis.pain_indicators > 0.7) {
    urgentConcerns.push({
      type: 'pain_indicators',
      severity: 'high',
      message: 'High pain indicators detected in facial expression analysis',
      recommendation: 'Monitor closely and consider veterinary evaluation'
    });
  }
  
  return urgentConcerns;
}

async function createUrgentAlerts(supabase: any, animalId: string, userId: string, urgentConcerns: any[]): Promise<void> {
  for (const concern of urgentConcerns) {
    if (concern.severity === 'high') {
      // Create high-priority alert
      await supabase.functions.invoke('send-push-notification', {
        body: {
          userId: userId,
          title: 'Urgent Health Alert',
          message: concern.message,
          priority: 'high',
          data: {
            alert_type: 'urgent_health_concern',
            animal_id: animalId,
            concern_data: concern
          }
        }
      });
    }
  }
}

async function generateRecommendations(analysisResult: ImageAnalysisResult, imageType: string): Promise<string[]> {
  const recommendations = [];
  
  // Image quality recommendations
  if (analysisResult.imageQualityScore && analysisResult.imageQualityScore < 0.7) {
    recommendations.push('For better analysis results, try taking photos in good lighting with the animal clearly visible');
  }
  
  // Body condition recommendations
  if (analysisResult.bodyConditionScore) {
    if (analysisResult.bodyConditionScore < 4) {
      recommendations.push('Consider increasing food portions or switching to a higher-calorie diet');
    } else if (analysisResult.bodyConditionScore > 6) {
      recommendations.push('Consider reducing food portions and increasing exercise activity');
    }
  }
  
  // Health monitoring recommendations
  if (analysisResult.healthIndicators) {
    recommendations.push('Continue regular health monitoring with photos to track changes over time');
  }
  
  // Behavioral recommendations
  if (analysisResult.behavioralMarkers?.stress_indicators) {
    const stressCount = Object.values(analysisResult.behavioralMarkers.stress_indicators).filter(Boolean).length;
    if (stressCount > 1) {
      recommendations.push('Multiple stress indicators detected - consider environmental enrichment and stress reduction techniques');
    }
  }
  
  return recommendations;
}