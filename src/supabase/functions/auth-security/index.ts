
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get("SUPABASE_URL") ?? "";
    const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "";
    const supabase = createClient(supabaseUrl, supabaseKey);

    const { action, email } = await req.json();

    if (!email) {
      return new Response(
        JSON.stringify({ error: "Email is required" }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
      );
    }

    // Get the current login attempts for this email
    const { data: loginAttempts, error: fetchError } = await supabase
      .from("login_attempts")
      .select("*")
      .eq("email", email)
      .single();

    if (fetchError && fetchError.code !== "PGRST116") {
      // PGRST116 is "no rows returned" error - that's expected for first-time logins
      throw fetchError;
    }

    const now = new Date();

    if (action === "failed_attempt") {
      if (!loginAttempts) {
        // First failed attempt
        await supabase.from("login_attempts").insert({
          email,
          attempts: 1,
          last_attempt: now.toISOString(),
          locked_until: null
        });

        return new Response(
          JSON.stringify({ 
            attempts: 1, 
            locked: false, 
            message: "First failed attempt" 
          }),
          { headers: { ...corsHeaders, "Content-Type": "application/json" } }
        );
      } else {
        // Check if account is locked
        if (loginAttempts.locked_until && new Date(loginAttempts.locked_until) > now) {
          return new Response(
            JSON.stringify({ 
              attempts: loginAttempts.attempts, 
              locked: true, 
              lockedUntil: loginAttempts.locked_until,
              message: "Account is locked" 
            }),
            { headers: { ...corsHeaders, "Content-Type": "application/json" } }
          );
        }

        // Increment attempts
        const attempts = loginAttempts.attempts + 1;
        let lockedUntil = null;

        // Determine if we should lock the account and for how long
        if (attempts >= 10) {
          // Lock indefinitely after 10 attempts (requires password reset)
          lockedUntil = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000).toISOString(); // 1 year
        } else if (attempts >= 8) {
          // Lock for 10 minutes after 8 attempts
          lockedUntil = new Date(now.getTime() + 10 * 60 * 1000).toISOString();
        } else if (attempts >= 6) {
          // Lock for 5 minutes after 6 attempts
          lockedUntil = new Date(now.getTime() + 5 * 60 * 1000).toISOString();
        } else if (attempts >= 5) {
          // Lock for 1 minute after 5 attempts
          lockedUntil = new Date(now.getTime() + 1 * 60 * 1000).toISOString();
        }

        await supabase
          .from("login_attempts")
          .update({
            attempts,
            last_attempt: now.toISOString(),
            locked_until: lockedUntil
          })
          .eq("email", email);

        return new Response(
          JSON.stringify({ 
            attempts, 
            locked: !!lockedUntil, 
            lockedUntil,
            message: lockedUntil ? "Account is now locked" : "Failed attempt recorded" 
          }),
          { headers: { ...corsHeaders, "Content-Type": "application/json" } }
        );
      }
    } else if (action === "check_status") {
      if (!loginAttempts) {
        return new Response(
          JSON.stringify({ 
            attempts: 0, 
            locked: false, 
            message: "No failed attempts" 
          }),
          { headers: { ...corsHeaders, "Content-Type": "application/json" } }
        );
      }

      const locked = loginAttempts.locked_until && new Date(loginAttempts.locked_until) > now;

      return new Response(
        JSON.stringify({ 
          attempts: loginAttempts.attempts, 
          locked, 
          lockedUntil: loginAttempts.locked_until,
          message: locked ? "Account is locked" : "Account is not locked" 
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    } else if (action === "reset_attempts") {
      // Reset attempts after successful login or password reset
      if (loginAttempts) {
        await supabase
          .from("login_attempts")
          .update({
            attempts: 0,
            last_attempt: now.toISOString(),
            locked_until: null
          })
          .eq("email", email);
      }

      return new Response(
        JSON.stringify({ 
          attempts: 0, 
          locked: false, 
          message: "Attempts reset" 
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    return new Response(
      JSON.stringify({ error: "Invalid action" }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
    );
  }
});
