import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface EnvironmentalAnalysisRequest {
  animalId: string;
  analysisPeriodDays?: number;
  includeWeatherData?: boolean;
  includeIndoorEnvironment?: boolean;
}

interface WeatherData {
  temperature_celsius: number;
  humidity_percentage: number;
  barometric_pressure_hpa: number;
  wind_speed_kmh: number;
  precipitation_mm: number;
  uv_index: number;
  air_quality_index: number;
}

interface IndoorEnvironment {
  indoor_temperature_celsius: number;
  indoor_humidity_percentage: number;
  indoor_air_quality_ppm: number;
  noise_level_db: number;
  light_intensity_lux: number;
  co2_level_ppm: number;
}

interface HealthCorrelation {
  factor: string;
  correlation_strength: number;
  impact_description: string;
  optimization_suggestion: string;
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get request data
    const { animalId, analysisPeriodDays = 30, includeWeatherData = true, includeIndoorEnvironment = true }: EnvironmentalAnalysisRequest = await req.json();

    console.log(`Starting environmental impact analysis for animal ${animalId}`);

    // Get animal data
    const { data: animal, error: animalError } = await supabase
      .from('animals')
      .select('*')
      .eq('id', animalId)
      .single();

    if (animalError || !animal) {
      throw new Error(`Animal not found: ${animalError?.message}`);
    }

    // Calculate analysis period
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - (analysisPeriodDays * 24 * 60 * 60 * 1000));

    // Get environmental data
    const { data: environmentalData, error: envError } = await supabase
      .from('environmental_data')
      .select('*')
      .eq('animal_id', animalId)
      .gte('recorded_at', startDate.toISOString())
      .lte('recorded_at', endDate.toISOString())
      .order('recorded_at', { ascending: false });

    if (envError) {
      console.error('Error fetching environmental data:', envError);
    }

    // Get health data for correlation analysis
    const { data: vitalsData, error: vitalsError } = await supabase
      .from('vitals')
      .select('*')
      .eq('animal_id', animalId)
      .gte('recorded_at', startDate.toISOString())
      .lte('recorded_at', endDate.toISOString())
      .order('recorded_at', { ascending: false });

    if (vitalsError) {
      console.error('Error fetching vitals data:', vitalsError);
    }

    // Get stress analysis data
    const { data: stressData, error: stressError } = await supabase
      .from('stress_analysis')
      .select('*')
      .eq('animal_id', animalId)
      .gte('analysis_timestamp', startDate.toISOString())
      .lte('analysis_timestamp', endDate.toISOString())
      .order('analysis_timestamp', { ascending: false });

    if (stressError) {
      console.error('Error fetching stress data:', stressError);
    }

    // 🚀 ENHANCED AI-POWERED ENVIRONMENTAL ANALYSIS
    console.log('🤖 Starting AI-enhanced environmental analysis...');
    
    // Get AI API key from Supabase secrets
    const { data: secretData } = await supabase
      .from('vault')
      .select('secret')
      .eq('name', 'AI_API_KEY')
      .single();
    
    const aiApiKey = secretData?.secret || Deno.env.get('AI_API_KEY');
    
    // Perform enhanced AI analysis
    const analysis = await performEnhancedAIAnalysis(
      environmentalData || [],
      vitalsData || [],
      stressData || [],
      animal,
      aiApiKey
    );

    // Store analysis results
    const { data: savedAnalysis, error: saveError } = await supabase
      .from('environmental_impact_analysis')
      .insert({
        animal_id: animalId,
        analysis_period_start: startDate.toISOString(),
        analysis_period_end: endDate.toISOString(),
        temperature_impact_score: analysis.temperatureImpact,
        humidity_impact_score: analysis.humidityImpact,
        pressure_impact_score: analysis.pressureImpact,
        air_quality_impact_score: analysis.airQualityImpact,
        seasonal_pattern_detected: analysis.seasonalPatternDetected,
        seasonal_health_trend: analysis.seasonalHealthTrend,
        seasonal_risk_factors: analysis.seasonalRiskFactors,
        location_health_correlation: analysis.locationHealthCorrelation,
        optimal_environment_suggestions: analysis.optimalEnvironmentSuggestions,
        environmental_stress_indicators: analysis.environmentalStressIndicators,
        environmental_health_score: analysis.environmentalHealthScore,
        environmental_risk_level: analysis.environmentalRiskLevel,
        analysis_model_version: 'enhanced-ai-v2.0',
        confidence_level: analysis.confidenceLevel,
        data_points_analyzed: (environmentalData?.length || 0) + (vitalsData?.length || 0),
        // 🆕 Enhanced AI fields
        detailed_insights: analysis.detailedInsights,
        ai_explanation: analysis.aiExplanation,
        priority_actions: analysis.priorityActions,
        personalized_recommendations: analysis.personalizedRecommendations
      })
      .select()
      .single();

    if (saveError) {
      console.error('Error saving analysis:', saveError);
      throw new Error(`Failed to save analysis: ${saveError.message}`);
    }

    // Generate environmental optimization recommendations
    const optimizations = await generateOptimizationRecommendations(analysis, animalId, supabase);

    console.log(`Environmental impact analysis completed for animal ${animalId}`);

    return new Response(
      JSON.stringify({
        success: true,
        analysis: {
          ...savedAnalysis,
          // Include rich AI insights in response
          insights: analysis.detailedInsights,
          explanation: analysis.aiExplanation,
          priority_actions: analysis.priorityActions,
          recommendations: analysis.personalizedRecommendations
        },
        optimizations: optimizations,
        dataPointsAnalyzed: (environmentalData?.length || 0) + (vitalsData?.length || 0),
        analysisPeriod: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
          days: analysisPeriodDays
        },
        processingModel: 'enhanced-ai-v2.0'
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('Environmental analysis error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Environmental analysis failed'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});

// 🚀 ENHANCED AI-POWERED ANALYSIS FUNCTION
async function performEnhancedAIAnalysis(
  environmentalData: any[],
  vitalsData: any[],
  stressData: any[],
  animal: any,
  aiApiKey: string
) {
  console.log(`🔬 Analyzing ${environmentalData.length} environmental readings for ${animal.name}`);
  
  // Perform traditional analysis first
  const traditionalAnalysis = await analyzeEnvironmentalImpact(
    environmentalData,
    vitalsData,
    stressData,
    animal
  );
  
  // 🤖 AI-ENHANCED INSIGHTS
  if (aiApiKey && environmentalData.length > 0) {
    try {
      const aiInsights = await generateAIInsights(
        environmentalData,
        vitalsData,
        stressData,
        animal,
        traditionalAnalysis,
        aiApiKey
      );
      
      return {
        ...traditionalAnalysis,
        detailedInsights: aiInsights.insights,
        aiExplanation: aiInsights.explanation,
        priorityActions: aiInsights.priorityActions,
        personalizedRecommendations: aiInsights.recommendations
      };
    } catch (aiError) {
      console.error('AI analysis failed, using traditional analysis:', aiError);
    }
  }
  
  return {
    ...traditionalAnalysis,
    detailedInsights: [],
    aiExplanation: 'Traditional analysis completed without AI enhancement.',
    priorityActions: [],
    personalizedRecommendations: []
  };
}

// 🤖 AI INSIGHTS GENERATION
async function generateAIInsights(
  environmentalData: any[],
  vitalsData: any[],
  stressData: any[],
  animal: any,
  traditionalAnalysis: any,
  aiApiKey: string
) {
  const analysisPrompt = `
You are Dr. Sarah Chen, a world-renowned livestock environmental health specialist with 20+ years of experience in precision agriculture and animal welfare optimization.

**ANIMAL PROFILE:**
🐄 Name: ${animal.name}
🏷️ Species: ${animal.species} (${animal.breed})
📏 Age: ${animal.age} years | Weight: ${animal.weight || 'Unknown'}
📍 Location: ${animal.location || 'Unknown'}
🏥 Health Status: ${animal.health_status || 'Unknown'}

**ENVIRONMENTAL DATA SUMMARY:**
📊 Analysis Period: ${environmentalData.length} readings
🌡️ Temperature Range: ${getDataRange(environmentalData, 'temperature_celsius')}°C
💧 Humidity Range: ${getDataRange(environmentalData, 'humidity_percentage')}%
🌬️ Air Quality: ${getDataRange(environmentalData, 'air_quality_index')} AQI
☀️ UV Index: ${getDataRange(environmentalData, 'uv_index')}

**HEALTH CORRELATION:**
❤️ Vital Signs: ${vitalsData.length} readings available
😰 Stress Events: ${stressData.length} stress indicators detected
📈 Environmental Health Score: ${traditionalAnalysis.environmentalHealthScore}/100
⚠️ Risk Level: ${traditionalAnalysis.environmentalRiskLevel.toUpperCase()}

**ANALYSIS REQUEST:**
Provide a comprehensive environmental impact analysis with:

1. **DETAILED INSIGHTS** (3-5 specific observations about environmental factors)
2. **CLEAR EXPLANATION** (Natural language summary a farmer can understand)
3. **PRIORITY ACTIONS** (3-4 immediate actionable steps)
4. **PERSONALIZED RECOMMENDATIONS** (Specific to this animal and environment)

Format as JSON with these exact fields:
{
  "insights": [
    {
      "category": "temperature|humidity|air_quality|seasonal|stress",
      "finding": "Specific observation",
      "impact_level": "low|moderate|high|critical",
      "confidence": 0.0-1.0,
      "actionable": true/false
    }
  ],
  "explanation": "Clear, farmer-friendly explanation of overall environmental impact",
  "priorityActions": [
    {
      "action": "Specific action to take",
      "urgency": "immediate|within_week|within_month",
      "expected_benefit": "What improvement to expect"
    }
  ],
  "recommendations": [
    {
      "title": "Recommendation title",
      "description": "Detailed description",
      "category": "temperature|humidity|air_quality|shelter|nutrition",
      "difficulty": "easy|moderate|difficult",
      "cost_estimate": "low|medium|high",
      "timeframe": "immediate|1-2_weeks|1-3_months"
    }
  ]
}
`;

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${aiApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-4-turbo-preview',
      messages: [
        {
          role: 'system',
          content: 'You are Dr. Sarah Chen, a livestock environmental health expert. Provide practical, actionable insights that help farmers optimize their animals\' environmental conditions for better health and productivity.'
        },
        {
          role: 'user',
          content: analysisPrompt
        }
      ],
      temperature: 0.2,
      max_tokens: 3000,
      response_format: { type: "json_object" }
    })
  });

  if (!response.ok) {
    throw new Error(`AI API error: ${response.statusText}`);
  }

  const result = await response.json();
  const aiContent = result.choices[0]?.message?.content;
  
  if (!aiContent) {
    throw new Error('No AI response received');
  }

  console.log('✅ AI insights generated successfully');
  return JSON.parse(aiContent);
}

// Helper function to get data ranges
function getDataRange(data: any[], field: string): string {
  const values = data.map(d => d[field]).filter(v => v !== null && v !== undefined);
  if (values.length === 0) return 'N/A';
  
  const min = Math.min(...values);
  const max = Math.max(...values);
  return min === max ? `${min}` : `${min}-${max}`;
}

// ORIGINAL ANALYSIS FUNCTION (Enhanced)
async function analyzeEnvironmentalImpact(
  environmentalData: any[],
  vitalsData: any[],
  stressData: any[],
  animal: any
) {
  // Temperature Impact Analysis
  const temperatureImpact = analyzeTemperatureImpact(environmentalData, vitalsData, animal);
  
  // Humidity Impact Analysis
  const humidityImpact = analyzeHumidityImpact(environmentalData, vitalsData);
  
  // Air Pressure Impact Analysis
  const pressureImpact = analyzePressureImpact(environmentalData, stressData);
  
  // Air Quality Impact Analysis
  const airQualityImpact = analyzeAirQualityImpact(environmentalData, vitalsData);
  
  // Seasonal Pattern Detection
  const seasonalAnalysis = detectSeasonalPatterns(environmentalData, vitalsData, stressData);
  
  // Location-based Health Correlation
  const locationCorrelation = analyzeLocationHealthCorrelation(environmentalData, vitalsData);
  
  // Environmental Stress Indicators
  const stressIndicators = identifyEnvironmentalStressors(environmentalData, stressData);
  
  // Calculate overall environmental health score
  const environmentalHealthScore = calculateEnvironmentalHealthScore({
    temperatureImpact,
    humidityImpact,
    pressureImpact,
    airQualityImpact,
    seasonalAnalysis,
    locationCorrelation
  });
  
  // Determine risk level
  const environmentalRiskLevel = determineRiskLevel(environmentalHealthScore);
  
  // Generate optimization suggestions
  const optimalEnvironmentSuggestions = generateEnvironmentSuggestions({
    temperatureImpact,
    humidityImpact,
    pressureImpact,
    airQualityImpact,
    animal
  });
  
  return {
    temperatureImpact,
    humidityImpact,
    pressureImpact,
    airQualityImpact,
    seasonalPatternDetected: seasonalAnalysis.patternDetected,
    seasonalHealthTrend: seasonalAnalysis.healthTrend,
    seasonalRiskFactors: seasonalAnalysis.riskFactors,
    locationHealthCorrelation: locationCorrelation,
    optimalEnvironmentSuggestions,
    environmentalStressIndicators: stressIndicators,
    environmentalHealthScore,
    environmentalRiskLevel,
    confidenceLevel: calculateConfidenceLevel(environmentalData, vitalsData)
  };
}

function analyzeTemperatureImpact(environmentalData: any[], vitalsData: any[], animal: any): number {
  if (!environmentalData.length || !vitalsData.length) return 50;
  
  // Get optimal temperature range for animal breed/species
  const optimalTempRange = getOptimalTemperatureRange(animal.breed, animal.species);
  
  let totalImpact = 0;
  let dataPoints = 0;
  
  environmentalData.forEach(envData => {
    if (envData.temperature_celsius) {
      const temp = envData.temperature_celsius;
      const deviation = Math.min(
        Math.abs(temp - optimalTempRange.min),
        Math.abs(temp - optimalTempRange.max)
      );
      
      // Find corresponding vital signs
      const correspondingVital = vitalsData.find(vital => 
        Math.abs(new Date(vital.recorded_at).getTime() - new Date(envData.recorded_at).getTime()) < 3600000 // 1 hour
      );
      
      if (correspondingVital) {
        // Analyze correlation between temperature and health indicators
        const healthImpact = calculateTemperatureHealthImpact(temp, correspondingVital, optimalTempRange);
        totalImpact += healthImpact;
        dataPoints++;
      }
    }
  });
  
  return dataPoints > 0 ? Math.round(totalImpact / dataPoints) : 50;
}

function analyzeHumidityImpact(environmentalData: any[], vitalsData: any[]): number {
  if (!environmentalData.length) return 50;
  
  const optimalHumidityRange = { min: 40, max: 60 }; // General optimal range
  
  let totalImpact = 0;
  let dataPoints = 0;
  
  environmentalData.forEach(envData => {
    if (envData.humidity_percentage) {
      const humidity = envData.humidity_percentage;
      let impact = 100;
      
      if (humidity < optimalHumidityRange.min) {
        impact = Math.max(0, 100 - (optimalHumidityRange.min - humidity) * 2);
      } else if (humidity > optimalHumidityRange.max) {
        impact = Math.max(0, 100 - (humidity - optimalHumidityRange.max) * 1.5);
      }
      
      totalImpact += impact;
      dataPoints++;
    }
  });
  
  return dataPoints > 0 ? Math.round(totalImpact / dataPoints) : 50;
}

function analyzePressureImpact(environmentalData: any[], stressData: any[]): number {
  if (!environmentalData.length) return 50;
  
  // Analyze correlation between barometric pressure changes and stress levels
  let totalImpact = 0;
  let dataPoints = 0;
  
  for (let i = 1; i < environmentalData.length; i++) {
    const current = environmentalData[i];
    const previous = environmentalData[i - 1];
    
    if (current.barometric_pressure_hpa && previous.barometric_pressure_hpa) {
      const pressureChange = Math.abs(current.barometric_pressure_hpa - previous.barometric_pressure_hpa);
      
      // Find corresponding stress data
      const correspondingStress = stressData.find(stress => 
        Math.abs(new Date(stress.analysis_timestamp).getTime() - new Date(current.recorded_at).getTime()) < 7200000 // 2 hours
      );
      
      if (correspondingStress) {
        // Rapid pressure changes can affect animal behavior and stress
        const impact = Math.max(0, 100 - (pressureChange * 10));
        totalImpact += impact;
        dataPoints++;
      }
    }
  }
  
  return dataPoints > 0 ? Math.round(totalImpact / dataPoints) : 50;
}

function analyzeAirQualityImpact(environmentalData: any[], vitalsData: any[]): number {
  if (!environmentalData.length) return 50;
  
  let totalImpact = 0;
  let dataPoints = 0;
  
  environmentalData.forEach(envData => {
    if (envData.air_quality_index) {
      const aqi = envData.air_quality_index;
      let impact = 100;
      
      if (aqi <= 50) impact = 100; // Good
      else if (aqi <= 100) impact = 80; // Moderate
      else if (aqi <= 150) impact = 60; // Unhealthy for sensitive groups
      else if (aqi <= 200) impact = 40; // Unhealthy
      else if (aqi <= 300) impact = 20; // Very unhealthy
      else impact = 0; // Hazardous
      
      totalImpact += impact;
      dataPoints++;
    }
  });
  
  return dataPoints > 0 ? Math.round(totalImpact / dataPoints) : 50;
}

function detectSeasonalPatterns(environmentalData: any[], vitalsData: any[], stressData: any[]) {
  // Analyze seasonal patterns in health data correlated with environmental changes
  const monthlyData = groupDataByMonth(environmentalData, vitalsData, stressData);
  
  const patternDetected = monthlyData.length >= 3; // Need at least 3 months of data
  let healthTrend = 'stable';
  const riskFactors = [];
  
  if (patternDetected) {
    // Analyze trends across months
    const trends = analyzeMonthlyTrends(monthlyData);
    healthTrend = trends.overallTrend;
    
    // Identify seasonal risk factors
    if (trends.winterStress > 70) riskFactors.push('winter_stress');
    if (trends.summerHeatStress > 70) riskFactors.push('summer_heat_stress');
    if (trends.humidityIssues > 60) riskFactors.push('humidity_sensitivity');
  }
  
  return {
    patternDetected,
    healthTrend,
    riskFactors
  };
}

function analyzeLocationHealthCorrelation(environmentalData: any[], vitalsData: any[]): number {
  // Analyze correlation between location changes and health indicators
  if (!environmentalData.length || !vitalsData.length) return 0;
  
  // Group data by location
  const locationGroups = groupDataByLocation(environmentalData, vitalsData);
  
  if (Object.keys(locationGroups).length < 2) return 0;
  
  // Calculate health variance across locations
  const locationHealthScores = Object.values(locationGroups).map(group => 
    calculateLocationHealthScore(group)
  );
  
  const variance = calculateVariance(locationHealthScores);
  return Math.min(100, variance * 10); // Convert to 0-100 scale
}

function identifyEnvironmentalStressors(environmentalData: any[], stressData: any[]) {
  const stressors = [];
  
  // Analyze noise levels
  const highNoiseEvents = environmentalData.filter(data => 
    data.noise_level_db && data.noise_level_db > 70
  );
  
  if (highNoiseEvents.length > 0) {
    stressors.push({
      type: 'noise',
      severity: 'high',
      frequency: highNoiseEvents.length,
      description: 'High noise levels detected'
    });
  }
  
  // Analyze air quality issues
  const poorAirQuality = environmentalData.filter(data => 
    data.air_quality_index && data.air_quality_index > 100
  );
  
  if (poorAirQuality.length > 0) {
    stressors.push({
      type: 'air_quality',
      severity: 'moderate',
      frequency: poorAirQuality.length,
      description: 'Poor air quality detected'
    });
  }
  
  return stressors;
}

function calculateEnvironmentalHealthScore(impacts: any): number {
  const weights = {
    temperature: 0.3,
    humidity: 0.2,
    pressure: 0.15,
    airQuality: 0.25,
    seasonal: 0.1
  };
  
  const weightedScore = 
    (impacts.temperatureImpact * weights.temperature) +
    (impacts.humidityImpact * weights.humidity) +
    (impacts.pressureImpact * weights.pressure) +
    (impacts.airQualityImpact * weights.airQuality) +
    (impacts.seasonalAnalysis.patternDetected ? 80 : 60) * weights.seasonal;
  
  return Math.round(weightedScore);
}

function determineRiskLevel(score: number): string {
  if (score >= 80) return 'low';
  if (score >= 60) return 'moderate';
  if (score >= 40) return 'high';
  return 'critical';
}

function generateEnvironmentSuggestions(impacts: any) {
  const suggestions = [];
  
  if (impacts.temperatureImpact < 70) {
    suggestions.push({
      category: 'temperature',
      priority: 'high',
      suggestion: 'Optimize temperature control for better health outcomes'
    });
  }
  
  if (impacts.humidityImpact < 70) {
    suggestions.push({
      category: 'humidity',
      priority: 'medium',
      suggestion: 'Adjust humidity levels to optimal range (40-60%)'
    });
  }
  
  if (impacts.airQualityImpact < 70) {
    suggestions.push({
      category: 'air_quality',
      priority: 'high',
      suggestion: 'Improve air quality through ventilation or air purification'
    });
  }
  
  return suggestions;
}

async function generateOptimizationRecommendations(analysis: any, animalId: string, supabase: any) {
  const recommendations = [];
  
  // Temperature optimization
  if (analysis.temperatureImpact < 70) {
    recommendations.push({
      animal_id: animalId,
      optimization_type: 'temperature',
      priority_level: 'high',
      current_value: null,
      optimal_value_min: 18,
      optimal_value_max: 24,
      improvement_potential_score: 100 - analysis.temperatureImpact,
      recommendation_title: 'Optimize Temperature Control',
      recommendation_description: 'Maintain consistent temperature within optimal range for improved health and comfort.',
      implementation_difficulty: 'moderate',
      estimated_cost_category: 'medium',
      expected_health_improvement: Math.min(30, 100 - analysis.temperatureImpact),
      implementation_steps: [
        'Install programmable thermostat',
        'Monitor temperature patterns',
        'Adjust heating/cooling schedule',
        'Consider insulation improvements'
      ],
      required_equipment: ['Smart thermostat', 'Temperature sensors'],
      monitoring_requirements: ['Daily temperature logging', 'Weekly health assessments']
    });
  }
  
  // Save recommendations to database
  if (recommendations.length > 0) {
    const { data, error } = await supabase
      .from('environmental_optimization')
      .insert(recommendations)
      .select();
    
    if (error) {
      console.error('Error saving optimization recommendations:', error);
      return [];
    }
    
    return data;
  }
  
  return [];
}

// Helper functions
function getOptimalTemperatureRange(breed: string, species: string) {
  // Default ranges - would be expanded with breed-specific data
  const defaults = {
    dog: { min: 18, max: 24 },
    cat: { min: 20, max: 26 },
    bird: { min: 22, max: 28 },
    rabbit: { min: 16, max: 22 }
  };
  
  return defaults[species.toLowerCase()] || { min: 18, max: 24 };
}

function calculateTemperatureHealthImpact(temp: number, vital: any, optimalRange: any): number {
  const deviation = Math.min(
    Math.abs(temp - optimalRange.min),
    Math.abs(temp - optimalRange.max)
  );
  
  // Base impact from temperature deviation
  let impact = Math.max(0, 100 - (deviation * 5));
  
  // Adjust based on vital signs
  if (vital.heart_rate) {
    const normalHeartRate = 80; // Average
    const heartRateDeviation = Math.abs(vital.heart_rate - normalHeartRate);
    impact -= heartRateDeviation * 0.5;
  }
  
  return Math.max(0, Math.min(100, impact));
}

function groupDataByMonth(environmentalData: any[], vitalsData: any[], stressData: any[]) {
  const monthlyGroups: { [key: string]: any } = {};
  
  environmentalData.forEach(data => {
    const month = new Date(data.recorded_at).toISOString().substring(0, 7);
    if (!monthlyGroups[month]) {
      monthlyGroups[month] = { environmental: [], vitals: [], stress: [] };
    }
    monthlyGroups[month].environmental.push(data);
  });
  
  return Object.values(monthlyGroups);
}

function analyzeMonthlyTrends(monthlyData: any[]) {
  return {
    overallTrend: 'stable',
    winterStress: 50,
    summerHeatStress: 60,
    humidityIssues: 40
  };
}

function groupDataByLocation(environmentalData: any[], vitalsData: any[]) {
  const locationGroups: { [key: string]: any } = {};
  
  environmentalData.forEach(data => {
    const location = data.location_name || 'unknown';
    if (!locationGroups[location]) {
      locationGroups[location] = { environmental: [], vitals: [] };
    }
    locationGroups[location].environmental.push(data);
  });
  
  return locationGroups;
}

function calculateLocationHealthScore(locationData: any): number {
  // Simplified health score calculation for location
  return Math.random() * 100; // Would be replaced with actual calculation
}

function calculateVariance(values: number[]): number {
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
  return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
}

function calculateConfidenceLevel(environmentalData: any[], vitalsData: any[]): number {
  const totalDataPoints = environmentalData.length + vitalsData.length;
  
  if (totalDataPoints >= 100) return 0.95;
  if (totalDataPoints >= 50) return 0.85;
  if (totalDataPoints >= 20) return 0.75;
  if (totalDataPoints >= 10) return 0.65;
  return 0.50;
}