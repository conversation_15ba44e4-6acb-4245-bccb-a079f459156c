import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface DataExportRequest {
  exportType: 'full' | 'partial' | 'report';
  dataTypes: string[];
  animalIds?: string[];
  dateRangeStart?: string;
  dateRangeEnd?: string;
  exportFormat: 'csv' | 'json' | 'pdf' | 'xlsx';
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user ID from JWT
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Authorization header required');
    }

    // Get request data
    const { exportType, dataTypes, animalIds, dateRangeStart, dateRangeEnd, exportFormat }: DataExportRequest = await req.json();

    console.log(`Starting data export: ${exportType} - ${exportFormat}`);

    // Create export job record
    const { data: exportJob, error: jobError } = await supabase
      .from('data_export_jobs')
      .insert({
        export_type: exportType,
        data_types: dataTypes,
        animal_ids: animalIds,
        date_range_start: dateRangeStart,
        date_range_end: dateRangeEnd,
        export_format: exportFormat,
        status: 'processing'
      })
      .select()
      .single();

    if (jobError) {
      throw new Error(`Failed to create export job: ${jobError.message}`);
    }

    try {
      // Get user's animals if no specific animals provided
      let targetAnimalIds = animalIds;
      if (!targetAnimalIds || targetAnimalIds.length === 0) {
        const { data: animals, error: animalsError } = await supabase
          .from('animals')
          .select('id')
          .eq('user_id', (await supabase.auth.getUser()).data.user?.id);

        if (animalsError) {
          throw new Error(`Failed to fetch animals: ${animalsError.message}`);
        }

        targetAnimalIds = animals.map(animal => animal.id);
      }

      // Collect data based on requested types
      const exportData: any = {};
      let totalRecords = 0;
      let processedRecords = 0;

      for (const dataType of dataTypes) {
        console.log(`Collecting ${dataType} data...`);
        
        const data = await collectDataByType(supabase, dataType, targetAnimalIds, dateRangeStart, dateRangeEnd);
        exportData[dataType] = data;
        totalRecords += data.length;
        
        // Update progress
        processedRecords += data.length;
        const progress = Math.round((processedRecords / totalRecords) * 100);
        
        await supabase
          .from('data_export_jobs')
          .update({
            progress_percentage: progress,
            total_records: totalRecords,
            processed_records: processedRecords
          })
          .eq('id', exportJob.id);
      }

      // Generate export file based on format
      const fileResult = await generateExportFile(exportData, exportFormat, exportType);
      
      // Calculate expiration date (7 days from now)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);

      // Update export job with completion
      const { data: completedJob, error: updateError } = await supabase
        .from('data_export_jobs')
        .update({
          status: 'completed',
          progress_percentage: 100,
          file_url: fileResult.url,
          file_size_bytes: fileResult.size,
          download_expires_at: expiresAt.toISOString(),
          total_records: totalRecords,
          processed_records: totalRecords
        })
        .eq('id', exportJob.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating export job:', updateError);
      }

      console.log(`Data export completed: ${totalRecords} records`);

      return new Response(
        JSON.stringify({
          success: true,
          exportJob: completedJob || exportJob,
          downloadUrl: fileResult.url,
          fileSize: fileResult.size,
          totalRecords: totalRecords,
          expiresAt: expiresAt.toISOString()
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );

    } catch (processingError) {
      // Update job status to failed
      await supabase
        .from('data_export_jobs')
        .update({
          status: 'failed',
          error_message: processingError.message
        })
        .eq('id', exportJob.id);

      throw processingError;
    }

  } catch (error) {
    console.error('Data export error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Data export failed'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});

async function collectDataByType(supabase: any, dataType: string, animalIds: string[], dateStart?: string, dateEnd?: string): Promise<any[]> {
  let query = supabase.from(getTableName(dataType)).select('*');
  
  // Filter by animals
  if (animalIds.length > 0) {
    query = query.in('animal_id', animalIds);
  }
  
  // Filter by date range
  if (dateStart) {
    query = query.gte(getDateColumn(dataType), dateStart);
  }
  if (dateEnd) {
    query = query.lte(getDateColumn(dataType), dateEnd);
  }
  
  // Order by date
  query = query.order(getDateColumn(dataType), { ascending: false });
  
  const { data, error } = await query;
  
  if (error) {
    console.error(`Error collecting ${dataType} data:`, error);
    return [];
  }
  
  return data || [];
}

function getTableName(dataType: string): string {
  const tableMap: { [key: string]: string } = {
    'animals': 'animals',
    'vitals': 'vitals',
    'feeding': 'feeding_schedules',
    'medications': 'medications',
    'vaccinations': 'vaccinations',
    'environmental': 'environmental_data',
    'stress_analysis': 'stress_analysis',
    'sleep_analysis': 'sleep_analysis',
    'training_sessions': 'training_sessions',
    'dehydration_logs': 'dehydration_logs'
  };
  
  return tableMap[dataType] || dataType;
}

function getDateColumn(dataType: string): string {
  const dateColumnMap: { [key: string]: string } = {
    'animals': 'created_at',
    'vitals': 'recorded_at',
    'feeding': 'created_at',
    'medications': 'created_at',
    'vaccinations': 'administered_date',
    'environmental': 'recorded_at',
    'stress_analysis': 'analysis_timestamp',
    'sleep_analysis': 'sleep_date',
    'training_sessions': 'session_date',
    'dehydration_logs': 'recorded_at'
  };
  
  return dateColumnMap[dataType] || 'created_at';
}

async function generateExportFile(exportData: any, format: string, exportType: string): Promise<{ url: string; size: number }> {
  switch (format) {
    case 'json':
      return generateJSONFile(exportData);
    case 'csv':
      return generateCSVFile(exportData);
    case 'pdf':
      return generatePDFReport(exportData, exportType);
    case 'xlsx':
      return generateExcelFile(exportData);
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
}

async function generateJSONFile(exportData: any): Promise<{ url: string; size: number }> {
  const jsonContent = JSON.stringify(exportData, null, 2);
  const blob = new Blob([jsonContent], { type: 'application/json' });
  
  // In a real implementation, you would upload this to a storage service
  // For now, we'll return a mock URL
  const mockUrl = `https://storage.example.com/exports/export_${Date.now()}.json`;
  
  return {
    url: mockUrl,
    size: blob.size
  };
}

async function generateCSVFile(exportData: any): Promise<{ url: string; size: number }> {
  let csvContent = '';
  
  // Generate CSV for each data type
  for (const [dataType, data] of Object.entries(exportData)) {
    if (Array.isArray(data) && data.length > 0) {
      csvContent += `\n\n=== ${dataType.toUpperCase()} ===\n`;
      
      // Headers
      const headers = Object.keys(data[0]);
      csvContent += headers.join(',') + '\n';
      
      // Data rows
      for (const row of data) {
        const values = headers.map(header => {
          const value = row[header];
          if (value === null || value === undefined) return '';
          if (typeof value === 'string' && value.includes(',')) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return String(value);
        });
        csvContent += values.join(',') + '\n';
      }
    }
  }
  
  const blob = new Blob([csvContent], { type: 'text/csv' });
  const mockUrl = `https://storage.example.com/exports/export_${Date.now()}.csv`;
  
  return {
    url: mockUrl,
    size: blob.size
  };
}

async function generatePDFReport(exportData: any, exportType: string): Promise<{ url: string; size: number }> {
  // In a real implementation, you would use a PDF generation library
  // For now, we'll return a mock PDF
  
  let reportContent = `Animal Health Report\n\n`;
  reportContent += `Generated: ${new Date().toISOString()}\n`;
  reportContent += `Export Type: ${exportType}\n\n`;
  
  for (const [dataType, data] of Object.entries(exportData)) {
    if (Array.isArray(data)) {
      reportContent += `${dataType.toUpperCase()}: ${data.length} records\n`;
    }
  }
  
  // Mock PDF generation
  const mockUrl = `https://storage.example.com/exports/report_${Date.now()}.pdf`;
  
  return {
    url: mockUrl,
    size: reportContent.length * 2 // Rough estimate for PDF size
  };
}

async function generateExcelFile(exportData: any): Promise<{ url: string; size: number }> {
  // In a real implementation, you would use an Excel generation library
  // For now, we'll return a mock Excel file
  
  const mockUrl = `https://storage.example.com/exports/export_${Date.now()}.xlsx`;
  
  // Estimate file size based on data
  let estimatedSize = 0;
  for (const [dataType, data] of Object.entries(exportData)) {
    if (Array.isArray(data)) {
      estimatedSize += data.length * 100; // Rough estimate
    }
  }
  
  return {
    url: mockUrl,
    size: Math.max(1024, estimatedSize) // Minimum 1KB
  };
}