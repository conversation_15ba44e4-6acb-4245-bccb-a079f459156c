import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface SleepAnalysisRequest {
  animalId: string;
  analysisDate?: string; // Default to yesterday
}

interface SleepStage {
  stage: 'deep' | 'light' | 'rem' | 'awake';
  startTime: string;
  endTime: string;
  durationMinutes: number;
}

interface SleepDisturbance {
  timestamp: string;
  type: string;
  severity: 'mild' | 'moderate' | 'severe';
  cause?: string;
  durationMinutes: number;
}

interface EnvironmentalConditions {
  averageTemperature: number;
  temperatureVariation: number;
  noiseLevel: number;
  lightExposure: number;
  humidity: number;
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    // Get user from JWT
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();
    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { animalId, analysisDate }: SleepAnalysisRequest = await req.json();

    if (!animalId) {
      return new Response(JSON.stringify({ error: 'Animal ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Verify animal ownership
    const { data: animal, error: animalError } = await supabaseClient
      .from('animals')
      .select('id, name')
      .eq('id', animalId)
      .eq('user_id', user.id)
      .single();

    if (animalError || !animal) {
      return new Response(JSON.stringify({ error: 'Animal not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Default to yesterday for sleep analysis
    const targetDate = analysisDate ? new Date(analysisDate) : new Date(Date.now() - 24 * 60 * 60 * 1000);
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(18, 0, 0, 0); // Sleep typically starts around 6 PM
    
    const endOfDay = new Date(targetDate);
    endOfDay.setDate(endOfDay.getDate() + 1);
    endOfDay.setHours(8, 0, 0, 0); // Sleep typically ends around 8 AM next day

    // Fetch vitals data for sleep period
    const { data: vitalsData } = await supabaseClient
      .from('vitals')
      .select('*')
      .eq('animal_id', animalId)
      .gte('recorded_at', startOfDay.toISOString())
      .lte('recorded_at', endOfDay.toISOString())
      .order('recorded_at', { ascending: true });

    // Fetch activity data to determine sleep/wake periods
    const { data: activityData } = await supabaseClient
      .from('training_sessions')
      .select('*')
      .eq('animal_id', animalId)
      .gte('session_date', startOfDay.toISOString())
      .lte('session_date', endOfDay.toISOString())
      .order('session_date', { ascending: true });

    // Fetch any stress events that might affect sleep
    const { data: stressEvents } = await supabaseClient
      .from('stress_events')
      .select('*')
      .eq('animal_id', animalId)
      .gte('event_timestamp', startOfDay.toISOString())
      .lte('event_timestamp', endOfDay.toISOString())
      .order('event_timestamp', { ascending: true });

    // Analyze sleep patterns
    const sleepStages = analyzeSleepStages(vitalsData || [], activityData || []);
    const sleepDisturbances = identifySleepDisturbances(vitalsData || [], stressEvents || []);
    const environmentalConditions = analyzeEnvironmentalConditions(vitalsData || []);
    
    // Calculate sleep metrics
    const sleepMetrics = calculateSleepMetrics(sleepStages, sleepDisturbances);
    
    // Calculate sleep quality score
    const sleepQualityScore = calculateSleepQualityScore(sleepMetrics, sleepDisturbances, environmentalConditions);
    
    // Analyze circadian rhythm alignment
    const circadianAlignment = analyzeCircadianRhythm(sleepStages);
    
    // Generate sleep recommendations
    const sleepRecommendations = generateSleepRecommendations(
      sleepQualityScore,
      sleepDisturbances,
      environmentalConditions,
      circadianAlignment
    );

    // Determine data sources
    const dataSources = {
      vitals_sensors: vitalsData?.length || 0,
      activity_monitors: activityData?.length || 0,
      environmental_sensors: 1, // Simulated
      total_data_points: (vitalsData?.length || 0) + (activityData?.length || 0)
    };

    // Store sleep analysis results
    const { data: sleepAnalysis, error: insertError } = await supabaseClient
      .from('sleep_analysis')
      .insert({
        animal_id: animalId,
        user_id: user.id,
        sleep_date: targetDate.toISOString().split('T')[0],
        sleep_start_time: sleepMetrics.sleepStartTime,
        sleep_end_time: sleepMetrics.sleepEndTime,
        total_sleep_duration_minutes: sleepMetrics.totalSleepDuration,
        sleep_efficiency_percentage: sleepMetrics.sleepEfficiency,
        deep_sleep_minutes: sleepMetrics.deepSleepMinutes,
        light_sleep_minutes: sleepMetrics.lightSleepMinutes,
        rem_sleep_minutes: sleepMetrics.remSleepMinutes,
        wake_episodes: sleepMetrics.wakeEpisodes,
        time_to_sleep_minutes: sleepMetrics.timeToSleep,
        sleep_quality_score: sleepQualityScore,
        sleep_disturbances: sleepDisturbances,
        environmental_conditions: environmentalConditions,
        circadian_rhythm_alignment: circadianAlignment,
        sleep_recommendations: sleepRecommendations,
        data_sources: dataSources
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting sleep analysis:', insertError);
      return new Response(JSON.stringify({ error: 'Failed to save analysis' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Store detailed sleep quality metrics
    if (sleepAnalysis) {
      await supabaseClient
        .from('sleep_quality_metrics')
        .insert({
          sleep_analysis_id: sleepAnalysis.id,
          animal_id: animalId,
          user_id: user.id,
          heart_rate_during_sleep: extractHeartRatePatterns(vitalsData || []),
          movement_during_sleep: extractMovementPatterns(activityData || []),
          breathing_patterns: extractBreathingPatterns(vitalsData || []),
          temperature_regulation: extractTemperaturePatterns(vitalsData || []),
          sleep_position_changes: sleepDisturbances.filter(d => d.type === 'position_change').length,
          sleep_interruption_causes: sleepDisturbances.map(d => ({ type: d.type, cause: d.cause })),
          sleep_stage_transitions: sleepStages.map(stage => ({
            from: stage.stage,
            timestamp: stage.startTime,
            duration: stage.durationMinutes
          }))
        });
    }

    return new Response(JSON.stringify({
      success: true,
      analysis: sleepAnalysis,
      summary: {
        sleep_quality_score: sleepQualityScore,
        total_sleep_hours: Math.round(sleepMetrics.totalSleepDuration / 60 * 10) / 10,
        sleep_efficiency: sleepMetrics.sleepEfficiency,
        disturbances_count: sleepDisturbances.length,
        circadian_alignment: circadianAlignment,
        data_quality: Math.min(100, (dataSources.total_data_points / 20) * 100)
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Sleep analysis error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
});

// Helper Functions

function analyzeSleepStages(vitalsData: any[], activityData: any[]): SleepStage[] {
  const stages: SleepStage[] = [];
  
  if (vitalsData.length === 0) {
    return stages;
  }

  // Sort data by timestamp
  const sortedVitals = vitalsData.sort((a, b) => new Date(a.recorded_at).getTime() - new Date(b.recorded_at).getTime());
  
  let currentStage: 'deep' | 'light' | 'rem' | 'awake' = 'awake';
  let stageStartTime = sortedVitals[0].recorded_at;
  
  for (let i = 0; i < sortedVitals.length; i++) {
    const vital = sortedVitals[i];
    const heartRate = vital.heart_rate || 0;
    const temperature = vital.temperature || 0;
    
    // Determine sleep stage based on heart rate and activity
    let newStage: typeof currentStage = 'awake';
    
    // Check if there's recent activity (indicates awake)
    const recentActivity = activityData.find(activity => {
      const activityTime = new Date(activity.session_date).getTime();
      const vitalTime = new Date(vital.recorded_at).getTime();
      return Math.abs(activityTime - vitalTime) < 30 * 60 * 1000; // Within 30 minutes
    });
    
    if (recentActivity) {
      newStage = 'awake';
    } else if (heartRate > 0) {
      // Estimate sleep stage based on heart rate patterns
      // These thresholds would be calibrated based on the specific animal type
      if (heartRate < 60) {
        newStage = 'deep';
      } else if (heartRate < 80) {
        newStage = 'light';
      } else if (heartRate < 100) {
        newStage = 'rem';
      } else {
        newStage = 'awake';
      }
    }
    
    // If stage changed, record the previous stage
    if (newStage !== currentStage) {
      const endTime = vital.recorded_at;
      const duration = (new Date(endTime).getTime() - new Date(stageStartTime).getTime()) / (1000 * 60);
      
      if (duration > 5) { // Only record stages longer than 5 minutes
        stages.push({
          stage: currentStage,
          startTime: stageStartTime,
          endTime: endTime,
          durationMinutes: Math.round(duration)
        });
      }
      
      currentStage = newStage;
      stageStartTime = vital.recorded_at;
    }
  }
  
  // Add the final stage
  if (sortedVitals.length > 0) {
    const lastVital = sortedVitals[sortedVitals.length - 1];
    const duration = (new Date(lastVital.recorded_at).getTime() - new Date(stageStartTime).getTime()) / (1000 * 60);
    
    if (duration > 5) {
      stages.push({
        stage: currentStage,
        startTime: stageStartTime,
        endTime: lastVital.recorded_at,
        durationMinutes: Math.round(duration)
      });
    }
  }
  
  return stages;
}

function identifySleepDisturbances(vitalsData: any[], stressEvents: any[]): SleepDisturbance[] {
  const disturbances: SleepDisturbance[] = [];
  
  // Check for heart rate spikes during sleep hours
  const sleepHours = vitalsData.filter(vital => {
    const hour = new Date(vital.recorded_at).getHours();
    return hour >= 22 || hour <= 6; // 10 PM to 6 AM
  });
  
  if (sleepHours.length > 1) {
    const heartRates = sleepHours.filter(v => v.heart_rate).map(v => v.heart_rate);
    const avgHeartRate = heartRates.reduce((sum, hr) => sum + hr, 0) / heartRates.length;
    
    sleepHours.forEach(vital => {
      if (vital.heart_rate && vital.heart_rate > avgHeartRate * 1.4) {
        disturbances.push({
          timestamp: vital.recorded_at,
          type: 'heart_rate_spike',
          severity: vital.heart_rate > avgHeartRate * 1.6 ? 'severe' : 'moderate',
          cause: 'physiological_arousal',
          durationMinutes: 5 // Estimated
        });
      }
    });
  }
  
  // Add stress events as sleep disturbances
  stressEvents.forEach(event => {
    const eventHour = new Date(event.event_timestamp).getHours();
    if (eventHour >= 22 || eventHour <= 6) {
      disturbances.push({
        timestamp: event.event_timestamp,
        type: 'stress_event',
        severity: event.severity_level === 'critical' ? 'severe' : 
                 event.severity_level === 'severe' ? 'moderate' : 'mild',
        cause: event.trigger_description || 'unknown_stress',
        durationMinutes: event.duration_minutes || 10
      });
    }
  });
  
  return disturbances;
}

function analyzeEnvironmentalConditions(vitalsData: any[]): EnvironmentalConditions {
  const temperatures = vitalsData
    .filter(v => v.temperature && v.temperature > 0)
    .map(v => v.temperature);
  
  const averageTemperature = temperatures.length > 0 
    ? temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length
    : 20;
  
  const temperatureVariation = temperatures.length > 1
    ? Math.max(...temperatures) - Math.min(...temperatures)
    : 0;
  
  return {
    averageTemperature: Math.round(averageTemperature * 100) / 100,
    temperatureVariation: Math.round(temperatureVariation * 100) / 100,
    noiseLevel: 30, // Simulated - would come from noise sensors
    lightExposure: 10, // Simulated - would come from light sensors
    humidity: 60 // Simulated - would come from humidity sensors
  };
}

function calculateSleepMetrics(sleepStages: SleepStage[], sleepDisturbances: SleepDisturbance[]) {
  if (sleepStages.length === 0) {
    return {
      sleepStartTime: null,
      sleepEndTime: null,
      totalSleepDuration: 0,
      sleepEfficiency: 0,
      deepSleepMinutes: 0,
      lightSleepMinutes: 0,
      remSleepMinutes: 0,
      wakeEpisodes: 0,
      timeToSleep: 0
    };
  }
  
  const sleepStartTime = sleepStages[0].startTime;
  const sleepEndTime = sleepStages[sleepStages.length - 1].endTime;
  
  const deepSleepMinutes = sleepStages
    .filter(stage => stage.stage === 'deep')
    .reduce((sum, stage) => sum + stage.durationMinutes, 0);
  
  const lightSleepMinutes = sleepStages
    .filter(stage => stage.stage === 'light')
    .reduce((sum, stage) => sum + stage.durationMinutes, 0);
  
  const remSleepMinutes = sleepStages
    .filter(stage => stage.stage === 'rem')
    .reduce((sum, stage) => sum + stage.durationMinutes, 0);
  
  const totalSleepDuration = deepSleepMinutes + lightSleepMinutes + remSleepMinutes;
  
  const totalTimeInBed = (new Date(sleepEndTime).getTime() - new Date(sleepStartTime).getTime()) / (1000 * 60);
  const sleepEfficiency = totalTimeInBed > 0 ? (totalSleepDuration / totalTimeInBed) * 100 : 0;
  
  const wakeEpisodes = sleepStages.filter(stage => stage.stage === 'awake').length;
  
  // Time to sleep is the duration of the first awake period
  const firstAwakeStage = sleepStages.find(stage => stage.stage === 'awake');
  const timeToSleep = firstAwakeStage ? firstAwakeStage.durationMinutes : 0;
  
  return {
    sleepStartTime,
    sleepEndTime,
    totalSleepDuration: Math.round(totalSleepDuration),
    sleepEfficiency: Math.round(sleepEfficiency * 100) / 100,
    deepSleepMinutes: Math.round(deepSleepMinutes),
    lightSleepMinutes: Math.round(lightSleepMinutes),
    remSleepMinutes: Math.round(remSleepMinutes),
    wakeEpisodes,
    timeToSleep: Math.round(timeToSleep)
  };
}

function calculateSleepQualityScore(
  sleepMetrics: any,
  sleepDisturbances: SleepDisturbance[],
  environmentalConditions: EnvironmentalConditions
): number {
  let score = 100;
  
  // Deduct points for poor sleep efficiency
  if (sleepMetrics.sleepEfficiency < 85) {
    score -= (85 - sleepMetrics.sleepEfficiency) * 0.5;
  }
  
  // Deduct points for excessive wake episodes
  if (sleepMetrics.wakeEpisodes > 2) {
    score -= (sleepMetrics.wakeEpisodes - 2) * 5;
  }
  
  // Deduct points for long time to sleep
  if (sleepMetrics.timeToSleep > 30) {
    score -= (sleepMetrics.timeToSleep - 30) * 0.2;
  }
  
  // Deduct points for sleep disturbances
  sleepDisturbances.forEach(disturbance => {
    switch (disturbance.severity) {
      case 'severe':
        score -= 15;
        break;
      case 'moderate':
        score -= 10;
        break;
      case 'mild':
        score -= 5;
        break;
    }
  });
  
  // Deduct points for poor environmental conditions
  if (Math.abs(environmentalConditions.averageTemperature - 20) > 5) {
    score -= Math.abs(environmentalConditions.averageTemperature - 20) * 2;
  }
  
  if (environmentalConditions.temperatureVariation > 5) {
    score -= environmentalConditions.temperatureVariation * 2;
  }
  
  if (environmentalConditions.noiseLevel > 40) {
    score -= (environmentalConditions.noiseLevel - 40) * 0.5;
  }
  
  return Math.max(0, Math.min(100, Math.round(score)));
}

function analyzeCircadianRhythm(sleepStages: SleepStage[]): number {
  if (sleepStages.length === 0) return 0.5;
  
  // Find the main sleep period
  const sleepPeriods = sleepStages.filter(stage => stage.stage !== 'awake');
  if (sleepPeriods.length === 0) return 0.3;
  
  const firstSleepTime = new Date(sleepPeriods[0].startTime).getHours();
  const lastSleepTime = new Date(sleepPeriods[sleepPeriods.length - 1].endTime).getHours();
  
  // Ideal sleep time: 10 PM to 6 AM (22:00 to 06:00)
  const idealSleepStart = 22;
  const idealSleepEnd = 6;
  
  // Calculate alignment score
  let alignmentScore = 1.0;
  
  // Penalize for sleeping too early or too late
  if (firstSleepTime < 20 || firstSleepTime > 24) {
    alignmentScore -= 0.2;
  }
  
  // Penalize for waking too early or too late
  if (lastSleepTime < 5 || lastSleepTime > 9) {
    alignmentScore -= 0.2;
  }
  
  return Math.max(0, Math.min(1, alignmentScore));
}

function generateSleepRecommendations(
  sleepQualityScore: number,
  sleepDisturbances: SleepDisturbance[],
  environmentalConditions: EnvironmentalConditions,
  circadianAlignment: number
): string {
  const recommendations = [];
  
  // Base recommendations by sleep quality
  if (sleepQualityScore < 60) {
    recommendations.push('Sleep quality is below optimal levels');
    recommendations.push('Consider environmental modifications and routine adjustments');
  } else if (sleepQualityScore < 80) {
    recommendations.push('Sleep quality is fair with room for improvement');
  } else {
    recommendations.push('Sleep quality is good - maintain current sleep environment');
  }
  
  // Disturbance-specific recommendations
  if (sleepDisturbances.length > 3) {
    recommendations.push('Multiple sleep disturbances detected - investigate potential causes');
  }
  
  const stressDisturbances = sleepDisturbances.filter(d => d.type === 'stress_event');
  if (stressDisturbances.length > 0) {
    recommendations.push('Stress-related sleep disruptions detected - address stress triggers');
  }
  
  // Environmental recommendations
  if (Math.abs(environmentalConditions.averageTemperature - 20) > 3) {
    recommendations.push('Optimize sleeping environment temperature (ideal: 18-22°C)');
  }
  
  if (environmentalConditions.temperatureVariation > 3) {
    recommendations.push('Reduce temperature fluctuations during sleep hours');
  }
  
  if (environmentalConditions.noiseLevel > 35) {
    recommendations.push('Reduce noise levels in sleeping area');
  }
  
  // Circadian rhythm recommendations
  if (circadianAlignment < 0.7) {
    recommendations.push('Sleep timing may not align with natural circadian rhythms');
    recommendations.push('Consider adjusting sleep schedule for better alignment');
  }
  
  return recommendations.join('. ');
}

// Additional helper functions for detailed metrics

function extractHeartRatePatterns(vitalsData: any[]) {
  const heartRates = vitalsData
    .filter(v => v.heart_rate && v.heart_rate > 0)
    .map(v => ({
      timestamp: v.recorded_at,
      heart_rate: v.heart_rate
    }));
  
  return {
    average_heart_rate: heartRates.length > 0 
      ? heartRates.reduce((sum, hr) => sum + hr.heart_rate, 0) / heartRates.length 
      : 0,
    heart_rate_variability: calculateHeartRateVariability(heartRates),
    patterns: heartRates
  };
}

function extractMovementPatterns(activityData: any[]) {
  return {
    total_movements: activityData.length,
    movement_intensity: activityData.map(a => a.intensity_level || 'low'),
    movement_times: activityData.map(a => a.session_date)
  };
}

function extractBreathingPatterns(vitalsData: any[]) {
  // In a real implementation, this would analyze respiratory rate data
  return {
    average_respiratory_rate: 20, // Simulated
    breathing_irregularities: 0,
    patterns: []
  };
}

function extractTemperaturePatterns(vitalsData: any[]) {
  const temperatures = vitalsData
    .filter(v => v.temperature && v.temperature > 0)
    .map(v => ({
      timestamp: v.recorded_at,
      temperature: v.temperature
    }));
  
  return {
    average_temperature: temperatures.length > 0
      ? temperatures.reduce((sum, t) => sum + t.temperature, 0) / temperatures.length
      : 0,
    temperature_variation: temperatures.length > 1
      ? Math.max(...temperatures.map(t => t.temperature)) - Math.min(...temperatures.map(t => t.temperature))
      : 0,
    patterns: temperatures
  };
}

function calculateHeartRateVariability(heartRates: any[]): number {
  if (heartRates.length < 2) return 0;
  
  const rates = heartRates.map(hr => hr.heart_rate);
  const differences = [];
  
  for (let i = 1; i < rates.length; i++) {
    differences.push(Math.abs(rates[i] - rates[i - 1]));
  }
  
  const meanDifference = differences.reduce((sum, diff) => sum + diff, 0) / differences.length;
  return Math.round(meanDifference * 100) / 100;
}