import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface NotificationRequest {
  user_id: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  priority: 'low' | 'normal' | 'high' | 'critical';
  animal_id?: string;
  alert_type?: string;
}

interface PushMessage {
  to: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  priority?: 'default' | 'normal' | 'high';
  sound?: 'default';
  badge?: number;
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Parse request body
    const notificationData: NotificationRequest = await req.json();
    
    console.log('Processing push notification request:', {
      user_id: notificationData.user_id,
      title: notificationData.title,
      priority: notificationData.priority,
      alert_type: notificationData.alert_type
    });

    // Get user's push token and notification preferences
    const { data: user, error: userError } = await supabaseClient
      .from('users')
      .select('push_token, push_notifications_enabled, notification_preferences')
      .eq('id', notificationData.user_id)
      .single();

    if (userError) {
      console.error('Error fetching user:', userError);
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check if user has notifications enabled
    if (!user.push_notifications_enabled) {
      console.log('Push notifications disabled for user:', notificationData.user_id);
      return new Response(
        JSON.stringify({ message: 'Notifications disabled for user' }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check if user has a valid push token
    if (!user.push_token || user.push_token.startsWith('mock-token')) {
      console.log('No valid push token for user:', notificationData.user_id);
      
      // Log the notification attempt for when real push tokens are available
      await supabaseClient
        .from('notification_logs')
        .insert({
          user_id: notificationData.user_id,
          title: notificationData.title,
          body: notificationData.body,
          priority: notificationData.priority,
          animal_id: notificationData.animal_id,
          alert_type: notificationData.alert_type,
          status: 'no_token',
          created_at: new Date().toISOString()
        });
      
      return new Response(
        JSON.stringify({ message: 'No push token available (logged for future delivery)' }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check notification preferences (if implemented)
    const preferences = user.notification_preferences || {};
    if (notificationData.alert_type && preferences[notificationData.alert_type] === false) {
      console.log('Notification type disabled by user preferences:', notificationData.alert_type);
      return new Response(
        JSON.stringify({ message: 'Notification type disabled by user' }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Prepare push message for Expo Push API
    const pushMessage: PushMessage = {
      to: user.push_token,
      title: notificationData.title,
      body: notificationData.body,
      data: {
        ...notificationData.data,
        timestamp: new Date().toISOString(),
        user_id: notificationData.user_id,
        animal_id: notificationData.animal_id,
        alert_type: notificationData.alert_type
      },
      priority: mapPriorityToExpo(notificationData.priority),
      sound: 'default',
      badge: 1
    };

    // Send push notification via Expo Push API
    const expoPushResponse = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pushMessage),
    });

    const expoPushResult = await expoPushResponse.json();
    
    console.log('Expo push response:', expoPushResult);

    // Log notification in database
    const logData = {
      user_id: notificationData.user_id,
      title: notificationData.title,
      body: notificationData.body,
      priority: notificationData.priority,
      animal_id: notificationData.animal_id,
      alert_type: notificationData.alert_type,
      push_token: user.push_token,
      expo_response: expoPushResult,
      status: expoPushResponse.ok ? 'sent' : 'failed',
      created_at: new Date().toISOString()
    };

    const { error: logError } = await supabaseClient
      .from('notification_logs')
      .insert(logData);

    if (logError) {
      console.error('Error logging notification:', logError);
    }

    // Check for Expo push errors
    if (!expoPushResponse.ok || (expoPushResult.data && expoPushResult.data.status === 'error')) {
      const errorMessage = expoPushResult.data?.message || 'Failed to send push notification';
      console.error('Expo push error:', errorMessage);
      
      return new Response(
        JSON.stringify({ 
          error: 'Failed to send push notification',
          details: errorMessage,
          expo_response: expoPushResult
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Success response
    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'Push notification sent successfully',
        notification_id: expoPushResult.data?.id,
        expo_response: expoPushResult
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error in send-push-notification function:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

// Helper function to map priority levels
function mapPriorityToExpo(priority: string): 'default' | 'normal' | 'high' {
  switch (priority) {
    case 'critical':
    case 'high':
      return 'high';
    case 'normal':
      return 'normal';
    case 'low':
    default:
      return 'default';
  }
}