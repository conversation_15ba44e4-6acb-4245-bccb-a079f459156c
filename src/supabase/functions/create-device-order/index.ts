// @verify_jwt: true
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface OrderRequest {
  product_id: string;
  quantity: number;
  shipping_address: {
    line1: string;
    line2?: string;
    city: string;
    state_province: string;
    postal_code: string;
    country: string;
  };
  notes?: string;
}

interface OrderResponse {
  success: boolean;
  order_id?: string;
  total_amount?: number;
  payment_intent_id?: string;
  error?: string;
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    // Verify request method
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        { 
          status: 405,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // Get authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ success: false, error: 'Authorization header required' }),
        { 
          status: 401,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify JWT and get user
    const jwt = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(jwt);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid authentication' }),
        { 
          status: 401,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // Parse request body
    const orderRequest: OrderRequest = await req.json();
    
    // Validate required fields
    if (!orderRequest.product_id || !orderRequest.quantity || !orderRequest.shipping_address) {
      return new Response(
        JSON.stringify({ success: false, error: 'Missing required fields: product_id, quantity, shipping_address' }),
        { 
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // Validate shipping address
    const { line1, city, state_province, postal_code, country } = orderRequest.shipping_address;
    if (!line1 || !city || !state_province || !postal_code || !country) {
      return new Response(
        JSON.stringify({ success: false, error: 'Incomplete shipping address' }),
        { 
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // Validate quantity
    if (orderRequest.quantity <= 0 || !Number.isInteger(orderRequest.quantity)) {
      return new Response(
        JSON.stringify({ success: false, error: 'Quantity must be a positive integer' }),
        { 
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // Fetch product details
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('*')
      .eq('id', orderRequest.product_id)
      .eq('is_active', true)
      .single();

    if (productError || !product) {
      console.error('Product fetch error:', productError);
      return new Response(
        JSON.stringify({ success: false, error: 'Product not found or inactive' }),
        { 
          status: 404,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // Check stock availability
    if (product.stock_quantity < orderRequest.quantity) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: `Insufficient stock. Available: ${product.stock_quantity}, Requested: ${orderRequest.quantity}` 
        }),
        { 
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // Calculate total amount
    const totalAmount = product.price * orderRequest.quantity;

    // Create order record
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        user_id: user.id,
        total_amount: totalAmount,
        currency: product.currency,
        status: 'pending',
        shipping_address_line1: orderRequest.shipping_address.line1,
        shipping_address_line2: orderRequest.shipping_address.line2 || null,
        shipping_city: orderRequest.shipping_address.city,
        shipping_state_province: orderRequest.shipping_address.state_province,
        shipping_postal_code: orderRequest.shipping_address.postal_code,
        shipping_country: orderRequest.shipping_address.country,
        payment_status: 'pending',
        notes: orderRequest.notes || null
      })
      .select()
      .single();

    if (orderError || !order) {
      console.error('Order creation error:', orderError);
      return new Response(
        JSON.stringify({ success: false, error: 'Failed to create order' }),
        { 
          status: 500,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // Create order item
    const { error: orderItemError } = await supabase
      .from('order_items')
      .insert({
        order_id: order.id,
        product_id: orderRequest.product_id,
        quantity: orderRequest.quantity,
        price_at_purchase: product.price
      });

    if (orderItemError) {
      console.error('Order item creation error:', orderItemError);
      
      // Rollback: Delete the order if order item creation fails
      await supabase
        .from('orders')
        .delete()
        .eq('id', order.id);

      return new Response(
        JSON.stringify({ success: false, error: 'Failed to create order item' }),
        { 
          status: 500,
          headers: { 
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      );
    }

    // For now, we'll simulate payment processing
    // In a real implementation, you would integrate with Stripe or another payment provider
    const mockPaymentIntentId = `pi_mock_${order.id.replace(/-/g, '').substring(0, 16)}`;

    // Update order with payment intent ID
    const { error: updateError } = await supabase
      .from('orders')
      .update({ payment_intent_id: mockPaymentIntentId })
      .eq('id', order.id);

    if (updateError) {
      console.error('Order update error:', updateError);
    }

    // Return success response
    const response: OrderResponse = {
      success: true,
      order_id: order.id,
      total_amount: totalAmount,
      payment_intent_id: mockPaymentIntentId
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 200,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      }
    );

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error' 
      }),
      { 
        status: 500,
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      }
    );
  }
});