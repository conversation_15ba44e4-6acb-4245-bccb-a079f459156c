
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the request body
    const { event, session } = await req.json()

    // Create a Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Handle different auth events
    if (event === 'SIGNED_IN') {
      // User has signed in, ensure they have a record in the users table
      const { user } = session

      // Check if user exists in our users table
      const { data: existingUser, error: queryError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single()

      if (queryError && queryError.code !== 'PGRST116') {
        // PGRST116 is the error code for "no rows returned" - that's expected if the user doesn't exist
        throw queryError
      }

      if (!existingUser) {
        // Create a new user record
        const { error: insertError } = await supabase
          .from('users')
          .insert({
            id: user.id,
            email: user.email,
            name: user.user_metadata?.full_name || user.user_metadata?.name || null,
            is_premium: false,
          })

        if (insertError) throw insertError
      }
    } else if (event === 'USER_UPDATED') {
      // User details have been updated, sync changes to users table
      const { user } = session

      if (!user) {
        throw new Error('No user data provided for USER_UPDATED event')
      }

      // Update user record in users table with new details
      const { error: updateError } = await supabase
        .from('users')
        .update({
          email: user.email,
          name: user.user_metadata?.full_name || user.user_metadata?.name || null,
        })
        .eq('id', user.id)

      if (updateError) {
        console.error('Failed to update user in users table:', updateError)
        throw updateError
      }

      console.log(`Successfully updated user ${user.id} in users table`)
    }

    // Return success response
    return new Response(
      JSON.stringify({ success: true }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
