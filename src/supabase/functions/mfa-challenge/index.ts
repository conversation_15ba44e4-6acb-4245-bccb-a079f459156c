import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

interface MfaChallengeRequest {
  email: string;
}

interface MfaChallengeResponse {
  success: boolean;
  challenge_id?: string;
  requires_mfa?: boolean;
  error?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse request body
    const { email }: MfaChallengeRequest = await req.json();
    
    if (!email) {
      return new Response(
        JSON.stringify({ success: false, error: 'Email is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`MFA challenge requested for email: ${email}`);

    // Find user by email
    const { data: users, error: userError } = await supabase.auth.admin.listUsers();
    
    if (userError) {
      console.error('Error fetching users:', userError);
      return new Response(
        JSON.stringify({ success: false, error: 'Failed to verify user' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const user = users.users.find(u => u.email === email);
    
    if (!user) {
      // Don't reveal if user exists or not for security
      return new Response(
        JSON.stringify({ 
          success: true, 
          requires_mfa: false,
          challenge_id: null
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check if user has MFA enabled
    const { data: mfaSettings, error: mfaError } = await supabase
      .from('user_mfa_settings')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_enabled', true)
      .single();

    if (mfaError || !mfaSettings) {
      // User doesn't have MFA enabled
      return new Response(
        JSON.stringify({ 
          success: true, 
          requires_mfa: false,
          challenge_id: null
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Generate a challenge ID
    const challengeId = generateChallengeId();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes from now

    // Store the challenge
    const { error: challengeError } = await supabase
      .from('mfa_challenges')
      .insert({
        challenge_id: challengeId,
        user_id: user.id,
        email: email,
        expires_at: expiresAt.toISOString(),
        is_verified: false,
        created_at: new Date().toISOString()
      });

    if (challengeError) {
      console.error('Error storing MFA challenge:', challengeError);
      return new Response(
        JSON.stringify({ success: false, error: 'Failed to create MFA challenge' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Clean up expired challenges
    await supabase
      .from('mfa_challenges')
      .delete()
      .lt('expires_at', new Date().toISOString());

    console.log(`MFA challenge created for user ${user.id}: ${challengeId}`);

    const response: MfaChallengeResponse = {
      success: true,
      requires_mfa: true,
      challenge_id: challengeId
    };

    return new Response(
      JSON.stringify(response),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('MFA challenge error:', error);
    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

// Helper Functions

function generateChallengeId(): string {
  // Generate a secure random challenge ID
  const bytes = new Uint8Array(32);
  crypto.getRandomValues(bytes);
  
  // Convert to hex string
  return Array.from(bytes)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}