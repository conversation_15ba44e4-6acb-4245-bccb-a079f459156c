// @verify_jwt: true
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// Function ID: get-realtime-animal-analysis

interface RealtimeAnalysisRequest {
  animalId: string;
}

interface RealtimeAnalysisResponse {
  analysis: string;
  timestamp: string;
  dataPoints: {
    speed: number | null;
    location: string | null;
    heartRate: number | null;
    temperature: number | null;
    speedHistory: Array<{ timestamp: string; speed: number }>;
  };
}

serve(async (req) => {
  // Define CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  try {
    // Handle OPTIONS preflight requests
    if (req.method === 'OPTIONS') {
      return new Response('ok', { headers: corsHeaders });
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify JWT and get user
    const jwt = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(jwt);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Parse request body
    const { animalId }: RealtimeAnalysisRequest = await req.json();
    
    if (!animalId) {
      return new Response(
        JSON.stringify({ error: 'animalId is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Fetching real-time analysis for animal ${animalId} for user ${user.id}`);

    // Fetch animal profile for basic info and current speed/location
    const { data: animal, error: animalError } = await supabase
      .from('animals')
      .select('*')
      .eq('id', animalId)
      .eq('user_id', user.id)
      .single();

    if (animalError || !animal) {
      console.error('Animal fetch error:', animalError);
      return new Response(
        JSON.stringify({ error: 'Animal not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Fetch latest vitals (heart rate and temperature from device)
    const { data: latestVitals, error: vitalsError } = await supabase
      .from('vitals')
      .select('*')
      .eq('animal_id', animalId)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(1);

    const vitals = latestVitals?.[0] || null;

    // Fetch recent speed history (last 3 hours)
    const threeHoursAgo = new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString();
    const { data: speedHistory, error: speedError } = await supabase
      .from('vitals')
      .select('created_at, speed')
      .eq('animal_id', animalId)
      .eq('user_id', user.id)
      .gte('created_at', threeHoursAgo)
      .order('created_at', { ascending: false })
      .limit(20);

    // Prepare data points
    const dataPoints = {
      speed: animal.speed || 0,
      location: animal.location || 'Unknown',
      heartRate: vitals?.heart_rate || null,
      temperature: vitals?.temperature || null,
      speedHistory: speedHistory?.map(record => ({
        timestamp: record.created_at,
        speed: record.speed || 0
      })) || []
    };

    // Generate contextual analysis based on available data
    let analysis = `${animal.name}'s Current Status:\n\n`;
    
    // Speed analysis
    if (dataPoints.speed !== null) {
      if (dataPoints.speed === 0) {
        analysis += `🐎 Currently at rest (Speed: 0 km/h)\n`;
      } else if (dataPoints.speed < 5) {
        analysis += `🚶 Walking pace (Speed: ${dataPoints.speed} km/h)\n`;
      } else if (dataPoints.speed < 15) {
        analysis += `🏃 Trotting (Speed: ${dataPoints.speed} km/h)\n`;
      } else if (dataPoints.speed < 25) {
        analysis += `🏇 Cantering (Speed: ${dataPoints.speed} km/h)\n`;
      } else {
        analysis += `⚡ Galloping (Speed: ${dataPoints.speed} km/h)\n`;
      }
    }

    // Location analysis
    if (dataPoints.location && dataPoints.location !== 'Unknown') {
      analysis += `📍 Location: ${dataPoints.location}\n`;
    }

    // Heart rate analysis
    if (dataPoints.heartRate) {
      analysis += `❤️ Heart Rate: ${dataPoints.heartRate} bpm`;
      if (dataPoints.heartRate < 30) {
        analysis += ` (Resting)\n`;
      } else if (dataPoints.heartRate < 60) {
        analysis += ` (Light activity)\n`;
      } else if (dataPoints.heartRate < 100) {
        analysis += ` (Moderate activity)\n`;
      } else if (dataPoints.heartRate < 150) {
        analysis += ` (Intense activity)\n`;
      } else {
        analysis += ` (Very high - monitor closely)\n`;
      }
    }

    // Temperature analysis
    if (dataPoints.temperature) {
      analysis += `🌡️ Temperature: ${dataPoints.temperature}°C`;
      if (dataPoints.temperature < 37.5) {
        analysis += ` (Below normal)\n`;
      } else if (dataPoints.temperature <= 38.5) {
        analysis += ` (Normal range)\n`;
      } else if (dataPoints.temperature <= 39.5) {
        analysis += ` (Slightly elevated)\n`;
      } else {
        analysis += ` (High - requires attention)\n`;
      }
    }

    // Speed history analysis
    if (dataPoints.speedHistory.length > 0) {
      const avgSpeed = dataPoints.speedHistory.reduce((sum, record) => sum + record.speed, 0) / dataPoints.speedHistory.length;
      analysis += `\n📊 Recent Activity (last 3 hours):\n`;
      analysis += `Average speed: ${avgSpeed.toFixed(1)} km/h\n`;
      
      const recentActivity = avgSpeed > 5 ? 'Active period' : 'Mostly resting';
      analysis += `Pattern: ${recentActivity}\n`;
    }

    // Activity state inference
    analysis += `\n🔍 Inferred State: `;
    if (dataPoints.speed === 0 && dataPoints.heartRate && dataPoints.heartRate < 40) {
      analysis += `Resting/Sleeping`;
    } else if (dataPoints.speed > 0 && dataPoints.heartRate && dataPoints.heartRate > 60) {
      analysis += `Active Training/Exercise`;
    } else if (dataPoints.speed === 0 && dataPoints.heartRate && dataPoints.heartRate < 60) {
      analysis += `Calm/Grazing`;
    } else {
      analysis += `Normal Activity`;
    }

    // Add timestamp
    const timestamp = new Date().toISOString();
    analysis += `\n\n⏰ Analysis generated at: ${new Date().toLocaleString()}`;

    // Store the real-time analysis as a health assessment
    const { data: analysisRecord, error: insertError } = await supabase
      .from('ai_health_assessments')
      .insert({
        animal_id: animalId,
        user_id: user.id,
        assessment_text: analysis,
        severity_level: 'normal', // Real-time analysis is typically informational
        generated_at: timestamp
      })
      .select()
      .single();

    if (insertError) {
      console.error('Analysis insert error:', insertError);
    }

    const response: RealtimeAnalysisResponse = {
      analysis,
      timestamp,
      dataPoints
    };

    console.log('Generated real-time analysis:', response);

    return new Response(
      JSON.stringify({
        success: true,
        data: response,
        analysisId: analysisRecord?.assessment_id
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});