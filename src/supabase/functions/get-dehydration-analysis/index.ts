// @verify_jwt: true
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface DehydrationAnalysisRequest {
  animal_id: string;
  days?: number;
}

interface DehydrationReading {
  id: string;
  hydration_level: number;
  hydration_status: string;
  bioimpedance_reading: number;
  body_temperature?: number;
  created_at: string;
  signal_quality?: string;
}

interface DehydrationAnalysis {
  overall_status: 'excellent' | 'good' | 'concerning' | 'critical';
  trend: 'improving' | 'stable' | 'declining';
  average_hydration: number;
  hydration_variance: number;
  risk_factors: string[];
  recommendations: string[];
  alerts: {
    type: 'dehydration_risk' | 'trend_warning' | 'data_quality';
    severity: 'low' | 'medium' | 'high';
    message: string;
  }[];
  insights: {
    category: 'hydration_patterns' | 'environmental_factors' | 'health_indicators';
    finding: string;
    confidence: number;
  }[];
  next_actions: string[];
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response('ok', {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    // Get request data
    const { animal_id, days = 7 }: DehydrationAnalysisRequest = await req.json();

    if (!animal_id) {
      return new Response('Missing animal_id', { status: 400 });
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get JWT from Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response('Missing authorization header', { status: 401 });
    }

    // Verify JWT and get user
    const jwt = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(jwt);
    
    if (authError || !user) {
      return new Response('Invalid token', { status: 401 });
    }

    // Fetch dehydration readings for the specified period
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data: readings, error: readingsError } = await supabase
      .from('dehydration_logs')
      .select('*')
      .eq('animal_id', animal_id)
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });

    if (readingsError) {
      console.error('Error fetching dehydration readings:', readingsError);
      return new Response('Failed to fetch readings', { status: 500 });
    }

    if (!readings || readings.length === 0) {
      return new Response(JSON.stringify({
        overall_status: 'unknown',
        trend: 'stable',
        average_hydration: 0,
        hydration_variance: 0,
        risk_factors: ['Insufficient data'],
        recommendations: ['Take regular hydration readings to establish baseline'],
        alerts: [],
        insights: [],
        next_actions: ['Start regular hydration monitoring']
      }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Analyze the readings
    const analysis = analyzeDehydrationData(readings as DehydrationReading[]);

    return new Response(JSON.stringify(analysis), {
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
    });

  } catch (error) {
    console.error('Error in dehydration analysis:', error);
    return new Response('Internal server error', { status: 500 });
  }
});

function analyzeDehydrationData(readings: DehydrationReading[]): DehydrationAnalysis {
  // Calculate basic statistics
  const hydrationLevels = readings.map(r => r.hydration_level);
  const averageHydration = hydrationLevels.reduce((a, b) => a + b, 0) / hydrationLevels.length;
  const variance = calculateVariance(hydrationLevels);
  
  // Analyze trend
  const trend = analyzeTrend(readings);
  
  // Determine overall status
  const overallStatus = determineOverallStatus(averageHydration, variance, readings);
  
  // Identify risk factors
  const riskFactors = identifyRiskFactors(readings, averageHydration, variance);
  
  // Generate recommendations
  const recommendations = generateRecommendations(overallStatus, trend, riskFactors, readings);
  
  // Create alerts
  const alerts = generateAlerts(readings, trend, averageHydration);
  
  // Generate insights
  const insights = generateInsights(readings, averageHydration, variance);
  
  // Determine next actions
  const nextActions = determineNextActions(overallStatus, trend, readings);
  
  return {
    overall_status: overallStatus,
    trend,
    average_hydration: Math.round(averageHydration * 100) / 100,
    hydration_variance: Math.round(variance * 100) / 100,
    risk_factors: riskFactors,
    recommendations,
    alerts,
    insights,
    next_actions: nextActions
  };
}

function calculateVariance(values: number[]): number {
  const mean = values.reduce((a, b) => a + b, 0) / values.length;
  const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
  return squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
}

function analyzeTrend(readings: DehydrationReading[]): 'improving' | 'stable' | 'declining' {
  if (readings.length < 3) return 'stable';
  
  const recentReadings = readings.slice(-5); // Last 5 readings
  const earlierReadings = readings.slice(0, Math.min(5, readings.length - 5));
  
  if (earlierReadings.length === 0) return 'stable';
  
  const recentAvg = recentReadings.reduce((a, b) => a + b.hydration_level, 0) / recentReadings.length;
  const earlierAvg = earlierReadings.reduce((a, b) => a + b.hydration_level, 0) / earlierReadings.length;
  
  const difference = recentAvg - earlierAvg;
  
  if (difference > 2) return 'improving';
  if (difference < -2) return 'declining';
  return 'stable';
}

function determineOverallStatus(
  averageHydration: number, 
  variance: number, 
  readings: DehydrationReading[]
): 'excellent' | 'good' | 'concerning' | 'critical' {
  const recentCritical = readings.slice(-3).some(r => r.hydration_status === 'severe_dehydration');
  const recentModerate = readings.slice(-5).some(r => r.hydration_status === 'moderate_dehydration');
  
  if (recentCritical || averageHydration < 60) return 'critical';
  if (recentModerate || averageHydration < 70 || variance > 100) return 'concerning';
  if (averageHydration >= 80 && variance < 50) return 'excellent';
  return 'good';
}

function identifyRiskFactors(
  readings: DehydrationReading[], 
  averageHydration: number, 
  variance: number
): string[] {
  const factors: string[] = [];
  
  if (averageHydration < 70) factors.push('Low average hydration level');
  if (variance > 100) factors.push('High hydration variability');
  
  const poorQualityReadings = readings.filter(r => r.signal_quality === 'poor').length;
  if (poorQualityReadings > readings.length * 0.3) {
    factors.push('Frequent poor signal quality readings');
  }
  
  const highTempReadings = readings.filter(r => r.body_temperature && r.body_temperature > 39).length;
  if (highTempReadings > 0) {
    factors.push('Elevated body temperature detected');
  }
  
  const recentDehydration = readings.slice(-7).filter(r => 
    r.hydration_status === 'moderate_dehydration' || r.hydration_status === 'severe_dehydration'
  ).length;
  if (recentDehydration > 2) {
    factors.push('Multiple dehydration episodes in recent period');
  }
  
  return factors;
}

function generateRecommendations(
  status: string, 
  trend: string, 
  riskFactors: string[], 
  readings: DehydrationReading[]
): string[] {
  const recommendations: string[] = [];
  
  if (status === 'critical') {
    recommendations.push('Immediate veterinary consultation required');
    recommendations.push('Increase water availability and monitor closely');
  }
  
  if (status === 'concerning' || trend === 'declining') {
    recommendations.push('Increase monitoring frequency to twice daily');
    recommendations.push('Review environmental conditions and water access');
  }
  
  if (riskFactors.includes('High hydration variability')) {
    recommendations.push('Establish consistent monitoring schedule');
    recommendations.push('Check for environmental stressors affecting hydration');
  }
  
  if (riskFactors.includes('Frequent poor signal quality readings')) {
    recommendations.push('Ensure proper sensor placement and animal restraint');
    recommendations.push('Check sensor calibration and battery levels');
  }
  
  if (readings.length < 10) {
    recommendations.push('Continue regular monitoring to establish baseline patterns');
  }
  
  if (status === 'excellent') {
    recommendations.push('Maintain current hydration management practices');
    recommendations.push('Continue regular monitoring schedule');
  }
  
  return recommendations;
}

function generateAlerts(
  readings: DehydrationReading[], 
  trend: string, 
  averageHydration: number
): DehydrationAnalysis['alerts'] {
  const alerts: DehydrationAnalysis['alerts'] = [];
  
  const latestReading = readings[readings.length - 1];
  if (latestReading?.hydration_status === 'severe_dehydration') {
    alerts.push({
      type: 'dehydration_risk',
      severity: 'high',
      message: 'Critical dehydration detected in latest reading'
    });
  }
  
  if (trend === 'declining') {
    alerts.push({
      type: 'trend_warning',
      severity: 'medium',
      message: 'Declining hydration trend observed over recent readings'
    });
  }
  
  const poorQualityCount = readings.filter(r => r.signal_quality === 'poor').length;
  if (poorQualityCount > readings.length * 0.4) {
    alerts.push({
      type: 'data_quality',
      severity: 'medium',
      message: 'High number of poor quality readings may affect analysis accuracy'
    });
  }
  
  return alerts;
}

function generateInsights(
  readings: DehydrationReading[], 
  averageHydration: number, 
  variance: number
): DehydrationAnalysis['insights'] {
  const insights: DehydrationAnalysis['insights'] = [];
  
  // Hydration patterns
  if (variance < 25) {
    insights.push({
      category: 'hydration_patterns',
      finding: 'Consistent hydration levels indicate stable water management',
      confidence: 0.85
    });
  } else if (variance > 100) {
    insights.push({
      category: 'hydration_patterns',
      finding: 'High variability suggests inconsistent hydration or external stressors',
      confidence: 0.75
    });
  }
  
  // Temperature correlation
  const tempReadings = readings.filter(r => r.body_temperature);
  if (tempReadings.length > 5) {
    const avgTemp = tempReadings.reduce((a, b) => a + (b.body_temperature || 0), 0) / tempReadings.length;
    if (avgTemp > 38.5) {
      insights.push({
        category: 'health_indicators',
        finding: 'Elevated body temperature may be contributing to dehydration risk',
        confidence: 0.70
      });
    }
  }
  
  // Data quality insights
  const excellentQuality = readings.filter(r => r.signal_quality === 'excellent').length;
  if (excellentQuality > readings.length * 0.8) {
    insights.push({
      category: 'environmental_factors',
      finding: 'High quality sensor readings indicate optimal monitoring conditions',
      confidence: 0.90
    });
  }
  
  return insights;
}

function determineNextActions(
  status: string, 
  trend: string, 
  readings: DehydrationReading[]
): string[] {
  const actions: string[] = [];
  
  if (status === 'critical') {
    actions.push('Contact veterinarian immediately');
    actions.push('Provide immediate water access');
    actions.push('Monitor every 2-4 hours until improvement');
  } else if (status === 'concerning') {
    actions.push('Increase monitoring frequency');
    actions.push('Review water sources and accessibility');
    actions.push('Schedule veterinary check within 24-48 hours');
  } else {
    actions.push('Continue regular monitoring schedule');
    if (readings.length < 14) {
      actions.push('Build baseline data with daily readings');
    }
  }
  
  if (trend === 'declining') {
    actions.push('Investigate potential causes of declining hydration');
    actions.push('Consider environmental or dietary changes');
  }
  
  return actions;
}