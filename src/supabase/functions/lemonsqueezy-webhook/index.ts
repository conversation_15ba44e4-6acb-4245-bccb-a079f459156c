import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { createHmac } from "https://deno.land/std@0.168.0/node/crypto.ts";

interface LemonSqueezyWebhookPayload {
  meta: {
    event_name: string;
    custom_data?: {
      user_id?: string;
      order_id?: string;
    };
  };
  data: {
    id: string;
    type: string;
    attributes: {
      store_id: number;
      customer_id: number;
      order_id: number;
      order_number: number;
      product_id: number;
      variant_id: number;
      product_name: string;
      variant_name: string;
      user_name: string;
      user_email: string;
      status: string;
      status_formatted: string;
      refunded: boolean;
      refunded_at: string | null;
      subtotal: number;
      discount_total: number;
      tax: number;
      total: number;
      subtotal_usd: number;
      discount_total_usd: number;
      tax_usd: number;
      total_usd: number;
      currency: string;
      currency_rate: string;
      created_at: string;
      updated_at: string;
      [key: string]: any;
    };
  };
}

function verifyWebhookSignature(payload: string, signature: string, secret: string): boolean {
  try {
    const hmac = createHmac('sha256', secret);
    hmac.update(payload);
    const expectedSignature = hmac.digest('hex');
    
    // Lemon Squeezy sends signature in format: sha256=<hash>
    const receivedSignature = signature.replace('sha256=', '');
    
    return expectedSignature === receivedSignature;
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    return false;
  }
}

serve(async (req) => {
  // Handle CORS for preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-signature',
      },
    });
  }

  try {
    // Only accept POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get webhook secret
    const webhookSecret = Deno.env.get('LEMONSQUEEZY_WEBHOOK_SECRET');
    if (!webhookSecret) {
      console.error('Missing webhook secret');
      return new Response(
        JSON.stringify({ error: 'Webhook not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get request body and signature
    const body = await req.text();
    const signature = req.headers.get('x-signature');

    if (!signature) {
      console.error('Missing webhook signature');
      return new Response(
        JSON.stringify({ error: 'Missing signature' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Verify webhook signature
    if (!verifyWebhookSignature(body, signature, webhookSecret)) {
      console.error('Invalid webhook signature');
      return new Response(
        JSON.stringify({ error: 'Invalid signature' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Parse webhook payload
    const payload: LemonSqueezyWebhookPayload = JSON.parse(body);
    console.log('Received webhook:', payload.meta.event_name, payload.data.id);

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Handle different webhook events
    switch (payload.meta.event_name) {
      case 'order_created':
        await handleOrderCreated(supabase, payload);
        break;
      
      case 'order_refunded':
        await handleOrderRefunded(supabase, payload);
        break;
      
      case 'subscription_created':
        await handleSubscriptionCreated(supabase, payload);
        break;
      
      case 'subscription_updated':
        await handleSubscriptionUpdated(supabase, payload);
        break;
      
      case 'subscription_cancelled':
        await handleSubscriptionCancelled(supabase, payload);
        break;
      
      case 'subscription_resumed':
        await handleSubscriptionResumed(supabase, payload);
        break;
      
      case 'subscription_expired':
        await handleSubscriptionExpired(supabase, payload);
        break;
      
      default:
        console.log('Unhandled webhook event:', payload.meta.event_name);
    }

    return new Response(
      JSON.stringify({ success: true }),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        } 
      }
    );

  } catch (error) {
    console.error('Error processing webhook:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        } 
      }
    );
  }
});

async function handleOrderCreated(supabase: any, payload: LemonSqueezyWebhookPayload) {
  try {
    const { data, meta } = payload;
    const { attributes } = data;
    
    console.log('Processing order created:', attributes.order_number);
    
    // Find user by email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', attributes.user_email)
      .single();
    
    if (userError || !user) {
      console.error('User not found for email:', attributes.user_email);
      return;
    }
    
    // Determine plan type from product name
    const planType = attributes.variant_name.toLowerCase().includes('monthly') ? 'monthly' : 'yearly';
    
    // Update or create order in database
    const { error: orderError } = await supabase
      .from('orders')
      .upsert({
        id: `ls_${data.id}`, // Prefix with 'ls_' for Lemon Squeezy orders
        user_id: user.id,
        product_id: attributes.variant_id.toString(),
        quantity: 1,
        total_amount: attributes.total_usd / 100, // Convert cents to dollars
        currency: 'USD',
        status: 'paid',
        payment_method: 'lemonsqueezy',
        plan_type: planType,
        lemonsqueezy_order_id: attributes.order_id,
        lemonsqueezy_order_number: attributes.order_number,
        created_at: attributes.created_at,
        updated_at: attributes.updated_at,
      });
    
    if (orderError) {
      console.error('Error updating order:', orderError);
      return;
    }
    
    // Update user subscription status
    const startDate = new Date();
    const endDate = new Date();
    
    if (planType === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1);
    } else {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }
    
    const { error: subscriptionError } = await supabase
      .from('users')
      .update({
        is_premium: true,
        subscription_type: planType,
        subscription_start_date: startDate.toISOString(),
        subscription_end_date: endDate.toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id);
    
    if (subscriptionError) {
      console.error('Error updating user subscription:', subscriptionError);
      return;
    }
    
    console.log(`Successfully processed order ${attributes.order_number} for user ${user.email}`);
    
  } catch (error) {
    console.error('Error handling order created:', error);
  }
}

async function handleOrderRefunded(supabase: any, payload: LemonSqueezyWebhookPayload) {
  try {
    const { data } = payload;
    const { attributes } = data;
    
    console.log('Processing order refunded:', attributes.order_number);
    
    // Update order status
    const { error: orderError } = await supabase
      .from('orders')
      .update({
        status: 'refunded',
        updated_at: new Date().toISOString(),
      })
      .eq('lemonsqueezy_order_id', attributes.order_id);
    
    if (orderError) {
      console.error('Error updating refunded order:', orderError);
      return;
    }
    
    // Find and update user subscription
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('email', attributes.user_email)
      .single();
    
    if (userError || !user) {
      console.error('User not found for refund:', attributes.user_email);
      return;
    }
    
    // Deactivate premium subscription
    const { error: subscriptionError } = await supabase
      .from('users')
      .update({
        is_premium: false,
        subscription_type: null,
        subscription_start_date: null,
        subscription_end_date: null,
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id);
    
    if (subscriptionError) {
      console.error('Error deactivating subscription:', subscriptionError);
      return;
    }
    
    console.log(`Successfully processed refund for order ${attributes.order_number}`);
    
  } catch (error) {
    console.error('Error handling order refunded:', error);
  }
}

// Additional handlers for subscription events
async function handleSubscriptionCreated(supabase: any, payload: LemonSqueezyWebhookPayload) {
  console.log('Subscription created:', payload.data.id);
  // Handle subscription creation logic here
}

async function handleSubscriptionUpdated(supabase: any, payload: LemonSqueezyWebhookPayload) {
  console.log('Subscription updated:', payload.data.id);
  // Handle subscription update logic here
}

async function handleSubscriptionCancelled(supabase: any, payload: LemonSqueezyWebhookPayload) {
  console.log('Subscription cancelled:', payload.data.id);
  // Handle subscription cancellation logic here
}

async function handleSubscriptionResumed(supabase: any, payload: LemonSqueezyWebhookPayload) {
  console.log('Subscription resumed:', payload.data.id);
  // Handle subscription resumption logic here
}

async function handleSubscriptionExpired(supabase: any, payload: LemonSqueezyWebhookPayload) {
  console.log('Subscription expired:', payload.data.id);
  // Handle subscription expiration logic here
}