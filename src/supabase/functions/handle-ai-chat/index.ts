// Redeploy to update environment variables
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface ChatRequest {
  message: string;
  conversationId: string;
  animalId?: string;
  attachments?: {
    url: string;
    type: 'image' | 'file';
    name: string;
  }[];
}

interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | {
    type: 'text' | 'image_url';
    text?: string;
    image_url?: {
      url: string;
      detail?: 'low' | 'high' | 'auto';
    };
  }[];
}

serve(async (req) => {
  // Set CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify JWT and get user
    const jwt = authHeader.replace('Bearer ', '');
    console.log('Verifying user authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser(jwt);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Parse request body
    let requestBody;
    try {
      requestBody = await req.json();
    } catch (parseError) {
      console.error('Failed to parse request body:', parseError);
      return new Response(
        JSON.stringify({ error: 'Invalid JSON in request body' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    const { message, conversationId, animalId, attachments }: ChatRequest = requestBody;
    
    if (!message || !conversationId) {
      return new Response(
        JSON.stringify({ error: 'Message and conversationId are required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Processing AI chat for user ${user.id}, conversation ${conversationId}`);

    // 🔐 Get OpenRouter API key from Supabase vault (secure)
    console.log('Fetching OpenRouter API key from vault...');
    const { data: secretData, error: secretError } = await supabase
      .from('vault')
      .select('secret')
      .eq('name', 'OPENROUTER_API_KEY')
      .single();
    
    const openrouterApiKey = secretData?.secret || Deno.env.get('OPENROUTER_API_KEY');
    
    if (!openrouterApiKey) {
      console.error('OpenRouter API key not found in vault or environment');
      return new Response(
        JSON.stringify({ error: 'AI service not configured. Please contact support.' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    console.log('OpenRouter API key retrieved successfully');

    // Fetch animal data if animalId is provided
    let animalContext = '';
    if (animalId) {
      console.log(`Fetching animal data for ID: ${animalId}`);
      const { data: animal, error: animalError } = await supabase
        .from('animals')
        .select('*')
        .eq('id', animalId)
        .eq('user_id', user.id)
        .single();

      if (animalError) {
        console.error('Error fetching animal data:', animalError);
      } else if (animal) {
        console.log(`Found animal: ${animal.name}`);
        animalContext = `\n\nAnimal Context:\n- Name: ${animal.name}\n- Breed: ${animal.breed}\n- Age: ${animal.age} years\n- Gender: ${animal.gender}\n- Weight: ${animal.weight || 'Not specified'}\n- Notes: ${animal.notes || 'None'}`;
      }
    }

    // Fetch recent conversation history
    const { data: recentMessages, error: messagesError } = await supabase
      .from('ai_messages')
      .select('role, content')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (messagesError) {
      console.error('Error fetching conversation history:', messagesError);
    }

    // Build conversation history for context
    const conversationHistory: OpenAIMessage[] = [];
    
    if (recentMessages && recentMessages.length > 0) {
      // Reverse to get chronological order
      recentMessages.reverse().forEach(msg => {
        conversationHistory.push({
          role: msg.role,
          content: msg.content
        });
      });
    }

    // System prompt optimized for Mistral model
    const systemPrompt = `You are an expert AI assistant specializing in livestock and animal care, training, health, and behavior. You provide helpful, accurate, and personalized advice for farmers and animal owners.

Key capabilities:
- Provide personalized training plans and health recommendations
- Answer questions about nutrition, exercise, veterinary care, and behavior
- Offer guidance on livestock management and welfare
- Help with breeding, feeding schedules, and medication management
- Maintain a friendly, professional, and empathetic tone

Important guidelines:
- Always prioritize animal safety and welfare
- Recommend veterinary consultation for serious health concerns
- Provide practical, evidence-based advice
- Be encouraging and supportive to animal owners
- Focus on preventive care and best practices${animalContext}`;

    // Prepare messages for OpenAI
    const messages: OpenAIMessage[] = [
      {
        role: 'system',
        content: systemPrompt
      },
      ...conversationHistory
    ];

    // Handle current user message (text-only for Mistral)
    let userMessage = message;
    
    // If there are attachments, mention them in the text
    if (attachments && attachments.length > 0) {
      const attachmentDescriptions = attachments.map(att => 
        `${att.type}: ${att.name}`
      ).join(', ');
      userMessage += `\n\n[User has shared files: ${attachmentDescriptions}. Note: I cannot view images directly, but I can help based on your description of what you see.]`;
    }
    
    messages.push({
      role: 'user',
      content: userMessage
    });

    console.log('Sending request to OpenRouter with', messages.length, 'messages');

    // 🤖 Call OpenRouter API with Mistral model
    const openrouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openrouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://magically.life',
        'X-Title': 'Magically - AI Livestock Management'
      },
      body: JSON.stringify({
        model: 'mistralai/mistral-7b-instruct-v0.2',
        messages,
        max_tokens: 1500,
        temperature: 0.7,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
      })
    });

    if (!openrouterResponse.ok) {
      const errorText = await openrouterResponse.text();
      console.error('OpenRouter API error:', errorText);
      return new Response(
        JSON.stringify({ error: 'Failed to get AI response', details: errorText }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const openrouterResult = await openrouterResponse.json();
    const aiResponse = openrouterResult.choices[0]?.message?.content;

    if (!aiResponse) {
      return new Response(
        JSON.stringify({ error: 'No response from AI' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('Received AI response, storing messages...');

    // Use the new safe database function to handle the complete message flow
    const { data: dbResult, error: dbError } = await supabase.rpc('handle_chat_message_flow', {
      p_conversation_id: conversationId,
      p_user_id: user.id,
      p_user_message: message,
      p_ai_response: aiResponse,
      p_user_metadata: attachments ? { attachments } : null,
      p_ai_metadata: {
        model: 'mistralai/mistral-7b-instruct-v0.2',
        usage: openrouterResult.usage,
        response_time_ms: Date.now() - Date.now() // Will be calculated properly
      }
    });

    if (dbError) {
      console.error('Database operation failed:', dbError);
      // Don't fail the request, just log the error
    } else if (dbResult && !dbResult.success) {
      console.error('Database function returned error:', dbResult.error);
      // Don't fail the request, just log the error
    } else {
      console.log('Messages stored successfully:', dbResult);
    }

    // Return AI response
    return new Response(
      JSON.stringify({
        success: true,
        response: aiResponse,
        conversationId,
        usage: openrouterResult.usage
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});