import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface PredictiveInsightsRequest {
  animalId: string;
  predictionHorizons?: number[]; // Days: [7, 30, 90, 365]
  includeRiskAssessment?: boolean;
  includeEnvironmentalFactors?: boolean;
}

interface HealthPrediction {
  date: string;
  healthScore: number;
  weight: number;
  activityLevel: string;
  stressLevel: string;
  sleepQuality: number;
  confidence: number;
}

interface RiskPrediction {
  riskType: string;
  probability: number;
  timeframe: number;
  contributingFactors: string[];
  preventiveActions: string[];
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get request data
    const { 
      animalId, 
      predictionHorizons = [7, 30, 90], 
      includeRiskAssessment = true, 
      includeEnvironmentalFactors = true 
    }: PredictiveInsightsRequest = await req.json();

    console.log(`Starting predictive insights generation for animal ${animalId}`);

    // Get animal data
    const { data: animal, error: animalError } = await supabase
      .from('animals')
      .select('*')
      .eq('id', animalId)
      .single();

    if (animalError || !animal) {
      throw new Error(`Animal not found: ${animalError?.message}`);
    }

    // Get historical data for prediction models
    const historicalData = await gatherHistoricalData(supabase, animalId);
    
    // Generate predictions for each horizon
    const predictions = [];
    
    for (const horizonDays of predictionHorizons) {
      console.log(`Generating ${horizonDays}-day predictions`);
      
      const prediction = await generateHealthPredictions(
        animal,
        historicalData,
        horizonDays,
        includeEnvironmentalFactors
      );
      
      // Save prediction to database
      const { data: savedPrediction, error: saveError } = await supabase
        .from('predictive_health_insights')
        .insert({
          animal_id: animalId,
          prediction_date: new Date().toISOString(),
          prediction_horizon_days: horizonDays,
          predicted_health_score: prediction.healthScore,
          predicted_weight_kg: prediction.weight,
          predicted_activity_level: prediction.activityLevel,
          predicted_stress_level: prediction.stressLevel,
          predicted_sleep_quality: prediction.sleepQuality,
          disease_risk_probability: prediction.diseaseRisk,
          injury_risk_probability: prediction.injuryRisk,
          behavioral_issue_risk: prediction.behavioralRisk,
          environmental_stress_risk: prediction.environmentalStressRisk,
          vital_signs_forecast: prediction.vitalSignsForecast,
          weight_trend_forecast: prediction.weightTrendForecast,
          activity_pattern_forecast: prediction.activityPatternForecast,
          prediction_confidence: prediction.confidence,
          model_accuracy_score: prediction.modelAccuracy,
          contributing_factors: prediction.contributingFactors,
          preventive_actions: prediction.preventiveActions,
          environmental_adjustments: prediction.environmentalAdjustments,
          care_schedule_recommendations: prediction.careScheduleRecommendations,
          prediction_model_version: '1.0.0',
          training_data_period_days: 90
        })
        .select()
        .single();
      
      if (saveError) {
        console.error(`Error saving ${horizonDays}-day prediction:`, saveError);
      } else {
        predictions.push(savedPrediction);
      }
    }
    
    // Generate risk assessments
    let riskAssessments = [];
    if (includeRiskAssessment) {
      riskAssessments = await generateRiskAssessments(animal, historicalData, supabase);
    }
    
    // Generate predictive alerts
    const predictiveAlerts = await generatePredictiveAlerts(animal, predictions, riskAssessments, supabase);
    
    console.log(`Predictive insights generation completed for animal ${animalId}`);

    return new Response(
      JSON.stringify({
        success: true,
        predictions: predictions,
        riskAssessments: riskAssessments,
        predictiveAlerts: predictiveAlerts,
        modelMetadata: {
          version: '1.0.0',
          trainingDataPeriod: 90,
          predictionHorizons: predictionHorizons,
          dataQuality: calculateDataQuality(historicalData)
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('Predictive insights error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Predictive insights generation failed'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});

async function gatherHistoricalData(supabase: any, animalId: string) {
  const endDate = new Date();
  const startDate = new Date(endDate.getTime() - (90 * 24 * 60 * 60 * 1000)); // 90 days
  
  // Get vitals data
  const { data: vitals } = await supabase
    .from('vitals')
    .select('*')
    .eq('animal_id', animalId)
    .gte('recorded_at', startDate.toISOString())
    .order('recorded_at', { ascending: false });
  
  // Get stress analysis data
  const { data: stressData } = await supabase
    .from('stress_analysis')
    .select('*')
    .eq('animal_id', animalId)
    .gte('analysis_timestamp', startDate.toISOString())
    .order('analysis_timestamp', { ascending: false });
  
  // Get sleep analysis data
  const { data: sleepData } = await supabase
    .from('sleep_analysis')
    .select('*')
    .eq('animal_id', animalId)
    .gte('sleep_date', startDate.toISOString().split('T')[0])
    .order('sleep_date', { ascending: false });
  
  // Get environmental data
  const { data: environmentalData } = await supabase
    .from('environmental_data')
    .select('*')
    .eq('animal_id', animalId)
    .gte('recorded_at', startDate.toISOString())
    .order('recorded_at', { ascending: false });
  
  // Get feeding data
  const { data: feedingData } = await supabase
    .from('feeding_schedules')
    .select('*')
    .eq('animal_id', animalId)
    .gte('created_at', startDate.toISOString())
    .order('created_at', { ascending: false });
  
  // Get medication data
  const { data: medicationData } = await supabase
    .from('medications')
    .select('*')
    .eq('animal_id', animalId)
    .gte('created_at', startDate.toISOString())
    .order('created_at', { ascending: false });
  
  return {
    vitals: vitals || [],
    stress: stressData || [],
    sleep: sleepData || [],
    environmental: environmentalData || [],
    feeding: feedingData || [],
    medications: medicationData || []
  };
}

async function generateHealthPredictions(
  animal: any,
  historicalData: any,
  horizonDays: number,
  includeEnvironmental: boolean
): Promise<any> {
  
  // Analyze historical trends
  const trends = analyzeHistoricalTrends(historicalData);
  
  // Predict health score
  const predictedHealthScore = predictHealthScore(trends, horizonDays, animal);
  
  // Predict weight
  const predictedWeight = predictWeight(historicalData.vitals, horizonDays, animal);
  
  // Predict activity level
  const predictedActivityLevel = predictActivityLevel(trends, horizonDays);
  
  // Predict stress level
  const predictedStressLevel = predictStressLevel(historicalData.stress, horizonDays, includeEnvironmental ? historicalData.environmental : []);
  
  // Predict sleep quality
  const predictedSleepQuality = predictSleepQuality(historicalData.sleep, horizonDays);
  
  // Calculate risk probabilities
  const diseaseRisk = calculateDiseaseRisk(trends, horizonDays, animal);
  const injuryRisk = calculateInjuryRisk(trends, horizonDays, animal);
  const behavioralRisk = calculateBehavioralRisk(historicalData.stress, horizonDays);
  const environmentalStressRisk = calculateEnvironmentalStressRisk(historicalData.environmental, horizonDays);
  
  // Generate detailed forecasts
  const vitalSignsForecast = generateVitalSignsForecast(historicalData.vitals, horizonDays);
  const weightTrendForecast = generateWeightTrendForecast(historicalData.vitals, horizonDays);
  const activityPatternForecast = generateActivityPatternForecast(trends, horizonDays);
  
  // Calculate prediction confidence
  const confidence = calculatePredictionConfidence(historicalData, horizonDays);
  const modelAccuracy = calculateModelAccuracy(historicalData, horizonDays);
  
  // Identify contributing factors
  const contributingFactors = identifyContributingFactors(trends, historicalData);
  
  // Generate recommendations
  const preventiveActions = generatePreventiveActions(diseaseRisk, injuryRisk, behavioralRisk);
  const environmentalAdjustments = generateEnvironmentalAdjustments(historicalData.environmental, environmentalStressRisk);
  const careScheduleRecommendations = generateCareScheduleRecommendations(trends, horizonDays);
  
  return {
    healthScore: predictedHealthScore,
    weight: predictedWeight,
    activityLevel: predictedActivityLevel,
    stressLevel: predictedStressLevel,
    sleepQuality: predictedSleepQuality,
    diseaseRisk,
    injuryRisk,
    behavioralRisk,
    environmentalStressRisk,
    vitalSignsForecast,
    weightTrendForecast,
    activityPatternForecast,
    confidence,
    modelAccuracy,
    contributingFactors,
    preventiveActions,
    environmentalAdjustments,
    careScheduleRecommendations
  };
}

function analyzeHistoricalTrends(historicalData: any) {
  // Analyze trends in vitals
  const vitalsTrend = analyzeVitalsTrend(historicalData.vitals);
  
  // Analyze stress trends
  const stressTrend = analyzeStressTrend(historicalData.stress);
  
  // Analyze sleep trends
  const sleepTrend = analyzeSleepTrend(historicalData.sleep);
  
  // Analyze activity trends
  const activityTrend = analyzeActivityTrend(historicalData.vitals);
  
  return {
    vitals: vitalsTrend,
    stress: stressTrend,
    sleep: sleepTrend,
    activity: activityTrend,
    overall: calculateOverallTrend(vitalsTrend, stressTrend, sleepTrend, activityTrend)
  };
}

function predictHealthScore(trends: any, horizonDays: number, animal: any): number {
  let baseScore = 75; // Default healthy score
  
  // Adjust based on trends
  if (trends.overall === 'improving') {
    baseScore += Math.min(15, horizonDays * 0.2);
  } else if (trends.overall === 'declining') {
    baseScore -= Math.min(20, horizonDays * 0.3);
  }
  
  // Adjust based on age
  const ageAdjustment = Math.max(0, (animal.age - 5) * -2);
  baseScore += ageAdjustment;
  
  // Ensure score is within valid range
  return Math.max(0, Math.min(100, Math.round(baseScore)));
}

function predictWeight(vitalsData: any[], horizonDays: number, animal: any): number {
  if (!vitalsData.length) return animal.weight || 25;
  
  // Get recent weight measurements
  const recentWeights = vitalsData
    .filter(vital => vital.weight)
    .slice(0, 10)
    .map(vital => vital.weight);
  
  if (recentWeights.length === 0) return animal.weight || 25;
  
  // Calculate trend
  const avgWeight = recentWeights.reduce((sum, weight) => sum + weight, 0) / recentWeights.length;
  const weightTrend = recentWeights.length > 1 ? 
    (recentWeights[0] - recentWeights[recentWeights.length - 1]) / recentWeights.length : 0;
  
  // Project weight based on trend
  const projectedWeight = avgWeight + (weightTrend * horizonDays / 7);
  
  return Math.max(0, Math.round(projectedWeight * 100) / 100);
}

function predictActivityLevel(trends: any, horizonDays: number): string {
  const activityLevels = ['very_low', 'low', 'moderate', 'high', 'very_high'];
  let currentLevel = 2; // moderate
  
  if (trends.activity === 'increasing') {
    currentLevel = Math.min(4, currentLevel + Math.floor(horizonDays / 14));
  } else if (trends.activity === 'decreasing') {
    currentLevel = Math.max(0, currentLevel - Math.floor(horizonDays / 14));
  }
  
  return activityLevels[currentLevel];
}

function predictStressLevel(stressData: any[], horizonDays: number, environmentalData: any[]): string {
  const stressLevels = ['very_low', 'low', 'moderate', 'high', 'very_high'];
  
  if (!stressData.length) return 'moderate';
  
  // Analyze recent stress trends
  const recentStress = stressData.slice(0, 5);
  const avgStressScore = recentStress.reduce((sum, stress) => sum + stress.stress_score, 0) / recentStress.length;
  
  // Adjust for environmental factors
  let environmentalAdjustment = 0;
  if (environmentalData.length > 0) {
    const recentEnvData = environmentalData.slice(0, 10);
    const avgTemp = recentEnvData.reduce((sum, env) => sum + (env.temperature_celsius || 20), 0) / recentEnvData.length;
    
    // Temperature stress adjustment
    if (avgTemp > 30 || avgTemp < 10) {
      environmentalAdjustment += 10;
    }
  }
  
  const adjustedStressScore = avgStressScore + environmentalAdjustment;
  
  if (adjustedStressScore < 20) return 'very_low';
  if (adjustedStressScore < 40) return 'low';
  if (adjustedStressScore < 60) return 'moderate';
  if (adjustedStressScore < 80) return 'high';
  return 'very_high';
}

function predictSleepQuality(sleepData: any[], horizonDays: number): number {
  if (!sleepData.length) return 75;
  
  const recentSleep = sleepData.slice(0, 7);
  const avgQuality = recentSleep.reduce((sum, sleep) => sum + sleep.sleep_quality_score, 0) / recentSleep.length;
  
  // Project based on trend
  let projectedQuality = avgQuality;
  
  if (recentSleep.length > 3) {
    const trend = (recentSleep[0].sleep_quality_score - recentSleep[recentSleep.length - 1].sleep_quality_score) / recentSleep.length;
    projectedQuality += trend * (horizonDays / 7);
  }
  
  return Math.max(0, Math.min(100, Math.round(projectedQuality)));
}

function calculateDiseaseRisk(trends: any, horizonDays: number, animal: any): number {
  let baseRisk = 0.05; // 5% base risk
  
  // Age factor
  if (animal.age > 8) baseRisk += 0.02;
  if (animal.age > 12) baseRisk += 0.03;
  
  // Health trend factor
  if (trends.overall === 'declining') {
    baseRisk += 0.03 * (horizonDays / 30);
  }
  
  // Stress factor
  if (trends.stress === 'increasing') {
    baseRisk += 0.02;
  }
  
  return Math.min(1.0, baseRisk);
}

function calculateInjuryRisk(trends: any, horizonDays: number, animal: any): number {
  let baseRisk = 0.03; // 3% base risk
  
  // Activity level factor
  if (trends.activity === 'increasing') {
    baseRisk += 0.01;
  }
  
  // Age factor
  if (animal.age > 10) {
    baseRisk += 0.02;
  }
  
  return Math.min(1.0, baseRisk);
}

function calculateBehavioralRisk(stressData: any[], horizonDays: number): number {
  if (!stressData.length) return 0.02;
  
  const recentStress = stressData.slice(0, 5);
  const avgStressScore = recentStress.reduce((sum, stress) => sum + stress.stress_score, 0) / recentStress.length;
  
  let risk = 0.02;
  if (avgStressScore > 70) risk += 0.05;
  if (avgStressScore > 85) risk += 0.03;
  
  return Math.min(1.0, risk);
}

function calculateEnvironmentalStressRisk(environmentalData: any[], horizonDays: number): number {
  if (!environmentalData.length) return 0.02;
  
  let risk = 0.02;
  
  const recentEnvData = environmentalData.slice(0, 10);
  
  // Check for extreme conditions
  const extremeTemp = recentEnvData.some(env => 
    env.temperature_celsius && (env.temperature_celsius > 35 || env.temperature_celsius < 5)
  );
  
  const poorAirQuality = recentEnvData.some(env => 
    env.air_quality_index && env.air_quality_index > 150
  );
  
  if (extremeTemp) risk += 0.03;
  if (poorAirQuality) risk += 0.02;
  
  return Math.min(1.0, risk);
}

async function generateRiskAssessments(animal: any, historicalData: any, supabase: any): Promise<RiskPrediction[]> {
  const riskAssessments: RiskPrediction[] = [];
  
  // Disease risk assessment
  const diseaseRisk = calculateDiseaseRisk(analyzeHistoricalTrends(historicalData), 30, animal);
  if (diseaseRisk > 0.1) {
    riskAssessments.push({
      riskType: 'disease',
      probability: diseaseRisk,
      timeframe: 30,
      contributingFactors: ['age', 'declining_health_trend', 'stress_levels'],
      preventiveActions: ['regular_vet_checkups', 'stress_reduction', 'nutrition_optimization']
    });
  }
  
  // Environmental stress risk
  const envStressRisk = calculateEnvironmentalStressRisk(historicalData.environmental, 14);
  if (envStressRisk > 0.05) {
    riskAssessments.push({
      riskType: 'environmental_stress',
      probability: envStressRisk,
      timeframe: 14,
      contributingFactors: ['temperature_extremes', 'poor_air_quality'],
      preventiveActions: ['environment_optimization', 'climate_control', 'air_purification']
    });
  }
  
  return riskAssessments;
}

async function generatePredictiveAlerts(animal: any, predictions: any[], riskAssessments: RiskPrediction[], supabase: any) {
  const alerts = [];
  
  // Check for high-risk predictions
  for (const prediction of predictions) {
    if (prediction.disease_risk_probability > 0.15) {
      alerts.push({
        animal_id: animal.id,
        alert_type: 'disease_risk',
        severity_level: 'warning',
        predicted_event: 'Increased disease risk detected',
        probability_percentage: prediction.disease_risk_probability * 100,
        predicted_timeframe_days: prediction.prediction_horizon_days,
        confidence_level: prediction.prediction_confidence,
        alert_title: 'Health Risk Alert',
        alert_message: `${animal.name} shows increased disease risk over the next ${prediction.prediction_horizon_days} days.`,
        recommended_actions: prediction.preventive_actions,
        environmental_factors: prediction.environmental_adjustments,
        health_indicators: {
          predicted_health_score: prediction.predicted_health_score,
          stress_level: prediction.predicted_stress_level
        },
        behavioral_patterns: {
          activity_level: prediction.predicted_activity_level,
          sleep_quality: prediction.predicted_sleep_quality
        }
      });
    }
  }
  
  // Save alerts to database
  if (alerts.length > 0) {
    const { data, error } = await supabase
      .from('predictive_alerts')
      .insert(alerts)
      .select();
    
    if (error) {
      console.error('Error saving predictive alerts:', error);
      return [];
    }
    
    return data;
  }
  
  return [];
}

// Helper functions for trend analysis
function analyzeVitalsTrend(vitalsData: any[]): string {
  if (vitalsData.length < 3) return 'stable';
  
  const recent = vitalsData.slice(0, 5);
  const older = vitalsData.slice(5, 10);
  
  if (recent.length === 0 || older.length === 0) return 'stable';
  
  const recentAvg = recent.reduce((sum, vital) => sum + (vital.heart_rate || 80), 0) / recent.length;
  const olderAvg = older.reduce((sum, vital) => sum + (vital.heart_rate || 80), 0) / older.length;
  
  const change = (recentAvg - olderAvg) / olderAvg;
  
  if (change > 0.05) return 'increasing';
  if (change < -0.05) return 'decreasing';
  return 'stable';
}

function analyzeStressTrend(stressData: any[]): string {
  if (stressData.length < 3) return 'stable';
  
  const recent = stressData.slice(0, 3);
  const older = stressData.slice(3, 6);
  
  if (recent.length === 0 || older.length === 0) return 'stable';
  
  const recentAvg = recent.reduce((sum, stress) => sum + stress.stress_score, 0) / recent.length;
  const olderAvg = older.reduce((sum, stress) => sum + stress.stress_score, 0) / older.length;
  
  const change = (recentAvg - olderAvg) / olderAvg;
  
  if (change > 0.1) return 'increasing';
  if (change < -0.1) return 'decreasing';
  return 'stable';
}

function analyzeSleepTrend(sleepData: any[]): string {
  if (sleepData.length < 3) return 'stable';
  
  const recent = sleepData.slice(0, 3);
  const older = sleepData.slice(3, 6);
  
  if (recent.length === 0 || older.length === 0) return 'stable';
  
  const recentAvg = recent.reduce((sum, sleep) => sum + sleep.sleep_quality_score, 0) / recent.length;
  const olderAvg = older.reduce((sum, sleep) => sum + sleep.sleep_quality_score, 0) / older.length;
  
  const change = (recentAvg - olderAvg) / olderAvg;
  
  if (change > 0.1) return 'improving';
  if (change < -0.1) return 'declining';
  return 'stable';
}

function analyzeActivityTrend(vitalsData: any[]): string {
  // Simplified activity trend analysis based on heart rate variability
  return 'stable';
}

function calculateOverallTrend(vitals: string, stress: string, sleep: string, activity: string): string {
  const trends = [vitals, stress, sleep, activity];
  
  const improvingCount = trends.filter(t => t === 'improving' || t === 'increasing').length;
  const decliningCount = trends.filter(t => t === 'declining' || t === 'decreasing').length;
  
  if (improvingCount > decliningCount) return 'improving';
  if (decliningCount > improvingCount) return 'declining';
  return 'stable';
}

function generateVitalSignsForecast(vitalsData: any[], horizonDays: number) {
  // Generate forecast for heart rate, temperature, respiratory rate
  return {
    heart_rate: {
      predicted_average: 85,
      predicted_range: [75, 95],
      trend: 'stable'
    },
    temperature: {
      predicted_average: 38.5,
      predicted_range: [38.0, 39.0],
      trend: 'stable'
    },
    respiratory_rate: {
      predicted_average: 25,
      predicted_range: [20, 30],
      trend: 'stable'
    }
  };
}

function generateWeightTrendForecast(vitalsData: any[], horizonDays: number) {
  return {
    predicted_change_kg: 0.2,
    trend_direction: 'stable',
    confidence: 0.8,
    factors: ['normal_growth', 'stable_diet']
  };
}

function generateActivityPatternForecast(trends: any, horizonDays: number) {
  return {
    predicted_activity_level: 'moderate',
    daily_activity_hours: 6,
    peak_activity_times: ['08:00-10:00', '16:00-18:00'],
    seasonal_adjustments: []
  };
}

function calculatePredictionConfidence(historicalData: any, horizonDays: number): number {
  const totalDataPoints = Object.values(historicalData).reduce((sum: number, data: any) => sum + data.length, 0);
  
  let confidence = 0.5; // Base confidence
  
  // Adjust based on data availability
  if (totalDataPoints > 100) confidence += 0.3;
  else if (totalDataPoints > 50) confidence += 0.2;
  else if (totalDataPoints > 20) confidence += 0.1;
  
  // Adjust based on prediction horizon
  if (horizonDays <= 7) confidence += 0.2;
  else if (horizonDays <= 30) confidence += 0.1;
  else confidence -= 0.1;
  
  return Math.max(0.3, Math.min(0.95, confidence));
}

function calculateModelAccuracy(historicalData: any, horizonDays: number): number {
  // Simplified model accuracy calculation
  // In a real implementation, this would be based on historical prediction performance
  return 0.85;
}

function identifyContributingFactors(trends: any, historicalData: any) {
  const factors = [];
  
  if (trends.stress === 'increasing') {
    factors.push({ factor: 'stress_levels', impact: 'high', description: 'Increasing stress levels detected' });
  }
  
  if (trends.sleep === 'declining') {
    factors.push({ factor: 'sleep_quality', impact: 'medium', description: 'Declining sleep quality observed' });
  }
  
  if (historicalData.environmental.length > 0) {
    factors.push({ factor: 'environmental_conditions', impact: 'medium', description: 'Environmental factors considered' });
  }
  
  return factors;
}

function generatePreventiveActions(diseaseRisk: number, injuryRisk: number, behavioralRisk: number) {
  const actions = [];
  
  if (diseaseRisk > 0.1) {
    actions.push({
      action: 'schedule_vet_checkup',
      priority: 'high',
      timeframe: '7_days',
      description: 'Schedule preventive veterinary examination'
    });
  }
  
  if (injuryRisk > 0.05) {
    actions.push({
      action: 'activity_monitoring',
      priority: 'medium',
      timeframe: '14_days',
      description: 'Monitor activity levels and exercise intensity'
    });
  }
  
  if (behavioralRisk > 0.05) {
    actions.push({
      action: 'stress_reduction',
      priority: 'medium',
      timeframe: 'ongoing',
      description: 'Implement stress reduction strategies'
    });
  }
  
  return actions;
}

function generateEnvironmentalAdjustments(environmentalData: any[], environmentalStressRisk: number) {
  const adjustments = [];
  
  if (environmentalStressRisk > 0.05) {
    adjustments.push({
      adjustment: 'temperature_optimization',
      priority: 'high',
      description: 'Optimize temperature control for comfort'
    });
    
    adjustments.push({
      adjustment: 'air_quality_improvement',
      priority: 'medium',
      description: 'Improve air quality through ventilation or purification'
    });
  }
  
  return adjustments;
}

function generateCareScheduleRecommendations(trends: any, horizonDays: number) {
  const recommendations = [];
  
  if (trends.overall === 'declining') {
    recommendations.push({
      task: 'health_monitoring',
      frequency: 'daily',
      duration: `${horizonDays}_days`,
      description: 'Increase health monitoring frequency'
    });
  }
  
  recommendations.push({
    task: 'routine_checkup',
    frequency: 'monthly',
    duration: 'ongoing',
    description: 'Maintain regular health assessments'
  });
  
  return recommendations;
}

function calculateDataQuality(historicalData: any): string {
  const totalDataPoints = Object.values(historicalData).reduce((sum: number, data: any) => sum + data.length, 0);
  
  if (totalDataPoints > 100) return 'excellent';
  if (totalDataPoints > 50) return 'good';
  if (totalDataPoints > 20) return 'fair';
  return 'limited';
}