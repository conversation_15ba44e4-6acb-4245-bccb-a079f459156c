import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface RealtimeDataRequest {
  animalId: string;
  streamType: string;
  dataSource: string;
  deviceId?: string;
  streamData: any;
  timestamp?: string;
}

interface ProcessedData {
  vitals?: {
    heart_rate?: number;
    temperature?: number;
    respiratory_rate?: number;
    activity_level?: number;
  };
  activity?: {
    steps?: number;
    distance?: number;
    calories?: number;
    active_minutes?: number;
  };
  location?: {
    latitude?: number;
    longitude?: number;
    accuracy?: number;
    altitude?: number;
  };
  environmental?: {
    temperature?: number;
    humidity?: number;
    air_quality?: number;
    noise_level?: number;
  };
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get request data
    const { animalId, streamType, dataSource, deviceId, streamData, timestamp }: RealtimeDataRequest = await req.json();

    console.log(`Processing realtime data for animal ${animalId}, type: ${streamType}`);

    // Validate animal exists
    const { data: animal, error: animalError } = await supabase
      .from('animals')
      .select('id, user_id')
      .eq('id', animalId)
      .single();

    if (animalError || !animal) {
      throw new Error(`Animal not found: ${animalError?.message}`);
    }

    // Store raw realtime data
    const { data: streamRecord, error: streamError } = await supabase
      .from('realtime_data_streams')
      .insert({
        animal_id: animalId,
        stream_type: streamType,
        data_source: dataSource,
        device_id: deviceId,
        stream_data: streamData,
        data_quality_score: calculateDataQuality(streamData, streamType),
        received_at: timestamp || new Date().toISOString()
      })
      .select()
      .single();

    if (streamError) {
      console.error('Error storing stream data:', streamError);
      throw new Error(`Failed to store stream data: ${streamError.message}`);
    }

    // Process data based on stream type
    const processedData = await processStreamData(streamType, streamData, dataSource);
    
    // Store processed data in appropriate tables
    const storageResults = await storeProcessedData(supabase, animalId, streamType, processedData, timestamp);
    
    // Check for alerts and anomalies
    const alerts = await checkForAlerts(supabase, animalId, streamType, processedData);
    
    // Update device status if applicable
    if (deviceId) {
      await updateDeviceStatus(supabase, deviceId, streamData);
    }
    
    // Update stream processing status
    await supabase
      .from('realtime_data_streams')
      .update({
        processing_status: 'processed',
        processed_at: new Date().toISOString()
      })
      .eq('id', streamRecord.id);
    
    // Send real-time notifications if needed
    if (alerts.length > 0) {
      await sendRealtimeNotifications(supabase, animal.user_id, alerts);
    }

    console.log(`Realtime data processing completed for animal ${animalId}`);

    return new Response(
      JSON.stringify({
        success: true,
        streamId: streamRecord.id,
        processedData: processedData,
        storageResults: storageResults,
        alerts: alerts,
        dataQuality: streamRecord.data_quality_score
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('Realtime data processing error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Realtime data processing failed'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});

async function processStreamData(streamType: string, streamData: any, dataSource: string): Promise<ProcessedData> {
  const processed: ProcessedData = {};
  
  switch (streamType) {
    case 'vitals':
      processed.vitals = processVitalsData(streamData, dataSource);
      break;
    case 'activity':
      processed.activity = processActivityData(streamData, dataSource);
      break;
    case 'location':
      processed.location = processLocationData(streamData, dataSource);
      break;
    case 'environmental':
      processed.environmental = processEnvironmentalData(streamData, dataSource);
      break;
    default:
      console.warn(`Unknown stream type: ${streamType}`);
  }
  
  return processed;
}

function processVitalsData(streamData: any, dataSource: string): any {
  const vitals: any = {};
  
  // Normalize data based on source
  switch (dataSource) {
    case 'fitbit_collar':
      vitals.heart_rate = streamData.heartRate;
      vitals.activity_level = streamData.activityLevel;
      break;
    case 'whistle_gps':
      vitals.heart_rate = streamData.hr;
      vitals.temperature = streamData.temp;
      break;
    case 'manual':
      vitals.heart_rate = streamData.heart_rate;
      vitals.temperature = streamData.temperature;
      vitals.respiratory_rate = streamData.respiratory_rate;
      break;
    default:
      // Generic mapping
      vitals.heart_rate = streamData.heart_rate || streamData.hr || streamData.heartRate;
      vitals.temperature = streamData.temperature || streamData.temp;
      vitals.respiratory_rate = streamData.respiratory_rate || streamData.rr;
      vitals.activity_level = streamData.activity_level || streamData.activity;
  }
  
  // Validate and clean data
  if (vitals.heart_rate) {
    vitals.heart_rate = Math.max(0, Math.min(300, vitals.heart_rate)); // Reasonable bounds
  }
  if (vitals.temperature) {
    vitals.temperature = Math.max(35, Math.min(45, vitals.temperature)); // Celsius bounds
  }
  if (vitals.respiratory_rate) {
    vitals.respiratory_rate = Math.max(0, Math.min(100, vitals.respiratory_rate));
  }
  
  return vitals;
}

function processActivityData(streamData: any, dataSource: string): any {
  const activity: any = {};
  
  switch (dataSource) {
    case 'fitbit_collar':
      activity.steps = streamData.steps;
      activity.distance = streamData.distance;
      activity.calories = streamData.calories;
      activity.active_minutes = streamData.activeMinutes;
      break;
    case 'whistle_gps':
      activity.distance = streamData.distance;
      activity.active_minutes = streamData.activity_time;
      break;
    default:
      activity.steps = streamData.steps;
      activity.distance = streamData.distance;
      activity.calories = streamData.calories;
      activity.active_minutes = streamData.active_minutes || streamData.activeMinutes;
  }
  
  // Validate data
  if (activity.steps) {
    activity.steps = Math.max(0, activity.steps);
  }
  if (activity.distance) {
    activity.distance = Math.max(0, activity.distance);
  }
  if (activity.calories) {
    activity.calories = Math.max(0, activity.calories);
  }
  
  return activity;
}

function processLocationData(streamData: any, dataSource: string): any {
  const location: any = {};
  
  switch (dataSource) {
    case 'whistle_gps':
      location.latitude = streamData.lat;
      location.longitude = streamData.lng;
      location.accuracy = streamData.accuracy;
      break;
    case 'apple_airtag':
      location.latitude = streamData.latitude;
      location.longitude = streamData.longitude;
      location.accuracy = streamData.horizontalAccuracy;
      break;
    default:
      location.latitude = streamData.latitude || streamData.lat;
      location.longitude = streamData.longitude || streamData.lng;
      location.accuracy = streamData.accuracy;
      location.altitude = streamData.altitude;
  }
  
  // Validate coordinates
  if (location.latitude && (location.latitude < -90 || location.latitude > 90)) {
    delete location.latitude;
  }
  if (location.longitude && (location.longitude < -180 || location.longitude > 180)) {
    delete location.longitude;
  }
  
  return location;
}

function processEnvironmentalData(streamData: any, dataSource: string): any {
  const environmental: any = {};
  
  switch (dataSource) {
    case 'weather_api':
      environmental.temperature = streamData.temp;
      environmental.humidity = streamData.humidity;
      environmental.air_quality = streamData.aqi;
      break;
    case 'home_sensor':
      environmental.temperature = streamData.temperature;
      environmental.humidity = streamData.humidity;
      environmental.noise_level = streamData.noise;
      break;
    default:
      environmental.temperature = streamData.temperature || streamData.temp;
      environmental.humidity = streamData.humidity;
      environmental.air_quality = streamData.air_quality || streamData.aqi;
      environmental.noise_level = streamData.noise_level || streamData.noise;
  }
  
  return environmental;
}

async function storeProcessedData(supabase: any, animalId: string, streamType: string, processedData: ProcessedData, timestamp?: string): Promise<any[]> {
  const results = [];
  const recordedAt = timestamp || new Date().toISOString();
  
  try {
    switch (streamType) {
      case 'vitals':
        if (processedData.vitals && Object.keys(processedData.vitals).length > 0) {
          const { data, error } = await supabase
            .from('vitals')
            .insert({
              animal_id: animalId,
              ...processedData.vitals,
              recorded_at: recordedAt,
              data_source: 'realtime_stream'
            })
            .select();
          
          if (error) {
            console.error('Error storing vitals:', error);
          } else {
            results.push({ type: 'vitals', success: true, data });
          }
        }
        break;
        
      case 'environmental':
        if (processedData.environmental && Object.keys(processedData.environmental).length > 0) {
          const { data, error } = await supabase
            .from('environmental_data')
            .insert({
              animal_id: animalId,
              temperature_celsius: processedData.environmental.temperature,
              humidity_percentage: processedData.environmental.humidity,
              air_quality_index: processedData.environmental.air_quality,
              noise_level_db: processedData.environmental.noise_level,
              data_source: 'realtime_stream',
              recorded_at: recordedAt
            })
            .select();
          
          if (error) {
            console.error('Error storing environmental data:', error);
          } else {
            results.push({ type: 'environmental', success: true, data });
          }
        }
        break;
    }
  } catch (error) {
    console.error('Error in storeProcessedData:', error);
    results.push({ type: streamType, success: false, error: error.message });
  }
  
  return results;
}

async function checkForAlerts(supabase: any, animalId: string, streamType: string, processedData: ProcessedData): Promise<any[]> {
  const alerts = [];
  
  try {
    // Check vitals alerts
    if (streamType === 'vitals' && processedData.vitals) {
      const vitals = processedData.vitals;
      
      // Heart rate alerts
      if (vitals.heart_rate) {
        if (vitals.heart_rate > 150) {
          alerts.push({
            type: 'high_heart_rate',
            severity: 'warning',
            message: `High heart rate detected: ${vitals.heart_rate} BPM`,
            value: vitals.heart_rate,
            threshold: 150
          });
        } else if (vitals.heart_rate < 60) {
          alerts.push({
            type: 'low_heart_rate',
            severity: 'warning',
            message: `Low heart rate detected: ${vitals.heart_rate} BPM`,
            value: vitals.heart_rate,
            threshold: 60
          });
        }
      }
      
      // Temperature alerts
      if (vitals.temperature) {
        if (vitals.temperature > 39.5) {
          alerts.push({
            type: 'high_temperature',
            severity: 'critical',
            message: `High temperature detected: ${vitals.temperature}°C`,
            value: vitals.temperature,
            threshold: 39.5
          });
        } else if (vitals.temperature < 37.5) {
          alerts.push({
            type: 'low_temperature',
            severity: 'warning',
            message: `Low temperature detected: ${vitals.temperature}°C`,
            value: vitals.temperature,
            threshold: 37.5
          });
        }
      }
    }
    
    // Check environmental alerts
    if (streamType === 'environmental' && processedData.environmental) {
      const env = processedData.environmental;
      
      if (env.temperature) {
        if (env.temperature > 35) {
          alerts.push({
            type: 'high_ambient_temperature',
            severity: 'warning',
            message: `High ambient temperature: ${env.temperature}°C`,
            value: env.temperature,
            threshold: 35
          });
        }
      }
      
      if (env.air_quality && env.air_quality > 150) {
        alerts.push({
          type: 'poor_air_quality',
          severity: 'warning',
          message: `Poor air quality detected: AQI ${env.air_quality}`,
          value: env.air_quality,
          threshold: 150
        });
      }
    }
    
  } catch (error) {
    console.error('Error checking for alerts:', error);
  }
  
  return alerts;
}

async function updateDeviceStatus(supabase: any, deviceId: string, streamData: any): Promise<void> {
  try {
    const updateData: any = {
      last_seen: new Date().toISOString(),
      status: 'active'
    };
    
    // Extract battery level if available
    if (streamData.battery_level !== undefined) {
      updateData.battery_level = streamData.battery_level;
    }
    
    // Extract signal strength if available
    if (streamData.signal_strength !== undefined) {
      updateData.signal_strength = streamData.signal_strength;
    }
    
    await supabase
      .from('iot_devices')
      .update(updateData)
      .eq('device_identifier', deviceId);
      
  } catch (error) {
    console.error('Error updating device status:', error);
  }
}

async function sendRealtimeNotifications(supabase: any, userId: string, alerts: any[]): Promise<void> {
  try {
    for (const alert of alerts) {
      if (alert.severity === 'critical' || alert.severity === 'high') {
        // Send immediate push notification for critical and high alerts
        console.log(`Triggering push notification for realtime alert: ${alert.type}`);
        const { error: invokeError } = await supabase.functions.invoke('send-push-notification', {
          body: {
            user_id: userId,
            title: `🚨 ${alert.severity.toUpperCase()} Alert`,
            body: alert.message,
            priority: alert.severity === 'critical' ? 'critical' : 'high',
            alert_type: alert.type,
            data: {
              alert_type: alert.type,
              alert_data: alert,
              source: 'realtime_data'
            }
          }
        });
        
        if (invokeError) {
          console.error(`Error invoking send-push-notification for realtime alert ${alert.type}:`, invokeError);
        } else {
          console.log(`Push notification successfully triggered for realtime alert ${alert.type}`);
        }
      }
    }
  } catch (error) {
    console.error('Error sending realtime notifications:', error);
  }
}

function calculateDataQuality(streamData: any, streamType: string): number {
  let quality = 1.0;
  
  // Check for missing required fields
  const requiredFields = getRequiredFields(streamType);
  const missingFields = requiredFields.filter(field => !streamData[field]);
  
  if (missingFields.length > 0) {
    quality -= (missingFields.length / requiredFields.length) * 0.3;
  }
  
  // Check for data freshness (if timestamp is provided)
  if (streamData.timestamp) {
    const dataAge = Date.now() - new Date(streamData.timestamp).getTime();
    const maxAge = 5 * 60 * 1000; // 5 minutes
    
    if (dataAge > maxAge) {
      quality -= 0.2;
    }
  }
  
  // Check for reasonable value ranges
  if (streamType === 'vitals') {
    if (streamData.heart_rate && (streamData.heart_rate < 30 || streamData.heart_rate > 250)) {
      quality -= 0.2;
    }
    if (streamData.temperature && (streamData.temperature < 35 || streamData.temperature > 42)) {
      quality -= 0.2;
    }
  }
  
  return Math.max(0.1, Math.min(1.0, quality));
}

function getRequiredFields(streamType: string): string[] {
  switch (streamType) {
    case 'vitals':
      return ['heart_rate', 'temperature'];
    case 'activity':
      return ['steps', 'distance'];
    case 'location':
      return ['latitude', 'longitude'];
    case 'environmental':
      return ['temperature', 'humidity'];
    default:
      return [];
  }
}