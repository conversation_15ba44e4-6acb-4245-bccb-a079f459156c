// @verify_jwt: true
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface HealthScoreRequest {
  animal_id: string;
  target_date?: string; // Optional, defaults to today
}

interface HealthScoreResponse {
  success: boolean;
  health_score?: {
    overall_score: number;
    vitals_score: number;
    activity_score: number;
    feeding_score: number;
    medication_score: number;
    data_quality_score: number;
    score_explanation: string;
    contributing_factors: Record<string, any>;
    recommendations: string[];
  };
  error?: string;
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response('ok', {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          'Access-Control-Allow-Methods': 'POST, OPTIONS'
        }
      });
    }

    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        { status: 405, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user from JWT
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ success: false, error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Parse request body
    const { animal_id, target_date }: HealthScoreRequest = await req.json();
    
    if (!animal_id) {
      return new Response(
        JSON.stringify({ success: false, error: 'Animal ID is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Verify animal ownership
    const { data: animal, error: animalError } = await supabase
      .from('animals')
      .select('id, name')
      .eq('id', animal_id)
      .eq('user_id', user.id)
      .single();

    if (animalError || !animal) {
      return new Response(
        JSON.stringify({ success: false, error: 'Animal not found or access denied' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Set target date (default to today)
    const scoreDate = target_date || new Date().toISOString().split('T')[0];
    const startDate = new Date(scoreDate);
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + 1);

    console.log(`Calculating health score for animal ${animal_id} on ${scoreDate}`);

    // Fetch data for the past 24 hours
    const [vitalsData, feedingData, medicationData] = await Promise.all([
      // Vitals data
      supabase
        .from('vitals')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('recorded_at', startDate.toISOString())
        .lt('recorded_at', endDate.toISOString())
        .order('recorded_at', { ascending: false }),
      
      // Feeding data
      supabase
        .from('feeding_schedules')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('created_at', startDate.toISOString())
        .lt('created_at', endDate.toISOString()),
      
      // Medication data
      supabase
        .from('medications')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('created_at', startDate.toISOString())
        .lt('created_at', endDate.toISOString())
    ]);

    // Calculate individual scores
    const vitalsScore = calculateVitalsScore(vitalsData.data || []);
    const activityScore = calculateActivityScore(vitalsData.data || []);
    const feedingScore = calculateFeedingScore(feedingData.data || []);
    const medicationScore = calculateMedicationScore(medicationData.data || []);
    
    // Calculate data quality score
    const dataQualityScore = calculateDataQualityScore({
      vitals: vitalsData.data || [],
      feeding: feedingData.data || [],
      medication: medicationData.data || []
    });

    // Calculate overall score (weighted average)
    const weights = {
      vitals: 0.35,
      activity: 0.25,
      feeding: 0.20,
      medication: 0.20
    };

    const overallScore = Math.round(
      vitalsScore * weights.vitals +
      activityScore * weights.activity +
      feedingScore * weights.feeding +
      medicationScore * weights.medication
    );

    // Generate explanation and recommendations
    const { explanation, recommendations, contributingFactors } = generateInsights({
      overall: overallScore,
      vitals: vitalsScore,
      activity: activityScore,
      feeding: feedingScore,
      medication: medicationScore,
      dataQuality: dataQualityScore
    });

    // Save health score to database
    const { error: insertError } = await supabase
      .from('ai_health_scores')
      .upsert({
        animal_id,
        user_id: user.id,
        score_date: scoreDate,
        overall_score: overallScore,
        vitals_score: vitalsScore,
        activity_score: activityScore,
        feeding_score: feedingScore,
        medication_score: medicationScore,
        score_explanation: explanation,
        data_quality_score: dataQualityScore,
        contributing_factors: contributingFactors,
        recommendations
      }, {
        onConflict: 'animal_id,score_date'
      });

    if (insertError) {
      console.error('Error saving health score:', insertError);
      throw insertError;
    }

    console.log(`Health score calculated successfully: ${overallScore}/100`);

    const response: HealthScoreResponse = {
      success: true,
      health_score: {
        overall_score: overallScore,
        vitals_score: vitalsScore,
        activity_score: activityScore,
        feeding_score: feedingScore,
        medication_score: medicationScore,
        data_quality_score: dataQualityScore,
        score_explanation: explanation,
        contributing_factors: contributingFactors,
        recommendations
      }
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    );

  } catch (error) {
    console.error('Error in calculate-daily-health-score:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    );
  }
});

// Helper functions for score calculations
function calculateVitalsScore(vitals: any[]): number {
  if (vitals.length === 0) return 50; // Neutral score if no data
  
  let totalScore = 0;
  let validReadings = 0;
  
  vitals.forEach(vital => {
    let vitalScore = 100;
    
    // Heart rate assessment (assuming normal range 60-100 for most animals)
    if (vital.heart_rate) {
      if (vital.heart_rate < 50 || vital.heart_rate > 120) {
        vitalScore -= 20;
      } else if (vital.heart_rate < 60 || vital.heart_rate > 100) {
        vitalScore -= 10;
      }
    }
    
    // Temperature assessment (assuming normal range 38-39°C)
    if (vital.temperature) {
      if (vital.temperature < 37 || vital.temperature > 40) {
        vitalScore -= 25;
      } else if (vital.temperature < 37.5 || vital.temperature > 39.5) {
        vitalScore -= 10;
      }
    }
    
    // Weight assessment (look for sudden changes)
    if (vital.weight && vitals.length > 1) {
      const previousVital = vitals[vitals.indexOf(vital) + 1];
      if (previousVital && previousVital.weight) {
        const weightChange = Math.abs(vital.weight - previousVital.weight) / previousVital.weight;
        if (weightChange > 0.05) { // More than 5% change
          vitalScore -= 15;
        }
      }
    }
    
    totalScore += Math.max(0, vitalScore);
    validReadings++;
  });
  
  return validReadings > 0 ? Math.round(totalScore / validReadings) : 50;
}

function calculateActivityScore(vitals: any[]): number {
  // For now, use heart rate variability as a proxy for activity
  if (vitals.length < 2) return 70; // Default score
  
  const heartRates = vitals.map(v => v.heart_rate).filter(hr => hr);
  if (heartRates.length < 2) return 70;
  
  // Calculate heart rate variability
  const avgHeartRate = heartRates.reduce((sum, hr) => sum + hr, 0) / heartRates.length;
  const variance = heartRates.reduce((sum, hr) => sum + Math.pow(hr - avgHeartRate, 2), 0) / heartRates.length;
  
  // Good variability indicates healthy activity levels
  if (variance > 100) return 90; // High variability = good
  if (variance > 50) return 80;
  if (variance > 20) return 70;
  return 60; // Low variability might indicate inactivity
}

function calculateFeedingScore(feeding: any[]): number {
  if (feeding.length === 0) return 60; // Neutral score if no feeding data
  
  // Check for regular feeding patterns
  const feedingTimes = feeding.map(f => new Date(f.feeding_time || f.created_at));
  const now = new Date();
  const last24Hours = feedingTimes.filter(time => (now.getTime() - time.getTime()) < 24 * 60 * 60 * 1000);
  
  if (last24Hours.length >= 2) return 90; // Good feeding frequency
  if (last24Hours.length === 1) return 75;
  return 50; // No recent feeding recorded
}

function calculateMedicationScore(medications: any[]): number {
  if (medications.length === 0) return 85; // Good score if no medications needed
  
  // Check medication compliance
  const recentMeds = medications.filter(med => {
    const medTime = new Date(med.administered_at || med.created_at);
    const now = new Date();
    return (now.getTime() - medTime.getTime()) < 24 * 60 * 60 * 1000;
  });
  
  if (recentMeds.length === medications.length) return 95; // All medications taken
  if (recentMeds.length > medications.length * 0.8) return 85; // Most medications taken
  if (recentMeds.length > medications.length * 0.5) return 70; // Some medications taken
  return 40; // Poor medication compliance
}

function calculateDataQualityScore(data: { vitals: any[], feeding: any[], medication: any[] }): number {
  let score = 0;
  let maxScore = 0;
  
  // Vitals data quality (40% of total)
  maxScore += 40;
  if (data.vitals.length > 0) {
    score += 20;
    if (data.vitals.length >= 3) score += 10;
    if (data.vitals.some(v => v.heart_rate && v.temperature)) score += 10;
  }
  
  // Feeding data quality (30% of total)
  maxScore += 30;
  if (data.feeding.length > 0) {
    score += 15;
    if (data.feeding.length >= 2) score += 15;
  }
  
  // Medication data quality (30% of total)
  maxScore += 30;
  if (data.medication.length > 0) {
    score += 30;
  } else {
    score += 25; // Good if no medications needed
  }
  
  return Math.round((score / maxScore) * 100);
}

function generateInsights(scores: {
  overall: number;
  vitals: number;
  activity: number;
  feeding: number;
  medication: number;
  dataQuality: number;
}) {
  const recommendations: string[] = [];
  const contributingFactors: Record<string, any> = {};
  
  let explanation = `Overall health score: ${scores.overall}/100. `;
  
  // Analyze each component
  if (scores.vitals < 70) {
    explanation += "Vital signs show some concerning patterns. ";
    recommendations.push("Monitor vital signs more frequently");
    recommendations.push("Consider veterinary consultation");
    contributingFactors.vitals_concern = true;
  } else if (scores.vitals > 85) {
    explanation += "Excellent vital signs. ";
    contributingFactors.vitals_excellent = true;
  }
  
  if (scores.activity < 70) {
    explanation += "Activity levels could be improved. ";
    recommendations.push("Increase exercise and activity time");
    contributingFactors.low_activity = true;
  }
  
  if (scores.feeding < 70) {
    explanation += "Feeding patterns need attention. ";
    recommendations.push("Establish regular feeding schedule");
    contributingFactors.feeding_irregular = true;
  }
  
  if (scores.medication < 70) {
    explanation += "Medication compliance needs improvement. ";
    recommendations.push("Set medication reminders");
    recommendations.push("Track medication administration");
    contributingFactors.medication_compliance = false;
  }
  
  if (scores.dataQuality < 60) {
    explanation += "More consistent data logging would improve analysis accuracy.";
    recommendations.push("Log vital signs daily");
    recommendations.push("Record feeding times consistently");
    contributingFactors.data_quality_low = true;
  }
  
  if (scores.overall >= 85) {
    explanation += " Your animal is in excellent health!";
  } else if (scores.overall >= 70) {
    explanation += " Your animal is in good health with room for improvement.";
  } else {
    explanation += " Your animal's health needs attention.";
  }
  
  return {
    explanation,
    recommendations,
    contributingFactors
  };
}