import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface GooglePayRequest {
  order_id: string;
  amount: number;
  currency: string;
  plan_type: 'monthly' | 'yearly';
  product_id: string;
  payment_token?: string; // Google Pay payment token
}

serve(async (req) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token);

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const requestData: GooglePayRequest = await req.json();
    const { order_id, amount, currency, plan_type, product_id, payment_token } = requestData;

    // Validate required fields
    if (!order_id || !amount || !currency || !plan_type || !product_id) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Log payment attempt
    await supabaseClient
      .from('payment_logs')
      .insert({
        user_id: user.id,
        action: 'google_pay_initiated',
        provider: 'google_pay',
        order_id,
        plan_type,
        amount,
        currency,
        status: 'pending',
        metadata: {
          product_id,
          has_payment_token: !!payment_token
        }
      });

    // Verify order exists and belongs to user
    const { data: order, error: orderError } = await supabaseClient
      .from('orders')
      .select('*')
      .eq('id', order_id)
      .eq('user_id', user.id)
      .single();

    if (orderError || !order) {
      await supabaseClient
        .from('payment_logs')
        .insert({
          user_id: user.id,
          action: 'google_pay_failed',
          provider: 'google_pay',
          order_id,
          status: 'failed',
          error_message: 'Order not found or unauthorized'
        });

      return new Response(
        JSON.stringify({ error: 'Order not found or unauthorized' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // In a real implementation, you would:
    // 1. Validate the Google Pay payment token
    // 2. Process payment with your payment processor (Stripe, etc.)
    // 3. Handle payment confirmation
    
    // For now, simulate payment processing
    const paymentSuccess = true; // In real app, this would be the result of payment processing

    if (paymentSuccess) {
      // Update order status
      const { error: updateError } = await supabaseClient
        .from('orders')
        .update({
          status: 'completed',
          payment_method: 'google_pay',
          completed_at: new Date().toISOString()
        })
        .eq('id', order_id);

      if (updateError) {
        console.error('Error updating order:', updateError);
        throw new Error('Failed to update order status');
      }

      // Update user subscription
      const subscriptionEndDate = new Date();
      if (plan_type === 'monthly') {
        subscriptionEndDate.setMonth(subscriptionEndDate.getMonth() + 1);
      } else {
        subscriptionEndDate.setFullYear(subscriptionEndDate.getFullYear() + 1);
      }

      const { error: userUpdateError } = await supabaseClient
        .from('users')
        .update({
          subscription_type: 'premium',
          subscription_status: 'active',
          subscription_end_date: subscriptionEndDate.toISOString()
        })
        .eq('id', user.id);

      if (userUpdateError) {
        console.error('Error updating user subscription:', userUpdateError);
      }

      // Log successful payment
      await supabaseClient
        .from('payment_logs')
        .insert({
          user_id: user.id,
          action: 'google_pay_completed',
          provider: 'google_pay',
          order_id,
          plan_type,
          amount,
          currency,
          status: 'completed',
          metadata: {
            product_id,
            subscription_end_date: subscriptionEndDate.toISOString()
          }
        });

      return new Response(
        JSON.stringify({
          success: true,
          order_id,
          message: 'Google Pay payment processed successfully'
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    } else {
      // Log failed payment
      await supabaseClient
        .from('payment_logs')
        .insert({
          user_id: user.id,
          action: 'google_pay_failed',
          provider: 'google_pay',
          order_id,
          status: 'failed',
          error_message: 'Payment processing failed'
        });

      return new Response(
        JSON.stringify({
          success: false,
          error: 'Payment processing failed'
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
  } catch (error) {
    console.error('Google Pay processing error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error'
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});