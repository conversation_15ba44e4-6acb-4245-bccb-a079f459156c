
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the request body
    const { userId } = await req.json()

    // Create a Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Validate the request
    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'Missing user ID' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Get the user's subscription status
    const { data, error } = await supabase
      .from('users')
      .select('is_premium, subscription_type, subscription_start_date, subscription_end_date')
      .eq('id', userId)
      .single()

    if (error) {
      throw error
    }

    // Check if the subscription is active
    const now = new Date()
    const endDate = data.subscription_end_date ? new Date(data.subscription_end_date) : null
    const isActive = data.is_premium && endDate && endDate > now

    // Return the subscription status
    return new Response(
      JSON.stringify({
        isPremium: isActive,
        subscriptionType: isActive ? data.subscription_type : null,
        subscriptionStartDate: isActive ? data.subscription_start_date : null,
        subscriptionEndDate: isActive ? data.subscription_end_date : null,
        message: isActive ? 'Subscription is active' : 'No active subscription'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
