// @verify_jwt: true
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface CheckoutRequest {
  planType: 'monthly' | 'yearly';
  productId: string;
  userId: string;
  userEmail: string;
  userName: string;
}

interface LemonSqueezyCheckoutResponse {
  data: {
    id: string;
    attributes: {
      url: string;
      [key: string]: any;
    };
  };
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    // Verify request method
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        { status: 405, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get request body
    const body: CheckoutRequest = await req.json();
    const { planType, productId, userId, userEmail, userName } = body;

    // Validate required fields
    if (!planType || !productId || !userId || !userEmail) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get Lemon Squeezy credentials from secrets
    const apiKey = Deno.env.get('LEMONSQUEEZY_API_KEY');
    const storeId = Deno.env.get('LEMONSQUEEZY_STORE_ID');

    if (!apiKey || !storeId) {
      console.error('Missing Lemon Squeezy credentials');
      return new Response(
        JSON.stringify({ error: 'Payment service not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user details for checkout
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      console.error('User not found:', userError);
      return new Response(
        JSON.stringify({ error: 'User not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Create checkout session with Lemon Squeezy
    const checkoutData = {
      data: {
        type: 'checkouts',
        attributes: {
          checkout_options: {
            embed: false,
            media: false,
            logo: true,
          },
          checkout_data: {
            email: userEmail,
            name: userName,
            billing_address: {
              country: 'US', // Default country, can be customized
            },
          },
          product_options: {
            name: `HoofBeat Premium - ${planType.charAt(0).toUpperCase() + planType.slice(1)}`,
            description: `HoofBeat Premium ${planType} subscription`,
            media: [],
            redirect_url: `${Deno.env.get('APP_URL') || 'https://hoofbeat.app'}/payment-success`,
            receipt_button_text: 'Return to HoofBeat',
            receipt_link_url: `${Deno.env.get('APP_URL') || 'https://hoofbeat.app'}/dashboard`,
          },
          test_mode: Deno.env.get('ENVIRONMENT') !== 'production',
        },
        relationships: {
          store: {
            data: {
              type: 'stores',
              id: storeId,
            },
          },
          variant: {
            data: {
              type: 'variants',
              id: productId,
            },
          },
        },
      },
    };

    console.log('Creating Lemon Squeezy checkout:', JSON.stringify(checkoutData, null, 2));

    const response = await fetch('https://api.lemonsqueezy.com/v1/checkouts', {
      method: 'POST',
      headers: {
        'Accept': 'application/vnd.api+json',
        'Content-Type': 'application/vnd.api+json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(checkoutData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Lemon Squeezy API error:', response.status, errorText);
      return new Response(
        JSON.stringify({ 
          error: 'Failed to create checkout session',
          details: errorText 
        }),
        { status: response.status, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const checkoutResponse: LemonSqueezyCheckoutResponse = await response.json();
    const checkoutUrl = checkoutResponse.data.attributes.url;

    if (!checkoutUrl) {
      console.error('No checkout URL in response:', checkoutResponse);
      return new Response(
        JSON.stringify({ error: 'Invalid checkout response' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    console.log('Checkout created successfully:', checkoutResponse.data.id);

    // Log the checkout creation for tracking
    await supabase
      .from('payment_logs')
      .insert({
        user_id: userId,
        action: 'checkout_created',
        provider: 'lemonsqueezy',
        checkout_id: checkoutResponse.data.id,
        checkout_url: checkoutUrl,
        plan_type: planType,
        product_id: productId,
        metadata: {
          lemon_squeezy_checkout_id: checkoutResponse.data.id,
          user_email: userEmail,
        },
      });

    return new Response(
      JSON.stringify({ 
        checkoutUrl,
        checkoutId: checkoutResponse.data.id 
      }),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        } 
      }
    );

  } catch (error) {
    console.error('Error in create-lemonsqueezy-checkout:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        } 
      }
    );
  }
});