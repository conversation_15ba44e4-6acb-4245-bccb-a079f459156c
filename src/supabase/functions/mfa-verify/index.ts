import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { encode as base32Encode } from "https://deno.land/std@0.168.0/encoding/base32.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

interface MfaVerifyRequest {
  challenge_id: string;
  verification_code: string;
  is_backup_code?: boolean;
}

interface MfaVerifyResponse {
  success: boolean;
  verified?: boolean;
  session_token?: string;
  error?: string;
  attempts_remaining?: number;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse request body
    const { challenge_id, verification_code, is_backup_code = false }: MfaVerifyRequest = await req.json();
    
    if (!challenge_id || !verification_code) {
      return new Response(
        JSON.stringify({ success: false, error: 'Challenge ID and verification code are required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`MFA verification attempt for challenge: ${challenge_id}`);

    // Get the challenge
    const { data: challenge, error: challengeError } = await supabase
      .from('mfa_challenges')
      .select('*')
      .eq('challenge_id', challenge_id)
      .eq('is_verified', false)
      .single();

    if (challengeError || !challenge) {
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid or expired challenge' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check if challenge has expired
    if (new Date(challenge.expires_at) < new Date()) {
      // Clean up expired challenge
      await supabase
        .from('mfa_challenges')
        .delete()
        .eq('challenge_id', challenge_id);

      return new Response(
        JSON.stringify({ success: false, error: 'Challenge has expired. Please request a new one.' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check rate limiting (max 5 attempts per challenge)
    const currentAttempts = challenge.verification_attempts || 0;
    if (currentAttempts >= 5) {
      // Delete challenge after too many attempts
      await supabase
        .from('mfa_challenges')
        .delete()
        .eq('challenge_id', challenge_id);

      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Too many verification attempts. Please request a new challenge.',
          attempts_remaining: 0
        }),
        { status: 429, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get user's MFA settings
    const { data: mfaSettings, error: mfaError } = await supabase
      .from('user_mfa_settings')
      .select('*')
      .eq('user_id', challenge.user_id)
      .eq('is_enabled', true)
      .single();

    if (mfaError || !mfaSettings) {
      return new Response(
        JSON.stringify({ success: false, error: 'MFA not enabled for this user' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    let isValidCode = false;

    if (is_backup_code) {
      // Verify backup code
      isValidCode = await verifyBackupCode(mfaSettings.backup_codes, verification_code, challenge.user_id, supabase);
    } else {
      // Verify TOTP code
      isValidCode = await verifyTOTPCode(mfaSettings.totp_secret, verification_code);
    }

    // Update challenge with attempt
    await supabase
      .from('mfa_challenges')
      .update({
        verification_attempts: currentAttempts + 1,
        last_attempt_at: new Date().toISOString()
      })
      .eq('challenge_id', challenge_id);

    if (!isValidCode) {
      const attemptsRemaining = 5 - (currentAttempts + 1);
      
      return new Response(
        JSON.stringify({ 
          success: false, 
          verified: false,
          error: 'Invalid verification code. Please try again.',
          attempts_remaining: attemptsRemaining
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Code is valid - mark challenge as verified
    await supabase
      .from('mfa_challenges')
      .update({
        is_verified: true,
        verified_at: new Date().toISOString()
      })
      .eq('challenge_id', challenge_id);

    // Generate a session token for the verified user
    const sessionToken = await generateSessionToken(challenge.user_id, supabase);

    // Log successful MFA verification
    await supabase
      .from('mfa_verification_logs')
      .insert({
        user_id: challenge.user_id,
        challenge_id: challenge_id,
        verification_method: is_backup_code ? 'backup_code' : 'totp',
        success: true,
        ip_address: req.headers.get('x-forwarded-for') || 'unknown',
        user_agent: req.headers.get('user-agent') || 'unknown',
        verified_at: new Date().toISOString()
      });

    console.log(`MFA verification successful for user ${challenge.user_id}`);

    const response: MfaVerifyResponse = {
      success: true,
      verified: true,
      session_token: sessionToken
    };

    return new Response(
      JSON.stringify(response),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('MFA verification error:', error);
    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

// Helper Functions

async function verifyTOTPCode(secret: string, code: string): Promise<boolean> {
  try {
    // Get current time in 30-second intervals (TOTP standard)
    const timeStep = Math.floor(Date.now() / 1000 / 30);
    
    // Check current time step and previous/next for clock drift tolerance
    for (let i = -1; i <= 1; i++) {
      const testTimeStep = timeStep + i;
      const expectedCode = await generateTOTPCode(secret, testTimeStep);
      
      if (expectedCode === code) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error verifying TOTP code:', error);
    return false;
  }
}

async function verifyBackupCode(backupCodes: string[], code: string, userId: string, supabase: any): Promise<boolean> {
  if (!backupCodes || !Array.isArray(backupCodes)) {
    return false;
  }

  const codeIndex = backupCodes.indexOf(code);
  if (codeIndex === -1) {
    return false;
  }

  // Remove the used backup code
  const updatedCodes = backupCodes.filter((_, index) => index !== codeIndex);
  
  // Update the backup codes in the database
  await supabase
    .from('user_mfa_settings')
    .update({
      backup_codes: updatedCodes,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId);

  return true;
}

async function generateTOTPCode(secret: string, timeStep: number): Promise<string> {
  // Decode base32 secret
  const secretBytes = base32Decode(secret);
  
  // Convert time step to 8-byte big-endian
  const timeBytes = new ArrayBuffer(8);
  const timeView = new DataView(timeBytes);
  timeView.setUint32(4, timeStep, false); // Big-endian
  
  // Import secret as HMAC key
  const key = await crypto.subtle.importKey(
    'raw',
    secretBytes,
    { name: 'HMAC', hash: 'SHA-1' },
    false,
    ['sign']
  );
  
  // Generate HMAC
  const hmac = await crypto.subtle.sign('HMAC', key, timeBytes);
  const hmacBytes = new Uint8Array(hmac);
  
  // Dynamic truncation (RFC 4226)
  const offset = hmacBytes[19] & 0x0f;
  const code = (
    ((hmacBytes[offset] & 0x7f) << 24) |
    ((hmacBytes[offset + 1] & 0xff) << 16) |
    ((hmacBytes[offset + 2] & 0xff) << 8) |
    (hmacBytes[offset + 3] & 0xff)
  ) % 1000000;
  
  // Pad with leading zeros to ensure 6 digits
  return code.toString().padStart(6, '0');
}

function base32Decode(encoded: string): Uint8Array {
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
  const cleanInput = encoded.toUpperCase().replace(/[^A-Z2-7]/g, '');
  
  let bits = '';
  for (const char of cleanInput) {
    const index = alphabet.indexOf(char);
    if (index === -1) continue;
    bits += index.toString(2).padStart(5, '0');
  }
  
  const bytes = [];
  for (let i = 0; i < bits.length; i += 8) {
    const byte = bits.slice(i, i + 8);
    if (byte.length === 8) {
      bytes.push(parseInt(byte, 2));
    }
  }
  
  return new Uint8Array(bytes);
}

async function generateSessionToken(userId: string, supabase: any): Promise<string> {
  try {
    // Create a session token that can be used to complete the login
    const tokenBytes = new Uint8Array(32);
    crypto.getRandomValues(tokenBytes);
    
    const sessionToken = Array.from(tokenBytes)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    // Store the session token temporarily (expires in 10 minutes)
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);
    
    await supabase
      .from('mfa_session_tokens')
      .insert({
        token: sessionToken,
        user_id: userId,
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString()
      });
    
    // Clean up expired tokens
    await supabase
      .from('mfa_session_tokens')
      .delete()
      .lt('expires_at', new Date().toISOString());
    
    return sessionToken;
  } catch (error) {
    console.error('Error generating session token:', error);
    throw error;
  }
}