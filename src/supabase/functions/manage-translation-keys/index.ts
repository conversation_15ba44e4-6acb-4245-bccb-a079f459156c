import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface TranslationKeyRequest {
  action: 'get' | 'set' | 'delete' | 'list';
  provider?: 'google' | 'deepl' | 'openai' | 'azure';
  apiKey?: string;
  region?: string; // For Azure
  model?: string; // For OpenAI
}

interface TranslationKeyResponse {
  success: boolean;
  data?: any;
  error?: string;
}

serve(async (req) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS'
  };

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    // Get user from JWT
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      throw new Error('Invalid authentication');
    }

    // Check if user is admin (you can customize this logic)
    const { data: profile } = await supabaseClient
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profile?.role !== 'admin') {
      throw new Error('Admin access required');
    }

    const requestData: TranslationKeyRequest = await req.json();
    let response: TranslationKeyResponse;

    switch (requestData.action) {
      case 'get':
        response = await getTranslationKey(requestData.provider!);
        break;
      case 'set':
        response = await setTranslationKey(
          requestData.provider!,
          requestData.apiKey!,
          requestData.region,
          requestData.model
        );
        break;
      case 'delete':
        response = await deleteTranslationKey(requestData.provider!);
        break;
      case 'list':
        response = await listTranslationKeys();
        break;
      default:
        throw new Error('Invalid action');
    }

    return new Response(JSON.stringify(response), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    console.error('Translation key management error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400
      }
    );
  }
});

/**
 * Get translation API key for a provider
 */
async function getTranslationKey(provider: string): Promise<TranslationKeyResponse> {
  try {
    const secretName = `TRANSLATION_${provider.toUpperCase()}_API_KEY`;
    
    // In a real implementation, you'd retrieve from a secure secrets store
    // For now, we'll use environment variables
    const apiKey = Deno.env.get(secretName);
    
    if (!apiKey) {
      return {
        success: false,
        error: `API key for ${provider} not found`
      };
    }

    // Return masked key for security
    const maskedKey = apiKey.substring(0, 8) + '*'.repeat(apiKey.length - 8);
    
    return {
      success: true,
      data: {
        provider,
        apiKey: maskedKey,
        configured: true
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Set translation API key for a provider
 */
async function setTranslationKey(
  provider: string,
  apiKey: string,
  region?: string,
  model?: string
): Promise<TranslationKeyResponse> {
  try {
    // Validate API key format based on provider
    const isValid = validateApiKey(provider, apiKey);
    if (!isValid) {
      return {
        success: false,
        error: `Invalid API key format for ${provider}`
      };
    }

    // Test the API key by making a simple request
    const testResult = await testApiKey(provider, apiKey, region, model);
    if (!testResult.success) {
      return {
        success: false,
        error: `API key test failed: ${testResult.error}`
      };
    }

    // In a real implementation, you'd store in a secure secrets store
    // For now, we'll just validate and return success
    
    return {
      success: true,
      data: {
        provider,
        configured: true,
        tested: true,
        capabilities: testResult.capabilities
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Delete translation API key for a provider
 */
async function deleteTranslationKey(provider: string): Promise<TranslationKeyResponse> {
  try {
    // In a real implementation, you'd remove from secrets store
    
    return {
      success: true,
      data: {
        provider,
        configured: false
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * List all configured translation providers
 */
async function listTranslationKeys(): Promise<TranslationKeyResponse> {
  try {
    const providers = ['google', 'deepl', 'openai', 'azure'];
    const configured = [];
    
    for (const provider of providers) {
      const secretName = `TRANSLATION_${provider.toUpperCase()}_API_KEY`;
      const apiKey = Deno.env.get(secretName);
      
      if (apiKey) {
        configured.push({
          provider,
          configured: true,
          maskedKey: apiKey.substring(0, 8) + '*'.repeat(Math.max(0, apiKey.length - 8))
        });
      } else {
        configured.push({
          provider,
          configured: false
        });
      }
    }
    
    return {
      success: true,
      data: {
        providers: configured,
        totalConfigured: configured.filter(p => p.configured).length
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Validate API key format for different providers
 */
function validateApiKey(provider: string, apiKey: string): boolean {
  switch (provider) {
    case 'google':
      // Google API keys are typically 39 characters
      return /^AIza[0-9A-Za-z_-]{35}$/.test(apiKey);
    case 'deepl':
      // DeepL API keys end with :fx for free tier
      return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}(:fx)?$/.test(apiKey);
    case 'openai':
      // OpenAI API keys start with sk-
      return /^sk-[A-Za-z0-9]{48}$/.test(apiKey);
    case 'azure':
      // Azure keys are typically 32 characters
      return /^[0-9a-f]{32}$/.test(apiKey);
    default:
      return false;
  }
}

/**
 * Test API key by making a simple translation request
 */
async function testApiKey(
  provider: string,
  apiKey: string,
  region?: string,
  model?: string
): Promise<{ success: boolean; error?: string; capabilities?: any }> {
  const testText = 'Hello';
  const targetLanguage = 'es';
  
  try {
    switch (provider) {
      case 'google':
        return await testGoogleTranslate(apiKey, testText, targetLanguage);
      case 'deepl':
        return await testDeepL(apiKey, testText, targetLanguage);
      case 'openai':
        return await testOpenAI(apiKey, testText, targetLanguage, model);
      case 'azure':
        return await testAzureTranslate(apiKey, testText, targetLanguage, region);
      default:
        return { success: false, error: 'Unknown provider' };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Test Google Translate API
 */
async function testGoogleTranslate(apiKey: string, text: string, targetLang: string) {
  const url = `https://translation.googleapis.com/language/translate/v2?key=${apiKey}`;
  
  const response = await fetch(url, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      q: text,
      source: 'en',
      target: targetLang,
      format: 'text'
    })
  });
  
  if (!response.ok) {
    const error = await response.text();
    return { success: false, error };
  }
  
  const data = await response.json();
  return {
    success: true,
    capabilities: {
      batchTranslation: true,
      languageDetection: true,
      maxCharacters: 5000
    }
  };
}

/**
 * Test DeepL API
 */
async function testDeepL(apiKey: string, text: string, targetLang: string) {
  const url = 'https://api-free.deepl.com/v2/translate';
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `DeepL-Auth-Key ${apiKey}`,
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: new URLSearchParams({
      text,
      source_lang: 'EN',
      target_lang: 'ES'
    })
  });
  
  if (!response.ok) {
    const error = await response.text();
    return { success: false, error };
  }
  
  return {
    success: true,
    capabilities: {
      batchTranslation: true,
      highQuality: true,
      maxCharacters: 5000
    }
  };
}

/**
 * Test OpenAI API
 */
async function testOpenAI(apiKey: string, text: string, targetLang: string, model = 'gpt-3.5-turbo') {
  const url = 'https://api.openai.com/v1/chat/completions';
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model,
      messages: [{
        role: 'user',
        content: `Translate "${text}" to Spanish`
      }],
      max_tokens: 50
    })
  });
  
  if (!response.ok) {
    const error = await response.text();
    return { success: false, error };
  }
  
  return {
    success: true,
    capabilities: {
      contextAware: true,
      customPrompts: true,
      maxCharacters: 2000
    }
  };
}

/**
 * Test Azure Translate API
 */
async function testAzureTranslate(apiKey: string, text: string, targetLang: string, region = 'global') {
  const url = `https://api.cognitive.microsofttranslator.com/translate?api-version=3.0&to=${targetLang}`;
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Ocp-Apim-Subscription-Key': apiKey,
      'Ocp-Apim-Subscription-Region': region,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify([{ text }])
  });
  
  if (!response.ok) {
    const error = await response.text();
    return { success: false, error };
  }
  
  return {
    success: true,
    capabilities: {
      batchTranslation: true,
      languageDetection: true,
      maxCharacters: 10000
    }
  };
}