// @verify_jwt: true
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface SmartAlertsRequest {
  animal_id?: string; // Optional - if not provided, generates alerts for all user's animals
  alert_types?: string[]; // Optional - specific alert types to generate
}

interface SmartAlertsResponse {
  success: boolean;
  alerts_generated?: number;
  alerts?: any[];
  error?: string;
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response('ok', {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          'Access-Control-Allow-Methods': 'POST, OPTIONS'
        }
      });
    }

    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        { status: 405, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user from JWT
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ success: false, error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Parse request body
    const { animal_id, alert_types }: SmartAlertsRequest = await req.json();
    
    console.log(`Generating smart alerts for user ${user.id}${animal_id ? ` and animal ${animal_id}` : ''}`);

    // Get animals to analyze
    let animalsQuery = supabase
      .from('animals')
      .select('id, name, species, breed, age')
      .eq('user_id', user.id);
    
    if (animal_id) {
      animalsQuery = animalsQuery.eq('id', animal_id);
    }
    
    const { data: animals, error: animalsError } = await animalsQuery;
    
    if (animalsError || !animals || animals.length === 0) {
      return new Response(
        JSON.stringify({ success: false, error: 'No animals found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const generatedAlerts = [];
    
    // Generate alerts for each animal
    for (const animal of animals) {
      const animalAlerts = await generateAlertsForAnimal(supabase, user.id, animal, alert_types);
      generatedAlerts.push(...animalAlerts);
    }

    console.log(`Generated ${generatedAlerts.length} smart alerts`);

    const response: SmartAlertsResponse = {
      success: true,
      alerts_generated: generatedAlerts.length,
      alerts: generatedAlerts
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    );

  } catch (error) {
    console.error('Error in generate-smart-alerts:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    );
  }
});

async function generateAlertsForAnimal(supabase: any, userId: string, animal: any, alertTypes?: string[]) {
  const alerts = [];
  const now = new Date();
  const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  // Fetch recent data for analysis
  const [vitalsData, healthScores, riskAssessments, dehydrationData, medicationData] = await Promise.all([
    // Recent vitals
    supabase
      .from('vitals')
      .select('*')
      .eq('animal_id', animal.id)
      .gte('recorded_at', last7Days.toISOString())
      .order('recorded_at', { ascending: false }),
    
    // Recent health scores
    supabase
      .from('ai_health_scores')
      .select('*')
      .eq('animal_id', animal.id)
      .gte('score_date', last7Days.toISOString().split('T')[0])
      .order('score_date', { ascending: false }),
    
    // Active risk assessments
    supabase
      .from('disease_risk_assessments')
      .select('*')
      .eq('animal_id', animal.id)
      .eq('is_active', true)
      .gte('assessment_date', last7Days.toISOString()),
    
    // Recent dehydration data
    supabase
      .from('dehydration_logs')
      .select('*')
      .eq('animal_id', animal.id)
      .gte('recorded_at', last24Hours.toISOString()),
    
    // Recent medications
    supabase
      .from('medications')
      .select('*')
      .eq('animal_id', animal.id)
      .gte('created_at', last7Days.toISOString())
  ]);

  const data = {
    vitals: vitalsData.data || [],
    healthScores: healthScores.data || [],
    riskAssessments: riskAssessments.data || [],
    dehydration: dehydrationData.data || [],
    medications: medicationData.data || []
  };

  // Generate different types of alerts
  if (!alertTypes || alertTypes.includes('health_score')) {
    const healthScoreAlerts = generateHealthScoreAlerts(animal, data);
    alerts.push(...healthScoreAlerts);
  }

  if (!alertTypes || alertTypes.includes('vital_signs')) {
    const vitalSignsAlerts = generateVitalSignsAlerts(animal, data);
    alerts.push(...vitalSignsAlerts);
  }

  if (!alertTypes || alertTypes.includes('disease_risk')) {
    const diseaseRiskAlerts = generateDiseaseRiskAlerts(animal, data);
    alerts.push(...diseaseRiskAlerts);
  }

  if (!alertTypes || alertTypes.includes('medication')) {
    const medicationAlerts = generateMedicationAlerts(animal, data);
    alerts.push(...medicationAlerts);
  }

  if (!alertTypes || alertTypes.includes('dehydration')) {
    const dehydrationAlerts = generateDehydrationAlerts(animal, data);
    alerts.push(...dehydrationAlerts);
  }

  if (!alertTypes || alertTypes.includes('data_quality')) {
    const dataQualityAlerts = generateDataQualityAlerts(animal, data);
    alerts.push(...dataQualityAlerts);
  }

  // Save alerts to database
  const savedAlerts = [];
  for (const alert of alerts) {
    // Check if similar alert already exists
    const { data: existingAlert } = await supabase
      .from('smart_health_alerts')
      .select('id')
      .eq('animal_id', animal.id)
      .eq('alert_type', alert.alert_type)
      .eq('is_resolved', false)
      .gte('created_at', last24Hours.toISOString())
      .limit(1);

    if (!existingAlert || existingAlert.length === 0) {
      const { data: newAlert, error } = await supabase
        .from('smart_health_alerts')
        .insert({
          animal_id: animal.id,
          user_id: userId,
          alert_type: alert.alert_type,
          priority_level: alert.priority_level,
          title: alert.title,
          description: alert.description,
          recommended_actions: alert.recommended_actions,
          expires_at: alert.expires_at
        })
        .select()
        .single();
      
      if (!error && newAlert) {
        savedAlerts.push(newAlert);
        
        // Trigger push notification for high or critical alerts
        if (newAlert.priority_level === 'high' || newAlert.priority_level === 'critical') {
          try {
            console.log(`Triggering push notification for alert: ${newAlert.id}`);
            const { error: invokeError } = await supabase.functions.invoke('send-push-notification', {
              body: {
                user_id: userId,
                title: `${newAlert.priority_level.toUpperCase()} Alert: ${newAlert.title}`,
                body: newAlert.description,
                animal_id: animal.id,
                alert_type: newAlert.alert_type,
                priority: newAlert.priority_level,
              }
            });
            if (invokeError) {
              console.error(`Error invoking send-push-notification for alert ${newAlert.id}:`, invokeError);
            } else {
              console.log(`Push notification successfully triggered for alert ${newAlert.id}`);
            }
          } catch (e) {
            console.error(`Exception while invoking send-push-notification for alert ${newAlert.id}:`, e);
          }
        }
      }
    }
  }

  return savedAlerts;
}

function generateHealthScoreAlerts(animal: any, data: any) {
  const alerts = [];
  const latestScore = data.healthScores[0];
  
  if (!latestScore) {
    // No health score available
    alerts.push({
      alert_type: 'health_score_missing',
      priority_level: 'medium',
      title: `Health Score Missing for ${animal.name}`,
      description: `No recent health score available for ${animal.name}. Calculate a health score to get AI-powered insights.`,
      recommended_actions: ['Calculate health score', 'Log recent vital signs', 'Update feeding records'],
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    });
  } else {
    // Analyze health score trends
    if (latestScore.overall_score < 50) {
      alerts.push({
        alert_type: 'health_score_critical',
        priority_level: 'critical',
        title: `Critical Health Score for ${animal.name}`,
        description: `${animal.name}'s health score is ${latestScore.overall_score}/100, indicating serious health concerns that need immediate attention.`,
        recommended_actions: ['Contact veterinarian immediately', 'Monitor vital signs closely', 'Review all medications'],
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      });
    } else if (latestScore.overall_score < 70) {
      alerts.push({
        alert_type: 'health_score_concerning',
        priority_level: 'high',
        title: `Concerning Health Score for ${animal.name}`,
        description: `${animal.name}'s health score is ${latestScore.overall_score}/100, showing areas that need attention.`,
        recommended_actions: ['Schedule veterinary check-up', 'Review feeding schedule', 'Increase monitoring frequency'],
        expires_at: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
      });
    }
    
    // Check for declining trends
    if (data.healthScores.length >= 2) {
      const previousScore = data.healthScores[1];
      const scoreDrop = previousScore.overall_score - latestScore.overall_score;
      
      if (scoreDrop >= 15) {
        alerts.push({
          alert_type: 'health_score_declining',
          priority_level: 'high',
          title: `Declining Health Trend for ${animal.name}`,
          description: `${animal.name}'s health score dropped by ${scoreDrop} points from ${previousScore.overall_score} to ${latestScore.overall_score}.`,
          recommended_actions: ['Investigate cause of decline', 'Increase monitoring', 'Consider veterinary consultation'],
          expires_at: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString()
        });
      }
    }
  }
  
  return alerts;
}

function generateVitalSignsAlerts(animal: any, data: any) {
  const alerts = [];
  const recentVitals = data.vitals.slice(0, 5); // Last 5 readings
  
  if (recentVitals.length === 0) {
    alerts.push({
      alert_type: 'vitals_missing',
      priority_level: 'medium',
      title: `No Recent Vital Signs for ${animal.name}`,
      description: `No vital signs recorded for ${animal.name} in the past 7 days. Regular monitoring is important for health tracking.`,
      recommended_actions: ['Record vital signs', 'Set up monitoring schedule', 'Use connected devices if available'],
      expires_at: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
    });
    return alerts;
  }
  
  // Temperature alerts
  const temperatures = recentVitals.map(v => v.temperature).filter(t => t);
  if (temperatures.length > 0) {
    const avgTemp = temperatures.reduce((sum, t) => sum + t, 0) / temperatures.length;
    const highTempCount = temperatures.filter(t => t > 39.5).length;
    
    if (avgTemp > 40) {
      alerts.push({
        alert_type: 'temperature_critical',
        priority_level: 'critical',
        title: `High Fever Alert for ${animal.name}`,
        description: `${animal.name} has a dangerously high average temperature of ${avgTemp.toFixed(1)}°C. Immediate veterinary attention required.`,
        recommended_actions: ['Contact veterinarian immediately', 'Monitor temperature hourly', 'Provide cooling measures'],
        expires_at: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString()
      });
    } else if (highTempCount >= temperatures.length * 0.6) {
      alerts.push({
        alert_type: 'temperature_elevated',
        priority_level: 'high',
        title: `Persistent Fever for ${animal.name}`,
        description: `${animal.name} shows persistent elevated temperature patterns. Average: ${avgTemp.toFixed(1)}°C.`,
        recommended_actions: ['Schedule veterinary appointment', 'Monitor temperature every 4 hours', 'Ensure adequate hydration'],
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      });
    }
  }
  
  // Heart rate alerts
  const heartRates = recentVitals.map(v => v.heart_rate).filter(hr => hr);
  if (heartRates.length > 0) {
    const avgHeartRate = heartRates.reduce((sum, hr) => sum + hr, 0) / heartRates.length;
    
    if (avgHeartRate > 150) {
      alerts.push({
        alert_type: 'heart_rate_high',
        priority_level: 'high',
        title: `Elevated Heart Rate for ${animal.name}`,
        description: `${animal.name} has an elevated average heart rate of ${avgHeartRate.toFixed(0)} BPM.`,
        recommended_actions: ['Reduce activity level', 'Monitor for stress factors', 'Consider veterinary consultation'],
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      });
    } else if (avgHeartRate < 50) {
      alerts.push({
        alert_type: 'heart_rate_low',
        priority_level: 'high',
        title: `Low Heart Rate for ${animal.name}`,
        description: `${animal.name} has an unusually low average heart rate of ${avgHeartRate.toFixed(0)} BPM.`,
        recommended_actions: ['Monitor activity levels', 'Check for lethargy', 'Veterinary evaluation recommended'],
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      });
    }
  }
  
  return alerts;
}

function generateDiseaseRiskAlerts(animal: any, data: any) {
  const alerts = [];
  
  // Check for high-risk disease assessments
  const highRiskAssessments = data.riskAssessments.filter(ra => 
    ra.risk_level === 'critical' || ra.risk_level === 'high'
  );
  
  for (const assessment of highRiskAssessments) {
    const priority = assessment.risk_level === 'critical' ? 'critical' : 'high';
    
    alerts.push({
      alert_type: `disease_risk_${assessment.disease_category}`,
      priority_level: priority,
      title: `${assessment.disease_category.charAt(0).toUpperCase() + assessment.disease_category.slice(1)} Disease Risk for ${animal.name}`,
      description: `${animal.name} shows ${assessment.risk_level} risk for ${assessment.disease_category} conditions (${assessment.risk_score.toFixed(0)}% risk score).`,
      recommended_actions: assessment.recommendations.split('; '),
      expires_at: new Date(Date.now() + (priority === 'critical' ? 12 : 48) * 60 * 60 * 1000).toISOString()
    });
  }
  
  return alerts;
}

function generateMedicationAlerts(animal: any, data: any) {
  const alerts = [];
  const now = new Date();
  
  // Check for overdue medications
  const overdueMeds = data.medications.filter(med => {
    if (!med.next_dose_time) return false;
    const nextDose = new Date(med.next_dose_time);
    return nextDose < now;
  });
  
  if (overdueMeds.length > 0) {
    alerts.push({
      alert_type: 'medication_overdue',
      priority_level: 'high',
      title: `Overdue Medications for ${animal.name}`,
      description: `${animal.name} has ${overdueMeds.length} overdue medication(s): ${overdueMeds.map(m => m.medication_name).join(', ')}.`,
      recommended_actions: ['Administer overdue medications', 'Update medication schedule', 'Set medication reminders'],
      expires_at: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString()
    });
  }
  
  return alerts;
}

function generateDehydrationAlerts(animal: any, data: any) {
  const alerts = [];
  
  if (data.dehydration.length > 0) {
    const latestReading = data.dehydration[0];
    
    if (latestReading.hydration_level < 60) {
      alerts.push({
        alert_type: 'dehydration_critical',
        priority_level: 'critical',
        title: `Severe Dehydration Alert for ${animal.name}`,
        description: `${animal.name} shows severe dehydration with hydration level at ${latestReading.hydration_level}%.`,
        recommended_actions: ['Immediate hydration intervention', 'Contact veterinarian', 'Monitor every hour'],
        expires_at: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString()
      });
    } else if (latestReading.hydration_level < 75) {
      alerts.push({
        alert_type: 'dehydration_moderate',
        priority_level: 'high',
        title: `Dehydration Warning for ${animal.name}`,
        description: `${animal.name} shows moderate dehydration with hydration level at ${latestReading.hydration_level}%.`,
        recommended_actions: ['Increase water availability', 'Monitor hydration closely', 'Consider electrolyte supplements'],
        expires_at: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString()
      });
    }
  }
  
  return alerts;
}

function generateDataQualityAlerts(animal: any, data: any) {
  const alerts = [];
  const now = new Date();
  const last3Days = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
  
  // Check data completeness
  const recentVitals = data.vitals.filter(v => new Date(v.recorded_at) > last3Days);
  const recentHealthScores = data.healthScores.filter(hs => new Date(hs.score_date) > last3Days);
  
  if (recentVitals.length === 0 && recentHealthScores.length === 0) {
    alerts.push({
      alert_type: 'data_quality_low',
      priority_level: 'medium',
      title: `Insufficient Data for ${animal.name}`,
      description: `Limited health data available for ${animal.name} in the past 3 days. More data improves AI analysis accuracy.`,
      recommended_actions: ['Log vital signs regularly', 'Calculate health scores', 'Set up monitoring reminders'],
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    });
  }
  
  return alerts;
}