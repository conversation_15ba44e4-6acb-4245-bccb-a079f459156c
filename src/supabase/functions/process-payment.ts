
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the request body
    const { paymentMethod, planType, userId } = await req.json()

    // Create a Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Get user from JWT token for security
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header missing' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: userError } = await supabase.auth.getUser(token)
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }

    // Use the authenticated user's ID instead of the one from request body
    const authenticatedUserId = user.id

    // Validate the request
    if (!paymentMethod || !planType) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // In a real app, you would process the payment with a payment provider like Stripe
    // For this demo, we'll simulate a successful payment

    // Calculate subscription dates
    const now = new Date()
    const startDate = now.toISOString()
    let endDate = new Date(now)
    
    if (planType === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1)
    } else if (planType === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1)
    } else {
      return new Response(
        JSON.stringify({ error: 'Invalid plan type' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Update the user's subscription status in the database
    const { data, error } = await supabase
      .from('users')
      .update({
        is_premium: true,
        subscription_type: planType,
        subscription_start_date: startDate,
        subscription_end_date: endDate.toISOString()
      })
      .eq('id', authenticatedUserId)

    if (error) {
      throw error
    }

    // Return success response
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Payment processed successfully',
        subscription: {
          isPremium: true,
          type: planType,
          startDate,
          endDate: endDate.toISOString()
        }
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
