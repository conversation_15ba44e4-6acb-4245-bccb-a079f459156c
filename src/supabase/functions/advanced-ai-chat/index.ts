import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface ChatRequest {
  conversationId?: string;
  message: string;
  animalId?: string;
  conversationType?: string;
  context?: any;
}

interface ChatResponse {
  conversationId: string;
  messageId: string;
  response: string;
  intentAnalysis: any;
  extractedSymptoms: any[];
  extractedConcerns: any[];
  recommendedActions: any[];
  urgencyLevel: string;
  confidence: number;
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user ID from JWT
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Authorization header required');
    }

    const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
    if (!user) {
      throw new Error('Invalid authentication');
    }

    // Get request data
    const { conversationId, message, animalId, conversationType, context }: ChatRequest = await req.json();

    console.log(`Processing AI chat message for user ${user.id}`);

    const startTime = Date.now();

    // Get or create conversation
    let conversation;
    if (conversationId) {
      const { data: existingConversation, error: convError } = await supabase
        .from('ai_chat_conversations')
        .select('*')
        .eq('id', conversationId)
        .eq('user_id', user.id)
        .single();

      if (convError || !existingConversation) {
        throw new Error('Conversation not found or access denied');
      }
      conversation = existingConversation;
    } else {
      // Create new conversation
      const { data: newConversation, error: createError } = await supabase
        .from('ai_chat_conversations')
        .insert({
          user_id: user.id,
          animal_id: animalId,
          conversation_type: conversationType || 'general',
          conversation_title: generateConversationTitle(message),
          ai_model_version: 'gpt-4-health-v2.1',
          conversation_context: context || {}
        })
        .select()
        .single();

      if (createError) {
        throw new Error(`Failed to create conversation: ${createError.message}`);
      }
      conversation = newConversation;
    }

    // Get animal information if provided
    let animalInfo = null;
    if (animalId || conversation.animal_id) {
      const targetAnimalId = animalId || conversation.animal_id;
      const { data: animal, error: animalError } = await supabase
        .from('animals')
        .select('*')
        .eq('id', targetAnimalId)
        .eq('user_id', user.id)
        .single();

      if (!animalError && animal) {
        animalInfo = animal;
      }
    }

    // Get conversation history for context
    const { data: messageHistory, error: historyError } = await supabase
      .from('ai_chat_messages')
      .select('*')
      .eq('conversation_id', conversation.id)
      .order('created_at', { ascending: true })
      .limit(20);

    if (historyError) {
      console.error('Error fetching message history:', historyError);
    }

    // Analyze user message
    const messageAnalysis = await analyzeUserMessage(message, animalInfo, messageHistory || []);

    // Store user message
    const { data: userMessage, error: userMessageError } = await supabase
      .from('ai_chat_messages')
      .insert({
        conversation_id: conversation.id,
        message_type: 'user',
        message_content: message,
        intent_analysis: messageAnalysis.intent,
        sentiment_score: messageAnalysis.sentiment,
        extracted_symptoms: messageAnalysis.symptoms,
        extracted_concerns: messageAnalysis.concerns,
        medical_entities: messageAnalysis.medicalEntities
      })
      .select()
      .single();

    if (userMessageError) {
      console.error('Error storing user message:', userMessageError);
    }

    // Generate AI response
    const aiResponse = await generateAIResponse(
      message,
      messageAnalysis,
      animalInfo,
      messageHistory || [],
      conversation
    );

    const processingTime = Date.now() - startTime;

    // Store AI response
    const { data: aiMessage, error: aiMessageError } = await supabase
      .from('ai_chat_messages')
      .insert({
        conversation_id: conversation.id,
        message_type: 'ai',
        message_content: aiResponse.response,
        confidence_score: aiResponse.confidence,
        response_time_ms: processingTime,
        model_version: 'gpt-4-health-v2.1'
      })
      .select()
      .single();

    if (aiMessageError) {
      console.error('Error storing AI message:', aiMessageError);
    }

    // Update conversation
    await supabase
      .from('ai_chat_conversations')
      .update({
        total_messages: (conversation.total_messages || 0) + 2,
        last_message_at: new Date().toISOString(),
        health_concerns: messageAnalysis.concerns,
        mentioned_symptoms: messageAnalysis.symptoms,
        recommended_actions: aiResponse.recommendedActions,
        urgency_level: messageAnalysis.urgencyLevel
      })
      .eq('id', conversation.id);

    // Create health insights if significant health information was discussed
    if (messageAnalysis.symptoms.length > 0 || messageAnalysis.urgencyLevel !== 'low') {
      await createHealthInsights(supabase, user.id, animalInfo?.id, messageAnalysis, aiResponse);
    }

    // Send notifications for urgent concerns
    if (messageAnalysis.urgencyLevel === 'emergency') {
      await sendUrgentNotification(supabase, user.id, messageAnalysis, animalInfo);
    }

    console.log(`AI chat response generated in ${processingTime}ms`);

    return new Response(
      JSON.stringify({
        success: true,
        conversationId: conversation.id,
        messageId: aiMessage?.id,
        response: aiResponse.response,
        intentAnalysis: messageAnalysis.intent,
        extractedSymptoms: messageAnalysis.symptoms,
        extractedConcerns: messageAnalysis.concerns,
        recommendedActions: aiResponse.recommendedActions,
        urgencyLevel: messageAnalysis.urgencyLevel,
        confidence: aiResponse.confidence,
        processingTimeMs: processingTime
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('Advanced AI chat error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'AI chat processing failed'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});

function generateConversationTitle(message: string): string {
  // Generate a title based on the first message
  const words = message.split(' ').slice(0, 6);
  let title = words.join(' ');
  if (message.split(' ').length > 6) {
    title += '...';
  }
  return title || 'Health Consultation';
}

async function analyzeUserMessage(message: string, animalInfo: any, messageHistory: any[]): Promise<any> {
  // Simulate advanced NLP analysis
  const analysis = {
    intent: await analyzeIntent(message),
    sentiment: analyzeSentiment(message),
    symptoms: extractSymptoms(message),
    concerns: extractConcerns(message),
    medicalEntities: extractMedicalEntities(message),
    urgencyLevel: assessUrgency(message)
  };
  
  return analysis;
}

async function analyzeIntent(message: string): Promise<any> {
  // Simulate intent classification
  const intents = {
    'health_concern': 0,
    'symptom_report': 0,
    'general_question': 0,
    'emergency': 0,
    'medication_question': 0,
    'behavioral_concern': 0,
    'nutrition_question': 0,
    'preventive_care': 0
  };
  
  const lowerMessage = message.toLowerCase();
  
  // Health concern keywords
  if (lowerMessage.includes('sick') || lowerMessage.includes('ill') || lowerMessage.includes('not feeling well')) {
    intents.health_concern = 0.8;
  }
  
  // Symptom reporting keywords
  if (lowerMessage.includes('vomiting') || lowerMessage.includes('diarrhea') || lowerMessage.includes('limping')) {
    intents.symptom_report = 0.9;
  }
  
  // Emergency keywords
  if (lowerMessage.includes('emergency') || lowerMessage.includes('urgent') || lowerMessage.includes('help')) {
    intents.emergency = 0.7;
  }
  
  // Medication keywords
  if (lowerMessage.includes('medication') || lowerMessage.includes('medicine') || lowerMessage.includes('dose')) {
    intents.medication_question = 0.8;
  }
  
  // Behavioral keywords
  if (lowerMessage.includes('behavior') || lowerMessage.includes('acting') || lowerMessage.includes('aggressive')) {
    intents.behavioral_concern = 0.7;
  }
  
  // Nutrition keywords
  if (lowerMessage.includes('food') || lowerMessage.includes('eating') || lowerMessage.includes('diet')) {
    intents.nutrition_question = 0.6;
  }
  
  // Default to general question
  const maxIntent = Object.entries(intents).reduce((a, b) => intents[a[0]] > intents[b[0]] ? a : b);
  if (maxIntent[1] === 0) {
    intents.general_question = 0.5;
  }
  
  return intents;
}

function analyzeSentiment(message: string): number {
  // Simulate sentiment analysis (-1 to 1)
  const positiveWords = ['good', 'great', 'happy', 'healthy', 'better', 'improving'];
  const negativeWords = ['bad', 'sick', 'worried', 'concerned', 'worse', 'pain', 'hurt'];
  
  const words = message.toLowerCase().split(' ');
  let score = 0;
  
  for (const word of words) {
    if (positiveWords.includes(word)) score += 0.1;
    if (negativeWords.includes(word)) score -= 0.1;
  }
  
  return Math.max(-1, Math.min(1, score));
}

function extractSymptoms(message: string): any[] {
  const symptoms = [];
  const lowerMessage = message.toLowerCase();
  
  const symptomKeywords = {
    'vomiting': ['vomiting', 'throwing up', 'vomit'],
    'diarrhea': ['diarrhea', 'loose stool', 'runny stool'],
    'lethargy': ['tired', 'lethargic', 'sleepy', 'low energy'],
    'limping': ['limping', 'favoring leg', 'not walking normally'],
    'coughing': ['coughing', 'cough'],
    'sneezing': ['sneezing', 'sneeze'],
    'loss_of_appetite': ['not eating', 'loss of appetite', 'refusing food'],
    'excessive_drinking': ['drinking a lot', 'excessive thirst', 'drinking more'],
    'difficulty_breathing': ['breathing hard', 'panting', 'difficulty breathing'],
    'skin_irritation': ['scratching', 'itchy', 'skin irritation', 'rash']
  };
  
  for (const [symptom, keywords] of Object.entries(symptomKeywords)) {
    for (const keyword of keywords) {
      if (lowerMessage.includes(keyword)) {
        symptoms.push({
          symptom: symptom,
          confidence: 0.8,
          mentioned_text: keyword
        });
        break;
      }
    }
  }
  
  return symptoms;
}

function extractConcerns(message: string): any[] {
  const concerns = [];
  const lowerMessage = message.toLowerCase();
  
  const concernPatterns = {
    'health_decline': ['getting worse', 'declining', 'not improving'],
    'behavioral_change': ['acting different', 'behavior change', 'not normal'],
    'pain': ['in pain', 'hurting', 'seems painful'],
    'emergency': ['emergency', 'urgent', 'immediate help'],
    'medication_side_effects': ['side effects', 'reaction to medication']
  };
  
  for (const [concern, patterns] of Object.entries(concernPatterns)) {
    for (const pattern of patterns) {
      if (lowerMessage.includes(pattern)) {
        concerns.push({
          concern: concern,
          confidence: 0.7,
          mentioned_text: pattern
        });
        break;
      }
    }
  }
  
  return concerns;
}

function extractMedicalEntities(message: string): any[] {
  const entities = [];
  const lowerMessage = message.toLowerCase();
  
  // Common medications
  const medications = ['aspirin', 'ibuprofen', 'antibiotics', 'prednisone', 'insulin'];
  for (const med of medications) {
    if (lowerMessage.includes(med)) {
      entities.push({ type: 'medication', value: med, confidence: 0.9 });
    }
  }
  
  // Body parts
  const bodyParts = ['leg', 'eye', 'ear', 'stomach', 'back', 'tail', 'paw', 'nose'];
  for (const part of bodyParts) {
    if (lowerMessage.includes(part)) {
      entities.push({ type: 'body_part', value: part, confidence: 0.8 });
    }
  }
  
  // Time expressions
  const timeExpressions = ['today', 'yesterday', 'this morning', 'last night', 'for days'];
  for (const time of timeExpressions) {
    if (lowerMessage.includes(time)) {
      entities.push({ type: 'time', value: time, confidence: 0.7 });
    }
  }
  
  return entities;
}

function assessUrgency(message: string): string {
  const lowerMessage = message.toLowerCase();
  
  // Emergency keywords
  const emergencyKeywords = ['emergency', 'urgent', 'help', 'dying', 'collapsed', 'unconscious'];
  for (const keyword of emergencyKeywords) {
    if (lowerMessage.includes(keyword)) {
      return 'emergency';
    }
  }
  
  // High urgency keywords
  const highUrgencyKeywords = ['severe', 'blood', 'can\'t walk', 'won\'t eat', 'difficulty breathing'];
  for (const keyword of highUrgencyKeywords) {
    if (lowerMessage.includes(keyword)) {
      return 'high';
    }
  }
  
  // Medium urgency keywords
  const mediumUrgencyKeywords = ['vomiting', 'diarrhea', 'limping', 'not eating'];
  for (const keyword of mediumUrgencyKeywords) {
    if (lowerMessage.includes(keyword)) {
      return 'medium';
    }
  }
  
  return 'low';
}

async function generateAIResponse(message: string, analysis: any, animalInfo: any, messageHistory: any[], conversation: any): Promise<any> {
  // Simulate advanced AI response generation
  let response = '';
  const recommendedActions = [];
  let confidence = 0.8;
  
  // Determine primary intent
  const primaryIntent = Object.entries(analysis.intent).reduce((a, b) => a[1] > b[1] ? a : b)[0];
  
  // Generate response based on intent and urgency
  if (analysis.urgencyLevel === 'emergency') {
    response = "I understand this is an urgent situation. Based on what you've described, I strongly recommend contacting your veterinarian immediately or visiting an emergency animal hospital. ";
    recommendedActions.push('Contact emergency veterinarian immediately');
    recommendedActions.push('Monitor vital signs closely');
    confidence = 0.95;
  } else if (primaryIntent === 'symptom_report') {
    response = generateSymptomResponse(analysis.symptoms, animalInfo);
    recommendedActions.push(...generateSymptomRecommendations(analysis.symptoms));
  } else if (primaryIntent === 'health_concern') {
    response = generateHealthConcernResponse(analysis.concerns, animalInfo);
    recommendedActions.push(...generateHealthRecommendations(analysis.concerns));
  } else if (primaryIntent === 'medication_question') {
    response = "For medication-related questions, it's important to consult with your veterinarian who knows your pet's medical history. ";
    recommendedActions.push('Consult with veterinarian about medication');
    recommendedActions.push('Review medication instructions');
  } else {
    response = generateGeneralResponse(message, animalInfo);
    recommendedActions.push('Continue monitoring your pet\'s health');
  }
  
  // Add animal-specific context if available
  if (animalInfo) {
    response += ` Given that ${animalInfo.name} is a ${animalInfo.age}-year-old ${animalInfo.breed} ${animalInfo.species}, `;
    
    // Add breed-specific advice if relevant
    if (animalInfo.breed && primaryIntent === 'health_concern') {
      response += getBreedSpecificAdvice(animalInfo.breed, analysis);
    }
  }
  
  // Add general health monitoring advice
  response += " Remember to keep monitoring your pet's behavior and symptoms, and don't hesitate to contact your veterinarian if you notice any changes or if symptoms persist.";
  
  return {
    response: response,
    recommendedActions: recommendedActions,
    confidence: confidence
  };
}

function generateSymptomResponse(symptoms: any[], animalInfo: any): string {
  if (symptoms.length === 0) {
    return "I understand you're concerned about your pet's health. ";
  }
  
  const symptomNames = symptoms.map(s => s.symptom.replace('_', ' '));
  let response = `I see you've mentioned ${symptomNames.join(', ')}. `;
  
  // Provide specific advice for common symptoms
  for (const symptom of symptoms) {
    switch (symptom.symptom) {
      case 'vomiting':
        response += "For vomiting, it's important to withhold food for 12-24 hours and provide small amounts of water. ";
        break;
      case 'diarrhea':
        response += "For diarrhea, ensure your pet stays hydrated and consider a bland diet. ";
        break;
      case 'lethargy':
        response += "Lethargy can indicate various conditions and should be monitored closely. ";
        break;
      case 'limping':
        response += "Limping may indicate injury or pain and should be evaluated by a veterinarian. ";
        break;
    }
  }
  
  return response;
}

function generateSymptomRecommendations(symptoms: any[]): string[] {
  const recommendations = [];
  
  for (const symptom of symptoms) {
    switch (symptom.symptom) {
      case 'vomiting':
        recommendations.push('Withhold food for 12-24 hours');
        recommendations.push('Provide small amounts of water frequently');
        break;
      case 'diarrhea':
        recommendations.push('Ensure adequate hydration');
        recommendations.push('Consider bland diet (rice and chicken)');
        break;
      case 'lethargy':
        recommendations.push('Monitor activity levels closely');
        recommendations.push('Check for other symptoms');
        break;
      case 'limping':
        recommendations.push('Restrict activity and exercise');
        recommendations.push('Schedule veterinary examination');
        break;
    }
  }
  
  return [...new Set(recommendations)]; // Remove duplicates
}

function generateHealthConcernResponse(concerns: any[], animalInfo: any): string {
  let response = "I understand your concerns about your pet's health. ";
  
  if (concerns.some(c => c.concern === 'emergency')) {
    response += "This sounds like it could be an emergency situation. ";
  } else if (concerns.some(c => c.concern === 'pain')) {
    response += "If your pet appears to be in pain, this should be addressed promptly. ";
  } else {
    response += "It's good that you're being attentive to changes in your pet's condition. ";
  }
  
  return response;
}

function generateHealthRecommendations(concerns: any[]): string[] {
  const recommendations = [];
  
  for (const concern of concerns) {
    switch (concern.concern) {
      case 'emergency':
        recommendations.push('Seek immediate veterinary care');
        break;
      case 'pain':
        recommendations.push('Schedule veterinary examination for pain assessment');
        recommendations.push('Monitor pain levels and behavior');
        break;
      case 'behavioral_change':
        recommendations.push('Document behavioral changes');
        recommendations.push('Consider environmental factors');
        break;
    }
  }
  
  return recommendations;
}

function generateGeneralResponse(message: string, animalInfo: any): string {
  const responses = [
    "Thank you for reaching out about your pet's health. ",
    "I'm here to help with your pet health questions. ",
    "It's great that you're being proactive about your pet's wellbeing. "
  ];
  
  return responses[Math.floor(Math.random() * responses.length)];
}

function getBreedSpecificAdvice(breed: string, analysis: any): string {
  // Provide breed-specific health advice
  const breedAdvice = {
    'Golden Retriever': 'Golden Retrievers are prone to hip dysplasia and heart conditions. ',
    'German Shepherd': 'German Shepherds may be susceptible to hip and elbow dysplasia. ',
    'Labrador Retriever': 'Labradors can be prone to obesity and joint issues. ',
    'Persian': 'Persian cats may have respiratory issues due to their flat faces. ',
    'Siamese': 'Siamese cats are generally healthy but may be prone to dental issues. '
  };
  
  return breedAdvice[breed] || '';
}

async function createHealthInsights(supabase: any, userId: string, animalId: string | undefined, messageAnalysis: any, aiResponse: any): Promise<void> {
  if (!animalId) return;
  
  const insights = [];
  
  // Create insights for reported symptoms
  if (messageAnalysis.symptoms.length > 0) {
    insights.push({
      insight_type: 'symptom_report',
      insight_category: 'health_monitoring',
      insight_title: 'Symptoms Reported in Chat',
      insight_description: `User reported the following symptoms: ${messageAnalysis.symptoms.map(s => s.symptom.replace('_', ' ')).join(', ')}`,
      confidence_level: 0.8,
      importance_score: messageAnalysis.urgencyLevel === 'high' ? 0.9 : 0.7,
      supporting_data: {
        symptoms: messageAnalysis.symptoms,
        urgency_level: messageAnalysis.urgencyLevel
      },
      recommended_actions: aiResponse.recommendedActions
    });
  }
  
  // Save insights
  for (const insight of insights) {
    await supabase
      .from('ai_insights')
      .insert({
        animal_id: animalId,
        user_id: userId,
        ...insight,
        evidence_strength: insight.confidence_level > 0.8 ? 'strong' : 'moderate'
      });
  }
}

async function sendUrgentNotification(supabase: any, userId: string, messageAnalysis: any, animalInfo: any): Promise<void> {
  await supabase.functions.invoke('send-push-notification', {
    body: {
      userId: userId,
      title: 'Emergency Health Consultation',
      message: 'Your AI health consultation indicates an emergency situation. Please seek immediate veterinary care.',
      priority: 'high',
      data: {
        alert_type: 'emergency_consultation',
        animal_id: animalInfo?.id,
        urgency_level: messageAnalysis.urgencyLevel
      }
    }
  });
}