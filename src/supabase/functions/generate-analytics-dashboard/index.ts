import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// @verify_jwt: true

interface DashboardRequest {
  dashboardId?: string;
  dashboardConfig?: any;
  timeRange?: string;
  filters?: any;
  refreshCache?: boolean;
}

interface WidgetData {
  widgetId: string;
  widgetType: string;
  data: any;
  metadata: any;
  executionTime: number;
  cacheHit: boolean;
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        },
      });
    }

    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user ID from JWT
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('Authorization header required');
    }

    const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
    if (!user) {
      throw new Error('Invalid authentication');
    }

    // Get request data
    const { dashboardId, dashboardConfig, timeRange, filters, refreshCache }: DashboardRequest = await req.json();

    console.log(`Generating analytics dashboard for user ${user.id}`);

    const startTime = Date.now();
    let dashboard;
    let widgets = [];

    // Get dashboard configuration
    if (dashboardId) {
      // Load existing dashboard
      const { data: existingDashboard, error: dashboardError } = await supabase
        .from('custom_dashboards')
        .select('*, dashboard_widgets(*)')
        .eq('id', dashboardId)
        .single();

      if (dashboardError || !existingDashboard) {
        throw new Error(`Dashboard not found: ${dashboardError?.message}`);
      }

      dashboard = existingDashboard;
      widgets = existingDashboard.dashboard_widgets || [];
    } else if (dashboardConfig) {
      // Use provided configuration
      dashboard = dashboardConfig;
      widgets = dashboardConfig.widgets || [];
    } else {
      throw new Error('Either dashboardId or dashboardConfig must be provided');
    }

    // Generate data for each widget
    const widgetDataPromises = widgets.map(widget => 
      generateWidgetData(supabase, user.id, widget, timeRange, filters, refreshCache)
    );

    const widgetResults = await Promise.allSettled(widgetDataPromises);
    const widgetData: WidgetData[] = [];
    const errors: any[] = [];

    widgetResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        widgetData.push(result.value);
      } else {
        console.error(`Widget ${widgets[index].id} failed:`, result.reason);
        errors.push({
          widgetId: widgets[index].id,
          error: result.reason.message || 'Widget generation failed'
        });
      }
    });

    const totalTime = Date.now() - startTime;

    // Record dashboard analytics
    await recordDashboardAnalytics(supabase, dashboardId, user.id, widgetData, totalTime);

    // Generate insights from the dashboard data
    const insights = await generateDashboardInsights(supabase, user.id, widgetData, timeRange);

    console.log(`Dashboard generated in ${totalTime}ms with ${widgetData.length} widgets`);

    return new Response(
      JSON.stringify({
        success: true,
        dashboard: {
          id: dashboard.id || 'custom',
          name: dashboard.dashboard_name || dashboard.name,
          description: dashboard.dashboard_description || dashboard.description,
          layout: dashboard.layout_config || dashboard.layout,
          lastUpdated: new Date().toISOString()
        },
        widgets: widgetData,
        insights: insights,
        metadata: {
          generationTime: totalTime,
          widgetCount: widgetData.length,
          errorCount: errors.length,
          cacheHitRate: calculateCacheHitRate(widgetData)
        },
        errors: errors
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('Dashboard generation error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Dashboard generation failed'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});

async function generateWidgetData(supabase: any, userId: string, widget: any, timeRange?: string, filters?: any, refreshCache?: boolean): Promise<WidgetData> {
  const startTime = Date.now();
  let cacheHit = false;
  
  try {
    // Apply time range and filters to widget query
    const queryConfig = applyFiltersToQuery(widget.query_config, timeRange, filters);
    
    // Generate data based on widget type
    let data;
    switch (widget.widget_type) {
      case 'kpi':
        data = await generateKPIData(supabase, userId, queryConfig);
        break;
      case 'line_chart':
        data = await generateLineChartData(supabase, userId, queryConfig);
        break;
      case 'bar_chart':
        data = await generateBarChartData(supabase, userId, queryConfig);
        break;
      case 'pie_chart':
        data = await generatePieChartData(supabase, userId, queryConfig);
        break;
      case 'table':
        data = await generateTableData(supabase, userId, queryConfig);
        break;
      case 'heatmap':
        data = await generateHeatmapData(supabase, userId, queryConfig);
        break;
      case 'gauge':
        data = await generateGaugeData(supabase, userId, queryConfig);
        break;
      default:
        throw new Error(`Unsupported widget type: ${widget.widget_type}`);
    }

    const executionTime = Date.now() - startTime;

    // Record analytics query
    await supabase
      .from('analytics_queries')
      .insert({
        user_id: userId,
        query_type: 'dashboard',
        query_category: widget.widget_category,
        data_source: widget.data_source,
        query_parameters: queryConfig,
        execution_time_ms: executionTime,
        rows_returned: Array.isArray(data) ? data.length : 1,
        cache_hit: cacheHit,
        widget_id: widget.id
      });

    return {
      widgetId: widget.id,
      widgetType: widget.widget_type,
      data: data,
      metadata: {
        title: widget.widget_name,
        category: widget.widget_category,
        dataSource: widget.data_source,
        lastUpdated: new Date().toISOString(),
        queryConfig: queryConfig,
        visualizationConfig: widget.visualization_config
      },
      executionTime: executionTime,
      cacheHit: cacheHit
    };

  } catch (error) {
    console.error(`Error generating widget ${widget.id}:`, error);
    throw error;
  }
}

function applyFiltersToQuery(baseQuery: any, timeRange?: string, filters?: any): any {
  const query = { ...baseQuery };
  
  // Apply time range filter
  if (timeRange) {
    const now = new Date();
    let startDate;
    
    switch (timeRange) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    query.dateFilter = {
      start: startDate.toISOString(),
      end: now.toISOString()
    };
  }
  
  // Apply additional filters
  if (filters) {
    query.additionalFilters = filters;
  }
  
  return query;
}

async function generateKPIData(supabase: any, userId: string, queryConfig: any): Promise<any> {
  // Generate KPI data based on query configuration
  const metric = queryConfig.metric || 'count';
  const dataSource = queryConfig.dataSource || 'animals';
  
  let query = supabase.from(dataSource);
  
  // Apply user filter
  if (dataSource === 'animals') {
    query = query.eq('user_id', userId);
  } else if (dataSource === 'vitals') {
    query = query.in('animal_id', 
      supabase.from('animals').select('id').eq('user_id', userId)
    );
  }
  
  // Apply date filter
  if (queryConfig.dateFilter) {
    const dateColumn = getDateColumn(dataSource);
    query = query.gte(dateColumn, queryConfig.dateFilter.start)
                 .lte(dateColumn, queryConfig.dateFilter.end);
  }
  
  const { data, error } = await query;
  
  if (error) {
    throw new Error(`KPI query failed: ${error.message}`);
  }
  
  // Calculate KPI value
  let value;
  switch (metric) {
    case 'count':
      value = data?.length || 0;
      break;
    case 'avg':
      const values = data?.map(item => parseFloat(item[queryConfig.valueField])).filter(v => !isNaN(v)) || [];
      value = values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
      break;
    case 'sum':
      const sumValues = data?.map(item => parseFloat(item[queryConfig.valueField])).filter(v => !isNaN(v)) || [];
      value = sumValues.reduce((a, b) => a + b, 0);
      break;
    default:
      value = data?.length || 0;
  }
  
  return {
    value: value,
    metric: metric,
    unit: queryConfig.unit || '',
    trend: calculateTrend(data, queryConfig),
    comparison: await calculateComparison(supabase, userId, queryConfig)
  };
}

async function generateLineChartData(supabase: any, userId: string, queryConfig: any): Promise<any> {
  const dataSource = queryConfig.dataSource || 'vitals';
  const groupBy = queryConfig.groupBy || 'day';
  const valueField = queryConfig.valueField || 'heart_rate';
  
  let query = supabase.from(dataSource).select('*');
  
  // Apply user filter
  if (dataSource === 'animals') {
    query = query.eq('user_id', userId);
  } else {
    query = query.in('animal_id', 
      supabase.from('animals').select('id').eq('user_id', userId)
    );
  }
  
  // Apply date filter
  if (queryConfig.dateFilter) {
    const dateColumn = getDateColumn(dataSource);
    query = query.gte(dateColumn, queryConfig.dateFilter.start)
                 .lte(dateColumn, queryConfig.dateFilter.end)
                 .order(dateColumn, { ascending: true });
  }
  
  const { data, error } = await query;
  
  if (error) {
    throw new Error(`Line chart query failed: ${error.message}`);
  }
  
  // Group data by time period
  const groupedData = groupDataByTime(data || [], groupBy, valueField, getDateColumn(dataSource));
  
  return {
    labels: groupedData.map(item => item.label),
    datasets: [{
      label: queryConfig.label || valueField,
      data: groupedData.map(item => item.value),
      metadata: groupedData.map(item => item.metadata)
    }]
  };
}

async function generateBarChartData(supabase: any, userId: string, queryConfig: any): Promise<any> {
  const dataSource = queryConfig.dataSource || 'animals';
  const groupByField = queryConfig.groupByField || 'species';
  const valueField = queryConfig.valueField || 'count';
  
  let query = supabase.from(dataSource).select('*');
  
  // Apply user filter
  if (dataSource === 'animals') {
    query = query.eq('user_id', userId);
  } else {
    query = query.in('animal_id', 
      supabase.from('animals').select('id').eq('user_id', userId)
    );
  }
  
  // Apply date filter
  if (queryConfig.dateFilter) {
    const dateColumn = getDateColumn(dataSource);
    query = query.gte(dateColumn, queryConfig.dateFilter.start)
                 .lte(dateColumn, queryConfig.dateFilter.end);
  }
  
  const { data, error } = await query;
  
  if (error) {
    throw new Error(`Bar chart query failed: ${error.message}`);
  }
  
  // Group data by field
  const groupedData = groupDataByField(data || [], groupByField, valueField);
  
  return {
    labels: groupedData.map(item => item.label),
    datasets: [{
      label: queryConfig.label || valueField,
      data: groupedData.map(item => item.value),
      backgroundColor: generateColors(groupedData.length)
    }]
  };
}

async function generatePieChartData(supabase: any, userId: string, queryConfig: any): Promise<any> {
  // Similar to bar chart but formatted for pie chart
  const barData = await generateBarChartData(supabase, userId, queryConfig);
  
  return {
    labels: barData.labels,
    datasets: [{
      data: barData.datasets[0].data,
      backgroundColor: generateColors(barData.labels.length)
    }]
  };
}

async function generateTableData(supabase: any, userId: string, queryConfig: any): Promise<any> {
  const dataSource = queryConfig.dataSource || 'animals';
  const columns = queryConfig.columns || ['*'];
  const limit = queryConfig.limit || 50;
  
  let query = supabase.from(dataSource).select(columns.join(', ')).limit(limit);
  
  // Apply user filter
  if (dataSource === 'animals') {
    query = query.eq('user_id', userId);
  } else {
    query = query.in('animal_id', 
      supabase.from('animals').select('id').eq('user_id', userId)
    );
  }
  
  // Apply date filter
  if (queryConfig.dateFilter) {
    const dateColumn = getDateColumn(dataSource);
    query = query.gte(dateColumn, queryConfig.dateFilter.start)
                 .lte(dateColumn, queryConfig.dateFilter.end)
                 .order(dateColumn, { ascending: false });
  }
  
  const { data, error } = await query;
  
  if (error) {
    throw new Error(`Table query failed: ${error.message}`);
  }
  
  return {
    columns: columns,
    rows: data || [],
    totalRows: data?.length || 0
  };
}

async function generateHeatmapData(supabase: any, userId: string, queryConfig: any): Promise<any> {
  // Generate heatmap data (simplified implementation)
  const dataSource = queryConfig.dataSource || 'vitals';
  const xField = queryConfig.xField || 'hour';
  const yField = queryConfig.yField || 'day_of_week';
  const valueField = queryConfig.valueField || 'heart_rate';
  
  // This would generate a more complex heatmap query in production
  const mockData = [];
  for (let x = 0; x < 24; x++) {
    for (let y = 0; y < 7; y++) {
      mockData.push({
        x: x,
        y: y,
        value: Math.random() * 100 + 50
      });
    }
  }
  
  return {
    data: mockData,
    xLabels: Array.from({length: 24}, (_, i) => `${i}:00`),
    yLabels: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
  };
}

async function generateGaugeData(supabase: any, userId: string, queryConfig: any): Promise<any> {
  // Generate gauge data (similar to KPI but with ranges)
  const kpiData = await generateKPIData(supabase, userId, queryConfig);
  
  return {
    value: kpiData.value,
    min: queryConfig.minValue || 0,
    max: queryConfig.maxValue || 100,
    ranges: queryConfig.ranges || [
      { min: 0, max: 30, color: '#EF4444', label: 'Low' },
      { min: 30, max: 70, color: '#F59E0B', label: 'Medium' },
      { min: 70, max: 100, color: '#10B981', label: 'High' }
    ],
    unit: queryConfig.unit || ''
  };
}

function getDateColumn(dataSource: string): string {
  const dateColumns = {
    'animals': 'created_at',
    'vitals': 'recorded_at',
    'feeding_schedules': 'created_at',
    'medications': 'created_at',
    'vaccinations': 'administered_date',
    'environmental_data': 'recorded_at',
    'training_sessions': 'session_date'
  };
  
  return dateColumns[dataSource] || 'created_at';
}

function groupDataByTime(data: any[], groupBy: string, valueField: string, dateField: string): any[] {
  const groups = new Map();
  
  data.forEach(item => {
    const date = new Date(item[dateField]);
    let key;
    
    switch (groupBy) {
      case 'hour':
        key = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`;
        break;
      case 'day':
        key = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
        break;
      case 'week':
        const weekStart = new Date(date.getTime() - date.getDay() * 24 * 60 * 60 * 1000);
        key = `${weekStart.getFullYear()}-${weekStart.getMonth()}-${weekStart.getDate()}`;
        break;
      case 'month':
        key = `${date.getFullYear()}-${date.getMonth()}`;
        break;
      default:
        key = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
    }
    
    if (!groups.has(key)) {
      groups.set(key, { values: [], date: date });
    }
    
    const value = parseFloat(item[valueField]);
    if (!isNaN(value)) {
      groups.get(key).values.push(value);
    }
  });
  
  return Array.from(groups.entries()).map(([key, group]) => {
    const values = group.values;
    const avgValue = values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
    
    return {
      label: formatDateLabel(group.date, groupBy),
      value: avgValue,
      metadata: {
        count: values.length,
        min: Math.min(...values),
        max: Math.max(...values)
      }
    };
  }).sort((a, b) => a.label.localeCompare(b.label));
}

function groupDataByField(data: any[], groupByField: string, valueField: string): any[] {
  const groups = new Map();
  
  data.forEach(item => {
    const key = item[groupByField] || 'Unknown';
    
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    
    if (valueField === 'count') {
      groups.get(key).push(1);
    } else {
      const value = parseFloat(item[valueField]);
      if (!isNaN(value)) {
        groups.get(key).push(value);
      }
    }
  });
  
  return Array.from(groups.entries()).map(([key, values]) => ({
    label: key,
    value: valueField === 'count' ? values.length : values.reduce((a, b) => a + b, 0) / values.length
  }));
}

function formatDateLabel(date: Date, groupBy: string): string {
  switch (groupBy) {
    case 'hour':
      return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:00`;
    case 'day':
      return `${date.getMonth() + 1}/${date.getDate()}`;
    case 'week':
      return `Week of ${date.getMonth() + 1}/${date.getDate()}`;
    case 'month':
      return `${date.getFullYear()}-${date.getMonth() + 1}`;
    default:
      return `${date.getMonth() + 1}/${date.getDate()}`;
  }
}

function generateColors(count: number): string[] {
  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
  ];
  
  const result = [];
  for (let i = 0; i < count; i++) {
    result.push(colors[i % colors.length]);
  }
  
  return result;
}

function calculateTrend(data: any[], queryConfig: any): any {
  if (!data || data.length < 2) {
    return { direction: 'stable', percentage: 0 };
  }
  
  // Simple trend calculation (would be more sophisticated in production)
  const recent = data.slice(-Math.ceil(data.length / 2));
  const older = data.slice(0, Math.floor(data.length / 2));
  
  const recentAvg = recent.reduce((sum, item) => sum + (parseFloat(item[queryConfig.valueField]) || 0), 0) / recent.length;
  const olderAvg = older.reduce((sum, item) => sum + (parseFloat(item[queryConfig.valueField]) || 0), 0) / older.length;
  
  const change = ((recentAvg - olderAvg) / olderAvg) * 100;
  
  return {
    direction: change > 5 ? 'increasing' : change < -5 ? 'decreasing' : 'stable',
    percentage: Math.abs(change)
  };
}

async function calculateComparison(supabase: any, userId: string, queryConfig: any): Promise<any> {
  // Calculate comparison with previous period or benchmark
  return {
    previousPeriod: {
      value: 0,
      change: 0
    },
    benchmark: {
      value: 0,
      percentile: 50
    }
  };
}

function calculateCacheHitRate(widgetData: WidgetData[]): number {
  if (widgetData.length === 0) return 0;
  
  const cacheHits = widgetData.filter(widget => widget.cacheHit).length;
  return (cacheHits / widgetData.length) * 100;
}

async function recordDashboardAnalytics(supabase: any, dashboardId: string | undefined, userId: string, widgetData: WidgetData[], totalTime: number): Promise<void> {
  if (!dashboardId) return;
  
  try {
    await supabase
      .from('dashboard_analytics')
      .insert({
        dashboard_id: dashboardId,
        user_id: userId,
        load_time_ms: totalTime,
        query_count: widgetData.length,
        total_query_time_ms: widgetData.reduce((sum, widget) => sum + widget.executionTime, 0),
        cache_hit_rate: calculateCacheHitRate(widgetData) / 100,
        widgets_viewed: widgetData.map(widget => widget.widgetId)
      });
  } catch (error) {
    console.error('Error recording dashboard analytics:', error);
  }
}

async function generateDashboardInsights(supabase: any, userId: string, widgetData: WidgetData[], timeRange?: string): Promise<any[]> {
  const insights = [];
  
  // Performance insights
  const avgExecutionTime = widgetData.reduce((sum, widget) => sum + widget.executionTime, 0) / widgetData.length;
  if (avgExecutionTime > 2000) {
    insights.push({
      type: 'performance',
      title: 'Slow Dashboard Performance',
      description: `Dashboard is loading slowly (${avgExecutionTime.toFixed(0)}ms average). Consider optimizing queries or reducing data range.`,
      priority: 'medium'
    });
  }
  
  // Data insights
  const kpiWidgets = widgetData.filter(widget => widget.widgetType === 'kpi');
  kpiWidgets.forEach(widget => {
    if (widget.data.trend && widget.data.trend.direction === 'decreasing' && widget.data.trend.percentage > 10) {
      insights.push({
        type: 'trend',
        title: `Declining ${widget.metadata.title}`,
        description: `${widget.metadata.title} has decreased by ${widget.data.trend.percentage.toFixed(1)}% in the selected period.`,
        priority: 'high'
      });
    }
  });
  
  return insights;
}