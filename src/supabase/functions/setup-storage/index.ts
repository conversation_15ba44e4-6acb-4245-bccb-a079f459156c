// @verify_jwt: false
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// Function ID: setup-storage-bucket
serve(async (req) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  };

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create admin client with service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log('Setting up profile-images storage bucket...');

    // Create the profile-images bucket
    const { data: bucketData, error: bucketError } = await supabaseAdmin.storage
      .createBucket('profile-images', {
        public: true,
        fileSizeLimit: 5242880, // 5MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      });

    if (bucketError && !bucketError.message.includes('already exists')) {
      console.error('Error creating bucket:', bucketError);
      throw bucketError;
    }

    console.log('Bucket created or already exists:', bucketData);

    // Enable RLS on storage.objects if not already enabled
    try {
      await supabaseAdmin.rpc('exec_sql', {
        sql: 'ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;'
      });
      console.log('RLS enabled on storage.objects');
    } catch (error) {
      console.log('RLS already enabled or error:', error.message);
    }

    // Set up RLS policies using raw SQL
    const policies = [
      {
        name: 'Public read access for profile images',
        sql: `
          DROP POLICY IF EXISTS "Public read access for profile images" ON storage.objects;
          CREATE POLICY "Public read access for profile images"
          ON storage.objects FOR SELECT
          USING (bucket_id = 'profile-images');
        `
      },
      {
        name: 'Authenticated users can upload profile images',
        sql: `
          DROP POLICY IF EXISTS "Authenticated users can upload profile images" ON storage.objects;
          CREATE POLICY "Authenticated users can upload profile images"
          ON storage.objects FOR INSERT
          TO authenticated
          WITH CHECK (
            bucket_id = 'profile-images' 
            AND auth.uid() = owner
            AND (storage.foldername(name))[1] = auth.uid()::text
          );
        `
      },
      {
        name: 'Authenticated users can update their own profile images',
        sql: `
          DROP POLICY IF EXISTS "Authenticated users can update their own profile images" ON storage.objects;
          CREATE POLICY "Authenticated users can update their own profile images"
          ON storage.objects FOR UPDATE
          TO authenticated
          USING (
            bucket_id = 'profile-images' 
            AND auth.uid() = owner
            AND (storage.foldername(name))[1] = auth.uid()::text
          )
          WITH CHECK (
            bucket_id = 'profile-images' 
            AND auth.uid() = owner
            AND (storage.foldername(name))[1] = auth.uid()::text
          );
        `
      },
      {
        name: 'Authenticated users can delete their own profile images',
        sql: `
          DROP POLICY IF EXISTS "Authenticated users can delete their own profile images" ON storage.objects;
          CREATE POLICY "Authenticated users can delete their own profile images"
          ON storage.objects FOR DELETE
          TO authenticated
          USING (
            bucket_id = 'profile-images' 
            AND auth.uid() = owner
            AND (storage.foldername(name))[1] = auth.uid()::text
          );
        `
      }
    ];

    const policyResults = [];
    for (const policy of policies) {
      try {
        const { error: policyError } = await supabaseAdmin.rpc('exec_sql', {
          sql: policy.sql
        });
        
        if (policyError) {
          console.error(`Error creating policy ${policy.name}:`, policyError);
          policyResults.push({ name: policy.name, success: false, error: policyError.message });
        } else {
          console.log(`Policy created successfully: ${policy.name}`);
          policyResults.push({ name: policy.name, success: true });
        }
      } catch (error) {
        console.error(`Error with policy ${policy.name}:`, error);
        policyResults.push({ name: policy.name, success: false, error: error.message });
      }
    }

    // Verify bucket exists
    const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets();
    if (listError) {
      throw listError;
    }

    const profileImagesBucket = buckets.find(b => b.id === 'profile-images');
    
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Storage setup completed successfully',
        bucket: profileImagesBucket,
        policies: policyResults,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );

  } catch (error) {
    console.error('Error in setup-storage function:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});