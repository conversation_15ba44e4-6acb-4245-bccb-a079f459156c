// @verify_jwt: true
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

interface DiseaseRiskRequest {
  animal_id: string;
  analysis_days?: number; // Days of data to analyze (default: 7)
}

interface DiseaseRiskResponse {
  success: boolean;
  risk_assessments?: any[];
  error?: string;
}

serve(async (req) => {
  try {
    // CORS headers
    if (req.method === 'OPTIONS') {
      return new Response('ok', {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
          'Access-Control-Allow-Methods': 'POST, OPTIONS'
        }
      });
    }

    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        { status: 405, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user from JWT
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ success: false, error: 'Unauthorized' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Parse request body
    const { animal_id, analysis_days = 7 }: DiseaseRiskRequest = await req.json();
    
    if (!animal_id) {
      return new Response(
        JSON.stringify({ success: false, error: 'Animal ID is required' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Verify animal ownership
    const { data: animal, error: animalError } = await supabase
      .from('animals')
      .select('id, name, species, breed, age')
      .eq('id', animal_id)
      .eq('user_id', user.id)
      .single();

    if (animalError || !animal) {
      return new Response(
        JSON.stringify({ success: false, error: 'Animal not found or access denied' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Analyzing disease risk for animal ${animal_id} over ${analysis_days} days`);

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - analysis_days);

    // Fetch comprehensive data for analysis
    const [vitalsData, feedingData, medicationData, dehydrationData] = await Promise.all([
      // Vitals data
      supabase
        .from('vitals')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('recorded_at', startDate.toISOString())
        .order('recorded_at', { ascending: false }),
      
      // Feeding data
      supabase
        .from('feeding_schedules')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('created_at', startDate.toISOString()),
      
      // Medication data
      supabase
        .from('medications')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('created_at', startDate.toISOString()),
      
      // Dehydration data
      supabase
        .from('dehydration_logs')
        .select('*')
        .eq('animal_id', animal_id)
        .gte('recorded_at', startDate.toISOString())
    ]);

    // Perform disease risk analysis
    const riskAssessments = await analyzeMultipleDiseaseRisks({
      animal,
      vitals: vitalsData.data || [],
      feeding: feedingData.data || [],
      medications: medicationData.data || [],
      dehydration: dehydrationData.data || [],
      analysisDays: analysis_days
    });

    // Save risk assessments to database
    const savedAssessments = [];
    for (const assessment of riskAssessments) {
      const { data, error } = await supabase
        .from('disease_risk_assessments')
        .insert({
          animal_id,
          user_id: user.id,
          disease_category: assessment.disease_category,
          risk_level: assessment.risk_level,
          risk_score: assessment.risk_score,
          contributing_factors: assessment.contributing_factors,
          recommendations: assessment.recommendations,
          confidence_level: assessment.confidence_level
        })
        .select()
        .single();
      
      if (!error && data) {
        savedAssessments.push(data);
      }
    }

    console.log(`Disease risk analysis completed. Found ${riskAssessments.length} risk categories`);

    const response: DiseaseRiskResponse = {
      success: true,
      risk_assessments: savedAssessments
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    );

  } catch (error) {
    console.error('Error in analyze-disease-risk:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { 
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        } 
      }
    );
  }
});

// Disease risk analysis functions
async function analyzeMultipleDiseaseRisks(data: {
  animal: any;
  vitals: any[];
  feeding: any[];
  medications: any[];
  dehydration: any[];
  analysisDays: number;
}) {
  const assessments = [];
  
  // Cardiovascular disease risk
  const cardioRisk = analyzeCardiovascularRisk(data);
  if (cardioRisk) assessments.push(cardioRisk);
  
  // Metabolic disease risk
  const metabolicRisk = analyzeMetabolicRisk(data);
  if (metabolicRisk) assessments.push(metabolicRisk);
  
  // Infectious disease risk
  const infectiousRisk = analyzeInfectiousRisk(data);
  if (infectiousRisk) assessments.push(infectiousRisk);
  
  // Dehydration risk
  const dehydrationRisk = analyzeDehydrationRisk(data);
  if (dehydrationRisk) assessments.push(dehydrationRisk);
  
  // Nutritional deficiency risk
  const nutritionalRisk = analyzeNutritionalRisk(data);
  if (nutritionalRisk) assessments.push(nutritionalRisk);
  
  return assessments;
}

function analyzeCardiovascularRisk(data: any) {
  const { vitals, animal } = data;
  
  if (vitals.length === 0) return null;
  
  let riskScore = 0;
  let riskFactors = [];
  const contributingFactors: any = {};
  
  // Analyze heart rate patterns
  const heartRates = vitals.map(v => v.heart_rate).filter(hr => hr);
  if (heartRates.length > 0) {
    const avgHeartRate = heartRates.reduce((sum, hr) => sum + hr, 0) / heartRates.length;
    const maxHeartRate = Math.max(...heartRates);
    const minHeartRate = Math.min(...heartRates);
    
    // Age-adjusted normal ranges (simplified)
    const normalRange = animal.age > 5 ? [60, 100] : [70, 120];
    
    if (avgHeartRate > normalRange[1] * 1.2) {
      riskScore += 30;
      riskFactors.push('Consistently elevated heart rate');
      contributingFactors.elevated_heart_rate = true;
    } else if (avgHeartRate > normalRange[1]) {
      riskScore += 15;
      riskFactors.push('Moderately elevated heart rate');
    }
    
    if (avgHeartRate < normalRange[0] * 0.8) {
      riskScore += 20;
      riskFactors.push('Unusually low heart rate');
      contributingFactors.low_heart_rate = true;
    }
    
    // Heart rate variability
    const hrVariability = maxHeartRate - minHeartRate;
    if (hrVariability > 50) {
      riskScore += 10;
      riskFactors.push('High heart rate variability');
    }
    
    contributingFactors.avg_heart_rate = avgHeartRate;
    contributingFactors.hr_variability = hrVariability;
  }
  
  // Determine risk level
  let riskLevel: string;
  if (riskScore >= 40) riskLevel = 'critical';
  else if (riskScore >= 25) riskLevel = 'high';
  else if (riskScore >= 10) riskLevel = 'medium';
  else riskLevel = 'low';
  
  const recommendations = generateCardiovascularRecommendations(riskLevel, riskFactors);
  
  return {
    disease_category: 'cardiovascular',
    risk_level: riskLevel,
    risk_score: Math.min(riskScore, 100),
    contributing_factors: contributingFactors,
    recommendations,
    confidence_level: heartRates.length >= 5 ? 0.8 : 0.6
  };
}

function analyzeMetabolicRisk(data: any) {
  const { vitals, feeding, animal } = data;
  
  let riskScore = 0;
  let riskFactors = [];
  const contributingFactors: any = {};
  
  // Weight analysis
  const weights = vitals.map(v => v.weight).filter(w => w);
  if (weights.length >= 2) {
    const weightChange = ((weights[0] - weights[weights.length - 1]) / weights[weights.length - 1]) * 100;
    
    if (Math.abs(weightChange) > 10) {
      riskScore += 25;
      riskFactors.push(`Significant weight change: ${weightChange.toFixed(1)}%`);
      contributingFactors.weight_change = weightChange;
    } else if (Math.abs(weightChange) > 5) {
      riskScore += 10;
      riskFactors.push(`Moderate weight change: ${weightChange.toFixed(1)}%`);
    }
  }
  
  // Feeding pattern analysis
  if (feeding.length > 0) {
    const feedingFrequency = feeding.length / data.analysisDays;
    if (feedingFrequency < 1) {
      riskScore += 20;
      riskFactors.push('Irregular feeding patterns');
      contributingFactors.irregular_feeding = true;
    }
    
    contributingFactors.feeding_frequency = feedingFrequency;
  } else {
    riskScore += 15;
    riskFactors.push('No feeding data recorded');
  }
  
  // Temperature analysis (metabolic indicator)
  const temperatures = vitals.map(v => v.temperature).filter(t => t);
  if (temperatures.length > 0) {
    const avgTemp = temperatures.reduce((sum, t) => sum + t, 0) / temperatures.length;
    if (avgTemp > 39.5 || avgTemp < 37.5) {
      riskScore += 15;
      riskFactors.push('Abnormal body temperature patterns');
      contributingFactors.abnormal_temperature = true;
    }
    contributingFactors.avg_temperature = avgTemp;
  }
  
  let riskLevel: string;
  if (riskScore >= 35) riskLevel = 'critical';
  else if (riskScore >= 20) riskLevel = 'high';
  else if (riskScore >= 10) riskLevel = 'medium';
  else riskLevel = 'low';
  
  const recommendations = generateMetabolicRecommendations(riskLevel, riskFactors);
  
  return {
    disease_category: 'metabolic',
    risk_level: riskLevel,
    risk_score: Math.min(riskScore, 100),
    contributing_factors: contributingFactors,
    recommendations,
    confidence_level: (weights.length >= 2 && temperatures.length >= 3) ? 0.8 : 0.6
  };
}

function analyzeInfectiousRisk(data: any) {
  const { vitals, medications } = data;
  
  let riskScore = 0;
  let riskFactors = [];
  const contributingFactors: any = {};
  
  // Temperature analysis for infection
  const temperatures = vitals.map(v => v.temperature).filter(t => t);
  if (temperatures.length > 0) {
    const highTempCount = temperatures.filter(t => t > 39.5).length;
    const feverPercentage = (highTempCount / temperatures.length) * 100;
    
    if (feverPercentage > 50) {
      riskScore += 40;
      riskFactors.push('Persistent fever patterns');
      contributingFactors.persistent_fever = true;
    } else if (feverPercentage > 25) {
      riskScore += 20;
      riskFactors.push('Intermittent fever');
    }
    
    contributingFactors.fever_percentage = feverPercentage;
  }
  
  // Medication analysis (antibiotics indicate infection treatment)
  const antibiotics = medications.filter(med => 
    med.medication_name?.toLowerCase().includes('antibiotic') ||
    med.medication_name?.toLowerCase().includes('amoxicillin') ||
    med.medication_name?.toLowerCase().includes('penicillin')
  );
  
  if (antibiotics.length > 0) {
    riskScore += 15;
    riskFactors.push('Currently on antibiotic treatment');
    contributingFactors.on_antibiotics = true;
  }
  
  // Heart rate elevation (can indicate infection)
  const heartRates = vitals.map(v => v.heart_rate).filter(hr => hr);
  if (heartRates.length > 0) {
    const elevatedHRCount = heartRates.filter(hr => hr > 120).length;
    if (elevatedHRCount > heartRates.length * 0.3) {
      riskScore += 10;
      riskFactors.push('Elevated heart rate patterns');
    }
  }
  
  let riskLevel: string;
  if (riskScore >= 40) riskLevel = 'critical';
  else if (riskScore >= 25) riskLevel = 'high';
  else if (riskScore >= 10) riskLevel = 'medium';
  else riskLevel = 'low';
  
  const recommendations = generateInfectiousRecommendations(riskLevel, riskFactors);
  
  return {
    disease_category: 'infectious',
    risk_level: riskLevel,
    risk_score: Math.min(riskScore, 100),
    contributing_factors: contributingFactors,
    recommendations,
    confidence_level: temperatures.length >= 3 ? 0.7 : 0.5
  };
}

function analyzeDehydrationRisk(data: any) {
  const { dehydration, vitals } = data;
  
  let riskScore = 0;
  let riskFactors = [];
  const contributingFactors: any = {};
  
  // Direct dehydration readings
  if (dehydration.length > 0) {
    const latestReading = dehydration[0];
    if (latestReading.hydration_level < 70) {
      riskScore += 30;
      riskFactors.push('Low hydration levels detected');
      contributingFactors.low_hydration = true;
    }
    contributingFactors.latest_hydration = latestReading.hydration_level;
  }
  
  // Temperature as dehydration indicator
  const temperatures = vitals.map(v => v.temperature).filter(t => t);
  if (temperatures.length > 0) {
    const highTempCount = temperatures.filter(t => t > 39.0).length;
    if (highTempCount > temperatures.length * 0.5) {
      riskScore += 15;
      riskFactors.push('Elevated temperature increasing dehydration risk');
    }
  }
  
  let riskLevel: string;
  if (riskScore >= 30) riskLevel = 'critical';
  else if (riskScore >= 20) riskLevel = 'high';
  else if (riskScore >= 10) riskLevel = 'medium';
  else riskLevel = 'low';
  
  const recommendations = generateDehydrationRecommendations(riskLevel, riskFactors);
  
  return {
    disease_category: 'dehydration',
    risk_level: riskLevel,
    risk_score: Math.min(riskScore, 100),
    contributing_factors: contributingFactors,
    recommendations,
    confidence_level: dehydration.length > 0 ? 0.8 : 0.4
  };
}

function analyzeNutritionalRisk(data: any) {
  const { feeding, vitals } = data;
  
  let riskScore = 0;
  let riskFactors = [];
  const contributingFactors: any = {};
  
  // Feeding frequency analysis
  const feedingFrequency = feeding.length / data.analysisDays;
  if (feedingFrequency < 1) {
    riskScore += 25;
    riskFactors.push('Insufficient feeding frequency');
    contributingFactors.low_feeding_frequency = true;
  } else if (feedingFrequency < 2) {
    riskScore += 10;
    riskFactors.push('Below optimal feeding frequency');
  }
  
  // Weight loss analysis
  const weights = vitals.map(v => v.weight).filter(w => w);
  if (weights.length >= 2) {
    const weightLoss = weights[weights.length - 1] - weights[0];
    if (weightLoss > weights[0] * 0.05) { // More than 5% weight loss
      riskScore += 20;
      riskFactors.push('Significant weight loss detected');
      contributingFactors.weight_loss = true;
    }
  }
  
  contributingFactors.feeding_frequency = feedingFrequency;
  
  let riskLevel: string;
  if (riskScore >= 30) riskLevel = 'critical';
  else if (riskScore >= 20) riskLevel = 'high';
  else if (riskScore >= 10) riskLevel = 'medium';
  else riskLevel = 'low';
  
  const recommendations = generateNutritionalRecommendations(riskLevel, riskFactors);
  
  return {
    disease_category: 'nutritional',
    risk_level: riskLevel,
    risk_score: Math.min(riskScore, 100),
    contributing_factors: contributingFactors,
    recommendations,
    confidence_level: feeding.length > 0 ? 0.7 : 0.3
  };
}

// Recommendation generators
function generateCardiovascularRecommendations(riskLevel: string, factors: string[]) {
  const recommendations = [];
  
  if (riskLevel === 'critical' || riskLevel === 'high') {
    recommendations.push('Immediate veterinary consultation recommended');
    recommendations.push('Monitor heart rate every 2-4 hours');
    recommendations.push('Restrict strenuous activity');
  } else if (riskLevel === 'medium') {
    recommendations.push('Schedule veterinary check-up within 1-2 weeks');
    recommendations.push('Monitor heart rate daily');
    recommendations.push('Moderate exercise only');
  } else {
    recommendations.push('Continue regular monitoring');
    recommendations.push('Maintain normal activity levels');
  }
  
  return recommendations.join('; ');
}

function generateMetabolicRecommendations(riskLevel: string, factors: string[]) {
  const recommendations = [];
  
  if (riskLevel === 'critical' || riskLevel === 'high') {
    recommendations.push('Veterinary consultation for metabolic panel');
    recommendations.push('Establish consistent feeding schedule');
    recommendations.push('Monitor weight daily');
  } else if (riskLevel === 'medium') {
    recommendations.push('Review feeding schedule and portions');
    recommendations.push('Monitor weight weekly');
  } else {
    recommendations.push('Maintain current feeding routine');
    recommendations.push('Continue regular weight monitoring');
  }
  
  return recommendations.join('; ');
}

function generateInfectiousRecommendations(riskLevel: string, factors: string[]) {
  const recommendations = [];
  
  if (riskLevel === 'critical' || riskLevel === 'high') {
    recommendations.push('Immediate veterinary attention required');
    recommendations.push('Monitor temperature every 4 hours');
    recommendations.push('Isolate from other animals if applicable');
  } else if (riskLevel === 'medium') {
    recommendations.push('Schedule veterinary check-up');
    recommendations.push('Monitor temperature twice daily');
  } else {
    recommendations.push('Continue routine health monitoring');
  }
  
  return recommendations.join('; ');
}

function generateDehydrationRecommendations(riskLevel: string, factors: string[]) {
  const recommendations = [];
  
  if (riskLevel === 'critical' || riskLevel === 'high') {
    recommendations.push('Immediate hydration intervention needed');
    recommendations.push('Veterinary consultation for IV fluids');
    recommendations.push('Monitor hydration every 2 hours');
  } else if (riskLevel === 'medium') {
    recommendations.push('Increase water availability');
    recommendations.push('Monitor hydration levels closely');
  } else {
    recommendations.push('Maintain adequate water access');
    recommendations.push('Continue regular hydration monitoring');
  }
  
  return recommendations.join('; ');
}

function generateNutritionalRecommendations(riskLevel: string, factors: string[]) {
  const recommendations = [];
  
  if (riskLevel === 'critical' || riskLevel === 'high') {
    recommendations.push('Veterinary nutritional consultation');
    recommendations.push('Implement structured feeding plan');
    recommendations.push('Consider nutritional supplements');
  } else if (riskLevel === 'medium') {
    recommendations.push('Review and optimize feeding schedule');
    recommendations.push('Monitor food intake closely');
  } else {
    recommendations.push('Maintain current nutritional routine');
  }
  
  return recommendations.join('; ');
}