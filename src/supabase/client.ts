
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Initialize Supabase client
const supabaseUrl = 'https://hfqhqymuenbuzndkdcqf.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhmcWhxeW11ZW5idXpuZGtkY3FmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNDkxMTYsImV4cCI6MjA2MjgyNTExNn0.do6iCaniC596HejLA54i36fCdtH709fuEa9hZ5si09I';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  global: {
    headers: {
      'X-Client-Info': 'magically-app',
    },
  },
  // Fix CORS issues in web preview
  ...(Platform.OS === 'web' && {
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  }),
});

// Helper function to log API requests and responses
export const logApiCall = (action: string, data: any, error: any) => {
  if (error) {
    console.error(`API Error (${action}):`, error);
  } else {
    console.log(`API Success (${action}):`, data);
  }
  return { data, error };
};
