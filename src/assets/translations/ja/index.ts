// Modular Japanese translations - combines all feature-specific translation files
import { common } from './common';
import { navigation } from './navigation';
import { auth } from './auth';
import { animals } from './animals';
import { ai } from './ai';

// Combine all translation modules into a single Japanese translation object
import { devices } from './devices';

export const ja = {
  ...devices,
  ...common,
  ...navigation,
  ...auth,
  ...animals,
  
  // Nested objects for dot notation access
  common,
  ai,
  
  // AI Widget keys (at root level for flat access)
  aiWidgetInsights: 'AIインサイト',
  aiWidgetHealth: '健康状態',
  aiWidgetLiveCheckIn: 'ライブチェックイン',
  aiWidgetViewAll: 'すべて表示',
  aiWidgetNew: '新しい',
  aiWidgetLogTraining: 'トレーニング記録',
  aiWidgetNoAIData: 'AIデータなし',
  aiWidgetReadiness: '準備状態',
  aiWidgetLiveStatus: 'ライブステータス',
  aiWidgetAvailable: '利用可能',
  aiWidgetLatestTip: '最新のヒント',
  aiWidgetChecking: 'チェック中...',
  
  // Additional translations for backward compatibility
  language: '言語',
  languageSettings: '言語と地域',
  selectYourLanguage: '言語を選択してください',
  languageChanged: '言語が正常に変更されました',
  selectLanguage: '言語を選択',
  changingLanguage: '言語を変更中...',
  
  // Settings Categories
  dataManagement: 'データ管理',
  notifications: '通知',
  themeSettings: 'テーマ設定',
  darkMode: 'ダークモード',
  syncSettings: '同期設定',
  healthAlerts: '健康アラート',
  reminders: 'リマインダー',
  
  // Health Alert Settings
  heartRateAlerts: '心拍数アラート',
  heartRateRange: '心拍数範囲',
  minBpm: '最小 (bpm)',
  maxBpm: '最大 (bpm)',
  temperatureAlerts: '体温アラート',
  temperatureRange: '体温範囲',
  minCelsius: '最小 (℃)',
  maxCelsius: '最大 (℃)',
  locationAlerts: '位置アラート',
  locationUpdateInterval: '位置更新間隔',
  locationUpdateLabel: '更新がない場合のアラート時間 (時間):',
  
  // Reminder Settings
  reminderTimeBefore: 'リマインダー時間 (分):',
  saveSettings: '設定を保存',
  settingsSavedSuccess: '設定が正常に保存されました',
  errorFixBeforeSaving: '保存前にエラーを修正してください',
  errorMaxHeartRateHigher: '最大心拍数は最小心拍数より大きい必要があります',
  errorMaxTemperatureHigher: '最大体温は最小体温より高い必要があります',
  
  // Monitoring Settings
  heartRateLimits: '心拍数制限',
  locationTracking: '位置追跡',
  
  // Reminder Settings
  feedingReminders: '給餌リマインダー',
  medicationReminders: '薬のリマインダー',
  vaccinationReminders: 'ワクチンリマインダー',
  
  // Device Management
  scanForDevices: 'デバイスをスキャン',
  scanning: 'スキャン中...',
  devicesFound: 'デバイスが見つかりました',
  noDevicesFound: 'デバイスが見つかりません',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'あなたの牧場パートナー',
  animalSpeedTitle: '{{animalName}}の速度',
  speedUpdatedSuccess: '{{animalName}}の速度が更新されました',
  speedUpdateFailed: '速度の更新に失敗しました',
  
  // Quick Stats
  quickOverview: 'クイック概要',
  activeDevices: 'アクティブデバイス',
  todaysFeedings: '今日の給餌',
  medications: '薬',
  
  // Upcoming Tasks
  upcomingTasks: '今後のタスク',
  noTasksToday: '今日はスケジュールされたタスクはありません',
  allCaughtUp: 'すべて最新です！あなたの動物たちはよく世話されています。',
  feedingTask: '給餌: {{feedType}}',
  medicationTask: '薬: {{medicationName}}',
  amount: '量',
  dosage: '投与量',
  moreTasksToday: '今日はあと{{count}}個のタスク',
  
  // Premium Banner
  premium: 'プレミアム',
  upgradeToPremium: 'プレミアムにアップグレード',
  unlimitedAnimals: '無制限の動物',
  advancedAnalytics: '高度な分析',
  upgradeNow: '今すぐアップグレード',
  
  // Speed Monitor
  speed: '速度',
  currentSpeed: '現在の速度',
  lastUpdated: '最終更新',
  
  // Health and vitals
  health: '健康',
  vitals: 'バイタル',
  temperature: '体温',
  heartRate: '心拍数',
  respirationRate: '呼吸数',
  bloodPressure: '血圧',
  
  // Additional keys for backward compatibility
  behavioralAnalysis: '行動分析',
  environmentalAnalysisDescription: '環境が健康に与える影響を分析',
  environmentalImpact: '環境影響',
  viewAll: 'すべて表示',
  
  // Missing keys from en/common.ts - Japanese translations
  // Health Dashboard
  healthDashboard: 'ヘルスダッシュボード',
  overallHealthStatus: '全体的な健康状態',
  criticalAlerts: '重要なアラート',
  recentActivity: '最近のアクティビティ',
  healthInsights: '健康インサイト',
  quickActions: 'クイックアクション',
  viewHealthScore: 'ヘルススコアを表示',
  analyzeRisks: 'リスクを分析',
  viewTrends: 'トレンドを表示',
  stressAnalysis: 'ストレス分析',
  sleepMonitoring: '睡眠モニタリング',
  environmentalAnalysis: '環境分析',
  predictiveInsights: '予測インサイト',
  noAnimalsSelected: '動物が選択されていません',
  selectAnimalToViewHealth: '健康ダッシュボードを表示する動物を選択してください',
  healthDataLoading: '健康データを読み込み中...',
  healthDataError: '健康データの読み込みエラー',
  retryLoadingHealthData: '健康データの再読み込みを試行',
  
  // FAQ
  frequentlyAskedQuestions: 'よくある質問',
  howToAddAnimal: '新しい動物を追加するには？',
  howToAddAnimalAnswer: '新しい動物を追加するには、動物タブに移動して「+」ボタンをタップします。名前、品種、年齢、その他の詳細を含む動物の情報を入力してください。',
  howToConnectDevice: 'モニタリングデバイスを接続するには？',
  howToConnectDeviceAnswer: 'デバイスタブに移動し、「デバイスを追加」をタップして、ペアリング手順に従ってください。携帯電話でBluetoothが有効になっていることを確認してください。',
  whatIsHealthScore: 'ヘルススコアとは何ですか？',
  whatIsHealthScoreAnswer: 'ヘルススコアは、バイタルサイン、活動レベル、その他の監視データに基づいて、動物の全体的な健康状態をAIが生成した評価です。',
  howToSetupAlerts: '健康アラートを設定するには？',
  howToSetupAlertsAnswer: '設定 > 通知に移動して、動物の健康状態に関するアラートをいつ、どのように受信するかを設定してください。',
  whatDataIsTracked: 'アプリはどのようなデータを追跡しますか？',
  whatDataIsTrackedAnswer: 'アプリはバイタルサイン（心拍数、体温）、活動レベル、位置、給餌スケジュール、薬物、環境要因を追跡します。',
  howToExportData: '動物のデータをエクスポートするには？',
  howToExportDataAnswer: '動物のプロフィールに移動し、メニューボタンをタップして「データをエクスポート」を選択してください。エクスポートする日付範囲とデータタイプを選択できます。',
  isPremiumWorthIt: 'Premiumサブスクリプションは価値がありますか？',
  isPremiumWorthItAnswer: 'Premiumは無制限の動物、高度なAIインサイト、詳細な健康レポート、優先サポートを提供します。プロフェッショナル使用や複数の動物に最適です。',
  howToContactSupport: 'サポートに連絡するには？',
  howToContactSupportAnswer: 'プロフィールの連絡先タブからサポートに連絡するか、********************に直接メールを送信できます。',
  
  // Contact
  contactUs: 'お問い合わせ',
  messageSent: 'メッセージが正常に送信されました',
  messageSentTitle: 'メッセージ送信完了',
  messageSentDescription: 'あなたのメッセージは********************に送信されました。すぐにご返信いたします！',
  failedToSendMessage: 'メッセージの送信に失敗しました。もう一度お試しください。',
  errorSendingMessageTitle: 'メッセージ送信エラー',
  errorSendingMessageDescription: 'メッセージの送信中に問題が発生しました。もう一度お試しいただくか、ライブチャットオプションをご利用ください。',
  chatError: 'チャットエラー',
  failedToOpenChat: 'チャットを開けませんでした。もう一度お試しください。',
  openingChatSupport: 'チャットサポートを開いています...',
  
  // Print
  print: '印刷',
  printed: '印刷済み',
  download: 'ダウンロード',
  downloaded: 'ダウンロード済み',
  shared: '共有済み',
  animalNotFound: '動物が見つかりません',
  goBack: '戻る',
  
  // Barcode Scanner
  cameraPermissionRequired: 'バーコードをスキャンするにはカメラの許可が必要です',
  invalidBarcodeFormat: '無効なデバイスバーコード形式',
  deviceBarcodeScanned: 'デバイスバーコードがスキャンされました',
  requestingCameraPermission: 'カメラの許可を要求中...',
  noAccessToCamera: 'カメラにアクセスできません',
  scanDeviceBarcode: 'デバイスバーコードをスキャン',
  positionBarcodeInFrame: 'スキャンするためにバーコードをフレーム内に配置してください',
  scanAgain: '再スキャン',
  
  // Privacy Settings
  privacySettings: 'プライバシー設定',
  privacySettingsSaved: 'プライバシー設定が保存されました',
  accountDeleted: 'アカウントが削除されました',
  locationTracking: '位置追跡',
  locationTrackingDescription: 'アプリが動物の位置を追跡することを許可します。これは位置アラートに必要です。',
  shareDataWithVets: '獣医師とのデータ共有',
  shareDataWithVetsDescription: '共有時に獣医師が動物の健康記録にアクセスすることを許可します。',
  analytics: 'アナリティクス',
  analyticsDescription: '匿名の使用データ収集を許可して改善にご協力ください。',
  saveSettings: '設定を保存',
  
  // Alert Types
  heartRateAlert: '心拍数アラート',
  temperatureAlert: '体温アラート',
  locationAlert: '位置アラート',
  healthAlert: '健康アラート',
  
  // Health Status
  score: 'スコア',
  alerts: 'アラート',
  trends: 'トレンド',
  tracked: '追跡済み',
  criticalAlertsRequireAttention: '重要なアラートが注意を必要としています',
  lastUpdated: '最終更新',
  excellent: '優秀',
  good: '良好',
  fair: '普通',
  poor: '不良',
  critical: '重要',
  aiAnalysis: 'AI分析',
  active: 'アクティブ',
  seeAll: 'すべて表示',
  noAnimalsYet: 'まだ動物がいません',
  addFirstAnimal: '最初の動物を追加',
  
  // Missing AI & Health Analysis Keys (Japanese translations)
  diseaseRiskAssessment: '病気リスク評価',
  healthTrends: '健康トレンド',
  stressAnalysis: 'ストレス分析',
  sleepMonitoring: '睡眠モニタリング',
  environmentalAnalysis: '環境分析',
  predictiveInsights: '予測インサイト',
  aiHealthDashboard: 'AIヘルスダッシュボード',
  healthDashboard: 'ヘルスダッシュボード',
  smartAlerts: 'スマートアラート',
  healthScoreDetails: 'ヘルススコア詳細',
  trendsOverview: 'トレンド概要',
  riskOverview: 'リスク概要',
  analyzing: '分析中...',
  analyzingEnvironment: '環境を分析中...',
  analyzeEnvironment: '環境を分析',
  generatingPredictions: '予測を生成中...',
  generatePredictions: '予測を生成',
  predictionHorizon: '予測期間',
  oneWeek: '1週間',
  oneMonth: '1ヶ月',
  threeMonths: '3ヶ月',
  healthPredictions: '健康予測',
  predictedHealthScore: '予測ヘルススコア',
  predictedWeight: '予測体重',
  predictedActivity: '予測活動',
  predictedSleep: '予測睡眠',
  predictionConfidence: '予測信頼度',
  riskAssessment: 'リスク評価',
  diseaseRisk: '病気リスク',
  injuryRisk: '怠害リスク',
  behavioralRisk: '行動リスク',
  environmentalStressRisk: '環境ストレスリスク',
  predictiveAlerts: '予測アラート',
  acknowledgeAlert: '確認',
  resolveAlert: '解決',
  humidity: '湿度',
  airQuality: '空気品質',
  uvIndex: 'UV指数',
  moderate: '中程度',
  hazardous: '危険',
  optimal: '最適',
};