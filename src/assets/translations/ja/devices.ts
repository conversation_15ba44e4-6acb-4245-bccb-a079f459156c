export const devices = {
  // デバイスステータス
  paired: 'ペアリング済み',
  available: '利用可能',
  connected: '接続済み',
  disconnected: '切断済み',
  connecting: '接続中...',
  
  // デバイス検出
  findDevices: 'デバイスを検索',
  scanForDevices: 'デバイスをスキャン',
  scanning: 'スキャン中...',
  loadingDevices: 'デバイス読み込み中...',
  checkingBluetooth: 'Bluetooth確認中...',
  noDevicesFound: 'デバイスが見つかりません',
  
  // デバイスペアリング
  pairDevice: 'デバイスをペアリング',
  pairDevicePrompt: 'ペアリングするデバイスを選択',
  pairing: 'ペアリング中...',
  pairingSuccess: 'デバイスのペアリングが完了しました',
  pairingFailed: 'デバイスのペアリングに失敗しました',
  noPairedDevices: 'ペアリング済みデバイスが見つかりません',
  
  // デバイス管理
  unpairDevice: 'デバイスのペアリングを解除',
  deviceInfo: 'デバイス情報',
  batteryLevel: 'バッテリーレベル',
  signalStrength: '信号強度',
  lastSeen: '最終確認',
  
  // Bluetooth
  bluetoothDisabled: 'Bluetoothが無効です',
  enableBluetooth: 'Bluetoothを有効にする',
  bluetoothUnavailable: 'Bluetoothが利用できません',
  bluetoothPermission: 'Bluetooth権限が必要です',
  
  // デバイスタイプ
  heartRateMonitor: '心拍数モニター',
  activityTracker: 'アクティビティトラッカー',
  smartCollar: 'スマートカラー',
  temperatureSensor: '温度センサー',
  gpsTracker: 'GPSトラッカー',
  
  // アクション
  refresh: '更新',
  retry: '再試行',
  cancel: 'キャンセル',
  connect: '接続',
  disconnect: '切断',
  
  // DevicesScreenで使用される追加キー
  initializing: '初期化中...',
  bluetoothErrorPrefix: 'Bluetoothエラー:',
  retryBluetoothCheck: 'Bluetooth確認を再試行',
  bluetoothRequiredWarning: 'デバイス接続にはBluetoothが必要です',
  availableToAdd: '追加可能',
  addDevice: 'デバイスを追加',
  scanToFindDevicesPrompt: 'スキャンして近くのデバイスを検索',
  deviceIdPlaceholder: 'デバイスIDを入力またはバーコードをスキャン',
  add: '追加'
};