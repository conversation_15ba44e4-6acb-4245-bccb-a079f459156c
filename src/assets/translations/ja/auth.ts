// Authentication and security - Japanese
export const auth = {
  // Authentication
  createAccount: 'アカウント作成',
  resetPassword: 'パスワードリセット',
  signInToAccount: 'アカウントにサインイン',
  login: 'ログイン',
  logout: 'ログアウト',
  register: '登録',
  email: 'メール',
  password: 'パスワード',
  confirmPassword: 'パスワード確認',
  forgotPassword: 'パスワードを忘れましたか？',
  
  // Biometric Authentication
  biometricLogin: '生体認証ログイン',
  signInWithFingerprint: '指紋でサインイン',
  signInWithFaceId: 'Face IDでサインイン',
  signInWithBiometric: '生体認証でサインイン',
  biometricAuthenticationFailed: '生体認証に失敗しました',
  biometricNotAvailable: 'このデバイスでは生体認証が利用できません',
  biometricNotEnabled: '生体認証ログインが有効ではありません',
  biometricSetupRequired: '生体認証の設定が必要です',
  biometricSetupDescription: '生体認証ログインを使用するには、まずデバイスの設定で{{biometricType}}を設定してください。',
  enableBiometricLogin: 'アカウントへの迅速で安全なアクセスのために生体認証ログインを有効にします。',
  biometricLoginEnabled: '生体認証ログインが正常に有効になりました！',
  biometricLoginDisabled: '生体認証ログインが無効になりました',
  disableBiometricLogin: '生体認証ログインを無効にする',
  disableBiometricConfirm: '生体認証ログインを無効にしますか？サインインにパスワードが必要になります。',
  biometricDataSecure: 'あなたの生体データはデバイス上で安全に保管され、共有されることはありません。',
  enableBiometricPrompt: '生体認証ログインを有効にする',
  biometricSetupConfirm: '設定を確認するために生体認証を使用してください',
  biometricSignInPrompt: 'HoofBeatにサインイン',
  biometricSignInSubtitle: 'アカウントにアクセスするために生体認証を使用してください',
  biometricSetupCancelled: '生体認証の設定がキャンセルされました',
  biometricRefreshSession: 'セッションを更新するためにパスワードでサインインし、再度生体認証ログインを有効にしてください。',
  biometricCredentialsFirst: '生体認証ログインを有効にする前に資格情報を確認してください',
  biometricSignInPasswordFirst: '生体認証ログインを有効にするために、まずパスワードでサインインしてください',
  or: 'または',
  
  // Error Messages
  userNotFound: 'ユーザーが見つかりません',
  profileCreationFailed: 'ユーザープロフィールの作成に失敗しました',
  profileLoadFailed: 'ユーザープロフィールの読み込みに失敗しました',
  sessionExpired: 'セッションが期限切れです。再度サインインしてください。',
  networkError: 'ネットワークエラーです。接続を確認してください。',
  unexpectedError: '予期しないエラーが発生しました',
  
  // Biometric types
  biometric: '生体認証',
  fingerprint: '指紋',
  faceId: 'Face ID',
  biometricAuthentication: '生体認証',
  
  // MFA
  mfaSettings: '二段階認証',
  mfaEnabled: '二段階認証が有効になりました',
  mfaDisabled: '二段階認証が無効になりました',
};