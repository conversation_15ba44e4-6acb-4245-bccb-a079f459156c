// Animal management - Japanese
export const animals = {
  // Animal management
  animalName: '動物名',
  animalType: '動物の種類',
  breed: '品種',
  age: '年齢',
  gender: '性別',
  weight: '体重',
  color: '色',
  microchipId: 'マイクロチップID',
  registrationNumber: '登録番号',
  
  // Animals Overview
  myAnimals: '私の動物',
  seeAll: 'すべて表示',
  addAnimal: '動物を追加',
  noAnimalsYet: 'まだ動物が追加されていません',
  addFirstAnimal: '最初の動物を追加してください',
  id: 'ID',
  ageYears: '{{age}}歳',
  gps: 'GPS',
  
  // Animal Detail Screen
  animalDetailLatestVitals: '最新のバイタル',
  animalDetailDeviceOnly: 'デバイスのみ',
  animalDetailRecordedOn: '記録日',
  animalDetailTemperature: '体温',
  animalDetailHeartRate: '心拍数',
  animalDetailRespiration: '呼吸',
  animalDetailWeight: '体重',
  animalDetailNotes: 'メモ',
  animalDetailNoVitalsRecorded: 'バイタルが記録されていません',
  animalDetailConnectDevice: 'モニタリングを開始するためにデバイスを接続してください',
  animalDetailMedications: '薬',
  animalDetailAdd: '追加',
  animalDetailPrintMedicationSchedule: '薬のスケジュールを印刷',
  animalDetailNoMedicationsAdded: '薬が追加されていません',
  animalDetailAddMedication: '薬を追加',
  animalDetailVaccinations: 'ワクチン',
  animalDetailDue: '期限',
  animalDetailNoRenewal: '更新なし',
  animalDetailPrintVaccinationRecord: 'ワクチン記録を印刷',
  animalDetailNoVaccinationsRecorded: 'ワクチンが記録されていません',
  animalDetailAddVaccination: 'ワクチンを追加',
  animalDetailMicrochipRequired: 'ワクチンにはマイクロチップIDが必要です',
  animalDetailFeedingSchedule: '給餌スケジュール',
  animalDetailViewAll: 'すべて表示',
  animalDetailPrintFeedingSchedule: '給餌スケジュールを印刷',
  animalDetailNoFeedingSchedule: '給餌スケジュールが設定されていません',
  animalDetailSetFeedingSchedule: '給餌スケジュールを設定',
  animalDetailTrainingSessions: 'トレーニングセッション',
  animalDetailLoadingSessions: 'セッションを読み込み中...',
  animalDetailLogNewSession: '新しいセッションを記録',
  animalDetailNoTrainingSessions: 'トレーニングセッションがありません',
  animalDetailTrainingDescription: '進歩とパフォーマンスを追跡するためにトレーニングセッションの記録を開始してください。',
  animalDetailLogFirstSession: '最初のセッションを記録',
  
  // Animal not found
  animalNotFound: '動物が見つかりません',
};