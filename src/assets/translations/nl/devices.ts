export const devices = {
  // Apparaatstatus
  paired: 'Gekoppeld',
  available: 'Beschikbaar',
  connected: 'Verbonden',
  disconnected: 'Niet verbonden',
  connecting: 'Verbinden...',
  
  // Apparaat ontdekking
  findDevices: 'Apparaten zoeken',
  scanForDevices: 'Scannen naar apparaten',
  scanning: 'Scannen...',
  loadingDevices: 'Apparaten laden...',
  checkingBluetooth: 'Bluetooth controleren...',
  noDevicesFound: 'Geen apparaten gevonden',
  
  // Apparaat koppeling
  pairDevice: 'Apparaat koppelen',
  pairDevicePrompt: 'Selecteer een apparaat om te koppelen',
  pairing: 'Koppelen...',
  pairingSuccess: 'Apparaat succesvol gekoppeld',
  pairingFailed: 'Koppel<PERSON> van apparaat mislukt',
  noPairedDevices: 'Geen gekoppelde apparaten gevonden',
  
  // Apparaatbeheer
  unpairDevice: 'Apparaat ontkoppelen',
  deviceInfo: 'Apparaatinformatie',
  batteryLevel: 'Batterijn<PERSON><PERSON>',
  signalStrength: 'Signaalsterkte',
  lastSeen: 'Laatst gezien',
  
  // Bluetooth
  bluetoothDisabled: 'Bluetooth uitgeschakeld',
  enableBluetooth: 'Bluetooth inschakelen',
  bluetoothUnavailable: 'Bluetooth niet beschikbaar',
  bluetoothPermission: 'Bluetooth toestemming vereist',
  
  // Apparaattypes
  heartRateMonitor: 'Hartslagmonitor',
  activityTracker: 'Activiteitentracker',
  smartCollar: 'Slimme halsband',
  temperatureSensor: 'Temperatuursensor',
  gpsTracker: 'GPS-tracker',
  
  // Acties
  refresh: 'Vernieuwen',
  retry: 'Opnieuw proberen',
  cancel: 'Annuleren',
  connect: 'Verbinden',
  disconnect: 'Verbinding verbreken',
  
  // Extra sleutels gebruikt in DevicesScreen
  initializing: 'Initialiseren...',
  bluetoothErrorPrefix: 'Bluetooth Fout:',
  retryBluetoothCheck: 'Bluetooth controle opnieuw proberen',
  bluetoothRequiredWarning: 'Bluetooth is vereist voor apparaatconnectiviteit',
  availableToAdd: 'Beschikbaar om toe te voegen',
  addDevice: 'Apparaat toevoegen',
  scanToFindDevicesPrompt: 'Tik op scannen om apparaten in de buurt te vinden',
  deviceIdPlaceholder: 'Voer apparaat-ID in of scan barcode',
  add: 'Toevoegen'
};