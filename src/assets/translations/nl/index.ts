// Modular Dutch translations
import { devices } from './devices';

export const nl = {
  ...devices,
  // Common actions
  add: 'Toevoegen',
  edit: 'Bewerken',
  delete: 'Verwijderen',
  save: '<PERSON><PERSON><PERSON>',
  cancel: '<PERSON><PERSON><PERSON>',
  confirm: 'Bevestigen',
  back: '<PERSON><PERSON>',
  next: 'Volgende',
  previous: 'Vorige',
  loading: 'Laden...',
  error: 'Fout',
  success: 'Succes',
  warning: 'Waarschuwing',
  info: 'Informatie',
  retry: 'Opnieuw proberen',
  refresh: 'Vernieuwen',
  
  // Navigation
  home: 'Home',
  animals: 'Dieren',
  devices: 'Apparaten',
  profile: '<PERSON><PERSON>',
  settings: 'Instellingen',
  advancedSettings: 'Geavanceerde instellingen',
  
  // Tab Navigation
  tabHome: 'Home',
  tabAnimals: 'Dieren',
  tabDevices: 'Apparaten',
  tabHealth: 'Gezondheid',
  tabProfile: 'Profiel',
  
  // Authentication
  createAccount: 'Account aanmaken',
  resetPassword: 'Wachtwoord resetten',
  signInToAccount: 'Inloggen op je account',
  login: 'Inloggen',
  logout: 'Uitloggen',
  register: 'Registreren',
  email: 'E-mail',
  password: 'Wachtwoord',
  confirmPassword: 'Wachtwoord bevestigen',
  forgotPassword: 'Wachtwoord vergeten?',
  
  // Biometric Authentication
  biometricLogin: 'Biometrisch inloggen',
  signInWithFingerprint: 'Inloggen met vingerafdruk',
  signInWithFaceId: 'Inloggen met Face ID',
  signInWithBiometric: 'Inloggen met biometrie',
  biometricAuthenticationFailed: 'Biometrische authenticatie mislukt',
  biometricNotAvailable: 'Biometrische authenticatie is niet beschikbaar op dit apparaat',
  biometricNotEnabled: 'Biometrisch inloggen is niet ingeschakeld',
  biometricSetupRequired: 'Biometrische setup vereist',
  biometricSetupDescription: 'Om biometrisch inloggen te gebruiken, stel eerst {{biometricType}} in via de apparaatinstellingen.',
  enableBiometricLogin: 'Schakel biometrisch inloggen in voor snelle en veilige toegang tot je account.',
  biometricLoginEnabled: 'Biometrisch inloggen succesvol ingeschakeld!',
  biometricLoginDisabled: 'Biometrisch inloggen uitgeschakeld',
  disableBiometricLogin: 'Biometrisch inloggen uitschakelen',
  disableBiometricConfirm: 'Weet je zeker dat je biometrisch inloggen wilt uitschakelen? Je moet je wachtwoord gebruiken om in te loggen.',
  biometricDataSecure: 'Je biometrische gegevens blijven veilig op je apparaat en worden nooit gedeeld.',
  enableBiometricPrompt: 'Biometrisch inloggen inschakelen',
  biometricSetupConfirm: 'Gebruik je biometrie om de setup te bevestigen',
  biometricSignInPrompt: 'Inloggen bij HoofBeat',
  biometricSignInSubtitle: 'Gebruik je biometrie om toegang te krijgen tot je account',
  biometricSetupCancelled: 'Biometrische setup geannuleerd',
  biometricRefreshSession: 'Log in met je wachtwoord om je sessie te vernieuwen, schakel dan biometrisch inloggen opnieuw in.',
  biometricCredentialsFirst: 'Verifieer je inloggegevens voordat je biometrisch inloggen inschakelt',
  biometricSignInPasswordFirst: 'Log eerst in met je wachtwoord om biometrisch inloggen in te schakelen',
  or: 'of',
  
  // Error Messages
  userNotFound: 'Gebruiker niet gevonden',
  profileCreationFailed: 'Aanmaken gebruikersprofiel mislukt',
  profileLoadFailed: 'Laden gebruikersprofiel mislukt',
  sessionExpired: 'Sessie verlopen. Log opnieuw in.',
  networkError: 'Netwerkfout. Controleer je verbinding.',
  unexpectedError: 'Er is een onverwachte fout opgetreden',
  
  // Biometric types
  biometric: 'Biometrisch',
  fingerprint: 'Vingerafdruk',
  faceId: 'Face ID',
  biometricAuthentication: 'Biometrische authenticatie',
  
  // MFA
  mfaSettings: 'Twee-factor authenticatie',
  mfaEnabled: 'Twee-factor authenticatie ingeschakeld',
  mfaDisabled: 'Twee-factor authenticatie uitgeschakeld',
  
  // Animal management
  animalName: 'Diernaam',
  animalType: 'Diertype',
  breed: 'Ras',
  age: 'Leeftijd',
  gender: 'Geslacht',
  weight: 'Gewicht',
  color: 'Kleur',
  microchipId: 'Microchip ID',
  registrationNumber: 'Registratienummer',
  myAnimals: 'Mijn dieren',
  seeAll: 'Alles bekijken',
  addAnimal: 'Dier toevoegen',
  noAnimalsYet: 'Nog geen dieren toegevoegd',
  addFirstAnimal: 'Voeg je eerste dier toe',
  id: 'ID',
  ageYears: '{{age}} jaar',
  gps: 'GPS',
  animalNotFound: 'Dier niet gevonden',
  
  // AI Features
  aiAssistant: 'AI Assistent',
  aiChat: 'AI Chat',
  aiChatTitle: 'Chat met de AI assistent',
  aiChatPlaceholder: 'Stel vragen over de gezondheid, training of verzorging van je dier...',
  aiChatSend: 'Versturen',
  aiChatAttachment: 'Afbeelding bijvoegen',
  aiChatCamera: 'Foto maken',
  aiChatGallery: 'Kiezen uit galerij',
  aiChatAnalyzing: 'Analyseren...',
  aiChatTyping: 'AI typt...',
  aiChatNoMessages: 'Nog geen berichten',
  aiChatStartConversation: 'Begin een gesprek met je AI assistent over {{animalName}}',
  aiChatImageAnalysis: 'Afbeeldingsanalyse',
  aiChatCopyMessage: 'Bericht kopiëren',
  aiChatShareMessage: 'Bericht delen',
  aiChatMessageCopied: 'Bericht gekopieerd naar klembord',
  aiChatUploadingImage: 'Afbeelding uploaden...',
  aiChatImageUploadFailed: 'Afbeelding uploaden mislukt',
  aiChatSendingMessage: 'Bericht versturen...',
  aiChatMessageFailed: 'Bericht versturen mislukt',
  aiChatRetry: 'Opnieuw proberen',
  aiChatMaxFileSize: 'Bestandsgrootte moet kleiner zijn dan 10MB',
  aiChatUnsupportedFormat: 'Niet-ondersteund bestandsformaat',
  
  // Additional translations for backward compatibility
  language: 'Taal',
  languageSettings: 'Taal en regio',
  selectYourLanguage: 'Selecteer je taal',
  languageChanged: 'Taal succesvol gewijzigd',
  selectLanguage: 'Taal selecteren',
  changingLanguage: 'Taal wijzigen...',
  
  // Profile navigation
  profileSettings: 'Instellingen',
  profileAdvancedSettings: 'Geavanceerde instellingen',
  profilePrivacySettings: 'Privacy-instellingen',
  profileMfaSettings: 'Twee-factor authenticatie',
  profileUpgradeToPremium: 'Upgraden naar Premium',
  profileHelpSupport: 'Hulp en ondersteuning',
  profileFAQ: 'FAQ',
  profileContactSupport: 'Contact opnemen met ondersteuning',
  profileSignOut: 'Uitloggen',
  profileSignOutConfirm: 'Weet je zeker dat je wilt uitloggen?',
  profileCancel: 'Annuleren',
  profileVersion: 'Versie',
  profileSignOutFailed: 'Uitloggen mislukt',
  
  // Screen titles
  aiHealthDashboard: 'AI Gezondheid Dashboard',
  healthScoreDetail: 'Gezondheidsscore Detail',
  diseaseRisk: 'Ziekterisico',
  healthTrends: 'Gezondheidsrends',
  stressAnalysis: 'Stressanalyse',
  sleepMonitoring: 'Slaapmonitoring',
  environmentalAnalysis: 'Omgevingsanalyse',
  predictiveInsights: 'Voorspellende inzichten',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'Je boerderij partner',
  
  // Premium
  premium: 'Premium',
  upgradeToPremium: 'Upgraden naar Premium',
  unlimitedAnimals: 'Onbeperkt dieren',
  advancedAnalytics: 'Geavanceerde analyses',
  upgradeNow: 'Nu upgraden',
  
  // Health and vitals
  health: 'Gezondheid',
  vitals: 'Vitale functies',
  temperature: 'Temperatuur',
  heartRate: 'Hartslag',
  respirationRate: 'Ademhalingsfrequentie',
  bloodPressure: 'Bloeddruk',
  
  // Additional keys
  behavioralAnalysis: 'Gedragsanalyse',
  environmentalAnalysisDescription: 'Analyseer de impact van de omgeving op de gezondheid',
  environmentalImpact: 'Omgevingsimpact',
  viewAll: 'Alles bekijken',
  
  // Quick Actions
  quickActions: 'Snelle Acties',
  healthTrends: 'Gezondheids Trends',
  diseaseRisk: 'Ziekte Risico',
  stressAnalysis: 'Stress Analyse',
  sleepMonitoring: 'Slaap Monitoring',
  environmental: 'Milieu',
  predictiveInsights: 'Voorspellende Inzichten',
  assistant: 'AI Assistent',
  
  // Additional missing AI keys
  environmentalAnalysis: 'Omgevingsanalyse',
  behavioralAnalysis: 'Gedragsanalyse',
  aiHealthDashboard: 'AI Gezondheids Dashboard',
  smartAlerts: 'Slimme Waarschuwingen',
  diseaseRiskAssessment: 'Ziekte Risico Beoordeling',
  healthScoreDetails: 'Gezondheids Score Details',
  trendsOverview: 'Trends Overzicht',
  riskOverview: 'Risico Overzicht',
  
  // Health Dashboard
  healthDashboard: 'Gezondheids Dashboard',
  overallHealthStatus: 'Algemene Gezondheidsstatus',
  totalAnimals: 'Totaal Dieren',
  healthScore: 'Gezondheids Score',
  activeAlerts: 'Actieve Waarschuwingen',
  recentAnalyses: 'Recente Analyses',
  animalNotSelected: 'Geen dier geselecteerd',
  selectAnimalForDetails: 'Selecteer een dier om gezondheidsdetails te bekijken',
  viewHealthDetails: 'Bekijk Gezondheids Details',
  lastUpdated: 'Laatst bijgewerkt',
  noHealthData: 'Geen gezondheidsdata',
  tapToViewDetails: 'Tik om details te bekijken',
  
  // Common translations
  refresh: 'Vernieuwen',
  refreshing: 'Vernieuwen...',
  viewDetails: 'Bekijk details',
  
  // Nested objects for compatibility with modular structure
  common: {
    refresh: 'Vernieuwen',
    refreshing: 'Vernieuwen...',
  },
  
  ai: {
    quickActions: 'Snelle Acties',
    healthTrends: 'Gezondheids Trends',
    diseaseRisk: 'Ziekte Risico',
    stressAnalysis: 'Stress Analyse',
    sleepMonitoring: 'Slaap Monitoring',
    environmental: 'Milieu',
    predictiveInsights: 'Voorspellende Inzichten',
    assistant: 'AI Assistent',
    environmentalAnalysis: 'Omgevingsanalyse',
    behavioralAnalysis: 'Gedragsanalyse',
    aiHealthDashboard: 'AI Gezondheids Dashboard',
    healthDashboard: 'Gezondheids Dashboard',
    smartAlerts: 'Slimme Waarschuwingen',
    diseaseRiskAssessment: 'Ziekte Risico Beoordeling',
    healthScoreDetails: 'Gezondheids Score Details',
    trendsOverview: 'Trends Overzicht',
    riskOverview: 'Risico Overzicht',
    
    // AI Widget keys
    aiWidgetInsights: 'AI Inzichten',
    aiWidgetHealth: 'Gezondheids Status',
    aiWidgetLiveCheckIn: 'Live Check-in',
    aiWidgetViewAll: 'Bekijk Alles',
    aiWidgetNew: 'Nieuw',
    aiWidgetLogTraining: 'Training Loggen',
    aiWidgetNoAIData: 'Geen AI Data',
    aiWidgetReadiness: 'Gereedheid',
    aiWidgetLiveStatus: 'Live Status',
    aiWidgetAvailable: 'Beschikbaar',
    aiWidgetLatestTip: 'Laatste Tip',
    aiWidgetChecking: 'Controleren...',
  },
  
  // Root level AI widget keys for backward compatibility
  aiWidgetInsights: 'AI Inzichten',
  aiWidgetHealth: 'Gezondheids Status',
  aiWidgetLiveCheckIn: 'Live Check-in',
  aiWidgetViewAll: 'Bekijk Alles',
  aiWidgetNew: 'Nieuw',
  aiWidgetLogTraining: 'Training Loggen',
  aiWidgetNoAIData: 'Geen AI Data',
  aiWidgetReadiness: 'Gereedheid',
  aiWidgetLiveStatus: 'Live Status',
  aiWidgetAvailable: 'Beschikbaar',
  aiWidgetLatestTip: 'Laatste Tip',
  aiWidgetChecking: 'Controleren...',
  
  // Missing keys from en/common.ts - Dutch translations
  // Health Dashboard
  healthDashboard: 'Gezondheid Dashboard',
  overallHealthStatus: 'Algemene Gezondheidsstatus',
  criticalAlerts: 'Kritieke Waarschuwingen',
  recentActivity: 'Recente Activiteit',
  healthInsights: 'Gezondheid Inzichten',
  quickActions: 'Snelle Acties',
  viewHealthScore: 'Bekijk Gezondheidsscore',
  analyzeRisks: 'Analyseer Risico\'s',
  viewTrends: 'Bekijk Trends',
  stressAnalysis: 'Stress Analyse',
  sleepMonitoring: 'Slaap Monitoring',
  environmentalAnalysis: 'Omgevingsanalyse',
  predictiveInsights: 'Voorspellende Inzichten',
  noAnimalsSelected: 'Geen dieren geselecteerd',
  selectAnimalToViewHealth: 'Selecteer een dier om hun gezondheid dashboard te bekijken',
  healthDataLoading: 'Gezondheidsgegevens laden...',
  healthDataError: 'Fout bij laden gezondheidsgegevens',
  retryLoadingHealthData: 'Probeer gezondheidsgegevens opnieuw te laden',
  
  // FAQ
  frequentlyAskedQuestions: 'Veelgestelde Vragen',
  howToAddAnimal: 'Hoe voeg ik een nieuw dier toe?',
  howToAddAnimalAnswer: 'Om een nieuw dier toe te voegen, ga naar het Dieren tabblad en tik op de "+" knop. Vul de informatie van je dier in inclusief naam, ras, leeftijd en andere details.',
  howToConnectDevice: 'Hoe verbind ik een monitoring apparaat?',
  howToConnectDeviceAnswer: 'Ga naar het Apparaten tabblad, tik op "Apparaat Toevoegen", en volg de koppeling instructies. Zorg ervoor dat Bluetooth is ingeschakeld op je telefoon.',
  whatIsHealthScore: 'Wat is de Gezondheidsscore?',
  whatIsHealthScoreAnswer: 'De Gezondheidsscore is een AI-gegenereerde beoordeling van de algemene gezondheid van je dier gebaseerd op vitale functies, activiteitsniveaus en andere gemonitorde gegevens.',
  howToSetupAlerts: 'Hoe stel ik gezondheid waarschuwingen in?',
  howToSetupAlertsAnswer: 'Ga naar Instellingen > Meldingen om te configureren wanneer en hoe je waarschuwingen ontvangt over de gezondheidsstatus van je dier.',
  whatDataIsTracked: 'Welke gegevens volgt de app?',
  whatDataIsTrackedAnswer: 'De app volgt vitale functies (hartslag, temperatuur), activiteitsniveaus, locatie, voedingsschema\'s, medicijnen en omgevingsfactoren.',
  howToExportData: 'Hoe kan ik de gegevens van mijn dier exporteren?',
  howToExportDataAnswer: 'Ga naar het profiel van je dier, tik op de menu knop, en selecteer "Gegevens Exporteren". Je kunt het datumbereik en gegevenstypes kiezen om te exporteren.',
  isPremiumWorthIt: 'Is het Premium abonnement het waard?',
  isPremiumWorthItAnswer: 'Premium biedt onbeperkte dieren, geavanceerde AI inzichten, gedetailleerde gezondheidsrapporten en prioriteit ondersteuning. Het is ideaal voor professioneel gebruik of meerdere dieren.',
  howToContactSupport: 'Hoe neem ik contact op met ondersteuning?',
  howToContactSupportAnswer: 'Je kunt contact opnemen met ondersteuning via het Contact tabblad in je profiel, of stuur ons direct een e-<NAME_EMAIL>.',
  
  // Contact
  contactUs: 'Neem Contact Op',
  messageSent: 'Bericht succesvol verzonden',
  messageSentTitle: 'Bericht Verzonden',
  messageSentDescription: 'Je bericht is <NAME_EMAIL>. We nemen binnenkort contact met je op!',
  failedToSendMessage: 'Bericht verzenden mislukt. Probeer opnieuw.',
  errorSendingMessageTitle: 'Fout Bij Verzenden Bericht',
  errorSendingMessageDescription: 'Er was een probleem bij het verzenden van je bericht. Probeer opnieuw of gebruik de live chat optie.',
  chatError: 'Chat Fout',
  failedToOpenChat: 'Chat openen mislukt. Probeer opnieuw.',
  openingChatSupport: 'Chat ondersteuning openen...',
  
  // Print
  print: 'Afdrukken',
  printed: 'Afgedrukt',
  download: 'Downloaden',
  downloaded: 'Gedownload',
  shared: 'Gedeeld',
  animalNotFound: 'Dier niet gevonden',
  goBack: 'Ga Terug',
  
  // Barcode Scanner
  cameraPermissionRequired: 'Camera toestemming vereist om barcodes te scannen',
  invalidBarcodeFormat: 'Ongeldig apparaat barcode formaat',
  deviceBarcodeScanned: 'Apparaat barcode gescand',
  requestingCameraPermission: 'Camera toestemming aanvragen...',
  noAccessToCamera: 'Geen toegang tot camera',
  scanDeviceBarcode: 'Scan Apparaat Barcode',
  positionBarcodeInFrame: 'Positioneer de barcode binnen het kader om te scannen',
  scanAgain: 'Scan Opnieuw',
  
  // Privacy Settings
  privacySettings: 'Privacy Instellingen',
  privacySettingsSaved: 'Privacy instellingen opgeslagen',
  accountDeleted: 'Account verwijderd',
  locationTracking: 'Locatie Tracking',
  locationTrackingDescription: 'Sta de app toe om de locaties van je dieren te volgen. Dit is vereist voor locatie waarschuwingen.',
  shareDataWithVets: 'Deel Gegevens met Dierenartsen',
  shareDataWithVetsDescription: 'Sta dierenartsen toe om toegang te krijgen tot de gezondheidsrecords van je dieren wanneer je ze deelt.',
  analytics: 'Analytics',
  analyticsDescription: 'Help ons verbeteren door anonieme gebruiksgegevens verzameling toe te staan.',
  saveSettings: 'Instellingen Opslaan',
  
  // Alert Types
  heartRateAlert: 'Hartslag Waarschuwing',
  temperatureAlert: 'Temperatuur Waarschuwing',
  locationAlert: 'Locatie Waarschuwing',
  healthAlert: 'Gezondheid Waarschuwing',
  
  // Health Status
  score: 'Score',
  alerts: 'Waarschuwingen',
  trends: 'Trends',
  tracked: 'Gevolgd',
  criticalAlertsRequireAttention: 'kritieke waarschuwingen vereisen aandacht',
  lastUpdated: 'Laatst bijgewerkt',
  excellent: 'Uitstekend',
  good: 'Goed',
  fair: 'Redelijk',
  poor: 'Slecht',
  critical: 'Kritiek',
  aiAnalysis: 'AI Analyse',
  active: 'Actief',
  seeAll: 'Bekijk Alles',
  noAnimalsYet: 'Nog geen dieren',
  addFirstAnimal: 'Voeg je eerste dier toe',
  
  // Missing AI & Health Analysis Keys (Dutch translations)
  diseaseRiskAssessment: 'Ziekte Risico Beoordeling',
  healthTrends: 'Gezondheids Trends',
  stressAnalysis: 'Stress Analyse',
  sleepMonitoring: 'Slaap Monitoring',
  environmentalAnalysis: 'Omgevingsanalyse',
  predictiveInsights: 'Voorspellende Inzichten',
  aiHealthDashboard: 'AI Gezondheids Dashboard',
  healthDashboard: 'Gezondheids Dashboard',
  smartAlerts: 'Slimme Waarschuwingen',
  healthScoreDetails: 'Gezondheids Score Details',
  trendsOverview: 'Trends Overzicht',
  riskOverview: 'Risico Overzicht',
  analyzing: 'Analyseren...',
  analyzingEnvironment: 'Omgeving analyseren...',
  analyzeEnvironment: 'Omgeving Analyseren',
  generatingPredictions: 'Voorspellingen genereren...',
  generatePredictions: 'Voorspellingen Genereren',
  predictionHorizon: 'Voorspellings Horizon',
  oneWeek: '1 Week',
  oneMonth: '1 Maand',
  threeMonths: '3 Maanden',
  healthPredictions: 'Gezondheids Voorspellingen',
  predictedHealthScore: 'Voorspelde Gezondheids Score',
  predictedWeight: 'Voorspeld Gewicht',
  predictedActivity: 'Voorspelde Activiteit',
  predictedSleep: 'Voorspelde Slaap',
  predictionConfidence: 'Voorspellings Vertrouwen',
  riskAssessment: 'Risico Beoordeling',
  diseaseRisk: 'Ziekte Risico',
  injuryRisk: 'Verwondings Risico',
  behavioralRisk: 'Gedragsrisico',
  environmentalStressRisk: 'Omgevingsstress Risico',
  predictiveAlerts: 'Voorspellende Waarschuwingen',
  acknowledgeAlert: 'Bevestigen',
  resolveAlert: 'Oplossen',
  humidity: 'Vochtigheid',
  airQuality: 'Luchtkwaliteit',
  uvIndex: 'UV Index',
  moderate: 'Gematigd',
  hazardous: 'Gevaarlijk',
  optimal: 'Optimaal',
};