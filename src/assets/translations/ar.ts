/**
 * @magic_description Arabic translations for the HoofBeat app
 * Contains all Arabic language strings used throughout the application
 * This is a Right-to-Left (RTL) language
 */

export const ar = {
  // Navigation & Headers
  settings: 'الإعدادات',
  advancedSettings: 'الإعدادات المتقدمة',
  profile: 'الملف الشخصي',
  animals: 'الحلال',
  devices: 'الأجهزة',
  home: 'الرئيسية',
  
  // Tab Navigation
  tabHome: 'الرئيسية',
  tabAnimals: 'الحلال',
  tabDevices: 'الأجهزة',
  tabHealth: 'الصحة',
  tabProfile: 'الملف الشخصي',
  
  // Language Settings
  language: 'اللغة',
  languageSettings: 'اللغة والمنطقة',
  selectYourLanguage: 'اختر لغتك',
  languageChanged: 'تم تغيير اللغة بنجاح',
  
  // Settings Categories
  dataManagement: 'إدارة البيانات',
  notifications: 'الإشعارات',
  themeSettings: 'إعدادات المظهر',
  darkMode: 'الوضع الليلي',
  syncSettings: 'إعدادات المزامنة',
  healthAlerts: 'تنبيهات صحية',
  reminders: 'تذكيرات',
  
  // Health Alert Settings
  heartRateAlerts: 'تنبيهات معدل ضربات القلب',
  heartRateRange: 'نطاق معدل ضربات القلب',
  minBpm: 'الحد الأدنى (ضربة/د)',
  maxBpm: 'الحد الأقصى (ضربة/د)',
  temperatureAlerts: 'تنبيهات درجة الحرارة',
  temperatureRange: 'نطاق درجة الحرارة',
  minCelsius: 'الحد الأدنى (°م)',
  maxCelsius: 'الحد الأقصى (°م)',
  locationAlerts: 'تنبيهات الموقع',
  locationUpdateInterval: 'فترة تحديث الموقع',
  locationUpdateLabel: 'تنبيه إذا لم يتم التحديث لأكثر من (ساعات):',
  
  // Reminder Settings
  reminderTimeBefore: 'وقت التذكير قبل (دقائق):',
  saveSettings: 'حفظ الإعدادات',
  settingsSavedSuccess: 'تم حفظ الإعدادات بنجاح',
  errorFixBeforeSaving: 'يرجى إصلاح الأخطاء قبل الحفظ',
  errorMaxHeartRateHigher: 'يجب أن يكون الحد الأقصى لمعدل ضربات القلب أعلى من الحد الأدنى',
  errorMaxTemperatureHigher: 'يجب أن تكون درجة الحرارة القصوى أعلى من الدنيا',
  
  // Monitoring Settings
  heartRateLimits: 'حدود معدل ضربات القلب',
  temperatureAlerts: 'تنبيهات درجة الحرارة',
  locationTracking: 'تتبع الموقع',
  
  // Reminder Settings
  feedingReminders: 'تذكيرات التغذية',
  medicationReminders: 'تذكيرات الأدوية',
  vaccinationReminders: 'تذكيرات التطعيم',
  
  // Actions
  saveChanges: 'حفظ التغييرات',
  cancel: 'إلغاء',
  confirm: 'تأكيد',
  delete: 'حذف',
  edit: 'تعديل',
  add: 'إضافة',
  
  // Device Management
  scanForDevices: 'البحث عن الأجهزة',
  scanning: 'جاري البحث...',
  devicesFound: 'جهاز موجود',
  noDevicesFound: 'لم يتم العثور على أجهزة',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'رفيقك في تربية الحلال',
  animalSpeedTitle: 'سرعة {{animalName}}',
  speedUpdatedSuccess: 'تم تحديث سرعة {{animalName}}',
  speedUpdateFailed: 'فشل في تحديث السرعة',
  
  // Quick Stats
  quickOverview: 'نظرة سريعة',
  activeDevices: 'الأجهزة النشطة',
  todaysFeedings: 'تغذية اليوم',
  medications: 'الأدوية',
  
  // Animals Overview
  myAnimals: 'حلالي',
  seeAll: 'عرض الكل',
  addAnimal: 'إضافة حلال',
  noAnimalsYet: 'لم تتم إضافة حلال بعد',
  addFirstAnimal: 'أضف حلالك الأول',
  
  // Upcoming Tasks
  upcomingTasks: 'المهام القادمة',
  noTasksToday: 'لا توجد مهام مجدولة لليوم',
  allCaughtUp: 'كل شيء محدث! حلالك في رعاية جيدة.',
  feedingTask: 'التغذية: {{feedType}}',
  medicationTask: 'الدواء: {{medicationName}}',
  amount: 'الكمية',
  dosage: 'الجرعة',
  moreTasksToday: '+{{count}} مهام أخرى اليوم',
  
  // Premium Banner
  premium: 'مميز',
  upgradeToPremium: 'الترقية إلى المميز',
  unlimitedAnimals: 'حلال غير محدود',
  advancedAnalytics: 'تحليلات متقدمة',
  upgradeNow: 'ترقية الآن',
  
  // AI Onboarding
  meetYourAIAssistant: 'تعرف على مساعدك الذكي',
  aiAssistantDescription: 'احصل على خطط تدريب مخصصة ورؤى صحية ونصائح تدريبية مدعومة بالذكاء الاصطناعي.',
  readinessScores: 'درجات الاستعداد',
  trainingPlans: 'خطط التدريب',
  logFirstTrainingSession: 'سجل جلسة التدريب الأولى',
  
  // Speed Monitor
  speed: 'السرعة',
  currentSpeed: 'السرعة الحالية',
  refresh: 'تحديث',
  lastUpdated: 'آخر تحديث',
  
  // Animals Screen
  id: 'الهوية',
  ageYears: '{{age}} سنة',
  gps: 'جي بي إس',
  selectLanguage: 'اختر اللغة',
  changingLanguage: 'جاري تغيير اللغة...',
  
  // Profile Screen
  profileSettings: 'الإعدادات',
  profileAdvancedSettings: 'إعدادات متقدمة',
  profilePrivacySettings: 'إعدادات الخصوصية',
  profileMfaSettings: 'المصادقة الثنائية',
  profileUpgradeToPremium: 'الترقية إلى بريميوم',
  profileHelpSupport: 'المساعدة والدعم',
  profileFAQ: 'الأسئلة الشائعة',
  profileContactSupport: 'اتصل بالدعم',
  profileSignOut: 'تسجيل الخروج',
  profileSignOutConfirm: 'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
  profileCancel: 'إلغاء',
  profileVersion: 'الإصدار',
  profileSignOutFailed: 'فشل في تسجيل الخروج',
  
  // Biometric Authentication
  biometricLogin: 'تسجيل الدخول البيومتري',
  signInWithFingerprint: 'تسجيل الدخول ببصمة الإصبع',
  signInWithFaceId: 'تسجيل الدخول بـ Face ID',
  signInWithBiometric: 'تسجيل الدخول البيومتري',
  biometricAuthenticationFailed: 'فشل في المصادقة البيومترية',
  biometricNotAvailable: 'المصادقة البيومترية غير متاحة على هذا الجهاز',
  biometricNotEnabled: 'تسجيل الدخول البيومتري غير مفعل',
  biometricSetupRequired: 'إعداد بيومتري مطلوب',
  biometricSetupDescription: 'لاستخدام تسجيل الدخول البيومتري، يرجى إعداد {{biometricType}} في إعدادات جهازك أولاً.',
  enableBiometricLogin: 'فعل تسجيل الدخول البيومتري للوصول السريع والآمن لحسابك.',
  biometricLoginEnabled: 'تم تفعيل تسجيل الدخول البيومتري بنجاح!',
  biometricLoginDisabled: 'تم إلغاء تسجيل الدخول البيومتري',
  disableBiometricLogin: 'إلغاء تسجيل الدخول البيومتري',
  disableBiometricConfirm: 'هل أنت متأكد من رغبتك في إلغاء تسجيل الدخول البيومتري؟ ستحتاج لاستخدام كلمة المرور لتسجيل الدخول.',
  biometricDataSecure: 'بياناتك البيومترية تبقى آمنة على جهازك ولا يتم مشاركتها أبداً.',
  enableBiometricPrompt: 'تفعيل تسجيل الدخول البيومتري',
  biometricSetupConfirm: 'استخدم بياناتك البيومترية لتأكيد الإعداد',
  biometricSignInPrompt: 'تسجيل الدخول إلى HoofBeat',
  biometricSignInSubtitle: 'استخدم بياناتك البيومترية للوصول لحسابك',
  biometricSetupCancelled: 'تم إلغاء الإعداد البيومتري',
  biometricRefreshSession: 'يرجى تسجيل الدخول بكلمة المرور لتحديث جلستك، ثم تفعيل تسجيل الدخول البيومتري مرة أخرى.',
  biometricCredentialsFirst: 'يرجى التحقق من بياناتك قبل تفعيل تسجيل الدخول البيومتري',
  biometricSignInPasswordFirst: 'يرجى تسجيل الدخول بكلمة المرور أولاً لتفعيل تسجيل الدخول البيومتري',
  or: 'أو',
  
  // AI Guidance
  aiGuidanceTitle: 'احصل على المزيد من مساعدك الذكي',
  aiGuidanceDescription: 'يحتاج مساعدك الذكي إلى المزيد من البيانات لتقديم رؤى وتوصيات مخصصة.',
  aiGuidanceWhatYouGet: 'ما ستحصل عليه:',
  aiGuidancePersonalizedPlans: 'خطط تدريب مخصصة',
  aiGuidanceHealthInsights: 'رؤى ونبيهات صحية',
  aiGuidancePerformanceTracking: 'تتبع الأداء',
  aiGuidanceSmartRecommendations: 'توصيات ذكية',
  aiGuidanceDataProgress: 'تقدم البيانات',
  aiGuidanceCompleteData: '{{percentage}}% مكتمل',
  aiGuidanceLogTrainingSession: 'تسجيل جلسة تدريب',
  aiGuidanceRecordVitals: 'تسجيل العلامات الحيوية',
  aiGuidanceUpdateFeedingSchedule: 'تحديث جدول التغذية',
  aiGuidanceStartLogging: 'ابدأ بتسجيل البيانات لفتح الرؤى الذكية',
  aiGuidanceMoreDataNeeded: 'مطلوب المزيد من البيانات للرؤى الذكية',
  aiGuidanceGetStarted: 'ابدأ بتسجيل جلسة التدريب الأولى أو تسجيل العلامات الحيوية.',
  
  // AI Chat
  aiChat: 'محادثة ذكية',
  aiChatTitle: 'تحدث مع المساعد الذكي',
  aiChatPlaceholder: 'اسأل عن صحة حيوانك أو تدريبه أو رعايته...',
  aiChatSend: 'إرسال',
  aiChatAttachment: 'إرفاق صورة',
  aiChatCamera: 'التقاط صورة',
  aiChatGallery: 'اختيار من المعرض',
  aiChatAnalyzing: 'جاري التحليل...',
  aiChatTyping: 'الذكاء الاصطناعي يكتب...',
  aiChatNoMessages: 'لا توجد رسائل بعد',
  aiChatStartConversation: 'ابدأ محادثة مع مساعدك الذكي حول {{animalName}}',
  aiChatImageAnalysis: 'تحليل الصورة',
  aiChatCopyMessage: 'نسخ الرسالة',
  aiChatShareMessage: 'مشاركة الرسالة',
  aiChatMessageCopied: 'تم نسخ الرسالة للحافظة',
  aiChatUploadingImage: 'جاري رفع الصورة...',
  aiChatImageUploadFailed: 'فشل في رفع الصورة',
  aiChatSendingMessage: 'جاري إرسال الرسالة...',
  aiChatMessageFailed: 'فشل في إرسال الرسالة',
  aiChatRetry: 'إعادة المحاولة',
  aiChatMaxFileSize: 'يجب أن يكون حجم الملف أقل من 10 ميجابايت',
  aiChatUnsupportedFormat: 'تنسيق ملف غير مدعوم',
  
  // Dehydration/Hydration Monitoring
  dehydrationMonitoring: 'مراقبة الجفاف',
  hydrationMonitoring: 'مراقبة الترطيب',
  optimal: 'مثالي',
  mildDehydration: 'جفاف خفيف',
  moderateDehydration: 'جفاف متوسط',
  severeDehydration: 'جفاف شديد',
  unknown: 'غير معروف',
  noHydrationData: 'لا توجد بيانات ترطيب',
  takeReadingToStart: 'اتخذ قراءة لبدء المراقبة',
  bodyTemp: 'حرارة الجسم',
  bioimpedance: 'المقاومة الحيوية',
  signalQuality: 'جودة الإشارة',
  takeReading: 'أخذ قراءة',
  reading: 'جاري القراءة...',
  startMonitoring: 'بدء المراقبة',
  stopMonitoring: 'إيقاف المراقبة',
  aiAnalysis: 'تحليل ذكي',
  overallStatus: 'الحالة العامة',
  trend: 'الاتجاه',
  avgHydration: 'متوسط الترطيب',
  variability: 'التباين',
  aiAlerts: 'تنبيهات ذكية',
  recommendations: 'توصيات',
  refreshAnalysis: 'تحديث التحليل',
  recentReadings: 'قراءات حديثة',
  failedToLoadHydrationData: 'فشل في تحميل بيانات الترطيب',
  retry: 'إعادة المحاولة',
  loadingHydrationData: 'جاري تحميل بيانات الترطيب...',
  monitoringSessionActive: 'جلسة مراقبة نشطة',
  readings: 'قراءات',
  excellent: 'ممتاز',
  good: 'جيد',
  fair: 'مقبول',
  poor: 'ضعيف',
  critical: 'حرج',
  stable: 'مستقر',
  improving: 'يتحسن',
  declining: 'يتدهور',
  concerning: 'مقلق',
  hydrationReadingRecorded: 'تم تسجيل قراءة الترطيب',
  failedToSaveHydrationReading: 'فشل في حفظ قراءة الترطيب',
  readingUpdated: 'تم تحديث القراءة',
  failedToUpdateReading: 'فشل في تحديث القراءة',
  readingDeleted: 'تم حذف القراءة',
  failedToDeleteReading: 'فشل في حذف القراءة',
  startedHydrationMonitoring: 'تم بدء مراقبة الترطيب',
  monitoringSessionCompleted: 'تم إنهاء جلسة المراقبة',
  duration: 'المدة',
  min: 'دقيقة',
  failedToCaptureReading: 'فشل في التقاط قراءة المستشعر',
  criticalDehydrationDetected: 'تم اكتشاف جفاف حرج',
  moderateDehydrationDetected: 'تم اكتشاف جفاف متوسط',
  
  // Authentication
  createAccount: 'إنشاء حساب',
  resetPassword: 'إعادة تعيين كلمة المرور',
  signInToAccount: 'تسجيل الدخول لحسابك',
  disable: 'إلغاء',
  enabled: 'مفعل',
  disabled: 'معطل',
  failedToUpdateBiometricSettings: 'فشل في تحديث إعدادات البيومتري',
  useYourBiometricQuickly: 'استخدم {{biometricType}} لتسجيل الدخول بسرعة وأمان لحسابك.',
  enableBiometricQuickAccess: 'فعل تسجيل الدخول بـ {{biometricType}} للوصول السريع والآمن.',
  
  // Error Messages
  userNotFound: 'المستخدم غير موجود',
  profileCreationFailed: 'فشل في إنشاء ملف المستخدم',
  profileLoadFailed: 'فشل في تحميل ملف المستخدم',
  sessionExpired: 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.',
  networkError: 'خطأ في الشبكة. يرجى التحقق من اتصالك.',
  unexpectedError: 'حدث خطأ غير متوقع',
  
  // Common UI
  loading: 'جاري التحميل...',
  error: 'خطأ',
  success: 'نجح',
  warning: 'تحذير',
  info: 'معلومات',
  
  // Dehydration Monitoring
  dehydrationMonitoring: 'مراقبة الجفاف',
  hydrationStatus: 'حالة الترطيب',
  optimal: 'مثالي',
  mildDehydration: 'جفاف خفيف',
  moderateDehydration: 'جفاف متوسط',
  severeDehydration: 'جفاف شديد',
  criticalDehydration: 'جفاف حرج',
  noDataAvailable: 'لا توجد بيانات متاحة',
  bodyTemp: 'درجة حرارة الجسم',
  bioimpedance: 'المقاومة الحيوية',
  signalQuality: 'جودة الإشارة',
  takeReading: 'أخذ قراءة',
  liveMonitoring: 'المراقبة المباشرة',
  startMonitoring: 'بدء المراقبة',
  stopMonitoring: 'إيقاف المراقبة',
  aiAnalysisTitle: 'تحليل الذكاء الاصطناعي',
  aiAlertsTitle: 'تنبيهات الذكاء الاصطناعي',
  aiRecommendationsTitle: 'توصيات الذكاء الاصطناعي',
  refreshAnalysis: 'تحديث التحليل',
  recentReadings: 'القراءات الأخيرة',
  dehydrationAnalysisStarted: 'بدأ تحليل الجفاف...',
  dehydrationAnalysisFailed: 'فشل تحليل الجفاف. يرجى المحاولة مرة أخرى.',
  
  // Biometric Authentication
  biometric: 'القياسات الحيوية',
  fingerprint: 'بصمة الإصبع',
  faceId: 'معرف الوجه',
  biometricLogin: 'تسجيل دخول {{type}}',
  enabled: 'مفعل',
  disabled: 'معطل',
  disableBiometricLogin: 'تعطيل تسجيل الدخول البيومتري',
  disableBiometricConfirmation: 'هل أنت متأكد من أنك تريد تعطيل تسجيل الدخول البيومتري؟ ستحتاج إلى استخدام كلمة المرور للدخول.',
  cancel: 'إلغاء',
  disable: 'تعطيل',
  signInPasswordFirstBiometric: 'يرجى تسجيل الدخول بكلمة المرور أولاً لتفعيل تسجيل الدخول البيومتري',
  failedUpdateBiometricSettings: 'فشل في تحديث إعدادات القياسات الحيوية',
  biometricSetupRequired: 'إعداد القياسات الحيوية مطلوب',
  biometricSetupInstructions: 'لاستخدام تسجيل الدخول البيومتري، يرجى إعداد {{type}} في إعدادات جهازك أولاً.',
  biometricEnabledDescription: 'استخدم {{type}} لتسجيل الدخول بسرعة وأمان إلى حسابك.',
  biometricDisabledDescription: 'فعل تسجيل دخول {{type}} للوصول السريع والآمن إلى حسابك.',
  biometricSecurityNote: 'بياناتك البيومترية تبقى آمنة على جهازك ولا يتم مشاركتها أبداً.',
  
  // AI Guidance Component
  logTrainingSession: 'تسجيل جلسة تدريب',
  recordTrainingActivities: 'سجل أنشطة التدريب للحصول على رؤى الأداء',
  recordVitals: 'تسجيل العلامات الحيوية',
  logHealthMeasurements: 'سجل القياسات الصحية لتحليل الذكاء الاصطناعي الصحي',
  updateFeedingSchedule: 'تحديث جدول التغذية',
  setFeedingTimes: 'حدد أوقات التغذية للحصول على توصيات التغذية',
  aiInsightsFor: 'رؤى الذكاء الاصطناعي لـ {{name}}',
  helpMeLearnAbout: 'ساعدني في التعلم عن {{name}} لتقديم رؤى أفضل',
  needMoreInformation: 'أحتاج إلى مزيد من المعلومات حول {{name}} لإنتاج رؤى شخصية للذكاء الاصطناعي. أكمل أي من الإجراءات أدناه لفتح التحليلات القوية!',
  getPersonalizedRecommendations: 'احصل على توصيات شخصية لـ {{name}}',
  whatYoullGet: 'ما ستحصل عليه:',
  personalizedHealthAssessments: '• تقييمات صحية شخصية',
  trainingPerformanceInsights: '• رؤى أداء التدريب',
  nutritionCareRecommendations: '• توصيات التغذية والرعاية',
  earlyHealthAlerts: '• تنبيهات وتحذيرات صحية مبكرة',
  
  // Additional Missing Keys
  noHydrationData: 'لا توجد بيانات ترطيب متاحة',
  takeReadingToStart: 'خذ قراءة لبدء المراقبة',
  reading: 'جاري القراءة...',
  failedToLoadHydrationData: 'فشل في تحميل بيانات الترطيب',
  retry: 'إعادة المحاولة',
  loadingHydrationData: 'جاري تحميل بيانات الترطيب...',
  overallStatus: 'الحالة العامة',
  trend: 'الاتجاه',
  avgHydration: 'متوسط الترطيب',
  variability: 'التباين',
  recommendations: 'التوصيات',
  unknown: 'غير معروف',
  excellent: 'ممتاز',
  good: 'جيد',
  concerning: 'مثير للقلق',
  critical: 'حرج',
  improving: 'يتحسن',
  declining: 'يتراجع',
  stable: 'مستقر',
  aiAlerts: 'تنبيهات الذكاء الاصطناعي',
  
  // Biometric Authentication (ensure all keys exist)
  enableBiometricLogin: 'تفعيل تسجيل الدخول البيومتري',
  biometricAuthentication: 'المصادقة البيومترية',
  
  // AI Assistant Screen
  aiAssistant: 'مساعد الذكاء الاصطناعي',
  animalNotFound: 'الحيوان غير موجود',
  dataCompleteness: 'اكتمال البيانات',
  analyzing: 'جاري التحليل...',
  getAIAnalysis: 'احصل على تحليل الذكاء الاصطناعي',
  readinessScore: 'نقاط الاستعداد',
  healthAssessment: 'التقييم الصحي',
  hydrationAnalysis: 'تحليل الترطيب',
  trainingPlan: 'خطة التدريب',
  coachingTips: 'نصائح التدريب',
  readyForAIAnalysis: 'جاهز لتحليل الذكاء الاصطناعي',
  greatYouHaveData: 'رائع! لديك بيانات لـ {{name}}. اطلب تحليل الذكاء الاصطناعي للحصول على رؤى شخصية.',
  alerts: 'التنبيهات',
  moreTips: '+{{count}} نصائح أخرى',
  
  // AI Widget Keys
  aiWidgetViewAll: 'عرض الكل',
  aiWidgetInsights: 'رؤى ذكية',
  aiWidgetReadiness: 'الجاهزية',
  aiWidgetNoAIData: 'لا توجد بيانات ذكية',
  aiWidgetLogTraining: 'تسجيل التدريب',
  aiWidgetLiveStatus: 'الحالة المباشرة',
  aiWidgetAvailable: 'متاح',
  aiWidgetHealth: 'الصحة',
  aiWidgetLatestTip: 'أحدث نصيحة',
  aiWidgetChecking: 'جاري الفحص...',
  aiWidgetLiveCheckIn: 'فحص مباشر',
  aiWidgetNew: 'جديد',
  
  // Multi-Animal AI Dashboard Keys (Arabic)
  stableOverview: 'نظرة عامة على الإسطبل',
  healthyAnimals: 'الحيوانات الصحية',
  totalAnimals: 'إجمالي الحيوانات',
  yourAnimals: 'حيواناتك',
  criticalAlertsRequireAttention: 'التنبيهات الحرجة تتطلب الانتباه',
  criticalAlertsNeedImmediate: 'تنبيهات حرجة تحتاج اهتمام فوري',
  reviewAlerts: 'مراجعة التنبيهات',
  recentActivity: 'النشاط الأخير',
  vitalsRecorded: 'العلامات الحيوية المسجلة',
  recordsToday: 'سجلات اليوم',
  aiAnalysisCompleted: 'التحليل الذكي مكتمل',
  assessments: 'التقييمات',
  trendsIdentified: 'الاتجاهات المحددة',
  patterns: 'الأنماط',
  healthInsights: 'رؤى صحية',
  avgHealthScore: 'متوسط النقاط الصحية',
  acrossAllAnimals: 'عبر جميع الحيوانات',
  riskFactors: 'عوامل الخطر',
  highRiskFactors: 'عوامل خطر عالية',
  refreshData: 'تحديث البيانات',
  aiInsights: 'رؤى ذكية',
  viewTrends: 'عرض الاتجاهات',
  noAnimalsFound: 'لم يتم العثور على حيوانات',
  addAnimalToStart: 'أضف حيوان للبدء',
  activeAlerts: 'التنبيهات النشطة',
  recordVitals: 'تسجيل العلامات الحيوية',
  aiHealthDashboard: 'لوحة الصحة الذكية',
  quickActions: 'إجراءات سريعة',
  viewAll: 'عرض الكل',
  viewDetails: 'عرض التفاصيل',
  
  // Animal Detail Screen Keys (Arabic)
  animalDetailLatestVitals: 'أحدث العلامات الحيوية',
  animalDetailDeviceOnly: 'الجهاز فقط',
  animalDetailRecordedOn: 'مسجل في',
  animalDetailTemperature: 'درجة الحرارة',
  animalDetailHeartRate: 'معدل ضربات القلب',
  animalDetailRespiration: 'التنفس',
  animalDetailWeight: 'الوزن',
  animalDetailNotes: 'ملاحظات',
  animalDetailNoVitalsRecorded: 'لم يتم تسجيل علامات حيوية بعد',
  animalDetailConnectDevice: 'اربط جهاز لبدء المراقبة',
  animalDetailMedications: 'الأدوية',
  animalDetailAdd: 'إضافة',
  animalDetailPrintMedicationSchedule: 'طباعة جدول الأدوية',
  animalDetailNoMedicationsAdded: 'لم تتم إضافة أدوية بعد',
  animalDetailAddMedication: 'إضافة دواء',
  animalDetailVaccinations: 'التطعيمات',
  animalDetailDue: 'مستحق',
  animalDetailNoRenewal: 'لا تجديد',
  animalDetailPrintVaccinationRecord: 'طباعة سجل التطعيمات',
  animalDetailNoVaccinationsRecorded: 'لم يتم تسجيل تطعيمات بعد',
  animalDetailAddVaccination: 'إضافة تطعيم',
  animalDetailMicrochipRequired: 'معرف الشريحة الدقيقة مطلوب للتطعيمات',
  animalDetailFeedingSchedule: 'جدول التغذية',
  animalDetailViewAll: 'عرض الكل',
  animalDetailPrintFeedingSchedule: 'طباعة جدول التغذية',
  animalDetailNoFeedingSchedule: 'لم يتم تعيين جدول تغذية',
  animalDetailSetFeedingSchedule: 'تعيين جدول التغذية',
  animalDetailTrainingSessions: 'جلسات التدريب',
  animalDetailLoadingSessions: 'جاري تحميل الجلسات...',
  animalDetailLogNewSession: 'تسجيل جلسة جديدة',
  animalDetailNoTrainingSessions: 'لا توجد جلسات تدريب بعد',
  animalDetailTrainingDescription: 'ابدأ بتتبع جلسات التدريب لمراقبة التقدم والأداء.',
  animalDetailLogFirstSession: 'تسجيل الجلسة الأولى',
};