// AI and machine learning features - English
export const ai = {
  // AI Assistant
  aiAssistant: 'AI Assistant',
  aiChat: 'AI Chat',
  aiChatTitle: 'Chat with AI Assistant',
  aiChatPlaceholder: 'Ask about your animal\'s health, training, or care...',
  aiChatSend: 'Send',
  aiChatAttachment: 'Attach Image',
  aiChatCamera: 'Take Photo',
  aiChatGallery: 'Choose from Gallery',
  aiChatAnalyzing: 'Analyzing...',
  aiChatTyping: 'AI is typing...',
  aiChatNoMessages: 'No messages yet',
  aiChatStartConversation: 'Start a conversation with your AI assistant about {{animalName}}',
  aiChatImageAnalysis: 'Image Analysis',
  aiChatCopyMessage: 'Copy Message',
  aiChatShareMessage: 'Share Message',
  aiChatMessageCopied: 'Message copied to clipboard',
  aiChatUploadingImage: 'Uploading image...',
  aiChatImageUploadFailed: 'Failed to upload image',
  aiChatSendingMessage: 'Sending message...',
  aiChatMessageFailed: 'Failed to send message',
  aiChatRetry: 'Retry',
  aiChatMaxFileSize: 'File size must be less than 10MB',
  aiChatUnsupportedFormat: 'Unsupported file format',
  
  // AI Onboarding
  meetYourAIAssistant: 'Meet Your AI Assistant',
  aiAssistantDescription: 'Get personalized training plans, health insights, and coaching tips powered by AI.',
  readinessScores: 'Readiness Scores',
  trainingPlans: 'Training Plans',
  logFirstTrainingSession: 'Log Your First Training Session',
  
  // AI Guidance
  aiGuidanceTitle: 'Get More from Your AI Assistant',
  aiGuidanceDescription: 'Your AI assistant needs more data to provide personalized insights and recommendations.',
  aiGuidanceWhatYouGet: 'What you\'ll get:',
  aiGuidancePersonalizedPlans: 'Personalized training plans',
  aiGuidanceHealthInsights: 'Health insights and alerts',
  aiGuidancePerformanceTracking: 'Performance tracking',
  aiGuidanceSmartRecommendations: 'Smart recommendations',
  aiGuidanceDataProgress: 'Data Progress',
  aiGuidanceCompleteData: '{{percentage}}% complete',
  aiGuidanceLogTrainingSession: 'Log a Training Session',
  aiGuidanceRecordVitals: 'Record Vitals',
  aiGuidanceUpdateFeedingSchedule: 'Update Feeding Schedule',
  aiGuidanceStartLogging: 'Start logging data to unlock AI insights',
  aiGuidanceMoreDataNeeded: 'More data needed for AI insights',
  aiGuidanceGetStarted: 'Get started by logging your first training session or recording vitals.',
  
  // AI Analysis
  dataCompleteness: 'Data completeness',
  analyzing: 'Analyzing...',
  getAIAnalysis: 'Get AI Analysis',
  readinessScore: 'Readiness Score',
  healthAssessment: 'Health Assessment',
  hydrationAnalysis: 'Hydration Analysis',
  trainingPlan: 'Training Plan',
  coachingTips: 'Coaching Tips',
  readyForAIAnalysis: 'Ready for AI Analysis',
  greatYouHaveData: 'Great! You have data for {{name}}. Request an AI analysis to get personalized insights.',
  alerts: 'Alerts',
  moreTips: '+{{count}} more tips',
  
  // AI Widget
  aiWidgetViewAll: 'View All',
  aiWidgetInsights: 'AI Insights',
  aiWidgetReadiness: 'Readiness',
  aiWidgetNoAIData: 'No AI Data',
  aiWidgetLogTraining: 'Log Training',
  aiWidgetLiveStatus: 'Live Status',
  aiWidgetAvailable: 'Available',
  aiWidgetHealth: 'Health',
  aiWidgetLatestTip: 'Latest Tip',
  aiWidgetChecking: 'Checking...',
  aiWidgetLiveCheckIn: 'Live Check-in',
  aiWidgetNew: 'New',
  
  // AI Insights
  aiInsights: 'AI Insights',
  aiInsightsFor: 'AI Insights for {{name}}',
  helpMeLearnAbout: 'Help me learn about {{name}} to provide better insights',
  needMoreInformation: 'I need more information about {{name}} to generate personalized AI insights. Complete any of the actions below to unlock powerful analytics!',
  getPersonalizedRecommendations: 'Get personalized recommendations for {{name}}',
  whatYoullGet: 'What you\'ll get:',
  personalizedHealthAssessments: '• Personalized health assessments',
  trainingPerformanceInsights: '• Training performance insights',
  nutritionCareRecommendations: '• Nutrition and care recommendations',
  earlyHealthAlerts: '• Early health alerts and warnings',
  
  // AI Actions
  logTrainingSession: 'Log a Training Session',
  recordTrainingActivities: 'Record training activities to get performance insights',
  recordVitals: 'Record Vitals',
  logHealthMeasurements: 'Log health measurements for AI health analysis',
  updateFeedingSchedule: 'Update Feeding Schedule',
  setFeedingTimes: 'Set feeding times for nutrition recommendations',
  
  // Quick Actions
  quickActions: 'Quick Actions',
  healthTrends: 'Health Trends',
  diseaseRisk: 'Disease Risk',
  stressAnalysis: 'Stress Analysis',
  sleepMonitoring: 'Sleep Monitoring',
  environmental: 'Environmental',
  predictiveInsights: 'Predictive Insights',
  assistant: 'AI Assistant',
  
  // Additional missing keys
  environmentalAnalysis: 'Environmental Analysis',
  behavioralAnalysis: 'Behavioral Analysis',
  aiHealthDashboard: 'AI Health Dashboard',
  healthDashboard: 'Health Dashboard',
  smartAlerts: 'Smart Alerts',
  diseaseRiskAssessment: 'Disease Risk Assessment',
  healthScoreDetails: 'Health Score Details',
  trendsOverview: 'Trends Overview',
  riskOverview: 'Risk Overview',
  
  // Health Dashboard
  healthDashboard: 'Health Dashboard',
  overallHealthStatus: 'Overall Health Status',
  totalAnimals: 'Total Animals',
  healthScore: 'Health Score',
  activeAlerts: 'Active Alerts',
  recentAnalyses: 'Recent Analyses',
  animalNotSelected: 'No animal selected',
  selectAnimalForDetails: 'Select an animal to view detailed health information',
  viewHealthDetails: 'View Health Details',
  lastUpdated: 'Last updated',
  noHealthData: 'No health data available',
  tapToViewDetails: 'Tap to view details',
  
  // AI Widget keys
  aiWidgetInsights: 'AI Insights',
  aiWidgetHealth: 'Health Status',
  aiWidgetLiveCheckIn: 'Live Check-in',
  aiWidgetViewAll: 'View All',
  
  // Missing keys from console errors
  logFirstTrainingSession: 'Log Your First Training Session',
  trainingPlans: 'Training Plans',
  readinessScores: 'Readiness Scores',
  aiAssistantDescription: 'Get personalized training plans, health insights, and coaching tips powered by AI.',
  meetYourAIAssistant: 'Meet Your AI Assistant',
  
  // Sleep Analysis Keys
  analyzeSleep: 'Analyze Sleep',
  runFirstSleepAnalysis: 'Run your first sleep analysis to get insights',
  noSleepAnalysis: 'No Sleep Analysis Yet',
};