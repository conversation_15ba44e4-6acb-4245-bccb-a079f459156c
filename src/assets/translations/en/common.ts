// Common UI elements and actions - English
export const common = {
  // Common actions
  add: 'Add',
  edit: 'Edit',
  delete: 'Delete',
  save: 'Save',
  cancel: 'Cancel',
  confirm: 'Confirm',
  back: 'Back',
  next: 'Next',
  previous: 'Previous',
  loading: 'Loading...',
  error: 'Error',
  success: 'Success',
  warning: 'Warning',
  info: 'Info',
  retry: 'Retry',
  refresh: 'Refresh',
  refreshing: 'Refreshing...',
  viewDetails: 'View Details',
  viewAll: 'View All',
  
  // Status
  active: 'Active',
  inactive: 'Inactive',
  online: 'Online',
  offline: 'Offline',
  connected: 'Connected',
  disconnected: 'Disconnected',
  enabled: 'Enabled',
  disabled: 'Disabled',
  
  // Time and dates
  today: 'Today',
  yesterday: 'Yesterday',
  thisWeek: 'This Week',
  thisMonth: 'This Month',
  thisYear: 'This Year',
  
  // Units
  celsius: '°C',
  fahrenheit: '°F',
  kg: 'kg',
  lbs: 'lbs',
  cm: 'cm',
  inches: 'inches',
  minutes: 'minutes',
  hours: 'hours',
  days: 'days',
  
  // Days of week
  monday: 'Monday',
  tuesday: 'Tuesday',
  wednesday: 'Wednesday',
  thursday: 'Thursday',
  friday: 'Friday',
  saturday: 'Saturday',
  sunday: 'Sunday',
  
  // Months
  january: 'January',
  february: 'February',
  march: 'March',
  april: 'April',
  may: 'May',
  june: 'June',
  july: 'July',
  august: 'August',
  september: 'September',
  october: 'October',
  november: 'November',
  december: 'December',
  
  // Health Status
  excellent: 'Excellent',
  good: 'Good',
  fair: 'Fair',
  poor: 'Poor',
  critical: 'Critical',
  concerning: 'Concerning',
  unknown: 'Unknown',
  
  // Error Messages
  failedToLoad: 'Failed to load',
  failedToSave: 'Failed to save',
  failedToUpdate: 'Failed to update',
  failedToDelete: 'Failed to delete',
  failedToConnect: 'Failed to connect',
  animalNotFound: 'Animal not found',
  missingAnimalId: 'Animal ID is required to view this screen',
  deviceNotFound: 'Device not found',
  userNotFound: 'User not found',
  failedToLoadHealthData: 'Failed to load health data',
  
  // Success Messages
  savedSuccessfully: 'Saved successfully',
  updatedSuccessfully: 'Updated successfully',
  deletedSuccessfully: 'Deleted successfully',
  connectedSuccessfully: 'Connected successfully',
  
  // Action Labels
  goBack: 'Go Back',
  tryAgainButton: 'Try Again',
  print: 'Print',
  printed: 'Printed',
  shared: 'Shared',
  downloaded: 'Downloaded',
  scanAgain: 'Scan Again',
  chatWithSupport: 'Chat with Support',
  contactUs: 'Contact Us',
  addMore: 'Add More',
  
  // Time Periods
  sevenDays: '7 Days',
  thirtyDays: '30 Days',
  ninetyDays: '90 Days',
  
  // Alerts
  heartRateAlert: 'Heart Rate Alert',
  temperatureAlert: 'Temperature Alert',
  locationAlert: 'Location Alert',
  healthAlert: 'Health Alert',
  
  // Permissions & Camera
  cameraPermissionRequired: 'Camera permission is required to scan barcodes',
  requestingCameraPermission: 'Requesting camera permission...',
  noAccessToCamera: 'No access to camera',
  
  // Barcode Scanner
  scanDeviceBarcode: 'Scan Device Barcode',
  positionBarcodeInFrame: 'Position the barcode within the frame to scan',
  invalidBarcodeFormat: 'Invalid device barcode format',
  deviceBarcodeScanned: 'Device barcode scanned',
  
  // FAQ
  frequentlyAskedQuestions: 'Frequently Asked Questions',
  stillHaveQuestions: 'Still have questions? Contact our support team.',
  openingChatSupport: 'Opening chat support...',
  
  // Contact
  messageSent: 'Message sent successfully',
  messageSentTitle: 'Message Sent',
  messageSentDescription: "Your message has been <NAME_EMAIL>. We'll get back to you soon!",
  failedToSendMessage: 'Failed to send message. Please try again.',
  errorSendingMessageTitle: 'Error Sending Message',
  errorSendingMessageDescription: 'There was a problem sending your message. Please try again or use the live chat option.',
  chatError: 'Chat Error',
  failedToOpenChat: 'Failed to open chat. Please try again.',
  
  // Privacy Settings
  privacySettings: 'Privacy Settings',
  privacySettingsSaved: 'Privacy settings saved',
  accountDeleted: 'Account deleted',
  locationTracking: 'Location Tracking',
  locationTrackingDescription: 'Allow the app to track your animals\' locations. This is required for location alerts.',
  shareDataWithVets: 'Share Data with Vets',
  shareDataWithVetsDescription: 'Allow veterinarians to access your animals\' health records when you share them.',
  analytics: 'Analytics',
  analyticsDescription: 'Help us improve by allowing anonymous usage data collection.',
  saveSettings: 'Save Settings',
  
  // Health Dashboard
  healthOverview: 'Health Overview',
  totalAnimals: 'Total Animals',
  healthy: 'Healthy',
  needsAttention: 'Needs Attention',
  activeAlerts: 'Active Alerts',
  yourAnimals: 'Your Animals',
  addAnimal: 'Add Animal',
  addYourFirstAnimal: 'Add Your First Animal',
  welcomeToAIHealthDashboard: 'Welcome to AI Health Dashboard',
  addFirstAnimalDescription: 'Add your first animal to start monitoring their health with AI-powered insights.',
  startMonitoring: 'Start monitoring',
  
  // Score and Analysis
  score: 'Score',
  aiAnalysis: 'AI Analysis',
  alerts: 'Alerts',
  alert: 'alert',
  trends: 'Trends',
  tracked: 'Tracked',
  criticalAlertsRequireAttention: 'critical alert(s) require attention',
  lastUpdated: 'Last updated',
  updated: 'Updated',
  
  // FAQ Questions and Answers
  whatIsHoofBeat: 'What is HoofBeat?',
  whatIsHoofBeatAnswer: 'HoofBeat is a comprehensive livestock management app designed to help you track and monitor the health and well-being of your animals. It provides tools for recording vital signs, managing feeding schedules, tracking medications, and monitoring vaccinations.',
  howToAddNewAnimal: 'How do I add a new animal?',
  howToAddNewAnimalAnswer: 'To add a new animal, navigate to the Animals tab and tap the "+" button. Fill in the required information such as name, type, and breed, then tap "Save Animal". You can also add optional details like age, gender, color, and identification information.',
  canRecordVitalsManually: 'Can I record vital signs manually?',
  canRecordVitalsManuallyAnswer: 'Yes, you can record vital signs manually by going to the Home tab, tapping "Record Vitals", selecting the animal, and entering the vital information. You can record temperature, heart rate, respiration rate, and weight.',
  freePremiumDifference: 'What is the difference between the free and premium versions?',
  freePremiumDifferenceAnswer: 'The free version allows you to manage up to 3 animals with basic health tracking features. The premium version offers unlimited animals, advanced health analytics, real-time monitoring via Bluetooth/WiFi, cloud data synchronization, vaccination tracking with alerts, and the ability to print and export reports.',
  howToConnectDevice: 'How do I connect a device to monitor my animal?',
  howToConnectDeviceAnswer: 'To connect a device, go to the Devices tab and tap "Scan for Devices". Make sure your device is in pairing mode. Once detected, select the device and follow the on-screen instructions to pair it with your animal.',
  howToSetupFeeding: 'How do I set up feeding schedules?',
  howToSetupFeedingAnswer: 'To set up a feeding schedule, go to the animal\'s detail page and tap "Feed Schedule". Tap "Add Feeding Schedule" and enter the feed type, amount, time, and any notes. You can also set reminders for feeding times.',
  howToTrackMedications: 'How do I track medications?',
  howToTrackMedicationsAnswer: 'To track medications, go to the animal\'s detail page and select the "Medications" tab. Tap "Add Medication" and enter the medication details including name, dosage, frequency, start date, and time. You can set reminders and mark medications as completed when administered.',
  canExportPrintRecords: 'Can I export or print my animal\'s health records?',
  canExportPrintRecordsAnswer: 'Yes, premium users can export and print health records. Navigate to the animal\'s detail page, select the data you want to print (vitals, medications, or vaccinations), and tap the print icon. You can then print the document or save it as a PDF.',
  howToGetSupport: 'How do I get support if I have issues with the app?',
  howToGetSupportAnswer: 'You can get support by going to the Profile tab and selecting "Chat with Us" to start a live chat with our support team. You can also visit our FAQ section or contact us through the "Contact Us" option.',
  howToCancelSubscription: 'How do I cancel my subscription?',
  howToCancelSubscriptionAnswer: 'To cancel your subscription, go to the Profile tab, select "Manage Subscription", and tap "Cancel Subscription". Follow the on-screen instructions to complete the cancellation process. Your premium features will remain active until the end of your current billing period.',
};