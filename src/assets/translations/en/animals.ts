// Animal management - English
export const animals = {
  // Animal management
  animalName: 'Animal Name',
  animalType: 'Animal Type',
  breed: 'Breed',
  age: 'Age',
  gender: 'Gender',
  weight: 'Weight',
  color: 'Color',
  microchipId: 'Microchip ID',
  registrationNumber: 'Registration Number',
  
  // Animals Overview
  myAnimals: 'My Animals',
  seeAll: 'See All',
  addAnimal: 'Add Animal',
  noAnimalsYet: 'No animals added yet',
  addFirstAnimal: 'Add Your First Animal',
  id: 'ID',
  ageYears: '{{age}} yrs',
  gps: 'GPS',
  
  // Animal Detail Screen
  animalDetailLatestVitals: 'Latest Vitals',
  animalDetailDeviceOnly: 'Device Only',
  animalDetailRecordedOn: 'Recorded on',
  animalDetailTemperature: 'Temperature',
  animalDetailHeartRate: 'Heart Rate',
  animalDetailRespiration: 'Respiration',
  animalDetailWeight: 'Weight',
  animalDetailNotes: 'Notes',
  animalDetailNoVitalsRecorded: 'No vitals recorded yet',
  animalDetailConnectDevice: 'Connect a device to start monitoring',
  animalDetailMedications: 'Medications',
  animalDetailAdd: 'Add',
  animalDetailPrintMedicationSchedule: 'Print Medication Schedule',
  animalDetailNoMedicationsAdded: 'No medications added yet',
  animalDetailAddMedication: 'Add Medication',
  animalDetailVaccinations: 'Vaccinations',
  animalDetailDue: 'Due',
  animalDetailNoRenewal: 'No renewal',
  animalDetailPrintVaccinationRecord: 'Print Vaccination Record',
  animalDetailNoVaccinationsRecorded: 'No vaccinations recorded yet',
  animalDetailAddVaccination: 'Add Vaccination',
  animalDetailMicrochipRequired: 'Microchip ID required for vaccinations',
  animalDetailFeedingSchedule: 'Feeding Schedule',
  animalDetailViewAll: 'View All',
  animalDetailPrintFeedingSchedule: 'Print Feeding Schedule',
  animalDetailNoFeedingSchedule: 'No feeding schedule set',
  animalDetailSetFeedingSchedule: 'Set Feeding Schedule',
  animalDetailTrainingSessions: 'Training Sessions',
  animalDetailLoadingSessions: 'Loading sessions...',
  animalDetailLogNewSession: 'Log New Session',
  animalDetailNoTrainingSessions: 'No training sessions yet',
  animalDetailTrainingDescription: 'Start tracking your training sessions to monitor progress and performance.',
  animalDetailLogFirstSession: 'Log First Session',
  
  // Animal not found
  animalNotFound: 'Animal not found',
};