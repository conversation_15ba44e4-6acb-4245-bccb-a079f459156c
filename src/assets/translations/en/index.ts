// Modular English translations - combines all feature-specific translation files
import { common } from './common';
import { navigation } from './navigation';
import { auth } from './auth';
import { animals } from './animals';
import { ai } from './ai';
import { devices } from './devices';

// Combine all translation modules into a single English translation object
export const en = {
  ...common,
  ...navigation,
  ...auth,
  ...animals,
  ...ai,
  ...devices,
  
  // Nested objects for dot notation access
  common,
  ai,
  
  // Additional translations that don't fit into specific modules yet
  // These can be moved to appropriate modules in future iterations
  
  // AI Widget keys (at root level for flat access)
  aiWidgetInsights: 'AI Insights',
  aiWidgetHealth: 'Health Status',
  aiWidgetLiveCheckIn: 'Live Check-in',
  aiWidgetViewAll: 'View All',
  aiWidgetNew: 'New',
  aiWidgetLogTraining: 'Log Training',
  aiWidgetNoAIData: 'No AI Data',
  aiWidgetReadiness: 'Readiness',
  aiWidgetLiveStatus: 'Live Status',
  aiWidgetAvailable: 'Available',
  aiWidgetLatestTip: 'Latest Tip',
  aiWidgetChecking: 'Checking...',
  
  // Language Settings
  language: 'Language',
  languageSettings: 'Language & Region',
  selectYourLanguage: 'Select your language',
  languageChanged: 'Language changed successfully',
  selectLanguage: 'Select language',
  changingLanguage: 'Changing language...',
  
  // Settings Categories
  dataManagement: 'Data Management',
  notifications: 'Notifications',
  themeSettings: 'Theme Settings',
  darkMode: 'Dark Mode',
  syncSettings: 'Sync Settings',
  healthAlerts: 'Health Alerts',
  reminders: 'Reminders',
  
  // Health Alert Settings
  heartRateAlerts: 'Heart Rate Alerts',
  heartRateRange: 'Heart Rate Range',
  minBpm: 'Min (bpm)',
  maxBpm: 'Max (bpm)',
  temperatureAlerts: 'Temperature Alerts',
  temperatureRange: 'Temperature Range',
  minCelsius: 'Min (°C)',
  maxCelsius: 'Max (°C)',
  locationAlerts: 'Location Alerts',
  locationUpdateInterval: 'Location Update Interval',
  locationUpdateLabel: 'Alert if no update for more than (hours):',
  
  // Reminder Settings
  reminderTimeBefore: 'Reminder time before (minutes):',
  saveSettings: 'Save Settings',
  settingsSavedSuccess: 'Settings saved successfully',
  errorFixBeforeSaving: 'Please fix the errors before saving',
  errorMaxHeartRateHigher: 'Maximum heart rate must be higher than minimum',
  errorMaxTemperatureHigher: 'Maximum temperature must be higher than minimum',
  
  // Monitoring Settings
  heartRateLimits: 'Heart Rate Limits',
  locationTracking: 'Location Tracking',
  
  // Reminder Settings
  feedingReminders: 'Feeding Reminders',
  medicationReminders: 'Medication Reminders',
  vaccinationReminders: 'Vaccination Reminders',
  
  // Device Management
  scanForDevices: 'Scan for Devices',
  scanning: 'Scanning...',
  devicesFound: 'devices found',
  noDevicesFound: 'No devices found',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'Your livestock companion',
  animalSpeedTitle: '{{animalName}} Speed',
  speedUpdatedSuccess: '{{animalName}} speed updated',
  speedUpdateFailed: 'Failed to update speed',
  
  // Quick Stats
  quickOverview: 'Quick Overview',
  activeDevices: 'Active Devices',
  todaysFeedings: 'Today\'s Feedings',
  medications: 'Medications',
  
  // Upcoming Tasks
  upcomingTasks: 'Upcoming Tasks',
  noTasksToday: 'No tasks scheduled for today',
  allCaughtUp: 'All caught up! Your animals are well taken care of.',
  feedingTask: 'Feeding: {{feedType}}',
  medicationTask: 'Medication: {{medicationName}}',
  amount: 'Amount',
  dosage: 'Dosage',
  moreTasksToday: '+{{count}} more tasks today',
  
  // Premium Banner
  premium: 'Premium',
  upgradeToPremium: 'Upgrade to Premium',
  unlimitedAnimals: 'Unlimited animals',
  advancedAnalytics: 'Advanced analytics',
  upgradeNow: 'Upgrade Now',
  
  // Speed Monitor
  speed: 'Speed',
  currentSpeed: 'Current Speed',
  lastUpdated: 'Last updated',
  
  // Health and vitals
  health: 'Health',
  vitals: 'Vitals',
  temperature: 'Temperature',
  heartRate: 'Heart Rate',
  respirationRate: 'Respiration Rate',
  bloodPressure: 'Blood Pressure',
  
  // Dehydration/Hydration Monitoring
  dehydrationMonitoring: 'Dehydration Monitoring',
  hydrationMonitoring: 'Hydration Monitoring',
  optimal: 'Optimal',
  mildDehydration: 'Mild Dehydration',
  moderateDehydration: 'Moderate Dehydration',
  severeDehydration: 'Severe Dehydration',
  unknown: 'Unknown',
  noHydrationData: 'No hydration data available',
  takeReadingToStart: 'Take a reading to start monitoring',
  bodyTemp: 'Body Temp',
  bioimpedance: 'Bioimpedance',
  signalQuality: 'Signal Quality',
  takeReading: 'Take Reading',
  reading: 'Reading...',
  startMonitoring: 'Start Monitoring',
  stopMonitoring: 'Stop Monitoring',
  aiAnalysis: 'AI Analysis',
  overallStatus: 'Overall Status',
  trend: 'Trend',
  avgHydration: 'Avg Hydration',
  variability: 'Variability',
  aiAlerts: 'AI Alerts',
  recommendations: 'Recommendations',
  refreshAnalysis: 'Refresh Analysis',
  recentReadings: 'Recent Readings',
  failedToLoadHydrationData: 'Failed to load hydration data',
  loadingHydrationData: 'Loading hydration data...',
  monitoringSessionActive: 'Monitoring session active',
  readings: 'readings',
  excellent: 'Excellent',
  good: 'Good',
  fair: 'Fair',
  poor: 'Poor',
  critical: 'Critical',
  stable: 'Stable',
  improving: 'Improving',
  declining: 'Declining',
  concerning: 'Concerning',
  hydrationReadingRecorded: 'Hydration reading recorded',
  failedToSaveHydrationReading: 'Failed to save hydration reading',
  readingUpdated: 'Reading updated',
  failedToUpdateReading: 'Failed to update reading',
  readingDeleted: 'Reading deleted',
  failedToDeleteReading: 'Failed to delete reading',
  startedHydrationMonitoring: 'Started hydration monitoring',
  monitoringSessionCompleted: 'Monitoring session completed',
  duration: 'Duration',
  min: 'min',
  failedToCaptureReading: 'Failed to capture sensor reading',
  criticalDehydrationDetected: 'Critical dehydration detected',
  moderateDehydrationDetected: 'Moderate dehydration detected',
  
  // Additional keys that need to be organized into modules later
  // This ensures backward compatibility while we refactor
  
  // Environmental Analysis
  environmentalImpact: 'Environmental Impact',
  environmentalAnalysisDescription: 'Analyze environmental impact on animal health',
  
  // Behavioral Analysis
  behavioralAnalysis: 'Behavioral Analysis',
  
  // Multi-Animal AI Dashboard Keys
  stableOverview: 'Stable Overview',
  healthyAnimals: 'Healthy Animals',
  totalAnimals: 'Total Animals',
  yourAnimals: 'Your Animals',
  criticalAlertsRequireAttention: 'Critical Alerts Require Attention',
  criticalAlertsNeedImmediate: 'critical alerts need immediate attention',
  reviewAlerts: 'Review Alerts',
  recentActivity: 'Recent Activity',
  vitalsRecorded: 'Vitals Recorded',
  recordsToday: 'records today',
  aiAnalysisCompleted: 'AI Analysis Completed',
  assessments: 'assessments',
  trendsIdentified: 'Trends Identified',
  patterns: 'patterns',
  healthInsights: 'Health Insights',
  avgHealthScore: 'Avg Health Score',
  acrossAllAnimals: 'across all animals',
  riskFactors: 'Risk Factors',
  highRiskFactors: 'high risk factors',
  refreshData: 'Refresh Data',
  viewTrends: 'View Trends',
  noAnimalsFound: 'No animals found',
  addAnimalToStart: 'Add an animal to get started',
  viewAll: 'View All',
  activeAlerts: 'Active Alerts',
  recordVitals: 'Record Vitals',
  
  // Missing AI & Health Analysis Keys (fixing "diseaseRiskAssessment" display issue)
  diseaseRiskAssessment: 'Disease Risk Assessment',
  healthTrends: 'Health Trends',
  stressAnalysis: 'Stress Analysis',
  sleepMonitoring: 'Sleep Monitoring',
  environmentalAnalysis: 'Environmental Analysis',
  predictiveInsights: 'Predictive Insights',
  aiHealthDashboard: 'AI Health Dashboard',
  healthDashboard: 'Health Dashboard',
  smartAlerts: 'Smart Alerts',
  healthScoreDetails: 'Health Score Details',
  trendsOverview: 'Trends Overview',
  riskOverview: 'Risk Overview',
  
  // AI Analysis Terms
  analyzing: 'Analyzing...',
  analyzingEnvironment: 'Analyzing Environment...',
  analyzeEnvironment: 'Analyze Environment',
  generatingPredictions: 'Generating Predictions...',
  generatePredictions: 'Generate Predictions',
  predictionHorizon: 'Prediction Horizon',
  oneWeek: '1 Week',
  oneMonth: '1 Month',
  threeMonths: '3 Months',
  healthPredictions: 'Health Predictions',
  predictedHealthScore: 'Predicted Health Score',
  predictedWeight: 'Predicted Weight',
  predictedActivity: 'Predicted Activity',
  predictedSleep: 'Predicted Sleep',
  predictionConfidence: 'Prediction Confidence',
  riskAssessment: 'Risk Assessment',
  diseaseRisk: 'Disease Risk',
  injuryRisk: 'Injury Risk',
  behavioralRisk: 'Behavioral Risk',
  environmentalStressRisk: 'Environmental Stress Risk',
  predictiveAlerts: 'Predictive Alerts',
  acknowledgeAlert: 'Acknowledge',
  resolveAlert: 'Resolve',
  
  // Environmental Analysis
  humidity: 'Humidity',
  airQuality: 'Air Quality',
  uvIndex: 'UV Index',
  moderate: 'Moderate',
  hazardous: 'Hazardous',
  
  // Missing Animal Card Keys
  id: 'ID',
  ageYears: '{{age}} years',
  gps: 'GPS',
};