export const devices = {
  // Device Status
  paired: 'Paired',
  available: 'Available',
  connected: 'Connected',
  disconnected: 'Disconnected',
  connecting: 'Connecting...',
  
  // Device Discovery
  findDevices: 'Find Devices',
  scanForDevices: 'Scan for Devices',
  scanning: 'Scanning...',
  loadingDevices: 'Loading devices...',
  checkingBluetooth: 'Checking Bluetooth...',
  noDevicesFound: 'No devices found',
  
  // Device Pairing
  pairDevice: 'Pair Device',
  pairDevicePrompt: 'Select a device to pair',
  pairing: 'Pairing...',
  pairingSuccess: 'Device paired successfully',
  pairingFailed: 'Failed to pair device',
  noPairedDevices: 'No paired devices found',
  
  // Device Management
  unpairDevice: 'Unpair Device',
  deviceInfo: 'Device Information',
  batteryLevel: 'Battery Level',
  signalStrength: 'Signal Strength',
  lastSeen: 'Last Seen',
  
  // Bluetooth
  bluetoothDisabled: 'Bluetooth is disabled',
  enableBluetooth: 'Enable Bluetooth',
  bluetoothUnavailable: 'Bluetooth unavailable',
  bluetoothPermission: 'Bluetooth permission required',
  
  // Device Types
  heartRateMonitor: 'Heart Rate Monitor',
  activityTracker: 'Activity Tracker',
  smartCollar: 'Smart Collar',
  temperatureSensor: 'Temperature Sensor',
  gpsTracker: 'GPS Tracker',
  
  // Actions
  refresh: 'Refresh',
  retry: 'Retry',
  cancel: 'Cancel',
  connect: 'Connect',
  disconnect: 'Disconnect',
  
  // Additional keys used in DevicesScreen
  initializing: 'Initializing...',
  bluetoothErrorPrefix: 'Bluetooth Error:',
  retryBluetoothCheck: 'Retry Bluetooth Check',
  bluetoothRequiredWarning: 'Bluetooth is required for device connectivity',
  availableToAdd: 'Available to add',
  addDevice: 'Add Device',
  scanToFindDevicesPrompt: 'Tap scan to find nearby devices',
  deviceIdPlaceholder: 'Enter device ID or scan barcode',
  add: 'Add'
};