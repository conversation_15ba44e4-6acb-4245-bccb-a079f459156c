// Authentication and security - English
export const auth = {
  // Authentication
  createAccount: 'Create an account',
  resetPassword: 'Reset your password',
  signInToAccount: 'Sign in to your account',
  login: 'Login',
  logout: 'Logout',
  register: 'Register',
  email: 'Email',
  password: 'Password',
  confirmPassword: 'Confirm Password',
  forgotPassword: 'Forgot Password?',
  
  // Biometric Authentication
  biometricLogin: 'Biometric Login',
  signInWithFingerprint: 'Sign in with Fingerprint',
  signInWithFaceId: 'Sign in with Face ID',
  signInWithBiometric: 'Sign in with Biometric',
  biometricAuthenticationFailed: 'Biometric authentication failed',
  biometricNotAvailable: 'Biometric authentication is not available on this device',
  biometricNotEnabled: 'Biometric login is not enabled',
  biometricSetupRequired: 'Biometric Setup Required',
  biometricSetupDescription: 'To use biometric login, please set up {{biometricType}} in your device settings first.',
  enableBiometricLogin: 'Enable biometric login for quick and secure access to your account.',
  biometricLoginEnabled: 'Biometric login enabled successfully!',
  biometricLoginDisabled: 'Biometric login disabled',
  disableBiometricLogin: 'Disable Biometric Login',
  disableBiometricConfirm: 'Are you sure you want to disable biometric login? You will need to use your password to sign in.',
  biometricDataSecure: 'Your biometric data stays secure on your device and is never shared.',
  enableBiometricPrompt: 'Enable biometric login',
  biometricSetupConfirm: 'Use your biometric to confirm setup',
  biometricSignInPrompt: 'Sign in to HoofBeat',
  biometricSignInSubtitle: 'Use your biometric to access your account',
  biometricSetupCancelled: 'Biometric setup cancelled',
  biometricRefreshSession: 'Please sign in with your password to refresh your session, then enable biometric login again.',
  biometricCredentialsFirst: 'Please verify your credentials before enabling biometric login',
  biometricSignInPasswordFirst: 'Please sign in with your password first to enable biometric login',
  or: 'or',
  
  // Error Messages
  userNotFound: 'User not found',
  profileCreationFailed: 'Failed to create user profile',
  profileLoadFailed: 'Failed to load user profile',
  sessionExpired: 'Session expired. Please sign in again.',
  networkError: 'Network error. Please check your connection.',
  unexpectedError: 'An unexpected error occurred',
  
  // Biometric types
  biometric: 'Biometric',
  fingerprint: 'Fingerprint',
  faceId: 'Face ID',
  biometricAuthentication: 'Biometric Authentication',
  
  // MFA
  mfaSettings: 'Two-Factor Authentication',
  mfaEnabled: 'Two-Factor Authentication Enabled',
  mfaDisabled: 'Two-Factor Authentication Disabled',
};