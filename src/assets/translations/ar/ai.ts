// AI and machine learning features - Arabic
export const ai = {
  // AI Assistant
  aiAssistant: 'المساعد الذكي',
  aiChat: 'محادثة ذكية',
  aiChatTitle: 'محادثة مع المساعد الذكي',
  aiChatPlaceholder: 'اسأل عن صحة حيوانك أو تدريبه أو رعايته...',
  aiChatSend: 'إرسال',
  aiChatAttachment: 'إرفاق صورة',
  aiChatCamera: 'التقاط صورة',
  aiChatGallery: 'اختيار من المعرض',
  aiChatAnalyzing: 'جاري التحليل...',
  aiChatTyping: 'الذكاء الاصطناعي يكتب...',
  aiChatNoMessages: 'لا توجد رسائل بعد',
  aiChatStartConversation: 'ابدأ محادثة مع مساعدك الذكي حول {{animalName}}',
  aiChatImageAnalysis: 'تحليل الصورة',
  aiChatCopyMessage: 'نسخ الرسالة',
  aiChatShareMessage: 'مشاركة الرسالة',
  aiChatMessageCopied: 'تم نسخ الرسالة إلى الحافظة',
  aiChatUploadingImage: 'جاري رفع الصورة...',
  aiChatImageUploadFailed: 'فشل في رفع الصورة',
  aiChatSendingMessage: 'جاري إرسال الرسالة...',
  aiChatMessageFailed: 'فشل في إرسال الرسالة',
  aiChatRetry: 'إعادة المحاولة',
  aiChatMaxFileSize: 'يجب أن يكون حجم الملف أقل من 10 ميجابايت',
  aiChatUnsupportedFormat: 'تنسيق ملف غير مدعوم',
  
  // AI Onboarding
  meetYourAIAssistant: 'التق بمساعدك الذكي',
  aiAssistantDescription: 'احصل على خطط تدريب مخصصة ورؤى صحية ونصائح تدريب مدعومة بالذكاء الاصطناعي.',
  readinessScores: 'نقاط الجاهزية',
  trainingPlans: 'خطط التدريب',
  logFirstTrainingSession: 'سجل جلسة التدريب الأولى',
  
  // AI Guidance
  aiGuidanceTitle: 'احصل على المزيد من مساعدك الذكي',
  aiGuidanceDescription: 'يحتاج مساعدك الذكي إلى مزيد من البيانات لتقديم رؤى وتوصيات مخصصة.',
  aiGuidanceWhatYouGet: 'ما ستحصل عليه:',
  aiGuidancePersonalizedPlans: 'خطط تدريب مخصصة',
  aiGuidanceHealthInsights: 'رؤى وتنبيهات صحية',
  aiGuidancePerformanceTracking: 'تتبع الأداء',
  aiGuidanceSmartRecommendations: 'توصيات ذكية',
  aiGuidanceDataProgress: 'تقدم البيانات',
  aiGuidanceCompleteData: 'مكتمل بنسبة {{percentage}}%',
  aiGuidanceLogTrainingSession: 'تسجيل جلسة تدريب',
  aiGuidanceRecordVitals: 'تسجيل العلامات الحيوية',
  aiGuidanceUpdateFeedingSchedule: 'تحديث جدول التغذية',
  aiGuidanceStartLogging: 'ابدأ في تسجيل البيانات لفتح رؤى الذكاء الاصطناعي',
  aiGuidanceMoreDataNeeded: 'مطلوب مزيد من البيانات لرؤى الذكاء الاصطناعي',
  aiGuidanceGetStarted: 'ابدأ بتسجيل جلسة التدريب الأولى أو تسجيل العلامات الحيوية.',
  
  // AI Analysis
  dataCompleteness: 'اكتمال البيانات',
  analyzing: 'جاري التحليل...',
  getAIAnalysis: 'احصل على تحليل ذكي',
  readinessScore: 'نقاط الجاهزية',
  healthAssessment: 'تقييم صحي',
  hydrationAnalysis: 'تحليل الترطيب',
  trainingPlan: 'خطة التدريب',
  coachingTips: 'نصائح التدريب',
  readyForAIAnalysis: 'جاهز للتحليل الذكي',
  greatYouHaveData: 'رائع! لديك بيانات عن {{name}}. اطلب تحليلاً ذكياً للحصول على رؤى مخصصة.',
  alerts: 'التنبيهات',
  moreTips: '+{{count}} نصائح أخرى',
  
  // AI Widget
  aiWidgetViewAll: 'عرض الكل',
  aiWidgetInsights: 'رؤى ذكية',
  aiWidgetReadiness: 'الجاهزية',
  aiWidgetNoAIData: 'لا توجد بيانات ذكية',
  aiWidgetLogTraining: 'تسجيل التدريب',
  aiWidgetLiveStatus: 'الحالة المباشرة',
  aiWidgetAvailable: 'متاح',
  aiWidgetHealth: 'الصحة',
  aiWidgetLatestTip: 'آخر نصيحة',
  aiWidgetChecking: 'جاري الفحص...',
  aiWidgetLiveCheckIn: 'فحص مباشر',
  aiWidgetNew: 'جديد',
  
  // AI Insights
  aiInsights: 'رؤى ذكية',
  aiInsightsFor: 'رؤى ذكية لـ {{name}}',
  helpMeLearnAbout: 'ساعدني في التعلم عن {{name}} لتقديم رؤى أفضل',
  needMoreInformation: 'أحتاج إلى مزيد من المعلومات عن {{name}} لإنتاج رؤى ذكية مخصصة. أكمل أي من الإجراءات أدناه لفتح تحليلات قوية!',
  getPersonalizedRecommendations: 'احصل على توصيات مخصصة لـ {{name}}',
  whatYoullGet: 'ما ستحصل عليه:',
  personalizedHealthAssessments: '• تقييمات صحية مخصصة',
  trainingPerformanceInsights: '• رؤى أداء التدريب',
  nutritionCareRecommendations: '• توصيات التغذية والرعاية',
  earlyHealthAlerts: '• تنبيهات صحية مبكرة وتحذيرات',
  
  // AI Actions
  logTrainingSession: 'تسجيل جلسة تدريب',
  recordTrainingActivities: 'سجل أنشطة التدريب للحصول على رؤى الأداء',
  recordVitals: 'تسجيل العلامات الحيوية',
  logHealthMeasurements: 'سجل قياسات الصحة للتحليل الصحي الذكي',
  updateFeedingSchedule: 'تحديث جدول التغذية',
  setFeedingTimes: 'حدد أوقات التغذية للحصول على توصيات التغذية',
  
  // Quick Actions
  quickActions: 'إجراءات سريعة',
  healthTrends: 'اتجاهات الصحة',
  diseaseRisk: 'مخاطر الأمراض',
  stressAnalysis: 'تحليل الإجهاد',
  sleepMonitoring: 'مراقبة النوم',
  environmental: 'بيئي',
  predictiveInsights: 'رؤى تنبؤية',
  assistant: 'مساعد ذكي',
  
  // Additional missing keys
  environmentalAnalysis: 'التحليل البيئي',
  behavioralAnalysis: 'التحليل السلوكي',
  aiHealthDashboard: 'لوحة الصحة الذكية',
  healthDashboard: 'لوحة الصحة',
  smartAlerts: 'التنبيهات الذكية',
  diseaseRiskAssessment: 'تقييم مخاطر الأمراض',
  healthScoreDetails: 'تفاصيل نقاط الصحة',
  trendsOverview: 'نظرة عامة على الاتجاهات',
  riskOverview: 'نظرة عامة على المخاطر',
  
  // Health Dashboard
  healthDashboard: 'لوحة الصحة',
  overallHealthStatus: 'الحالة الصحية العامة',
  totalAnimals: 'إجمالي الحيوانات',
  healthScore: 'نقاط الصحة',
  activeAlerts: 'التنبيهات النشطة',
  recentAnalyses: 'التحاليل الأخيرة',
  animalNotSelected: 'لم يتم اختيار حيوان',
  selectAnimalForDetails: 'اختر حيواناً لعرض معلومات صحية مفصلة',
  viewHealthDetails: 'عرض تفاصيل الصحة',
  lastUpdated: 'آخر تحديث',
  noHealthData: 'لا توجد بيانات صحية',
  tapToViewDetails: 'اضغط لعرض التفاصيل',
};