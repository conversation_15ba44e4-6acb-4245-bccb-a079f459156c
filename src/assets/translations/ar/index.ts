// Modular Arabic translations - combines all feature-specific translation files
import { common } from './common';
import { navigation } from './navigation';
import { auth } from './auth';
import { animals } from './animals';
import { ai } from './ai';

// Combine all translation modules into a single Arabic translation object
import { devices } from './devices';

export const ar = {
  ...devices,
  ...common,
  ...navigation,
  ...auth,
  ...animals,
  
  // Nested objects for dot notation access
  common,
  ai,
  
  // Additional translations that don't fit into specific modules yet
  // These can be moved to appropriate modules in future iterations
  
  // AI Widget keys (at root level for flat access)
  aiWidgetInsights: 'رؤى ذكية',
  aiWidgetHealth: 'حالة الصحة',
  aiWidgetLiveCheckIn: 'فحص مباشر',
  aiWidgetViewAll: 'عرض الكل',
  aiWidgetNew: 'جديد',
  aiWidgetLogTraining: 'تسجيل التدريب',
  aiWidgetNoAIData: 'لا توجد بيانات ذكية',
  aiWidgetReadiness: 'الاستعداد',
  aiWidgetLiveStatus: 'الحالة المباشرة',
  aiWidgetAvailable: 'متاح',
  aiWidgetLatestTip: 'آخر نصيحة',
  aiWidgetChecking: 'جاري الفحص...',
  
  // Language Settings
  language: 'اللغة',
  languageSettings: 'اللغة والمنطقة',
  selectYourLanguage: 'اختر لغتك',
  languageChanged: 'تم تغيير اللغة بنجاح',
  selectLanguage: 'اختيار اللغة',
  changingLanguage: 'جاري تغيير اللغة...',
  
  // Settings Categories
  dataManagement: 'إدارة البيانات',
  notifications: 'الإشعارات',
  themeSettings: 'إعدادات المظهر',
  darkMode: 'الوضع المظلم',
  syncSettings: 'إعدادات المزامنة',
  healthAlerts: 'تنبيهات صحية',
  reminders: 'التذكيرات',
  
  // Health Alert Settings
  heartRateAlerts: 'تنبيهات معدل ضربات القلب',
  heartRateRange: 'نطاق معدل ضربات القلب',
  minBpm: 'الحد الأدنى (ض/د)',
  maxBpm: 'الحد الأقصى (ض/د)',
  temperatureAlerts: 'تنبيهات درجة الحرارة',
  temperatureRange: 'نطاق درجة الحرارة',
  minCelsius: 'الحد الأدنى (°م)',
  maxCelsius: 'الحد الأقصى (°م)',
  locationAlerts: 'تنبيهات الموقع',
  locationUpdateInterval: 'فترة تحديث الموقع',
  locationUpdateLabel: 'تنبيه إذا لم يتم التحديث لأكثر من (ساعات):',
  
  // Reminder Settings
  reminderTimeBefore: 'وقت التذكير قبل (دقائق):',
  saveSettings: 'حفظ الإعدادات',
  settingsSavedSuccess: 'تم حفظ الإعدادات بنجاح',
  errorFixBeforeSaving: 'يرجى إصلاح الأخطاء قبل الحفظ',
  errorMaxHeartRateHigher: 'يجب أن يكون الحد الأقصى لمعدل ضربات القلب أعلى من الحد الأدنى',
  errorMaxTemperatureHigher: 'يجب أن تكون درجة الحرارة القصوى أعلى من الدنيا',
  
  // Monitoring Settings
  heartRateLimits: 'حدود معدل ضربات القلب',
  locationTracking: 'تتبع الموقع',
  
  // Reminder Settings
  feedingReminders: 'تذكيرات التغذية',
  medicationReminders: 'تذكيرات الأدوية',
  vaccinationReminders: 'تذكيرات التطعيمات',
  
  // Device Management
  scanForDevices: 'البحث عن أجهزة',
  scanning: 'جاري البحث...',
  devicesFound: 'تم العثور على أجهزة',
  noDevicesFound: 'لم يتم العثور على أجهزة',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'رفيقك في تربية الماشية',
  animalSpeedTitle: 'سرعة {{animalName}}',
  speedUpdatedSuccess: 'تم تحديث سرعة {{animalName}}',
  speedUpdateFailed: 'فشل في تحديث السرعة',
  
  // Quick Stats
  quickOverview: 'نظرة سريعة',
  activeDevices: 'الأجهزة النشطة',
  todaysFeedings: 'تغذية اليوم',
  medications: 'الأدوية',
  
  // Upcoming Tasks
  upcomingTasks: 'المهام القادمة',
  noTasksToday: 'لا توجد مهام مجدولة لليوم',
  allCaughtUp: 'كل شيء محدث! حيواناتك تتلقى رعاية جيدة.',
  feedingTask: 'التغذية: {{feedType}}',
  medicationTask: 'الدواء: {{medicationName}}',
  amount: 'الكمية',
  dosage: 'الجرعة',
  moreTasksToday: '+{{count}} مهام أخرى اليوم',
  
  // Premium Banner
  premium: 'بريميوم',
  upgradeToPremium: 'الترقية إلى بريميوم',
  unlimitedAnimals: 'حيوانات غير محدودة',
  advancedAnalytics: 'تحليلات متقدمة',
  upgradeNow: 'الترقية الآن',
  
  // Speed Monitor
  speed: 'السرعة',
  currentSpeed: 'السرعة الحالية',
  lastUpdated: 'آخر تحديث',
  
  // Health and vitals
  health: 'الصحة',
  vitals: 'العلامات الحيوية',
  temperature: 'درجة الحرارة',
  heartRate: 'معدل ضربات القلب',
  respirationRate: 'معدل التنفس',
  bloodPressure: 'ضغط الدم',
  
  // Additional keys for backward compatibility
  behavioralAnalysis: 'تحليل السلوك',
  stressAnalysis: 'تحليل الضغط',
  sleepMonitoring: 'مراقبة النوم',
  environmentalAnalysis: 'التحليل البيئي',
  environmentalAnalysisDescription: 'تحليل تأثير البيئة على الصحة',
  environmentalImpact: 'التأثير البيئي',
  predictiveInsights: 'رؤى تنبؤية',
  viewAll: 'عرض الكل',
  
  // Missing keys from en/common.ts - Arabic translations
  // Health Dashboard
  healthDashboard: 'لوحة الصحة',
  overallHealthStatus: 'الحالة الصحية العامة',
  criticalAlerts: 'تنبيهات حرجة',
  recentActivity: 'النشاط الأخير',
  healthInsights: 'رؤى صحية',
  quickActions: 'إجراءات سريعة',
  viewHealthScore: 'عرض نقاط الصحة',
  analyzeRisks: 'تحليل المخاطر',
  viewTrends: 'عرض الاتجاهات',
  stressAnalysis: 'تحليل الإجهاد',
  sleepMonitoring: 'مراقبة النوم',
  environmentalAnalysis: 'التحليل البيئي',
  predictiveInsights: 'رؤى تنبؤية',
  noAnimalsSelected: 'لم يتم اختيار حيوانات',
  selectAnimalToViewHealth: 'اختر حيواناً لعرض لوحة صحته',
  healthDataLoading: 'تحميل بيانات الصحة...',
  healthDataError: 'خطأ في تحميل بيانات الصحة',
  retryLoadingHealthData: 'إعادة محاولة تحميل بيانات الصحة',
  
  // FAQ
  frequentlyAskedQuestions: 'الأسئلة الشائعة',
  howToAddAnimal: 'كيف أضيف حيواناً جديداً؟',
  howToAddAnimalAnswer: 'لإضافة حيوان جديد، انتقل إلى تبويب الحيوانات واضغط على زر "+". املأ معلومات حيوانك بما في ذلك الاسم والسلالة والعمر وتفاصيل أخرى.',
  howToConnectDevice: 'كيف أربط جهاز مراقبة؟',
  howToConnectDeviceAnswer: 'انتقل إلى تبويب الأجهزة، اضغط على "إضافة جهاز"، واتبع تعليمات الاقتران. تأكد من تفعيل البلوتوث على هاتفك.',
  whatIsHealthScore: 'ما هي نقاط الصحة؟',
  whatIsHealthScoreAnswer: 'نقاط الصحة هي تقييم مولد بواسطة الذكاء الاصطناعي للصحة العامة لحيوانك بناءً على العلامات الحيوية ومستويات النشاط والبيانات المراقبة الأخرى.',
  howToSetupAlerts: 'كيف أعد تنبيهات الصحة؟',
  howToSetupAlertsAnswer: 'انتقل إلى الإعدادات > الإشعارات لتكوين متى وكيف تتلقى تنبيهات حول الحالة الصحية لحيوانك.',
  whatDataIsTracked: 'ما البيانات التي يتتبعها التطبيق؟',
  whatDataIsTrackedAnswer: 'يتتبع التطبيق العلامات الحيوية (معدل ضربات القلب، درجة الحرارة)، مستويات النشاط، الموقع، جداول التغذية، الأدوية، والعوامل البيئية.',
  howToExportData: 'كيف يمكنني تصدير بيانات حيواني؟',
  howToExportDataAnswer: 'انتقل إلى ملف حيوانك، اضغط على زر القائمة، واختر "تصدير البيانات". يمكنك اختيار نطاق التاريخ وأنواع البيانات للتصدير.',
  isPremiumWorthIt: 'هل اشتراك بريميوم يستحق الثمن؟',
  isPremiumWorthItAnswer: 'يوفر بريميوم حيوانات غير محدودة، رؤى ذكاء اصطناعي متقدمة، تقارير صحية مفصلة، ودعم ذو أولوية. إنه مثالي للاستخدام المهني أو الحيوانات المتعددة.',
  howToContactSupport: 'كيف أتواصل مع الدعم؟',
  howToContactSupportAnswer: 'يمكنك التواصل مع الدعم عبر تبويب الاتصال في ملفك الشخصي، أو إرسال بريد إلكتروني مباشرة إلى <EMAIL>.',
  
  // Contact
  contactUs: 'اتصل بنا',
  messageSent: 'تم إرسال الرسالة بنجاح',
  messageSentTitle: 'تم إرسال الرسالة',
  messageSentDescription: 'تم إرسال رسالتك إلى <EMAIL>. سنرد عليك قريباً!',
  failedToSendMessage: 'فشل إرسال الرسالة. يرجى المحاولة مرة أخرى.',
  errorSendingMessageTitle: 'خطأ في إرسال الرسالة',
  errorSendingMessageDescription: 'كانت هناك مشكلة في إرسال رسالتك. يرجى المحاولة مرة أخرى أو استخدام خيار الدردشة المباشرة.',
  chatError: 'خطأ في الدردشة',
  failedToOpenChat: 'فشل فتح الدردشة. يرجى المحاولة مرة أخرى.',
  openingChatSupport: 'فتح دعم الدردشة...',
  
  // Print
  print: 'طباعة',
  printed: 'تمت الطباعة',
  download: 'تحميل',
  downloaded: 'تم التحميل',
  shared: 'تمت المشاركة',
  animalNotFound: 'لم يتم العثور على الحيوان',
  goBack: 'عودة',
  
  // Barcode Scanner
  cameraPermissionRequired: 'مطلوب إذن الكاميرا لمسح الرموز الشريطية',
  invalidBarcodeFormat: 'تنسيق رمز شريطي للجهاز غير صالح',
  deviceBarcodeScanned: 'تم مسح الرمز الشريطي للجهاز',
  requestingCameraPermission: 'طلب إذن الكاميرا...',
  noAccessToCamera: 'لا يوجد وصول للكاميرا',
  scanDeviceBarcode: 'مسح الرمز الشريطي للجهاز',
  positionBarcodeInFrame: 'ضع الرمز الشريطي داخل الإطار للمسح',
  scanAgain: 'مسح مرة أخرى',
  
  // Privacy Settings
  privacySettings: 'إعدادات الخصوصية',
  privacySettingsSaved: 'تم حفظ إعدادات الخصوصية',
  accountDeleted: 'تم حذف الحساب',
  locationTracking: 'تتبع الموقع',
  locationTrackingDescription: 'السماح للتطبيق بتتبع مواقع حيواناتك. هذا مطلوب لتنبيهات الموقع.',
  shareDataWithVets: 'مشاركة البيانات مع الأطباء البيطريين',
  shareDataWithVetsDescription: 'السماح للأطباء البيطريين بالوصول إلى السجلات الصحية لحيواناتك عند مشاركتها.',
  analytics: 'التحليلات',
  analyticsDescription: 'ساعدنا في التحسين بالسماح بجمع بيانات الاستخدام المجهولة.',
  saveSettings: 'حفظ الإعدادات',
  
  // Alert Types
  heartRateAlert: 'تنبيه معدل ضربات القلب',
  temperatureAlert: 'تنبيه درجة الحرارة',
  locationAlert: 'تنبيه الموقع',
  healthAlert: 'تنبيه صحي',
  
  // Health Status
  score: 'نقاط',
  alerts: 'تنبيهات',
  trends: 'اتجاهات',
  tracked: 'متتبع',
  criticalAlertsRequireAttention: 'تنبيهات حرجة تتطلب انتباهاً',
  lastUpdated: 'آخر تحديث',
  excellent: 'ممتاز',
  good: 'جيد',
  fair: 'مقبول',
  poor: 'ضعيف',
  critical: 'حرج',
  aiAnalysis: 'تحليل ذكاء اصطناعي',
  active: 'نشط',
  seeAll: 'عرض الكل',
  noAnimalsYet: 'لا توجد حيوانات بعد',
  addFirstAnimal: 'أضف حيوانك الأول',
  
  // Missing AI & Health Analysis Keys (Arabic translations)
  diseaseRiskAssessment: 'تقييم مخاطر الأمراض',
  healthTrends: 'اتجاهات الصحة',
  aiHealthDashboard: 'لوحة معلومات الصحة بالذكاء الاصطناعي',
  smartAlerts: 'التنبيهات الذكية',
  healthScoreDetails: 'تفاصيل نقاط الصحة',
  trendsOverview: 'نظرة عامة على الاتجاهات',
  riskOverview: 'نظرة عامة على المخاطر',
  
  // AI Analysis Terms
  analyzing: 'جاري التحليل...',
  analyzingEnvironment: 'جاري تحليل البيئة...',
  analyzeEnvironment: 'تحليل البيئة',
  generatingPredictions: 'جاري إنشاء التنبؤات...',
  generatePredictions: 'إنشاء التنبؤات',
  predictionHorizon: 'أفق التنبؤ',
  oneWeek: 'أسبوع واحد',
  oneMonth: 'شهر واحد',
  threeMonths: '3 أشهر',
  healthPredictions: 'التنبؤات الصحية',
  predictedHealthScore: 'نقاط الصحة المتوقعة',
  predictedWeight: 'الوزن المتوقع',
  predictedActivity: 'النشاط المتوقع',
  predictedSleep: 'النوم المتوقع',
  predictionConfidence: 'ثقة التنبؤ',
  riskAssessment: 'تقييم المخاطر',
  diseaseRisk: 'مخاطر الأمراض',
  injuryRisk: 'مخاطر الإصابة',
  behavioralRisk: 'المخاطر السلوكية',
  environmentalStressRisk: 'مخاطر الإجهاد البيئي',
  predictiveAlerts: 'التنبيهات التنبؤية',
  acknowledgeAlert: 'إقرار',
  resolveAlert: 'حل',
  
  // Environmental Analysis
  humidity: 'الرطوبة',
  airQuality: 'جودة الهواء',
  uvIndex: 'مؤشر الأشعة فوق البنفسجية',
  moderate: 'معتدل',
  hazardous: 'خطير',
  optimal: 'مثالي',
};