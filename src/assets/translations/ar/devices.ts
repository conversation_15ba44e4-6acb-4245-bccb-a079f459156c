export const devices = {
  // حالة الجهاز
  paired: 'مقترن',
  available: 'متاح',
  connected: 'متصل',
  disconnected: 'غير متصل',
  connecting: 'جاري الاتصال...',
  
  // اكتشاف الأجهزة
  findDevices: 'ابحث عن الأجهزة',
  scanForDevices: 'فحص الأجهزة',
  scanning: 'جاري الفحص...',
  loadingDevices: 'جاري تحميل الأجهزة...',
  checkingBluetooth: 'جاري فحص البلوتوث...',
  noDevicesFound: 'لم يتم العثور على أجهزة',
  
  // اقتران الأجهزة
  pairDevice: 'اقتران الجهاز',
  pairDevicePrompt: 'اختر جهازاً للاقتران',
  pairing: 'جاري الاقتران...',
  pairingSuccess: 'تم اقتران الجهاز بنجاح',
  pairingFailed: 'فشل في اقتران الجهاز',
  noPairedDevices: 'لم يتم العثور على أجهزة مقترنة',
  
  // إدارة الأجهزة
  unpairDevice: 'إلغاء اقتران الجهاز',
  deviceInfo: 'معلومات الجهاز',
  batteryLevel: 'مستوى البطارية',
  signalStrength: 'قوة الإشارة',
  lastSeen: 'آخر ظهور',
  
  // البلوتوث
  bluetoothDisabled: 'البلوتوث معطل',
  enableBluetooth: 'تفعيل البلوتوث',
  bluetoothUnavailable: 'البلوتوث غير متاح',
  bluetoothPermission: 'مطلوب إذن البلوتوث',
  
  // أنواع الأجهزة
  heartRateMonitor: 'مراقب معدل ضربات القلب',
  activityTracker: 'متتبع النشاط',
  smartCollar: 'طوق ذكي',
  temperatureSensor: 'مستشعر درجة الحرارة',
  gpsTracker: 'متتبع GPS',
  
  // الإجراءات
  refresh: 'تحديث',
  retry: 'إعادة المحاولة',
  cancel: 'إلغاء',
  connect: 'اتصال',
  disconnect: 'قطع الاتصال',
  
  // مفاتيح إضافية مستخدمة في DevicesScreen
  initializing: 'جاري التهيئة...',
  bluetoothErrorPrefix: 'خطأ في البلوتوث:',
  retryBluetoothCheck: 'إعادة محاولة فحص البلوتوث',
  bluetoothRequiredWarning: 'البلوتوث مطلوب لاتصال الأجهزة',
  availableToAdd: 'متاح للإضافة',
  addDevice: 'إضافة جهاز',
  scanToFindDevicesPrompt: 'اضغط على الفحص للعثور على الأجهزة القريبة',
  deviceIdPlaceholder: 'أدخل معرف الجهاز أو امسح الرمز الشريطي',
  add: 'إضافة'
};