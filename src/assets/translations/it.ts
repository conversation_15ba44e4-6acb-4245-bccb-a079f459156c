// Italian translations
export const it = {
  // Navigation
  home: 'Home',
  animals: 'Animali',
  devices: 'Dispositivi',
  profile: 'Profilo',
  settings: 'Impostazioni',
  
  // Tab Navigation
  tabHome: 'Home',
  tabAnimals: 'Animali',
  tabDevices: 'Dispositivi',
  tabHealth: 'Salute',
  tabProfile: 'Profilo',
  
  // Common actions
  add: 'Aggiungi',
  edit: 'Modi<PERSON>',
  delete: 'Elimina',
  save: '<PERSON><PERSON>',
  cancel: '<PERSON><PERSON><PERSON>',
  confirm: 'Conferma',
  back: '<PERSON><PERSON>',
  next: 'Avanti',
  previous: 'Precedente',
  loading: 'Caricamento...',
  error: 'Errore',
  success: 'Successo',
  warning: 'Avviso',
  info: 'Informazione',
  
  // Authentication
  login: 'Accedi',
  logout: 'Esci',
  register: 'Registrati',
  email: 'Email',
  password: 'Password',
  confirmPassword: 'Conferma password',
  forgotPassword: 'Password dimenticata?',
  resetPassword: 'Reimposta password',
  
  // Animal management
  animalName: 'Nome animale',
  animalType: 'Tipo di animale',
  breed: 'Ra<PERSON>',
  age: 'Età',
  gender: 'Se<PERSON>',
  weight: 'Peso',
  color: 'Colore',
  microchipId: 'ID microchip',
  registrationNumber: 'Numero di registrazione',
  
  // Animal detail sections
  animalDetailLatestVitals: 'Ultimi parametri vitali',
  animalDetailDeviceOnly: 'Solo dispositivo',
  animalDetailRecordedOn: 'Registrato il',
  animalDetailTemperature: 'Temperatura',
  animalDetailHeartRate: 'Frequenza cardiaca',
  animalDetailRespiration: 'Respirazione',
  animalDetailWeight: 'Peso',
  animalDetailNotes: 'Note',
  animalDetailNoVitalsRecorded: 'Nessun parametro vitale registrato',
  animalDetailConnectDevice: 'Connetti dispositivo',
  
  animalDetailMedications: 'Farmaci',
  animalDetailAdd: 'Aggiungi',
  animalDetailPrintMedicationSchedule: 'Stampa programma farmaci',
  animalDetailNoMedicationsAdded: 'Nessun farmaco aggiunto',
  animalDetailAddMedication: 'Aggiungi farmaco',
  
  animalDetailVaccinations: 'Vaccinazioni',
  animalDetailDue: 'Scadenza',
  animalDetailNoRenewal: 'Nessun rinnovo',
  animalDetailPrintVaccinationRecord: 'Stampa registro vaccinazioni',
  animalDetailNoVaccinationsRecorded: 'Nessuna vaccinazione registrata',
  animalDetailAddVaccination: 'Aggiungi vaccinazione',
  animalDetailMicrochipRequired: 'Microchip richiesto',
  
  animalDetailFeedingSchedule: 'Programma alimentazione',
  animalDetailViewAll: 'Visualizza tutto',
  animalDetailPrintFeedingSchedule: 'Stampa programma alimentazione',
  animalDetailNoFeedingSchedule: 'Nessun programma alimentazione',
  animalDetailSetFeedingSchedule: 'Imposta programma alimentazione',
  
  animalDetailTrainingSessions: 'Sessioni di addestramento',
  animalDetailLoadingSessions: 'Caricamento sessioni...',
  animalDetailLogNewSession: 'Registra nuova sessione',
  animalDetailNoTrainingSessions: 'Nessuna sessione di addestramento',
  animalDetailTrainingDescription: 'Traccia i progressi di addestramento del tuo animale',
  animalDetailLogFirstSession: 'Registra prima sessione',
  
  // Behavioral analysis
  behavioralAnalysis: 'Analisi comportamentale',
  stressAnalysis: 'Analisi dello stress',
  sleepMonitoring: 'Monitoraggio del sonno',
  environmentalAnalysis: 'Analisi ambientale',
  environmentalAnalysisDescription: 'Analizza l\'impatto dell\'ambiente sulla salute',
  environmentalImpact: 'Impatto ambientale',
  predictiveInsights: 'Insights predittivi',
  
  // Health and vitals
  health: 'Salute',
  vitals: 'Parametri vitali',
  temperature: 'Temperatura',
  heartRate: 'Frequenza cardiaca',
  respirationRate: 'Frequenza respiratoria',
  bloodPressure: 'Pressione sanguigna',
  
  // Time and dates
  today: 'Oggi',
  yesterday: 'Ieri',
  thisWeek: 'Questa settimana',
  thisMonth: 'Questo mese',
  thisYear: 'Quest\'anno',
  
  // Status
  active: 'Attivo',
  inactive: 'Inattivo',
  online: 'Online',
  offline: 'Offline',
  connected: 'Connesso',
  disconnected: 'Disconnesso',
  
  // Notifications
  notifications: 'Notifiche',
  noNotifications: 'Nessuna notifica',
  markAsRead: 'Segna come letto',
  markAllAsRead: 'Segna tutto come letto',
  
  // Search and filters
  search: 'Cerca',
  filter: 'Filtra',
  sort: 'Ordina',
  sortBy: 'Ordina per',
  filterBy: 'Filtra per',
  
  // Language settings
  language: 'Lingua',
  selectLanguage: 'Seleziona lingua',
  
  // Device management
  deviceName: 'Nome dispositivo',
  deviceType: 'Tipo dispositivo',
  batteryLevel: 'Livello batteria',
  lastSync: 'Ultima sincronizzazione',
  deviceStatus: 'Stato dispositivo',
  
  // Reports and analytics
  reports: 'Report',
  analytics: 'Analisi',
  dashboard: 'Dashboard',
  export: 'Esporta',
  print: 'Stampa',
  
  // Emergency and alerts
  emergency: 'Emergenza',
  alert: 'Avviso',
  critical: 'Critico',
  urgent: 'Urgente',
  normal: 'Normale',
  
  // Units
  celsius: '°C',
  fahrenheit: '°F',
  kg: 'kg',
  lbs: 'lbs',
  cm: 'cm',
  inches: 'pollici',
  
  // Days of week
  monday: 'Lunedì',
  tuesday: 'Martedì',
  wednesday: 'Mercoledì',
  thursday: 'Giovedì',
  friday: 'Venerdì',
  saturday: 'Sabato',
  sunday: 'Domenica',
  
  // Months
  january: 'Gennaio',
  february: 'Febbraio',
  march: 'Marzo',
  april: 'Aprile',
  may: 'Maggio',
  june: 'Giugno',
  july: 'Luglio',
  august: 'Agosto',
  september: 'Settembre',
  october: 'Ottobre',
  november: 'Novembre',
  december: 'Dicembre',
  
  // Error Messages
  animalNotFound: 'Animale non trovato',
  missingAnimalId: 'ID animale richiesto per visualizzare questa schermata',
  
  // Action Labels
  goBack: 'Torna indietro',
};
