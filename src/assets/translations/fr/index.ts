// Modular French translations - combines all feature-specific translation files
import { common } from './common';
import { navigation } from './navigation';
import { auth } from './auth';
import { animals } from './animals';
import { ai } from './ai';
import { devices } from './devices';

// Combine all translation modules into a single French translation object
export const fr = {
  ...common,
  ...navigation,
  ...auth,
  ...animals,
  ...devices,
  
  // Nested objects for dot notation access
  common,
  ai,
  
  // AI Widget keys (at root level for flat access)
  aiWidgetInsights: 'Insights IA',
  aiWidgetHealth: 'État de Santé',
  aiWidgetLiveCheckIn: 'Check-in en Direct',
  aiWidgetViewAll: 'Voir Tout',
  aiWidgetNew: 'Nouveau',
  aiWidgetLogTraining: 'Enregistrer Entraînement',
  aiWidgetNoAIData: 'Aucune Donnée IA',
  aiWidgetReadiness: 'Prêt',
  aiWidgetLiveStatus: 'Statut en Direct',
  aiWidgetAvailable: 'Disponible',
  aiWidgetLatestTip: 'Dernier Conseil',
  aiWidgetChecking: 'Vérification...',
  
  // Additional translations for backward compatibility
  language: 'Langue',
  languageSettings: 'Langue et région',
  selectYourLanguage: 'Sélectionnez votre langue',
  languageChanged: 'Langue changée avec succès',
  selectLanguage: 'Sélectionner la langue',
  changingLanguage: 'Changement de langue...',
  
  // Settings Categories
  dataManagement: 'Gestion des données',
  notifications: 'Notifications',
  themeSettings: 'Paramètres de thème',
  darkMode: 'Mode sombre',
  syncSettings: 'Paramètres de synchronisation',
  healthAlerts: 'Alertes santé',
  reminders: 'Rappels',
  
  // Health Alert Settings
  heartRateAlerts: 'Alertes rythme cardiaque',
  heartRateRange: 'Plage de rythme cardiaque',
  minBpm: 'Min (bpm)',
  maxBpm: 'Max (bpm)',
  temperatureAlerts: 'Alertes température',
  temperatureRange: 'Plage de température',
  minCelsius: 'Min (°C)',
  maxCelsius: 'Max (°C)',
  locationAlerts: 'Alertes de localisation',
  locationUpdateInterval: 'Intervalle de mise à jour de localisation',
  locationUpdateLabel: 'Alerter si aucune mise à jour pendant plus de (heures):',
  
  // Reminder Settings
  reminderTimeBefore: 'Temps de rappel avant (minutes):',
  saveSettings: 'Enregistrer les paramètres',
  settingsSavedSuccess: 'Paramètres enregistrés avec succès',
  errorFixBeforeSaving: 'Veuillez corriger les erreurs avant d\'enregistrer',
  errorMaxHeartRateHigher: 'Le rythme cardiaque maximum doit être supérieur au minimum',
  errorMaxTemperatureHigher: 'La température maximum doit être supérieure au minimum',
  
  // Monitoring Settings
  heartRateLimits: 'Limites de rythme cardiaque',
  locationTracking: 'Suivi de localisation',
  
  // Reminder Settings
  feedingReminders: 'Rappels d\'alimentation',
  medicationReminders: 'Rappels de médicaments',
  vaccinationReminders: 'Rappels de vaccination',
  
  // Device Management
  scanForDevices: 'Rechercher des appareils',
  scanning: 'Recherche...',
  devicesFound: 'appareils trouvés',
  noDevicesFound: 'Aucun appareil trouvé',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'Votre compagnon d\'elevage',
  animalSpeedTitle: 'Vitesse de {{animalName}}',
  speedUpdatedSuccess: 'Vitesse de {{animalName}} mise à jour',
  speedUpdateFailed: 'Échec de la mise à jour de la vitesse',
  
  // Quick Stats
  quickOverview: 'Aperçu rapide',
  activeDevices: 'Appareils actifs',
  todaysFeedings: 'Alimentations d\'aujourd\'hui',
  medications: 'Médicaments',
  
  // Upcoming Tasks
  upcomingTasks: 'Tâches à venir',
  noTasksToday: 'Aucune tâche programmée pour aujourd\'hui',
  allCaughtUp: 'Tout est à jour! Vos animaux sont bien soignés.',
  feedingTask: 'Alimentation: {{feedType}}',
  medicationTask: 'Médicament: {{medicationName}}',
  amount: 'Quantité',
  dosage: 'Dosage',
  moreTasksToday: '+{{count}} tâches supplémentaires aujourd\'hui',
  
  // Premium Banner
  premium: 'Premium',
  upgradeToPremium: 'Passer à Premium',
  unlimitedAnimals: 'Animaux illimités',
  advancedAnalytics: 'Analyses avancées',
  upgradeNow: 'Mettre à niveau maintenant',
  
  // Speed Monitor
  speed: 'Vitesse',
  currentSpeed: 'Vitesse actuelle',
  lastUpdated: 'Dernière mise à jour',
  
  // Health and vitals
  health: 'Santé',
  vitals: 'Constantes',
  temperature: 'Température',
  heartRate: 'Rythme cardiaque',
  respirationRate: 'Fréquence respiratoire',
  bloodPressure: 'Pression artérielle',
  
  // Additional keys for backward compatibility
  behavioralAnalysis: 'Analyse comportementale',
  environmentalAnalysisDescription: 'Analyser l\'impact de l\'environnement sur la santé',
  environmentalImpact: 'Impact environnemental',
  viewAll: 'Voir tout',
  
  // Missing AI & Health Analysis Keys (French translations)
  diseaseRiskAssessment: 'Évaluation des Risques de Maladie',
  healthTrends: 'Tendances de Santé',
  stressAnalysis: 'Analyse du Stress',
  sleepMonitoring: 'Surveillance du Sommeil',
  environmentalAnalysis: 'Analyse Environnementale',
  predictiveInsights: 'Insights Prédictifs',
  aiHealthDashboard: 'Tableau de Bord Santé IA',
  healthDashboard: 'Tableau de Bord Santé',
  smartAlerts: 'Alertes Intelligentes',
  healthScoreDetails: 'Détails du Score de Santé',
  trendsOverview: 'Aperçu des Tendances',
  riskOverview: 'Aperçu des Risques',
  
  // AI Analysis Terms
  analyzing: 'Analyse en cours...',
  analyzingEnvironment: 'Analyse de l\'environnement...',
  analyzeEnvironment: 'Analyser l\'Environnement',
  generatingPredictions: 'Génération de prédictions...',
  generatePredictions: 'Générer des Prédictions',
  predictionHorizon: 'Horizon de Prédiction',
  oneWeek: '1 Semaine',
  oneMonth: '1 Mois',
  threeMonths: '3 Mois',
  healthPredictions: 'Prédictions de Santé',
  predictedHealthScore: 'Score de Santé Prédit',
  predictedWeight: 'Poids Prédit',
  predictedActivity: 'Activité Prédite',
  predictedSleep: 'Sommeil Prédit',
  predictionConfidence: 'Confiance de Prédiction',
  riskAssessment: 'Évaluation des Risques',
  diseaseRisk: 'Risque de Maladie',
  injuryRisk: 'Risque de Blessure',
  behavioralRisk: 'Risque Comportemental',
  environmentalStressRisk: 'Risque de Stress Environnemental',
  predictiveAlerts: 'Alertes Prédictives',
  acknowledgeAlert: 'Reconnaître',
  resolveAlert: 'Résoudre',
  
  // Environmental Analysis
  humidity: 'Humidité',
  airQuality: 'Qualité de l\'Air',
  uvIndex: 'Indice UV',
  moderate: 'Modéré',
  hazardous: 'Dangereux',
  optimal: 'Optimal',
};