// Authentication and security - French
export const auth = {
  // Authentication
  createAccount: 'Créer un compte',
  resetPassword: 'Réinitialiser le mot de passe',
  signInToAccount: 'Se connecter à votre compte',
  login: 'Connexion',
  logout: 'Déconnexion',
  register: 'S\'inscrire',
  email: 'Email',
  password: 'Mot de passe',
  confirmPassword: 'Confirmer le mot de passe',
  forgotPassword: 'Mot de passe oublié?',
  
  // Biometric Authentication
  biometricLogin: 'Connexion biométrique',
  signInWithFingerprint: 'Se connecter avec l\'empreinte',
  signInWithFaceId: 'Se connecter avec Face ID',
  signInWithBiometric: 'Se connecter avec la biométrie',
  biometricAuthenticationFailed: 'Échec de l\'authentification biométrique',
  biometricNotAvailable: 'L\'authentification biométrique n\'est pas disponible sur cet appareil',
  biometricNotEnabled: 'La connexion biométrique n\'est pas activée',
  biometricSetupRequired: 'Configuration biométrique requise',
  biometricSetupDescription: 'Pour utiliser la connexion biométrique, veuillez d\'abord configurer {{biometricType}} dans les paramètres de votre appareil.',
  enableBiometricLogin: 'Activer la connexion biométrique pour un accès rapide et sécurisé à votre compte.',
  biometricLoginEnabled: 'Connexion biométrique activée avec succès!',
  biometricLoginDisabled: 'Connexion biométrique désactivée',
  disableBiometricLogin: 'Désactiver la connexion biométrique',
  disableBiometricConfirm: 'Êtes-vous sûr de vouloir désactiver la connexion biométrique? Vous devrez utiliser votre mot de passe pour vous connecter.',
  biometricDataSecure: 'Vos données biométriques restent sécurisées sur votre appareil et ne sont jamais partagées.',
  enableBiometricPrompt: 'Activer la connexion biométrique',
  biometricSetupConfirm: 'Utilisez votre biométrie pour confirmer la configuration',
  biometricSignInPrompt: 'Se connecter à HoofBeat',
  biometricSignInSubtitle: 'Utilisez votre biométrie pour accéder à votre compte',
  biometricSetupCancelled: 'Configuration biométrique annulée',
  biometricRefreshSession: 'Veuillez vous connecter avec votre mot de passe pour actualiser votre session, puis réactiver la connexion biométrique.',
  biometricCredentialsFirst: 'Veuillez vérifier vos identifiants avant d\'activer la connexion biométrique',
  biometricSignInPasswordFirst: 'Veuillez d\'abord vous connecter avec votre mot de passe pour activer la connexion biométrique',
  or: 'ou',
  
  // Error Messages
  userNotFound: 'Utilisateur non trouvé',
  profileCreationFailed: 'Échec de la création du profil utilisateur',
  profileLoadFailed: 'Échec du chargement du profil utilisateur',
  sessionExpired: 'Session expirée. Veuillez vous reconnecter.',
  networkError: 'Erreur réseau. Vérifiez votre connexion.',
  unexpectedError: 'Une erreur inattendue s\'est produite',
  
  // Biometric types
  biometric: 'Biométrique',
  fingerprint: 'Empreinte digitale',
  faceId: 'Face ID',
  biometricAuthentication: 'Authentification biométrique',
  
  // MFA
  mfaSettings: 'Authentification à deux facteurs',
  mfaEnabled: 'Authentification à deux facteurs activée',
  mfaDisabled: 'Authentification à deux facteurs désactivée',
};