export const devices = {
  // Statut de l'appareil
  paired: '<PERSON><PERSON><PERSON>',
  available: 'Disponible',
  connected: 'Connecté',
  disconnected: 'Déconnecté',
  connecting: 'Connexion...',
  
  // Découverte d'appareils
  findDevices: 'Trouver des appareils',
  scanForDevices: 'Rechercher des appareils',
  scanning: 'Recherche...',
  loadingDevices: 'Chargement des appareils...',
  checkingBluetooth: 'Vérification du Bluetooth...',
  noDevicesFound: 'Aucun appareil trouvé',
  
  // Jumelage d'appareils
  pairDevice: 'Jumeler l\'appareil',
  pairDevicePrompt: 'Sélectionnez un appareil à jumeler',
  pairing: 'Jumelage...',
  pairingSuccess: 'Appareil jumelé avec succès',
  pairingFailed: 'Échec du jumelage de l\'appareil',
  noPairedDevices: 'Aucun appareil jumelé trouvé',
  
  // Gestion des appareils
  unpairDevice: 'Disso<PERSON> l\'appareil',
  deviceInfo: 'Informations sur l\'appareil',
  batteryLevel: 'Niveau de batterie',
  signalStrength: 'Force du signal',
  lastSeen: 'Vu pour la dernière fois',
  
  // Bluetooth
  bluetoothDisabled: 'Bluetooth désactivé',
  enableBluetooth: 'Activer le Bluetooth',
  bluetoothUnavailable: 'Bluetooth indisponible',
  bluetoothPermission: 'Permission Bluetooth requise',
  
  // Types d'appareils
  heartRateMonitor: 'Moniteur de fréquence cardiaque',
  activityTracker: 'Tracker d\'activité',
  smartCollar: 'Collier intelligent',
  temperatureSensor: 'Capteur de température',
  gpsTracker: 'Tracker GPS',
  
  // Actions
  refresh: 'Actualiser',
  retry: 'Réessayer',
  cancel: 'Annuler',
  connect: 'Connecter',
  disconnect: 'Déconnecter',
  
  // Clés supplémentaires utilisées dans DevicesScreen
  initializing: 'Initialisation...',
  bluetoothErrorPrefix: 'Erreur Bluetooth:',
  retryBluetoothCheck: 'Réessayer la vérification Bluetooth',
  bluetoothRequiredWarning: 'Bluetooth est requis pour la connectivité des appareils',
  availableToAdd: 'Disponible à ajouter',
  addDevice: 'Ajouter un appareil',
  scanToFindDevicesPrompt: 'Appuyez sur scanner pour trouver des appareils à proximité',
  deviceIdPlaceholder: 'Entrez l\'ID de l\'appareil ou scannez le code-barres',
  add: 'Ajouter'
};