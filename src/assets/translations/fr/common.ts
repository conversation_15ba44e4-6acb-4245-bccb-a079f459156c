// Common UI elements and actions - French
export const common = {
  // Common actions
  add: 'Ajouter',
  edit: 'Modifier',
  delete: 'Supprimer',
  save: 'Enregistrer',
  cancel: 'Annuler',
  confirm: 'Confirmer',
  back: 'Retour',
  next: 'Suivant',
  previous: 'Précédent',
  loading: 'Chargement...',
  error: 'Erreur',
  success: 'Succès',
  warning: 'Avertissement',
  info: 'Information',
  retry: 'Réessayer',
  refresh: 'Actualiser',
  refreshing: 'Actualisation...',
  refresh: 'Actualiser',
  refreshing: 'Actualisation...',
  viewDetails: 'Voir les détails',
  viewAll: 'Voir tout',
  
  // Status
  active: 'Actif',
  inactive: 'Inactif',
  online: 'En ligne',
  offline: 'Hors ligne',
  connected: 'Connecté',
  disconnected: 'Déconnecté',
  enabled: 'Activé',
  disabled: 'Désactivé',
  
  // Time and dates
  today: 'Aujourd\'hui',
  yesterday: 'Hier',
  thisWeek: 'Cette semaine',
  thisMonth: 'Ce mois',
  thisYear: 'Cette année',
  
  // Units
  celsius: '°C',
  fahrenheit: '°F',
  kg: 'kg',
  lbs: 'lbs',
  cm: 'cm',
  inches: 'pouces',
  minutes: 'minutes',
  hours: 'heures',
  days: 'jours',
  
  // Days of week
  monday: 'Lundi',
  tuesday: 'Mardi',
  wednesday: 'Mercredi',
  thursday: 'Jeudi',
  friday: 'Vendredi',
  saturday: 'Samedi',
  sunday: 'Dimanche',
  
  // Months
  january: 'Janvier',
  february: 'Février',
  march: 'Mars',
  april: 'Avril',
  may: 'Mai',
  june: 'Juin',
  july: 'Juillet',
  august: 'Août',
  september: 'Septembre',
  october: 'Octobre',
  november: 'Novembre',
  december: 'Décembre',
  
  // Missing keys from en/common.ts - French translations
  // Health Dashboard
  healthDashboard: 'Tableau de Bord Santé',
  overallHealthStatus: 'État de Santé Général',
  criticalAlerts: 'Alertes Critiques',
  recentActivity: 'Activité Récente',
  healthInsights: 'Aperçus Santé',
  quickActions: 'Actions Rapides',
  viewHealthScore: 'Voir Score de Santé',
  analyzeRisks: 'Analyser les Risques',
  viewTrends: 'Voir les Tendances',
  stressAnalysis: 'Analyse du Stress',
  sleepMonitoring: 'Surveillance du Sommeil',
  environmentalAnalysis: 'Analyse Environnementale',
  predictiveInsights: 'Aperçus Prédictifs',
  noAnimalsSelected: 'Aucun animal sélectionné',
  selectAnimalToViewHealth: 'Sélectionnez un animal pour voir son tableau de bord santé',
  healthDataLoading: 'Chargement des données de santé...',
  healthDataError: 'Erreur lors du chargement des données de santé',
  retryLoadingHealthData: 'Réessayer le chargement des données de santé',
  
  // FAQ
  frequentlyAskedQuestions: 'Questions Fréquemment Posées',
  howToAddAnimal: 'Comment ajouter un nouvel animal?',
  howToAddAnimalAnswer: 'Pour ajouter un nouvel animal, allez dans l\'onglet Animaux et appuyez sur le bouton "+". Remplissez les informations de votre animal incluant le nom, la race, l\'âge et autres détails.',
  howToConnectDevice: 'Comment connecter un appareil de surveillance?',
  howToConnectDeviceAnswer: 'Allez dans l\'onglet Appareils, appuyez sur "Ajouter Appareil", et suivez les instructions d\'appairage. Assurez-vous que le Bluetooth est activé sur votre téléphone.',
  whatIsHealthScore: 'Qu\'est-ce que le Score de Santé?',
  whatIsHealthScoreAnswer: 'Le Score de Santé est une évaluation générée par IA de la santé globale de votre animal basée sur les signes vitaux, niveaux d\'activité et autres données surveillées.',
  howToSetupAlerts: 'Comment configurer les alertes de santé?',
  howToSetupAlertsAnswer: 'Allez dans Paramètres > Notifications pour configurer quand et comment vous recevez les alertes sur l\'état de santé de votre animal.',
  whatDataIsTracked: 'Quelles données l\'application suit-elle?',
  whatDataIsTrackedAnswer: 'L\'application suit les signes vitaux (rythme cardiaque, température), niveaux d\'activité, localisation, horaires d\'alimentation, médicaments et facteurs environnementaux.',
  howToExportData: 'Comment puis-je exporter les données de mon animal?',
  howToExportDataAnswer: 'Allez au profil de votre animal, appuyez sur le bouton menu, et sélectionnez "Exporter Données". Vous pouvez choisir la plage de dates et types de données à exporter.',
  isPremiumWorthIt: 'L\'abonnement Premium en vaut-il la peine?',
  isPremiumWorthItAnswer: 'Premium offre des animaux illimités, des aperçus IA avancés, des rapports de santé détaillés et un support prioritaire. C\'est idéal pour un usage professionnel ou plusieurs animaux.',
  howToContactSupport: 'Comment contacter le support?',
  howToContactSupportAnswer: 'Vous pouvez contacter le support via l\'onglet Contact dans votre profil, ou nous envoyer un email directement à <EMAIL>.',
  
  // Contact
  contactUs: 'Nous Contacter',
  messageSent: 'Message envoyé avec succès',
  messageSentTitle: 'Message Envoyé',
  messageSentDescription: 'Votre message a été envoyé à <EMAIL>. Nous vous répondrons bientôt!',
  failedToSendMessage: 'Échec de l\'envoi du message. Veuillez réessayer.',
  errorSendingMessageTitle: 'Erreur d\'Envoi du Message',
  errorSendingMessageDescription: 'Il y a eu un problème lors de l\'envoi de votre message. Veuillez réessayer ou utiliser l\'option de chat en direct.',
  chatError: 'Erreur de Chat',
  failedToOpenChat: 'Échec de l\'ouverture du chat. Veuillez réessayer.',
  openingChatSupport: 'Ouverture du support chat...',
  
  // Print
  print: 'Imprimer',
  printed: 'Imprimé',
  download: 'Télécharger',
  downloaded: 'Téléchargé',
  shared: 'Partagé',
  animalNotFound: 'Animal non trouvé',
  missingAnimalId: 'ID de l\'animal requis pour afficher cet écran',
  goBack: 'Retour',
  
  // Barcode Scanner
  cameraPermissionRequired: 'Permission de caméra requise pour scanner les codes-barres',
  invalidBarcodeFormat: 'Format de code-barres d\'appareil invalide',
  deviceBarcodeScanned: 'Code-barres d\'appareil scanné',
  requestingCameraPermission: 'Demande de permission de caméra...',
  noAccessToCamera: 'Aucun accès à la caméra',
  scanDeviceBarcode: 'Scanner le Code-barres de l\'Appareil',
  positionBarcodeInFrame: 'Positionnez le code-barres dans le cadre pour scanner',
  scanAgain: 'Scanner à Nouveau',
  
  // Privacy Settings
  privacySettings: 'Paramètres de Confidentialité',
  privacySettingsSaved: 'Paramètres de confidentialité sauvegardés',
  accountDeleted: 'Compte supprimé',
  locationTracking: 'Suivi de Localisation',
  locationTrackingDescription: 'Permettre à l\'application de suivre les localisations de vos animaux. Ceci est requis pour les alertes de localisation.',
  shareDataWithVets: 'Partager Données avec Vétérinaires',
  shareDataWithVetsDescription: 'Permettre aux vétérinaires d\'accéder aux dossiers de santé de vos animaux quand vous les partagez.',
  analytics: 'Analytiques',
  analyticsDescription: 'Aidez-nous à améliorer en permettant la collecte de données d\'usage anonymes.',
  saveSettings: 'Sauvegarder Paramètres',
  
  // Alert Types
  heartRateAlert: 'Alerte Rythme Cardiaque',
  temperatureAlert: 'Alerte Température',
  locationAlert: 'Alerte Localisation',
  healthAlert: 'Alerte Santé',
  
  // Health Status
  score: 'Score',
  alerts: 'Alertes',
  trends: 'Tendances',
  tracked: 'Suivi',
  criticalAlertsRequireAttention: 'alertes critiques nécessitent attention',
  lastUpdated: 'Dernière mise à jour',
  excellent: 'Excellent',
  good: 'Bon',
  fair: 'Passable',
  poor: 'Mauvais',
  critical: 'Critique',
  aiAnalysis: 'Analyse IA',
  active: 'Actif',
  seeAll: 'Voir Tout',
  noAnimalsYet: 'Aucun animal pour le moment',
  addFirstAnimal: 'Ajouter votre premier animal',
};