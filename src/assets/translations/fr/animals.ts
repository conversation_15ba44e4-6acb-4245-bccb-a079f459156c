// Animal management - French
export const animals = {
  // Animal management
  animalName: 'Nom de l\'animal',
  animalType: 'Type d\'animal',
  breed: 'Race',
  age: 'Âge',
  gender: 'Sexe',
  weight: 'Poids',
  color: 'Couleur',
  microchipId: 'ID de la puce',
  registrationNumber: 'Numéro d\'enregistrement',
  
  // Animals Overview
  myAnimals: 'Mes animaux',
  seeAll: 'Voir tout',
  addAnimal: 'Ajouter un animal',
  noAnimalsYet: 'Aucun animal ajouté pour le moment',
  addFirstAnimal: 'Ajoutez votre premier animal',
  id: 'ID',
  ageYears: '{{age}} ans',
  gps: 'GPS',
  
  // Animal Detail Screen
  animalDetailLatestVitals: 'Dernières constantes',
  animalDetailDeviceOnly: 'Appareil uniquement',
  animalDetailRecordedOn: 'Enregistré le',
  animalDetailTemperature: 'Température',
  animalDetailHeartRate: 'Rythme cardiaque',
  animalDetailRespiration: 'Respiration',
  animalDetailWeight: 'Poids',
  animalDetailNotes: 'Notes',
  animalDetailNoVitalsRecorded: 'Aucune constante enregistrée',
  animalDetailConnectDevice: 'Connecter un appareil pour commencer la surveillance',
  animalDetailMedications: 'Médicaments',
  animalDetailAdd: 'Ajouter',
  animalDetailPrintMedicationSchedule: 'Imprimer le planning des médicaments',
  animalDetailNoMedicationsAdded: 'Aucun médicament ajouté',
  animalDetailAddMedication: 'Ajouter un médicament',
  animalDetailVaccinations: 'Vaccinations',
  animalDetailDue: 'Échéance',
  animalDetailNoRenewal: 'Pas de renouvellement',
  animalDetailPrintVaccinationRecord: 'Imprimer le carnet de vaccination',
  animalDetailNoVaccinationsRecorded: 'Aucune vaccination enregistrée',
  animalDetailAddVaccination: 'Ajouter une vaccination',
  animalDetailMicrochipRequired: 'ID de puce requis pour les vaccinations',
  animalDetailFeedingSchedule: 'Planning d\'alimentation',
  animalDetailViewAll: 'Voir tout',
  animalDetailPrintFeedingSchedule: 'Imprimer le planning d\'alimentation',
  animalDetailNoFeedingSchedule: 'Aucun planning d\'alimentation défini',
  animalDetailSetFeedingSchedule: 'Définir le planning d\'alimentation',
  animalDetailTrainingSessions: 'Séances d\'entraînement',
  animalDetailLoadingSessions: 'Chargement des séances...',
  animalDetailLogNewSession: 'Enregistrer une nouvelle séance',
  animalDetailNoTrainingSessions: 'Aucune séance d\'entraînement',
  animalDetailTrainingDescription: 'Commencez à suivre vos séances d\'entraînement pour surveiller les progrès et les performances.',
  animalDetailLogFirstSession: 'Enregistrer la première séance',
  
  // Animal not found
  animalNotFound: 'Animal non trouvé',
};