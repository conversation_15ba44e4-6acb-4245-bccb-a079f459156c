// This file has been deprecated and replaced by the modular translation system
// All translations are now managed through assets/translations/newIndex.ts

// Re-export everything from newIndex.ts for consolidated translation management
export * from './newIndex';
    biometricCredentialsFirst: 'Please verify your credentials before enabling biometric login',
    biometricSignInPasswordFirst: 'Please sign in with your password first to enable biometric login',
    or: 'or',
    
    // AI Guidance
    aiGuidanceTitle: 'Get More from Your AI Assistant',
    aiGuidanceDescription: 'Your AI assistant needs more data to provide personalized insights and recommendations.',
    aiGuidanceWhatYouGet: 'What you\'ll get:',
    aiGuidancePersonalizedPlans: 'Personalized training plans',
    aiGuidanceHealthInsights: 'Health insights and alerts',
    aiGuidancePerformanceTracking: 'Performance tracking',
    aiGuidanceSmartRecommendations: 'Smart recommendations',
    aiGuidanceDataProgress: 'Data Progress',
    aiGuidanceCompleteData: '{{percentage}}% complete',
    aiGuidanceLogTrainingSession: 'Log a Training Session',
    aiGuidanceRecordVitals: 'Record Vitals',
    aiGuidanceUpdateFeedingSchedule: 'Update Feeding Schedule',
    aiGuidanceStartLogging: 'Start logging data to unlock AI insights',
    aiGuidanceMoreDataNeeded: 'More data needed for AI insights',
    aiGuidanceGetStarted: 'Get started by logging your first training session or recording vitals.',
    
    // AI Chat
    aiChat: 'AI Chat',
    aiChatTitle: 'Chat with AI Assistant',
    aiChatPlaceholder: 'Ask about your animal\'s health, training, or care...',
    aiChatSend: 'Send',
    aiChatAttachment: 'Attach Image',
    aiChatCamera: 'Take Photo',
    aiChatGallery: 'Choose from Gallery',
    aiChatAnalyzing: 'Analyzing...',
    aiChatTyping: 'AI is typing...',
    aiChatNoMessages: 'No messages yet',
    aiChatStartConversation: 'Start a conversation with your AI assistant about {{animalName}}',
    aiChatImageAnalysis: 'Image Analysis',
    aiChatCopyMessage: 'Copy Message',
    aiChatShareMessage: 'Share Message',
    aiChatMessageCopied: 'Message copied to clipboard',
    aiChatUploadingImage: 'Uploading image...',
    aiChatImageUploadFailed: 'Failed to upload image',
    aiChatSendingMessage: 'Sending message...',
    aiChatMessageFailed: 'Failed to send message',
    aiChatRetry: 'Retry',
    aiChatMaxFileSize: 'File size must be less than 10MB',
    aiChatUnsupportedFormat: 'Unsupported file format',
    
    // Dehydration/Hydration Monitoring
    dehydrationMonitoring: 'Dehydration Monitoring',
    hydrationMonitoring: 'Hydration Monitoring',
    optimal: 'Optimal',
    mildDehydration: 'Mild Dehydration',
    moderateDehydration: 'Moderate Dehydration',
    severeDehydration: 'Severe Dehydration',
    unknown: 'Unknown',
    noHydrationData: 'No hydration data available',
    takeReadingToStart: 'Take a reading to start monitoring',
    bodyTemp: 'Body Temp',
    bioimpedance: 'Bioimpedance',
    signalQuality: 'Signal Quality',
    takeReading: 'Take Reading',
    reading: 'Reading...',
    startMonitoring: 'Start Monitoring',
    stopMonitoring: 'Stop Monitoring',
    aiAnalysis: 'AI Analysis',
    overallStatus: 'Overall Status',
    trend: 'Trend',
    avgHydration: 'Avg Hydration',
    variability: 'Variability',
    aiAlerts: 'AI Alerts',
    recommendations: 'Recommendations',
    refreshAnalysis: 'Refresh Analysis',
    recentReadings: 'Recent Readings',
    failedToLoadHydrationData: 'Failed to load hydration data',
    retry: 'Retry',
    loadingHydrationData: 'Loading hydration data...',
    excellent: 'Excellent',
    good: 'Good',
    concerning: 'Concerning',
    critical: 'Critical',
    improving: 'Improving',
    declining: 'Declining',
    stable: 'Stable',
    statusNormal: 'Normal',
    
    // AI Health Analysis
    aiHealthScore: 'AI Health Score',
    excellent: 'Excellent',
    good: 'Good',
    concerning: 'Concerning',
    critical: 'Critical',
    vitals: 'Vitals',
    activity: 'Activity',
    feeding: 'Feeding',
    medication: 'Medication',
    viewDetails: 'View Details',
    noHealthScore: 'No Health Score Yet',
    calculateFirstScore: 'Calculate your first AI health score to get insights',
    calculating: 'Calculating...',
    calculateScore: 'Calculate Score',
    
    // AI Health Dashboard
    aiHealthDashboard: 'AI Health Dashboard',
    activeAlerts: 'Active Alerts',
    highRisks: 'High Risks',
    smartAlerts: 'Smart Alerts',
    diseaseRiskAssessment: 'Disease Risk Assessment',
    healthTrends: 'Health Trends',
    quickActions: 'Quick Actions',
    updateHealthScore: 'Update Health Score',
    recordVitals: 'Record Vitals',
    viewAllAlerts: 'View All Alerts',
    more: 'more',
    noActiveAlerts: 'No active alerts',
    noRiskAssessments: 'No risk assessments',
    noHealthTrends: 'No health trends',
    noAnimalsFound: 'No animals found',
    addAnimalToStart: 'Add an animal to start using AI health analysis',
    
    // Smart Alerts
    recommendedActions: 'Recommended Actions',
    markResolved: 'Mark Resolved',
    expires: 'Expires',
    
    // Disease Risk
    disease: 'Disease',
    risk: 'Risk',
    confidence: 'Confidence',
    assessed: 'Assessed',
    contributingFactors: 'Contributing Factors',
    recommendations: 'Recommendations',
    criticalRiskDescription: 'Immediate veterinary attention required',
    highRiskDescription: 'Elevated risk - monitor closely',
    mediumRiskDescription: 'Moderate risk - routine monitoring',
    lowRiskDescription: 'Low risk - continue normal care',
    highConfidence: 'High',
    mediumConfidence: 'Medium',
    lowConfidence: 'Low',
    low: 'Low',
    high: 'High',
    
    // Health Score Details
    healthScoreDetails: 'Health Score Details',
    currentHealthScore: 'Current Health Score',
    scoreBreakdown: 'Score Breakdown',
    analysis: 'Analysis',
    healthScoreHistory: 'Health Score History',
    scoreHistory: 'Score History',
    entries: 'entries',
    noScoreHistory: 'No score history available',
    calculateFirstScore: 'Calculate First Score',
    dataQuality: 'Data Quality',
    excellent: 'Excellent',
    good: 'Good',
    concerning: 'Concerning',
    critical: 'Critical',
    sevenDays: '7 Days',
    thirtyDays: '30 Days',
    ninetyDays: '90 Days',
    vitals: 'Vitals',
    activity: 'Activity',
    feeding: 'Feeding',
    medication: 'Medication',
    
    // Disease Risk
    riskOverview: 'Risk Overview',
    analyzing: 'Analyzing...',
    runAnalysis: 'Run Analysis',
    totalAssessments: 'Total Assessments',
    lastUpdated: 'Last Updated',
    never: 'Never',
    runFirstAnalysis: 'Run your first analysis to get started',
    filterByCategory: 'Filter by Category',
    allCategories: 'All Categories',
    noRisksInCategory: 'No risks found in this category',
    tryDifferentCategory: 'Try selecting a different category',
    aboutRiskAssessment: 'About Risk Assessment',
    riskAssessmentInfo: 'AI-powered disease risk assessment analyzes your animal\'s health data to identify potential health concerns before they become serious.',
    riskLevels: 'Risk Levels',
    riskAnalysisCompleted: 'Risk analysis completed',
    riskAnalysisFailed: 'Risk analysis failed',
    detailedRiskView: 'Detailed risk view coming soon',
    
    // Health Trends
    trendsOverview: 'Trends Overview',
    processing: 'Processing...',
    processTrends: 'Process Trends',
    activeTrends: 'Active Trends',
    improving: 'Improving',
    declining: 'Declining',
    stable: 'Stable',
    trend: 'Trend',
    strength: 'Strength',
    period: 'Period',
    ongoing: 'Ongoing',
    trendData: 'Trend Data',
    processFirstTrends: 'Process your first trends to get started',
    filterByType: 'Filter by Type',
    allTrends: 'All Trends',
    noTrendsOfType: 'No trends found of this type',
    tryDifferentType: 'Try selecting a different type',
    aboutHealthTrends: 'About Health Trends',
    healthTrendsInfo: 'Health trends analyze patterns in your animal\'s data over time to identify improving or concerning patterns.',
    trendTypes: 'Trend Types',
    improvingTrendDescription: 'Positive health patterns showing improvement',
    stableTrendDescription: 'Consistent health patterns with minimal change',
    decliningTrendDescription: 'Concerning patterns that may need attention',
    trendsProcessed: 'Trends processed successfully',
    trendsProcessingFailed: 'Trends processing failed',
    
    // General
    animalNotFound: 'Animal not found',
    viewDetails: 'View Details',
    medium: 'Medium',
    
    // Health Score Details
    healthScoreDetails: 'Health Score Details',
    currentHealthScore: 'Current Health Score',
    scoreBreakdown: 'Score Breakdown',
    analysis: 'Analysis',
    healthScoreHistory: 'Health Score History',
    scoreHistory: 'Score History',
    entries: 'entries',
    noScoreHistory: 'No score history available',
    calculateFirstScore: 'Calculate First Score',
    dataQuality: 'Data Quality',
    excellent: 'Excellent',
    good: 'Good',
    concerning: 'Concerning',
    critical: 'Critical',
    sevenDays: '7 Days',
    thirtyDays: '30 Days',
    ninetyDays: '90 Days',
    vitals: 'Vitals',
    activity: 'Activity',
    feeding: 'Feeding',
    medication: 'Medication',
    
    // Disease Risk
    riskOverview: 'Risk Overview',
    analyzing: 'Analyzing...',
    runAnalysis: 'Run Analysis',
    totalAssessments: 'Total Assessments',
    lastUpdated: 'Last Updated',
    never: 'Never',
    runFirstAnalysis: 'Run your first analysis to get started',
    filterByCategory: 'Filter by Category',
    allCategories: 'All Categories',
    noRisksInCategory: 'No risks found in this category',
    tryDifferentCategory: 'Try selecting a different category',
    aboutRiskAssessment: 'About Risk Assessment',
    riskAssessmentInfo: 'AI-powered disease risk assessment analyzes your animal\'s health data to identify potential health concerns before they become serious.',
    riskLevels: 'Risk Levels',
    riskAnalysisCompleted: 'Risk analysis completed',
    riskAnalysisFailed: 'Risk analysis failed',
    detailedRiskView: 'Detailed risk view coming soon',
    
    // Health Trends
    trendsOverview: 'Trends Overview',
    processing: 'Processing...',
    processTrends: 'Process Trends',
    activeTrends: 'Active Trends',
    improving: 'Improving',
    declining: 'Declining',
    stable: 'Stable',
    trend: 'Trend',
    strength: 'Strength',
    period: 'Period',
    ongoing: 'Ongoing',
    trendData: 'Trend Data',
    processFirstTrends: 'Process your first trends to get started',
    filterByType: 'Filter by Type',
    allTrends: 'All Trends',
    noTrendsOfType: 'No trends found of this type',
    tryDifferentType: 'Try selecting a different type',
    aboutHealthTrends: 'About Health Trends',
    healthTrendsInfo: 'Health trends analyze patterns in your animal\'s data over time to identify improving or concerning patterns.',
    trendTypes: 'Trend Types',
    improvingTrendDescription: 'Positive health patterns showing improvement',
    stableTrendDescription: 'Consistent health patterns with minimal change',
    decliningTrendDescription: 'Concerning patterns that may need attention',
    trendsProcessed: 'Trends processed successfully',
    trendsProcessingFailed: 'Trends processing failed',
    
    // General
    animalNotFound: 'Animal not found',
    viewDetails: 'View Details',
    medium: 'Medium',
    
    // Error Messages
    userNotFound: 'User not found',
    profileCreationFailed: 'Failed to create user profile',
    profileLoadFailed: 'Failed to load user profile',
    sessionExpired: 'Session expired. Please sign in again.',
    networkError: 'Network error. Please check your connection.',
    unexpectedError: 'An unexpected error occurred',
    
    // AI Assistant Screen
    aiAssistant: 'AI Assistant',
    dataCompleteness: 'Data Completeness',
    analyzing: 'Analyzing...',
    getAIAnalysis: 'Get AI Analysis',
    readinessScore: 'Readiness Score',
    healthAssessment: 'Health Assessment',
    hydrationAnalysis: 'Hydration Analysis',
    avgHydration: 'Avg Hydration',
    variability: 'Variability',
    trainingPlan: 'Training Plan',
    coachingTips: 'Coaching Tips',
    readyForAIAnalysis: 'Ready for AI Analysis',
    
    // Phase 2: Stress Analysis
    stressAnalysis: 'Stress Analysis',
    analyzeStress: 'Analyze Stress',
    currentStressLevel: 'Current Stress Level',
    veryHigh: 'Very High',
    veryLow: 'Very Low',
    heartRateVariability: 'Heart Rate Variability',
    avgHeartRate: 'Avg Heart Rate',
    hrvScore: 'HRV Score',
    stressIndicator: 'Stress Indicator',
    activityIndicators: 'Activity Indicators',
    restlessness: 'Restlessness',
    activityLevel: 'Activity Level',
    movementPattern: 'Movement Pattern',
    environmentalFactors: 'Environmental Factors',
    temperature: 'Temperature',
    timeOfDay: 'Time of Day',
    identifiedTriggers: 'Identified Triggers',
    activeStressEvents: 'Active Stress Events',
    resolve: 'Resolve',
    stressHistory: 'Stress History',
    noStressAnalysis: 'No Stress Analysis Yet',
    runFirstStressAnalysis: 'Run your first stress analysis to get insights',
    
    // Phase 2: Sleep Monitoring
    sleepMonitoring: 'Sleep Monitoring',
    analyzeSleep: 'Analyze Sleep',
    lastNightSleep: 'Last Night\'s Sleep',
    totalSleep: 'Total Sleep',
    efficiency: 'Efficiency',
    sleepStages: 'Sleep Stages',
    deepSleep: 'Deep Sleep',
    lightSleep: 'Light Sleep',
    remSleep: 'REM Sleep',
    sleepMetrics: 'Sleep Metrics',
    timeToSleep: 'Time to Sleep',
    minutes: 'minutes',
    wakeEpisodes: 'Wake Episodes',
    circadianAlignment: 'Circadian Alignment',
    sleepEnvironment: 'Sleep Environment',
    avgTemperature: 'Avg Temperature',
    tempVariation: 'Temp Variation',
    humidity: 'Humidity',
    sleepDisturbances: 'Sleep Disturbances',
    sleepRecommendations: 'Sleep Recommendations',
    sleepHistory: 'Sleep History',
    noSleepAnalysis: 'No Sleep Analysis Yet',
    runFirstSleepAnalysis: 'Run your first sleep analysis to get insights',
    fair: 'Fair',
    poor: 'Poor',
    
    // Common UI
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information',
  },
  
  // Arabic (العربية) - RTL
  ar: {
    // Navigation & Headers
    settings: 'الإعدادات',
    advancedSettings: 'الإعدادات المتقدمة',
    profile: 'الملف الشخصي',
    animals: 'الحلال',
    devices: 'الأجهزة',
    home: 'الرئيسية',
    
    // Language Settings
    language: 'اللغة',
    languageSettings: 'اللغة والمنطقة',
    selectYourLanguage: 'اختر لغتك',
    languageChanged: 'تم تغيير اللغة بنجاح',
    
    // Settings Categories
    dataManagement: 'إدارة البيانات',
    notifications: 'الإشعارات',
    themeSettings: 'إعدادات المظهر',
    darkMode: 'الوضع الليلي',
    syncSettings: 'إعدادات المزامنة',
    healthAlerts: 'تنبيهات صحية',
    reminders: 'تذكيرات',
    
    // Health Alert Settings
    heartRateAlerts: 'تنبيهات معدل ضربات القلب',
    heartRateRange: 'نطاق معدل ضربات القلب',
    minBpm: 'الحد الأدنى (ضربة/د)',
    maxBpm: 'الحد الأقصى (ضربة/د)',
    temperatureAlerts: 'تنبيهات درجة الحرارة',
    temperatureRange: 'نطاق درجة الحرارة',
    minCelsius: 'الحد الأدنى (°م)',
    maxCelsius: 'الحد الأقصى (°م)',
    locationAlerts: 'تنبيهات الموقع',
    locationUpdateInterval: 'فترة تحديث الموقع',
    locationUpdateLabel: 'تنبيه إذا لم يتم التحديث لأكثر من (ساعات):',
    
    // Reminder Settings
    reminderTimeBefore: 'وقت التذكير قبل (دقائق):',
    saveSettings: 'حفظ الإعدادات',
    settingsSavedSuccess: 'تم حفظ الإعدادات بنجاح',
    errorFixBeforeSaving: 'يرجى إصلاح الأخطاء قبل الحفظ',
    errorMaxHeartRateHigher: 'يجب أن يكون الحد الأقصى لمعدل ضربات القلب أعلى من الحد الأدنى',
    errorMaxTemperatureHigher: 'يجب أن تكون درجة الحرارة القصوى أعلى من الدنيا',
    
    // Monitoring Settings
    heartRateLimits: 'حدود معدل ضربات القلب',
    temperatureAlerts: 'تنبيهات درجة الحرارة',
    locationTracking: 'تتبع الموقع',
    
    // Reminder Settings
    feedingReminders: 'تذكيرات التغذية',
    
    // Dehydration/Hydration Monitoring
    dehydrationMonitoring: 'مراقبة الجفاف',
    hydrationMonitoring: 'مراقبة الترطيب',
    optimal: 'مثالي',
    mildDehydration: 'جفاف خفيف',
    moderateDehydration: 'جفاف متوسط',
    severeDehydration: 'جفاف شديد',
    unknown: 'غير معروف',
    noHydrationData: 'لا توجد بيانات ترطيب',
    takeReadingToStart: 'اتخذ قراءة لبدء المراقبة',
    bodyTemp: 'حرارة الجسم',
    bioimpedance: 'المقاومة الحيوية',
    signalQuality: 'جودة الإشارة',
    takeReading: 'أخذ قراءة',
    reading: 'جاري القراءة...',
    startMonitoring: 'بدء المراقبة',
    stopMonitoring: 'إيقاف المراقبة',
    aiAnalysis: 'تحليل ذكي',
    overallStatus: 'الحالة العامة',
    trend: 'الاتجاه',
    avgHydration: 'متوسط الترطيب',
    variability: 'التباين',
    aiAlerts: 'تنبيهات ذكية',
    recommendations: 'توصيات',
    refreshAnalysis: 'تحديث التحليل',
    recentReadings: 'قراءات حديثة',
    failedToLoadHydrationData: 'فشل في تحميل بيانات الترطيب',
    retry: 'إعادة المحاولة',
    loadingHydrationData: 'جاري تحميل بيانات الترطيب...',
    excellent: 'ممتاز',
    good: 'جيد',
    concerning: 'مقلق',
    critical: 'حرج',
    improving: 'يتحسن',
    declining: 'يتدهور',
    stable: 'مستقر',
    statusNormal: 'طبيعي',
    
    // AI Health Analysis
    aiHealthScore: 'نقاط الصحة الذكية',
    excellent: 'ممتاز',
    good: 'جيد',
    concerning: 'مقلق',
    critical: 'حرج',
    vitals: 'العلامات الحيوية',
    activity: 'النشاط',
    feeding: 'التغذية',
    medication: 'الأدوية',
    viewDetails: 'عرض التفاصيل',
    noHealthScore: 'لا توجد نقاط صحة بعد',
    calculateFirstScore: 'احسب أول نقاط صحة ذكية للحصول على رؤى',
    calculating: 'جاري الحساب...',
    calculateScore: 'احسب النقاط',
    
    // AI Health Dashboard
    aiHealthDashboard: 'لوحة الصحة الذكية',
    activeAlerts: 'التنبيهات النشطة',
    highRisks: 'المخاطر العالية',
    smartAlerts: 'التنبيهات الذكية',
    diseaseRiskAssessment: 'تقييم مخاطر الأمراض',
    healthTrends: 'اتجاهات الصحة',
    quickActions: 'إجراءات سريعة',
    updateHealthScore: 'تحديث نقاط الصحة',
    recordVitals: 'تسجيل العلامات الحيوية',
    viewAllAlerts: 'عرض جميع التنبيهات',
    more: 'أكثر',
    noActiveAlerts: 'لا توجد تنبيهات نشطة',
    noRiskAssessments: 'لا توجد تقييمات مخاطر',
    noHealthTrends: 'لا توجد اتجاهات صحية',
    noAnimalsFound: 'لم يتم العثور على حيوانات',
    addAnimalToStart: 'أضف حيواناً لبدء استخدام تحليل الصحة الذكي',
    
    // Smart Alerts
    recommendedActions: 'الإجراءات الموصى بها',
    markResolved: 'وضع علامة محلول',
    expires: 'ينتهي',
    
    // Disease Risk
    disease: 'مرض',
    risk: 'مخاطرة',
    confidence: 'الثقة',
    assessed: 'مُقَيَّم',
    contributingFactors: 'العوامل المساهمة',
    recommendations: 'التوصيات',
    criticalRiskDescription: 'يتطلب عناية بيطرية فورية',
    highRiskDescription: 'مخاطرة مرتفعة - مراقبة دقيقة',
    mediumRiskDescription: 'مخاطرة متوسطة - مراقبة روتينية',
    lowRiskDescription: 'مخاطرة منخفضة - متابعة الرعاية العادية',
    highConfidence: 'عالية',
    mediumConfidence: 'متوسطة',
    lowConfidence: 'منخفضة',
    low: 'منخفض',
    high: 'عالي',
    medicationReminders: 'تذكيرات الأدوية',
    vaccinationReminders: 'تذكيرات التطعيم',
    
    // Actions
    saveChanges: 'حفظ التغييرات',
    cancel: 'إلغاء',
    confirm: 'تأكيد',
    delete: 'حذف',
    edit: 'تعديل',
    add: 'إضافة',
    
    // Device Management
    scanForDevices: 'البحث عن الأجهزة',
    scanning: 'جاري البحث...',
    devicesFound: 'جهاز موجود',
    noDevicesFound: 'لم يتم العثور على أجهزة',
    
    // Home Screen
    appTitle: 'HoofBeat',
    appSubtitle: 'رفيقك في تربية الحلال',
    animalSpeedTitle: 'سرعة {{animalName}}',
    speedUpdatedSuccess: 'تم تحديث سرعة {{animalName}}',
    speedUpdateFailed: 'فشل في تحديث السرعة',
    
    // Quick Stats
    quickOverview: 'نظرة سريعة',
    activeDevices: 'الأجهزة النشطة',
    todaysFeedings: 'تغذية اليوم',
    medications: 'الأدوية',
    
    // Animals Overview
    myAnimals: 'حلالي',
    seeAll: 'عرض الكل',
    addAnimal: 'إضافة حلال',
    noAnimalsYet: 'لم تتم إضافة حلال بعد',
    addFirstAnimal: 'أضف حلالك الأول',
    
    // Upcoming Tasks
    upcomingTasks: 'المهام القادمة',
    noTasksToday: 'لا توجد مهام مجدولة لليوم',
    allCaughtUp: 'كل شيء محدث! حلالك في رعاية جيدة.',
    feedingTask: 'التغذية: {{feedType}}',
    medicationTask: 'الدواء: {{medicationName}}',
    amount: 'الكمية',
    dosage: 'الجرعة',
    moreTasksToday: '+{{count}} مهام أخرى اليوم',
    
    // Premium Banner
    premium: 'مميز',
    upgradeToPremium: 'الترقية إلى المميز',
    unlimitedAnimals: 'حلال غير محدود',
    advancedAnalytics: 'تحليلات متقدمة',
    upgradeNow: 'ترقية الآن',
    
    // AI Onboarding
    meetYourAIAssistant: 'تعرف على مساعدك الذكي',
    aiAssistantDescription: 'احصل على خطط تدريب مخصصة ورؤى صحية ونصائح تدريبية مدعومة بالذكاء الاصطناعي.',
    readinessScores: 'درجات الاستعداد',
    trainingPlans: 'خطط التدريب',
    logFirstTrainingSession: 'سجل جلسة التدريب الأولى',
    
    // Speed Monitor
    speed: 'السرعة',
    currentSpeed: 'السرعة الحالية',
    refresh: 'تحديث',
    lastUpdated: 'آخر تحديث',
    
    // Animals Screen
    id: 'الهوية',
    ageYears: '{{age}} سنة',
    gps: 'جي بي إس',
    selectLanguage: 'اختر اللغة',
    changingLanguage: 'جاري تغيير اللغة...',
    
    // Animal Detail Screen
    animalDetailTrainingSessions: 'جلسات التدريب',
    animalDetailLoadingSessions: 'جاري تحميل الجلسات...',
    animalDetailLogNewSession: 'تسجيل جلسة جديدة',
    animalDetailNoTrainingSessions: 'لا توجد جلسات تدريب',
    animalDetailTrainingDescription: 'ابدأ بتسجيل جلسات التدريب للحصول على رؤى وتوصيات مدعومة بالذكاء الاصطناعي.',
    animalDetailLogFirstSession: 'تسجيل الجلسة الأولى',
    animalDetailLatestVitals: 'آخر العلامات الحيوية',
    animalDetailDeviceOnly: 'الجهاز فقط',
    animalDetailRecordedOn: 'مسجل في',
    animalDetailTemperature: 'درجة الحرارة',
    animalDetailHeartRate: 'معدل ضربات القلب',
    animalDetailRespiration: 'التنفس',
    animalDetailWeight: 'الوزن',
    animalDetailNotes: 'ملاحظات',
    animalDetailNoVitalsRecorded: 'لم يتم تسجيل علامات حيوية بعد',
    animalDetailConnectDevice: 'اربط جهازاً لتسجيل العلامات الحيوية تلقائياً',
    animalDetailFeedingSchedule: 'جدول التغذية',
    animalDetailViewAll: 'عرض الكل',
    animalDetailPrintFeedingSchedule: 'طباعة جدول التغذية',
    animalDetailNoFeedingSchedule: 'لم يتم تحديد جدول تغذية',
    animalDetailSetFeedingSchedule: 'تحديد جدول التغذية',
    animalDetailMedications: 'الأدوية',
    animalDetailAdd: '+ إضافة',
    animalDetailPrintMedicationSchedule: 'طباعة جدول الأدوية',
    animalDetailNoMedicationsAdded: 'لم تتم إضافة أدوية',
    animalDetailAddMedication: 'إضافة دواء',
    animalDetailVaccinations: 'التطعيمات',
    animalDetailDue: 'مستحق:',
    animalDetailNoRenewal: 'لا يوجد تجديد',
    animalDetailPrintVaccinationRecord: 'طباعة سجل التطعيمات',
    animalDetailNoVaccinationsRecorded: 'لم يتم تسجيل تطعيمات',
    animalDetailAddVaccination: 'إضافة تطعيم',
    animalDetailMicrochipRequired: 'معرف الشريحة الدقيقة مطلوب لسجلات التطعيم',
    animalDetailAIAssistant: 'المساعد الذكي',
    animalDetailReadinessScore: 'درجة الاستعداد',
    animalDetailHealthStatus: 'الحالة الصحية',
    animalDetailNewTips: 'نصائح جديدة',
    animalDetailTip: 'نصيحة',
    animalDetailTips: 'نصائح',
    animalDetailMoreTip: 'نصيحة أخرى',
    animalDetailMoreTips: 'نصائح أخرى',
    animalDetailNoAIInsights: 'لا توجد رؤى ذكية بعد. سجل جلسة تدريب للبدء.',
    animalDetailAnalyzing: 'جاري التحليل...',
    animalDetailGetAIAnalysis: 'الحصول على تحليل ذكي',
    animalDetailViewAllInsights: 'عرض جميع الرؤى',
    
    // AI Assistant Widget
    aiWidgetInsights: 'الرؤى الذكية',
    aiWidgetReadiness: 'الاستعداد:',
    aiWidgetNoAIData: 'لا توجد بيانات ذكية بعد',
    aiWidgetLogTraining: 'تسجيل التدريب',
    aiWidgetLiveStatus: 'الحالة المباشرة:',
    aiWidgetAvailable: 'متاح',
    aiWidgetHealth: 'الصحة:',
    aiWidgetLatestTip: 'آخر نصيحة:',
    aiWidgetChecking: 'جاري الفحص...',
    aiWidgetLiveCheckIn: 'فحص مباشر',
    aiWidgetViewAll: 'عرض الكل',
    aiWidgetNew: 'جديد',
    
    // Profile Screen
    profileSettings: 'الإعدادات',
    profileAdvancedSettings: 'إعدادات متقدمة',
    profilePrivacySettings: 'إعدادات الخصوصية',
    profileUpgradeToPremium: 'الترقية إلى بريميوم',
    profileHelpSupport: 'المساعدة والدعم',
    profileFAQ: 'الأسئلة الشائعة',
    profileContactSupport: 'اتصل بالدعم',
    profileSignOut: 'تسجيل الخروج',
    profileSignOutConfirm: 'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
    profileCancel: 'إلغاء',
    profileVersion: 'الإصدار',
    profileSignOutFailed: 'فشل في تسجيل الخروج',
    
    // Biometric Authentication
    biometricLogin: 'تسجيل الدخول البيومتري',
    signInWithFingerprint: 'تسجيل الدخول ببصمة الإصبع',
    signInWithFaceId: 'تسجيل الدخول بـ Face ID',
    signInWithBiometric: 'تسجيل الدخول البيومتري',
    biometricAuthenticationFailed: 'فشل في المصادقة البيومترية',
    biometricNotAvailable: 'المصادقة البيومترية غير متاحة على هذا الجهاز',
    biometricNotEnabled: 'تسجيل الدخول البيومتري غير مفعل',
    biometricSetupRequired: 'إعداد بيومتري مطلوب',
    biometricSetupDescription: 'لاستخدام تسجيل الدخول البيومتري، يرجى إعداد {{biometricType}} في إعدادات جهازك أولاً.',
    enableBiometricLogin: 'فعل تسجيل الدخول البيومتري للوصول السريع والآمن لحسابك.',
    biometricLoginEnabled: 'تم تفعيل تسجيل الدخول البيومتري بنجاح!',
    biometricLoginDisabled: 'تم إلغاء تسجيل الدخول البيومتري',
    disableBiometricLogin: 'إلغاء تسجيل الدخول البيومتري',
    disableBiometricConfirm: 'هل أنت متأكد من رغبتك في إلغاء تسجيل الدخول البيومتري؟ ستحتاج لاستخدام كلمة المرور لتسجيل الدخول.',
    biometricDataSecure: 'بياناتك البيومترية تبقى آمنة على جهازك ولا يتم مشاركتها أبداً.',
    enableBiometricPrompt: 'تفعيل تسجيل الدخول البيومتري',
    biometricSetupConfirm: 'استخدم بياناتك البيومترية لتأكيد الإعداد',
    biometricSignInPrompt: 'تسجيل الدخول إلى HoofBeat',
    biometricSignInSubtitle: 'استخدم بياناتك البيومترية للوصول لحسابك',
    biometricSetupCancelled: 'تم إلغاء الإعداد البيومتري',
    biometricRefreshSession: 'يرجى تسجيل الدخول بكلمة المرور لتحديث جلستك، ثم تفعيل تسجيل الدخول البيومتري مرة أخرى.',
    biometricCredentialsFirst: 'يرجى التحقق من بياناتك قبل تفعيل تسجيل الدخول البيومتري',
    biometricSignInPasswordFirst: 'يرجى تسجيل الدخول بكلمة المرور أولاً لتفعيل تسجيل الدخول البيومتري',
    or: 'أو',
    
    // AI Guidance
    aiGuidanceTitle: 'احصل على المزيد من مساعدك الذكي',
    aiGuidanceDescription: 'يحتاج مساعدك الذكي إلى المزيد من البيانات لتقديم رؤى وتوصيات مخصصة.',
    aiGuidanceWhatYouGet: 'ما ستحصل عليه:',
    aiGuidancePersonalizedPlans: 'خطط تدريب مخصصة',
    aiGuidanceHealthInsights: 'رؤى ونبيهات صحية',
    aiGuidancePerformanceTracking: 'تتبع الأداء',
    aiGuidanceSmartRecommendations: 'توصيات ذكية',
    aiGuidanceDataProgress: 'تقدم البيانات',
    aiGuidanceCompleteData: '{{percentage}}% مكتمل',
    aiGuidanceLogTrainingSession: 'تسجيل جلسة تدريب',
    aiGuidanceRecordVitals: 'تسجيل العلامات الحيوية',
    aiGuidanceUpdateFeedingSchedule: 'تحديث جدول التغذية',
    aiGuidanceStartLogging: 'ابدأ بتسجيل البيانات لفتح الرؤى الذكية',
    aiGuidanceMoreDataNeeded: 'مطلوب المزيد من البيانات للرؤى الذكية',
    aiGuidanceGetStarted: 'ابدأ بتسجيل جلسة التدريب الأولى أو تسجيل العلامات الحيوية.',
    
    // AI Chat
    aiChat: 'محادثة ذكية',
    aiChatTitle: 'تحدث مع المساعد الذكي',
    aiChatPlaceholder: 'اسأل عن صحة حيوانك أو تدريبه أو رعايته...',
    aiChatSend: 'إرسال',
    aiChatAttachment: 'إرفاق صورة',
    aiChatCamera: 'التقاط صورة',
    aiChatGallery: 'اختيار من المعرض',
    aiChatAnalyzing: 'جاري التحليل...',
    aiChatTyping: 'الذكاء الاصطناعي يكتب...',
    aiChatNoMessages: 'لا توجد رسائل بعد',
    aiChatStartConversation: 'ابدأ محادثة مع مساعدك الذكي حول {{animalName}}',
    aiChatImageAnalysis: 'تحليل الصورة',
    aiChatCopyMessage: 'نسخ الرسالة',
    aiChatShareMessage: 'مشاركة الرسالة',
    aiChatMessageCopied: 'تم نسخ الرسالة للحافظة',
    aiChatUploadingImage: 'جاري رفع الصورة...',
    aiChatImageUploadFailed: 'فشل في رفع الصورة',
    aiChatSendingMessage: 'جاري إرسال الرسالة...',
    aiChatMessageFailed: 'فشل في إرسال الرسالة',
    aiChatRetry: 'إعادة المحاولة',
    aiChatMaxFileSize: 'يجب أن يكون حجم الملف أقل من 10 ميجابايت',
    aiChatUnsupportedFormat: 'تنسيق ملف غير مدعوم',
    
    // Error Messages
    userNotFound: 'المستخدم غير موجود',
    profileCreationFailed: 'فشل في إنشاء ملف المستخدم',
    profileLoadFailed: 'فشل في تحميل ملف المستخدم',
    sessionExpired: 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.',
    networkError: 'خطأ في الشبكة. يرجى التحقق من اتصالك.',
    unexpectedError: 'حدث خطأ غير متوقع',
    
    // Common UI
    loading: 'جاري التحميل...',
    error: 'خطأ',
    success: 'نجح',
    warning: 'تحذير',
    info: 'معلومات',
  },
  
  // French (Français)
  fr: {
    // Navigation & Headers
    settings: 'Paramètres',
    advancedSettings: 'Paramètres Avancés',
    profile: 'Profil',
    animals: 'Animaux',
    devices: 'Appareils',
    home: 'Accueil',
    
    // Language Settings
    language: 'Langue',
    languageSettings: 'Langue et Région',
    selectYourLanguage: 'Choisissez votre langue',
    languageChanged: 'Langue changée avec succès',
    
    // Settings Categories
    dataManagement: 'Gestion des Données',
    notifications: 'Notifications',
    themeSettings: 'Paramètres de Thème',
    darkMode: 'Mode Sombre',
    syncSettings: 'Paramètres de Synchronisation',
    healthAlerts: 'Alertes de Santé',
    reminders: 'Rappels',
    
    // Health Alert Settings
    heartRateAlerts: 'Alertes de Fréquence Cardiaque',
    heartRateRange: 'Plage de Fréquence Cardiaque',
    minBpm: 'Min (bpm)',
    maxBpm: 'Max (bpm)',
    temperatureAlerts: 'Alertes de Température',
    temperatureRange: 'Plage de Température',
    minCelsius: 'Min (°C)',
    maxCelsius: 'Max (°C)',
    locationAlerts: 'Alertes de Localisation',
    locationUpdateInterval: 'Intervalle de Mise à Jour de Localisation',
    locationUpdateLabel: 'Alerter si aucune mise à jour pendant plus de (heures):',
    
    // Reminder Settings
    reminderTimeBefore: 'Temps de rappel avant (minutes):',
    saveSettings: 'Enregistrer les Paramètres',
    settingsSavedSuccess: 'Paramètres enregistrés avec succès',
    errorFixBeforeSaving: 'Veuillez corriger les erreurs avant d\'enregistrer',
    errorMaxHeartRateHigher: 'La fréquence cardiaque maximale doit être supérieure à la minimale',
    errorMaxTemperatureHigher: 'La température maximale doit être supérieure à la minimale',
    
    // Monitoring Settings
    heartRateLimits: 'Limites de Fréquence Cardiaque',
    temperatureAlerts: 'Alertes de Température',
    locationTracking: 'Suivi de Localisation',
    
    // Reminder Settings
    feedingReminders: 'Rappels d\'Alimentation',
    medicationReminders: 'Rappels de Médicaments',
    vaccinationReminders: 'Rappels de Vaccination',
    
    // Actions
    saveChanges: 'Enregistrer les Modifications',
    cancel: 'Annuler',
    confirm: 'Confirmer',
    delete: 'Supprimer',
    edit: 'Modifier',
    add: 'Ajouter',
    
    // Device Management
    scanForDevices: 'Rechercher des Appareils',
    scanning: 'Recherche en cours...',
    devicesFound: 'appareils trouvés',
    noDevicesFound: 'Aucun appareil trouvé',
    
    // Home Screen
    appTitle: 'HoofBeat',
    appSubtitle: 'Votre compagnon d\'élevage',
    animalSpeedTitle: 'Vitesse de {{animalName}}',
    speedUpdatedSuccess: 'Vitesse de {{animalName}} mise à jour',
    speedUpdateFailed: 'Échec de la mise à jour de la vitesse',
    
    // Quick Stats
    quickOverview: 'Aperçu Rapide',
    activeDevices: 'Appareils Actifs',
    todaysFeedings: 'Alimentations d\'Aujourd\'hui',
    medications: 'Médicaments',
    
    // Animals Overview
    myAnimals: 'Mes Animaux',
    seeAll: 'Voir Tout',
    addAnimal: 'Ajouter un Animal',
    noAnimalsYet: 'Aucun animal ajouté pour le moment',
    addFirstAnimal: 'Ajoutez Votre Premier Animal',
    
    // Upcoming Tasks
    upcomingTasks: 'Tâches à Venir',
    noTasksToday: 'Aucune tâche programmée pour aujourd\'hui',
    allCaughtUp: 'Tout est à jour! Vos animaux sont bien pris en charge.',
    feedingTask: 'Alimentation: {{feedType}}',
    medicationTask: 'Médicament: {{medicationName}}',
    amount: 'Quantité',
    dosage: 'Dosage',
    moreTasksToday: '+{{count}} tâches de plus aujourd\'hui',
    
    // Premium Banner
    premium: 'Premium',
    upgradeToPremium: 'Passer à Premium',
    unlimitedAnimals: 'Animaux illimités',
    advancedAnalytics: 'Analyses avancées',
    upgradeNow: 'Mettre à Niveau Maintenant',
    
    // AI Onboarding
    meetYourAIAssistant: 'Rencontrez Votre Assistant IA',
    aiAssistantDescription: 'Obtenez des plans d\'entraînement personnalisés, des informations sur la santé et des conseils d\'entraînement alimentés par l\'IA.',
    readinessScores: 'Scores de Préparation',
    trainingPlans: 'Plans d\'Entraînement',
    logFirstTrainingSession: 'Enregistrez Votre Première Session d\'Entraînement',
    
    // Speed Monitor
    speed: 'Vitesse',
    currentSpeed: 'Vitesse Actuelle',
    refresh: 'Actualiser',
    lastUpdated: 'Dernière mise à jour',
    
    // Animals Screen
    id: 'ID',
    ageYears: '{{age}} ans',
    gps: 'GPS',
    selectLanguage: 'Sélectionner la langue',
    changingLanguage: 'Changement de langue...',
    
    // Animal Detail Screen
    animalDetailTrainingSessions: 'Sessions d\'Entraînement',
    animalDetailLoadingSessions: 'Chargement des sessions...',
    animalDetailLogNewSession: 'Enregistrer Nouvelle Session',
    animalDetailNoTrainingSessions: 'Aucune Session d\'Entraînement',
    animalDetailTrainingDescription: 'Commencez à enregistrer des sessions d\'entraînement pour obtenir des informations et recommandations alimentées par l\'IA.',
    animalDetailLogFirstSession: 'Enregistrer Première Session',
    animalDetailLatestVitals: 'Derniers Signes Vitaux',
    animalDetailDeviceOnly: 'Appareil uniquement',
    animalDetailRecordedOn: 'Enregistré le',
    animalDetailTemperature: 'Température',
    animalDetailHeartRate: 'Fréquence Cardiaque',
    animalDetailRespiration: 'Respiration',
    animalDetailWeight: 'Poids',
    animalDetailNotes: 'Notes',
    animalDetailNoVitalsRecorded: 'Aucun signe vital enregistré',
    animalDetailConnectDevice: 'Connectez un appareil pour enregistrer automatiquement les signes vitaux',
    animalDetailFeedingSchedule: 'Horaire d\'Alimentation',
    animalDetailViewAll: 'Voir Tout',
    animalDetailPrintFeedingSchedule: 'Imprimer Horaire d\'Alimentation',
    animalDetailNoFeedingSchedule: 'Aucun horaire d\'alimentation défini',
    animalDetailSetFeedingSchedule: 'Définir Horaire d\'Alimentation',
    animalDetailMedications: 'Médicaments',
    animalDetailAdd: '+ Ajouter',
    animalDetailPrintMedicationSchedule: 'Imprimer Horaire des Médicaments',
    animalDetailNoMedicationsAdded: 'Aucun médicament ajouté',
    animalDetailAddMedication: 'Ajouter Médicament',
    animalDetailVaccinations: 'Vaccinations',
    animalDetailDue: 'Échéance:',
    animalDetailNoRenewal: 'Pas de renouvellement',
    animalDetailPrintVaccinationRecord: 'Imprimer Dossier de Vaccination',
    animalDetailNoVaccinationsRecorded: 'Aucune vaccination enregistrée',
    animalDetailAddVaccination: 'Ajouter Vaccination',
    animalDetailMicrochipRequired: 'ID de puce électronique requis pour les dossiers de vaccination',
    animalDetailAIAssistant: 'Assistant IA',
    animalDetailReadinessScore: 'Score de Préparation',
    animalDetailHealthStatus: 'État de Santé',
    animalDetailNewTips: 'Nouveaux Conseils',
    animalDetailTip: 'conseil',
    animalDetailTips: 'conseils',
    animalDetailMoreTip: 'conseil de plus',
    animalDetailMoreTips: 'conseils de plus',
    animalDetailNoAIInsights: 'Aucune information IA pour le moment. Enregistrez une session d\'entraînement pour commencer.',
    animalDetailAnalyzing: 'Analyse en cours...',
    animalDetailGetAIAnalysis: 'Obtenir Analyse IA',
    animalDetailViewAllInsights: 'Voir Toutes les Informations',
    
    // AI Assistant Widget
    aiWidgetInsights: 'Informations IA',
    aiWidgetReadiness: 'Préparation:',
    aiWidgetNoAIData: 'Pas encore de données IA',
    aiWidgetLogTraining: 'Enregistrer entraînement',
    aiWidgetLiveStatus: 'État en Direct:',
    aiWidgetAvailable: 'Disponible',
    aiWidgetHealth: 'Santé:',
    aiWidgetLatestTip: 'Dernier Conseil:',
    aiWidgetChecking: 'Vérification...',
    aiWidgetLiveCheckIn: 'Contrôle en Direct',
    aiWidgetViewAll: 'Voir Tout',
    aiWidgetNew: 'NOUVEAU',
    
    // Profile Screen
    profileSettings: 'Paramètres',
    profileAdvancedSettings: 'Paramètres Avancés',
    profilePrivacySettings: 'Paramètres de Confidentialité',
    profileUpgradeToPremium: 'Passer à Premium',
    profileHelpSupport: 'Aide et Support',
    profileFAQ: 'FAQ',
    profileContactSupport: 'Contacter le Support',
    profileSignOut: 'Se Déconnecter',
    profileSignOutConfirm: 'Êtes-vous sûr de vouloir vous déconnecter?',
    profileCancel: 'Annuler',
    profileVersion: 'Version',
    profileSignOutFailed: 'Échec de la déconnexion',
    
    // Common UI
    loading: 'Chargement...',
    error: 'Erreur',
    success: 'Succès',
    warning: 'Avertissement',
    info: 'Information',
  },
  
  // Japanese (日本語)
  ja: {
    // Navigation & Headers
    settings: '設定',
    advancedSettings: '詳細設定',
    profile: 'プロフィール',
    animals: '動物',
    devices: 'デバイス',
    home: 'ホーム',
    
    // Language Settings
    language: '言語',
    languageSettings: '言語と地域',
    selectYourLanguage: '言語を選択してください',
    languageChanged: '言語が正常に変更されました',
    
    // Settings Categories
    dataManagement: 'データ管理',
    notifications: '通知',
    themeSettings: 'テーマ設定',
    darkMode: 'ダークモード',
    syncSettings: '同期設定',
    healthAlerts: '健康アラート',
    reminders: 'リマインダー',
    
    // Health Alert Settings
    heartRateAlerts: '心拍数アラート',
    heartRateRange: '心拍数範囲',
    minBpm: '最小 (bpm)',
    maxBpm: '最大 (bpm)',
    temperatureAlerts: '温度アラート',
    temperatureRange: '温度範囲',
    minCelsius: '最小 (°C)',
    maxCelsius: '最大 (°C)',
    locationAlerts: '位置アラート',
    locationUpdateInterval: '位置更新間隔',
    locationUpdateLabel: '更新がない場合のアラート時間 (時間):',
    
    // Reminder Settings
    reminderTimeBefore: 'リマインダー時間 (分前):',
    saveSettings: '設定を保存',
    settingsSavedSuccess: '設定が正常に保存されました',
    errorFixBeforeSaving: '保存前にエラーを修正してください',
    errorMaxHeartRateHigher: '最大心拍数は最小心拍数より高くする必要があります',
    errorMaxTemperatureHigher: '最高温度は最低温度より高くする必要があります',
    
    // Monitoring Settings
    heartRateLimits: '心拍数制限',
    temperatureAlerts: '温度アラート',
    locationTracking: '位置追跡',
    
    // Reminder Settings
    feedingReminders: '給餌リマインダー',
    medicationReminders: '薬物リマインダー',
    vaccinationReminders: 'ワクチンリマインダー',
    
    // Actions
    saveChanges: '変更を保存',
    cancel: 'キャンセル',
    confirm: '確認',
    delete: '削除',
    edit: '編集',
    add: '追加',
    
    // Device Management
    scanForDevices: 'デバイスをスキャン',
    scanning: 'スキャン中...',
    devicesFound: 'デバイスが見つかりました',
    noDevicesFound: 'デバイスが見つかりません',
    
    // Home Screen
    appTitle: 'HoofBeat',
    appSubtitle: 'あなたの家畜の相棒',
    animalSpeedTitle: '{{animalName}}の速度',
    speedUpdatedSuccess: '{{animalName}}の速度が更新されました',
    speedUpdateFailed: '速度の更新に失敗しました',
    
    // Quick Stats
    quickOverview: 'クイック概要',
    activeDevices: 'アクティブデバイス',
    todaysFeedings: '今日の給餌',
    medications: '薬物',
    
    // Animals Overview
    myAnimals: '私の動物',
    seeAll: 'すべて見る',
    addAnimal: '動物を追加',
    noAnimalsYet: 'まだ動物が追加されていません',
    addFirstAnimal: '最初の動物を追加',
    
    // Upcoming Tasks
    upcomingTasks: '今後のタスク',
    noTasksToday: '今日予定されているタスクはありません',
    allCaughtUp: 'すべて完了！あなたの動物はよく世話されています。',
    feedingTask: '給餌: {{feedType}}',
    medicationTask: '薬物: {{medicationName}}',
    amount: '量',
    dosage: '投与量',
    moreTasksToday: '今日あと{{count}}個のタスク',
    
    // Premium Banner
    premium: 'プレミアム',
    upgradeToPremium: 'プレミアムにアップグレード',
    unlimitedAnimals: '無制限の動物',
    advancedAnalytics: '高度な分析',
    upgradeNow: '今すぐアップグレード',
    
    // AI Onboarding
    meetYourAIAssistant: 'AIアシスタントに会う',
    aiAssistantDescription: 'AIによるパーソナライズされたトレーニングプラン、健康洞察、コーチングのヒントを取得します。',
    readinessScores: '準備スコア',
    trainingPlans: 'トレーニングプラン',
    logFirstTrainingSession: '最初のトレーニングセッションを記録',
    
    // Speed Monitor
    speed: '速度',
    currentSpeed: '現在の速度',
    refresh: '更新',
    lastUpdated: '最終更新',
    
    // Animals Screen
    id: 'ID',
    ageYears: '{{age}}歳',
    gps: 'GPS',
    selectLanguage: '言語を選択',
    changingLanguage: '言語を変更中...',
    
    // Animal Detail Screen
    animalDetailTrainingSessions: 'トレーニングセッション',
    animalDetailLoadingSessions: 'セッションを読み込み中...',
    animalDetailLogNewSession: '新しいセッションを記録',
    animalDetailNoTrainingSessions: 'トレーニングセッションなし',
    animalDetailTrainingDescription: 'AIパワーの洞察と推奨を得るためにトレーニングセッションの記録を始めてください。',
    animalDetailLogFirstSession: '最初のセッションを記録',
    animalDetailLatestVitals: '最新のバイタル',
    animalDetailDeviceOnly: 'デバイスのみ',
    animalDetailRecordedOn: '記録日',
    animalDetailTemperature: '体温',
    animalDetailHeartRate: '心拍数',
    animalDetailRespiration: '呼吸',
    animalDetailWeight: '体重',
    animalDetailNotes: 'メモ',
    animalDetailNoVitalsRecorded: 'まだバイタルが記録されていません',
    animalDetailConnectDevice: 'デバイスを接続してバイタルを自動記録',
    animalDetailFeedingSchedule: '給餌スケジュール',
    animalDetailViewAll: 'すべて表示',
    animalDetailPrintFeedingSchedule: '給餌スケジュールを印刷',
    animalDetailNoFeedingSchedule: '給餌スケジュールが設定されていません',
    animalDetailSetFeedingSchedule: '給餌スケジュールを設定',
    animalDetailMedications: '薬物',
    animalDetailAdd: '+ 追加',
    animalDetailPrintMedicationSchedule: '薬物スケジュールを印刷',
    animalDetailNoMedicationsAdded: '薬物が追加されていません',
    animalDetailAddMedication: '薬物を追加',
    animalDetailVaccinations: 'ワクチン',
    animalDetailDue: '期限:',
    animalDetailNoRenewal: '更新なし',
    animalDetailPrintVaccinationRecord: 'ワクチン記録を印刷',
    animalDetailNoVaccinationsRecorded: 'ワクチンが記録されていません',
    animalDetailAddVaccination: 'ワクチンを追加',
    animalDetailMicrochipRequired: 'ワクチン記録にはマイクロチップIDが必要です',
    animalDetailAIAssistant: 'AIアシスタント',
    animalDetailReadinessScore: '準備スコア',
    animalDetailHealthStatus: '健康状態',
    animalDetailNewTips: '新しいヒント',
    animalDetailTip: 'ヒント',
    animalDetailTips: 'ヒント',
    animalDetailMoreTip: 'さらにヒント',
    animalDetailMoreTips: 'さらにヒント',
    animalDetailNoAIInsights: 'まだAI洞察がありません。トレーニングセッションを記録して始めてください。',
    animalDetailAnalyzing: '分析中...',
    animalDetailGetAIAnalysis: 'AI分析を取得',
    animalDetailViewAllInsights: 'すべての洞察を表示',
    
    // AI Assistant Widget
    aiWidgetInsights: 'AI洞察',
    aiWidgetReadiness: '準備:',
    aiWidgetNoAIData: 'まだAIデータがありません',
    aiWidgetLogTraining: 'トレーニングを記録',
    aiWidgetLiveStatus: 'ライブステータス:',
    aiWidgetAvailable: '利用可能',
    aiWidgetHealth: '健康:',
    aiWidgetLatestTip: '最新のヒント:',
    aiWidgetChecking: 'チェック中...',
    aiWidgetLiveCheckIn: 'ライブチェックイン',
    aiWidgetViewAll: 'すべて表示',
    aiWidgetNew: '新しい',
    
    // Profile Screen
    profileSettings: '設定',
    profileAdvancedSettings: '高度な設定',
    profilePrivacySettings: 'プライバシー設定',
    profileUpgradeToPremium: 'プレミアムにアップグレード',
    profileHelpSupport: 'ヘルプとサポート',
    profileFAQ: 'FAQ',
    profileContactSupport: 'サポートに連絡',
    profileSignOut: 'サインアウト',
    profileSignOutConfirm: 'サインアウトしてもよろしいですか？',
    profileCancel: 'キャンセル',
    profileVersion: 'バージョン',
    profileSignOutFailed: 'サインアウトに失敗しました',
    
    // Common UI
    loading: '読み込み中...',
    error: 'エラー',
    success: '成功',
    warning: '警告',
    info: '情報',
  },
  
  // Italian (Italiano)
  it: {
    // Navigation & Headers
    settings: 'Impostazioni',
    advancedSettings: 'Impostazioni Avanzate',
    profile: 'Profilo',
    animals: 'Animali',
    devices: 'Dispositivi',
    home: 'Home',
    
    // Language Settings
    language: 'Lingua',
    languageSettings: 'Lingua e Regione',
    selectYourLanguage: 'Seleziona la tua lingua',
    languageChanged: 'Lingua cambiata con successo',
    
    // Settings Categories
    dataManagement: 'Gestione Dati',
    notifications: 'Notifiche',
    themeSettings: 'Impostazioni Tema',
    darkMode: 'Modalità Scura',
    syncSettings: 'Impostazioni Sincronizzazione',
    healthAlerts: 'Avvisi Salute',
    reminders: 'Promemoria',
    
    // Health Alert Settings
    heartRateAlerts: 'Avvisi Frequenza Cardiaca',
    heartRateRange: 'Intervallo Frequenza Cardiaca',
    minBpm: 'Min (bpm)',
    maxBpm: 'Max (bpm)',
    temperatureAlerts: 'Avvisi Temperatura',
    temperatureRange: 'Intervallo Temperatura',
    minCelsius: 'Min (°C)',
    maxCelsius: 'Max (°C)',
    locationAlerts: 'Avvisi Posizione',
    locationUpdateInterval: 'Intervallo Aggiornamento Posizione',
    locationUpdateLabel: 'Avvisa se nessun aggiornamento per più di (ore):',
    
    // Reminder Settings
    reminderTimeBefore: 'Tempo promemoria prima (minuti):',
    saveSettings: 'Salva Impostazioni',
    settingsSavedSuccess: 'Impostazioni salvate con successo',
    errorFixBeforeSaving: 'Correggi gli errori prima di salvare',
    errorMaxHeartRateHigher: 'La frequenza cardiaca massima deve essere superiore alla minima',
    errorMaxTemperatureHigher: 'La temperatura massima deve essere superiore alla minima',
    
    // Monitoring Settings
    heartRateLimits: 'Limiti Frequenza Cardiaca',
    temperatureAlerts: 'Avvisi Temperatura',
    locationTracking: 'Tracciamento Posizione',
    
    // Reminder Settings
    feedingReminders: 'Promemoria Alimentazione',
    medicationReminders: 'Promemoria Farmaci',
    vaccinationReminders: 'Promemoria Vaccinazioni',
    
    // Actions
    saveChanges: 'Salva Modifiche',
    cancel: 'Annulla',
    confirm: 'Conferma',
    delete: 'Elimina',
    edit: 'Modifica',
    add: 'Aggiungi',
    
    // Device Management
    scanForDevices: 'Cerca Dispositivi',
    scanning: 'Ricerca in corso...',
    devicesFound: 'dispositivi trovati',
    noDevicesFound: 'Nessun dispositivo trovato',
    
    // Home Screen
    appTitle: 'HoofBeat',
    appSubtitle: 'Il tuo compagno per il bestiame',
    animalSpeedTitle: 'Velocità di {{animalName}}',
    speedUpdatedSuccess: 'Velocità di {{animalName}} aggiornata',
    speedUpdateFailed: 'Aggiornamento velocità fallito',
    
    // Quick Stats
    quickOverview: 'Panoramica Rapida',
    activeDevices: 'Dispositivi Attivi',
    todaysFeedings: 'Alimentazioni di Oggi',
    medications: 'Farmaci',
    
    // Animals Overview
    myAnimals: 'I Miei Animali',
    seeAll: 'Vedi Tutto',
    addAnimal: 'Aggiungi Animale',
    noAnimalsYet: 'Nessun animale aggiunto ancora',
    addFirstAnimal: 'Aggiungi il Tuo Primo Animale',
    
    // Upcoming Tasks
    upcomingTasks: 'Attività Prossime',
    noTasksToday: 'Nessuna attività programmata per oggi',
    allCaughtUp: 'Tutto a posto! I tuoi animali sono ben curati.',
    feedingTask: 'Alimentazione: {{feedType}}',
    medicationTask: 'Farmaco: {{medicationName}}',
    amount: 'Quantità',
    dosage: 'Dosaggio',
    moreTasksToday: '+{{count}} altre attività oggi',
    
    // Premium Banner
    premium: 'Premium',
    upgradeToPremium: 'Passa a Premium',
    unlimitedAnimals: 'Animali illimitati',
    advancedAnalytics: 'Analisi avanzate',
    upgradeNow: 'Aggiorna Ora',
    
    // AI Onboarding
    meetYourAIAssistant: 'Incontra il Tuo Assistente AI',
    aiAssistantDescription: 'Ottieni piani di allenamento personalizzati, informazioni sulla salute e consigli di coaching alimentati dall\'AI.',
    readinessScores: 'Punteggi di Prontezza',
    trainingPlans: 'Piani di Allenamento',
    logFirstTrainingSession: 'Registra la Tua Prima Sessione di Allenamento',
    
    // Speed Monitor
    speed: 'Velocità',
    currentSpeed: 'Velocità Attuale',
    refresh: 'Aggiorna',
    lastUpdated: 'Ultimo aggiornamento',
    
    // Animals Screen
    id: 'ID',
    ageYears: '{{age}} anni',
    gps: 'GPS',
    selectLanguage: 'Seleziona lingua',
    changingLanguage: 'Cambio lingua...',
    
    // Animal Detail Screen
    animalDetailTrainingSessions: 'Sessioni di Allenamento',
    animalDetailLoadingSessions: 'Caricamento sessioni...',
    animalDetailLogNewSession: 'Registra Nuova Sessione',
    animalDetailNoTrainingSessions: 'Nessuna Sessione di Allenamento',
    animalDetailTrainingDescription: 'Inizia a registrare sessioni di allenamento per ottenere informazioni e raccomandazioni alimentate dall\'AI.',
    animalDetailLogFirstSession: 'Registra Prima Sessione',
    animalDetailLatestVitals: 'Ultimi Segni Vitali',
    animalDetailDeviceOnly: 'Solo dispositivo',
    animalDetailRecordedOn: 'Registrato il',
    animalDetailTemperature: 'Temperatura',
    animalDetailHeartRate: 'Frequenza Cardiaca',
    animalDetailRespiration: 'Respirazione',
    animalDetailWeight: 'Peso',
    animalDetailNotes: 'Note',
    animalDetailNoVitalsRecorded: 'Nessun segno vitale registrato',
    animalDetailConnectDevice: 'Collega un dispositivo per registrare automaticamente i segni vitali',
    animalDetailFeedingSchedule: 'Programma Alimentazione',
    animalDetailViewAll: 'Vedi Tutto',
    animalDetailPrintFeedingSchedule: 'Stampa Programma Alimentazione',
    animalDetailNoFeedingSchedule: 'Nessun programma di alimentazione impostato',
    animalDetailSetFeedingSchedule: 'Imposta Programma Alimentazione',
    animalDetailMedications: 'Farmaci',
    animalDetailAdd: '+ Aggiungi',
    animalDetailPrintMedicationSchedule: 'Stampa Programma Farmaci',
    animalDetailNoMedicationsAdded: 'Nessun farmaco aggiunto',
    animalDetailAddMedication: 'Aggiungi Farmaco',
    animalDetailVaccinations: 'Vaccinazioni',
    animalDetailDue: 'Scadenza:',
    animalDetailNoRenewal: 'Nessun rinnovo',
    animalDetailPrintVaccinationRecord: 'Stampa Registro Vaccinazioni',
    animalDetailNoVaccinationsRecorded: 'Nessuna vaccinazione registrata',
    animalDetailAddVaccination: 'Aggiungi Vaccinazione',
    animalDetailMicrochipRequired: 'ID microchip richiesto per i registri di vaccinazione',
    animalDetailAIAssistant: 'Assistente AI',
    animalDetailReadinessScore: 'Punteggio di Prontezza',
    animalDetailHealthStatus: 'Stato di Salute',
    animalDetailNewTips: 'Nuovi Consigli',
    animalDetailTip: 'consiglio',
    animalDetailTips: 'consigli',
    animalDetailMoreTip: 'consiglio in più',
    animalDetailMoreTips: 'consigli in più',
    animalDetailNoAIInsights: 'Nessuna informazione AI ancora. Registra una sessione di allenamento per iniziare.',
    animalDetailAnalyzing: 'Analizzando...',
    animalDetailGetAIAnalysis: 'Ottieni Analisi AI',
    animalDetailViewAllInsights: 'Vedi Tutte le Informazioni',
    
    // AI Assistant Widget
    aiWidgetInsights: 'Informazioni AI',
    aiWidgetReadiness: 'Prontezza:',
    aiWidgetNoAIData: 'Nessun dato AI ancora',
    aiWidgetLogTraining: 'Registra allenamento',
    aiWidgetLiveStatus: 'Stato Live:',
    aiWidgetAvailable: 'Disponibile',
    aiWidgetHealth: 'Salute:',
    aiWidgetLatestTip: 'Ultimo Consiglio:',
    aiWidgetChecking: 'Controllo...',
    aiWidgetLiveCheckIn: 'Check-in Live',
    aiWidgetViewAll: 'Vedi Tutto',
    aiWidgetNew: 'NUOVO',
    
    // Profile Screen
    profileSettings: 'Impostazioni',
    profileAdvancedSettings: 'Impostazioni Avanzate',
    profilePrivacySettings: 'Impostazioni Privacy',
    profileUpgradeToPremium: 'Aggiorna a Premium',
    profileHelpSupport: 'Aiuto e Supporto',
    profileFAQ: 'FAQ',
    profileContactSupport: 'Contatta Supporto',
    profileSignOut: 'Disconnetti',
    profileSignOutConfirm: 'Sei sicuro di voler disconnetterti?',
    profileCancel: 'Annulla',
    profileVersion: 'Versione',
    profileSignOutFailed: 'Disconnessione fallita',
    
    // Common UI
    loading: 'Caricamento...',
    error: 'Errore',
    success: 'Successo',
    warning: 'Avviso',
    info: 'Informazioni',
  },
  
  // Turkish (Türkçe)
  tr: {
    // Navigation & Headers
    settings: 'Ayarlar',
    advancedSettings: 'Gelişmiş Ayarlar',
    profile: 'Profil',
    animals: 'Hayvanlar',
    devices: 'Cihazlar',
    home: 'Ana Sayfa',
    
    // Language Settings
    language: 'Dil',
    languageSettings: 'Dil ve Bölge',
    selectYourLanguage: 'Dilinizi seçin',
    languageChanged: 'Dil başarıyla değiştirildi',
    
    // Settings Categories
    dataManagement: 'Veri Yönetimi',
    notifications: 'Bildirimler',
    themeSettings: 'Tema Ayarları',
    darkMode: 'Karanlık Mod',
    syncSettings: 'Senkronizasyon Ayarları',
    healthAlerts: 'Sağlık Uyarıları',
    reminders: 'Hatırlatıcılar',
    
    // Health Alert Settings
    heartRateAlerts: 'Kalp Atış Hızı Uyarıları',
    heartRateRange: 'Kalp Atış Hızı Aralığı',
    minBpm: 'Min (bpm)',
    maxBpm: 'Max (bpm)',
    temperatureAlerts: 'Sıcaklık Uyarıları',
    temperatureRange: 'Sıcaklık Aralığı',
    minCelsius: 'Min (°C)',
    maxCelsius: 'Max (°C)',
    locationAlerts: 'Konum Uyarıları',
    locationUpdateInterval: 'Konum Güncelleme Aralığı',
    locationUpdateLabel: 'Daha fazla güncelleme yoksa uyar (saat):',
    
    // Reminder Settings
    reminderTimeBefore: 'Hatırlatıcı zamanı önce (dakika):',
    saveSettings: 'Ayarları Kaydet',
    settingsSavedSuccess: 'Ayarlar başarıyla kaydedildi',
    errorFixBeforeSaving: 'Kaydetmeden önce hataları düzeltiniz',
    errorMaxHeartRateHigher: 'Maksimum kalp atış hızı minimumdan yüksek olmalıdır',
    errorMaxTemperatureHigher: 'Maksimum sıcaklık minimumdan yüksek olmalıdır',
    
    // Monitoring Settings
    heartRateLimits: 'Kalp Atış Hızı Sınırları',
    temperatureAlerts: 'Sıcaklık Uyarıları',
    locationTracking: 'Konum Takibi',
    
    // Reminder Settings
    feedingReminders: 'Beslenme Hatırlatıcıları',
    medicationReminders: 'İlaç Hatırlatıcıları',
    vaccinationReminders: 'Aşı Hatırlatıcıları',
    
    // Actions
    saveChanges: 'Değişiklikleri Kaydet',
    cancel: 'İptal',
    confirm: 'Onayla',
    delete: 'Sil',
    edit: 'Düzenle',
    add: 'Ekle',
    
    // Device Management
    scanForDevices: 'Cihazları Tara',
    scanning: 'Taranıyor...',
    devicesFound: 'cihaz bulundu',
    noDevicesFound: 'Cihaz bulunamadı',
    
    // Home Screen
    appTitle: 'HoofBeat',
    appSubtitle: 'Hayvancılık arkadaşınız',
    animalSpeedTitle: '{{animalName}} Hızı',
    speedUpdatedSuccess: '{{animalName}} hızı güncellendi',
    speedUpdateFailed: 'Hız güncellemesi başarısız',
    
    // Quick Stats
    quickOverview: 'Hızlı Genel Bakış',
    activeDevices: 'Aktif Cihazlar',
    todaysFeedings: 'Bugünkü Beslemeler',
    medications: 'İlaçlar',
    
    // Animals Overview
    myAnimals: 'Hayvanlarım',
    seeAll: 'Hepsini Gör',
    addAnimal: 'Hayvan Ekle',
    noAnimalsYet: 'Henüz hayvan eklenmedi',
    addFirstAnimal: 'İlk Hayvanınızı Ekleyin',
    
    // Upcoming Tasks
    upcomingTasks: 'Yaklaşan Görevler',
    noTasksToday: 'Bugün için planlanmış görev yok',
    allCaughtUp: 'Her şey tamam! Hayvanlarınız iyi bakılıyor.',
    feedingTask: 'Besleme: {{feedType}}',
    medicationTask: 'İlaç: {{medicationName}}',
    amount: 'Miktar',
    dosage: 'Doz',
    moreTasksToday: 'Bugün +{{count}} görev daha',
    
    // Premium Banner
    premium: 'Premium',
    upgradeToPremium: 'Premium\'a Yükselt',
    unlimitedAnimals: 'Sınırsız hayvan',
    advancedAnalytics: 'Gelişmiş analitik',
    upgradeNow: 'Şimdi Yükselt',
    
    // AI Onboarding
    meetYourAIAssistant: 'AI Asistanınızla Tanışın',
    aiAssistantDescription: 'AI tarafından desteklenen kişiselleştirilmiş antrenman planları, sağlık görüşleri ve koçluk ipuçları alın.',
    readinessScores: 'Hazırlık Puanları',
    trainingPlans: 'Antrenman Planları',
    logFirstTrainingSession: 'İlk Antrenman Seansnızı Kaydedin',
    
    // Speed Monitor
    speed: 'Hız',
    currentSpeed: 'Mevcut Hız',
    refresh: 'Yenile',
    lastUpdated: 'Son güncelleme',
    
    // Animals Screen
    id: 'ID',
    ageYears: '{{age}} yaş',
    gps: 'GPS',
    selectLanguage: 'Dil Seç',
    changingLanguage: 'Dil değiştiriliyor...',
    
    // Animal Detail Screen
    animalDetailTrainingSessions: 'Antrenman Seansları',
    animalDetailLoadingSessions: 'Seanslar yüklüyor...',
    animalDetailLogNewSession: 'Yeni Seans Kaydet',
    animalDetailNoTrainingSessions: 'Antrenman Seansı Yok',
    animalDetailTrainingDescription: 'AI destekli görüşler ve öneriler almak için antrenman seanslarını kaydetmeye başlayın.',
    animalDetailLogFirstSession: 'İlk Seansı Kaydet',
    animalDetailLatestVitals: 'Son Vital Bulgular',
    animalDetailDeviceOnly: 'Sadece cihaz',
    animalDetailRecordedOn: 'Kaydedildi',
    animalDetailTemperature: 'Sıcaklık',
    animalDetailHeartRate: 'Kalp Atış Hızı',
    animalDetailRespiration: 'Solunum',
    animalDetailWeight: 'Ağırlık',
    animalDetailNotes: 'Notlar',
    animalDetailNoVitalsRecorded: 'Henüz vital bulgu kaydedilmedi',
    animalDetailConnectDevice: 'Vital bulguları otomatik kaydetmek için bir cihaz bağlayın',
    animalDetailFeedingSchedule: 'Beslenme Programı',
    animalDetailViewAll: 'Hepsini Gör',
    animalDetailPrintFeedingSchedule: 'Beslenme Programını Yazdır',
    animalDetailNoFeedingSchedule: 'Beslenme programı ayarlanmadı',
    animalDetailSetFeedingSchedule: 'Beslenme Programı Ayarla',
    animalDetailMedications: 'İlaçlar',
    animalDetailAdd: '+ Ekle',
    animalDetailPrintMedicationSchedule: 'İlaç Programını Yazdır',
    animalDetailNoMedicationsAdded: 'İlaç eklenmedi',
    animalDetailAddMedication: 'İlaç Ekle',
    animalDetailVaccinations: 'Aşılar',
    animalDetailDue: 'Vadesi:',
    animalDetailNoRenewal: 'Yenileme yok',
    animalDetailPrintVaccinationRecord: 'Aşı Kaydını Yazdır',
    animalDetailNoVaccinationsRecorded: 'Aşı kaydedilmedi',
    animalDetailAddVaccination: 'Aşı Ekle',
    animalDetailMicrochipRequired: 'Aşı kayıtları için mikrochip ID gerekli',
    animalDetailAIAssistant: 'AI Asistan',
    animalDetailReadinessScore: 'Hazırlık Puanı',
    animalDetailHealthStatus: 'Sağlık Durumu',
    animalDetailNewTips: 'Yeni İpuçları',
    animalDetailTip: 'ipucu',
    animalDetailTips: 'ipuçları',
    animalDetailMoreTip: 'daha ipucu',
    animalDetailMoreTips: 'daha ipuçları',
    animalDetailNoAIInsights: 'Henüz AI görüşü yok. Başlamak için bir antrenman seansı kaydedin.',
    animalDetailAnalyzing: 'Analiz ediliyor...',
    animalDetailGetAIAnalysis: 'AI Analizi Al',
    animalDetailViewAllInsights: 'Tüm Görüşleri Gör',
    
    // AI Assistant Widget
    aiWidgetInsights: 'AI Görüşleri',
    aiWidgetReadiness: 'Hazırlık:',
    aiWidgetNoAIData: 'Henüz AI verisi yok',
    aiWidgetLogTraining: 'Antrenman kaydet',
    aiWidgetLiveStatus: 'Canlı Durum:',
    aiWidgetAvailable: 'Mevcut',
    aiWidgetHealth: 'Sağlık:',
    aiWidgetLatestTip: 'Son İpucu:',
    aiWidgetChecking: 'Kontrol ediliyor...',
    aiWidgetLiveCheckIn: 'Canlı Kontrol',
    aiWidgetViewAll: 'Hepsini Gör',
    aiWidgetNew: 'YENİ',
    
    // Common UI
    loading: 'Yükleniyor...',
    error: 'Hata',
    success: 'Başarılı',
    warning: 'Uyarı',
    info: 'Bilgi',
  },
  
  // Dutch (Nederlands)
  nl: {
    // Navigation & Headers
    settings: 'Instellingen',
    advancedSettings: 'Geavanceerde Instellingen',
    profile: 'Profiel',
    animals: 'Dieren',
    devices: 'Apparaten',
    home: 'Thuis',
    
    // Language Settings
    language: 'Taal',
    languageSettings: 'Taal en Regio',
    selectYourLanguage: 'Selecteer uw taal',
    languageChanged: 'Taal succesvol gewijzigd',
    
    // Settings Categories
    dataManagement: 'Gegevensbeheer',
    notifications: 'Meldingen',
    themeSettings: 'Thema-instellingen',
    darkMode: 'Donkere Modus',
    syncSettings: 'Synchronisatie-instellingen',
    healthAlerts: 'Gezondheidswaarschuwingen',
    reminders: 'Herinneringen',
    
    // Health Alert Settings
    heartRateAlerts: 'Hartslagwaarschuwingen',
    heartRateRange: 'Hartslagbereik',
    minBpm: 'Min (bpm)',
    maxBpm: 'Max (bpm)',
    temperatureAlerts: 'Temperatuurwaarschuwingen',
    temperatureRange: 'Temperatuurbereik',
    minTemp: 'Min (°C)',
    maxTemp: 'Max (°C)',
    locationAlerts: 'Locatiewaarschuwingen',
    locationUpdateInterval: 'Locatie Update Interval',
    alertIfNoUpdateFor: 'Waarschuwen als geen update voor meer dan (uren):',
    
    // Reminder Settings
    reminderTimeBefore: 'Herinneringstijd voor (minuten):',
    saveSettings: 'Instellingen Opslaan',
    settingsSaved: 'Instellingen succesvol opgeslagen',
    fixErrorsBeforeSaving: 'Corrigeer fouten voordat u opslaat',
    maxHeartRateError: 'Maximale hartslag moet hoger zijn dan minimale',
    maxTemperatureError: 'Maximale temperatuur moet hoger zijn dan minimale',
    
    // Monitoring Settings
    heartRateLimits: 'Hartslaglimieten',
    temperatureAlerts: 'Temperatuurwaarschuwingen',
    locationTracking: 'Locatietracking',
    
    // Reminder Settings
    feedingReminders: 'Voedingsherinneringen',
    medicationReminders: 'Medicatieherinneringen',
    vaccinationReminders: 'Vaccinatieherinneringen',
    
    // Actions
    saveChanges: 'Wijzigingen Opslaan',
    cancel: 'Annuleren',
    confirm: 'Bevestigen',
    delete: 'Verwijderen',
    edit: 'Bewerken',
    add: 'Toevoegen',
    
    // Device Management
    scanForDevices: 'Zoeken naar Apparaten',
    scanning: 'Zoeken...',
    devicesFound: 'apparaten gevonden',
    noDevicesFound: 'Geen apparaten gevonden',
    
    // Home Screen
    appTitle: 'HoofBeat',
    appSubtitle: 'Uw vee-metgezel',
    animalSpeedTitle: '{{animalName}} Snelheid',
    speedUpdatedSuccess: '{{animalName}} snelheid bijgewerkt',
    speedUpdateFailed: 'Snelheid bijwerken mislukt',
    
    // Quick Stats
    quickOverview: 'Snel Overzicht',
    activeDevices: 'Actieve Apparaten',
    todaysFeedings: 'Vandaag Voeding',
    medications: 'Medicijnen',
    
    // Animals Overview
    myAnimals: 'Mijn Dieren',
    seeAll: 'Alles Bekijken',
    addAnimal: 'Dier Toevoegen',
    noAnimalsYet: 'Nog geen dieren toegevoegd',
    addFirstAnimal: 'Voeg Uw Eerste Dier Toe',
    
    // Upcoming Tasks
    upcomingTasks: 'Aankomende Taken',
    noTasksToday: 'Geen taken gepland voor vandaag',
    allCaughtUp: 'Alles bijgewerkt! Uw dieren worden goed verzorgd.',
    feedingTask: 'Voeding: {{feedType}}',
    medicationTask: 'Medicatie: {{medicationName}}',
    amount: 'Hoeveelheid',
    dosage: 'Dosering',
    moreTasksToday: '+{{count}} meer taken vandaag',
    
    // Premium Banner
    premium: 'Premium',
    upgradeToPremium: 'Upgrade naar Premium',
    unlimitedAnimals: 'Onbeperkte dieren',
    advancedAnalytics: 'Geavanceerde analyses',
    upgradeNow: 'Nu Upgraden',
    
    // AI Onboarding
    meetYourAIAssistant: 'Ontmoet Uw AI-Assistent',
    aiAssistantDescription: 'Krijg gepersonaliseerde trainingsplannen, gezondheidsinzichten en coaching tips aangedreven door AI.',
    readinessScores: 'Gereedheidsscores',
    trainingPlans: 'Trainingsplannen',
    logFirstTrainingSession: 'Log Uw Eerste Trainingssessie',
    
    // Speed Monitor
    speed: 'Snelheid',
    currentSpeed: 'Huidige Snelheid',
    refresh: 'Vernieuwen',
    lastUpdated: 'Laatst bijgewerkt',
    
    // Animals Screen
    id: 'ID',
    ageYears: '{{age}} jaar',
    gps: 'GPS',
    selectLanguage: 'Selecteer taal',
    changingLanguage: 'Taal wijzigen...',
    
    // Animal Detail Screen
    animalDetailTrainingSessions: 'Trainingssessies',
    animalDetailLoadingSessions: 'Sessies laden...',
    animalDetailLogNewSession: 'Nieuwe Sessie Vastleggen',
    animalDetailNoTrainingSessions: 'Geen Trainingssessies',
    animalDetailTrainingDescription: 'Begin met het vastleggen van trainingssessies om AI-aangedreven inzichten en aanbevelingen te krijgen.',
    animalDetailLogFirstSession: 'Eerste Sessie Vastleggen',
    animalDetailLatestVitals: 'Laatste Vitale Functies',
    animalDetailDeviceOnly: 'Alleen apparaat',
    animalDetailRecordedOn: 'Vastgelegd op',
    animalDetailTemperature: 'Temperatuur',
    animalDetailHeartRate: 'Hartslag',
    animalDetailRespiration: 'Ademhaling',
    animalDetailWeight: 'Gewicht',
    animalDetailNotes: 'Notities',
    animalDetailNoVitalsRecorded: 'Nog geen vitale functies vastgelegd',
    animalDetailConnectDevice: 'Verbind een apparaat om vitale functies automatisch vast te leggen',
    animalDetailFeedingSchedule: 'Voedingsschema',
    animalDetailViewAll: 'Alles Bekijken',
    animalDetailPrintFeedingSchedule: 'Voedingsschema Afdrukken',
    animalDetailNoFeedingSchedule: 'Geen voedingsschema ingesteld',
    animalDetailSetFeedingSchedule: 'Voedingsschema Instellen',
    animalDetailMedications: 'Medicijnen',
    animalDetailAdd: '+ Toevoegen',
    animalDetailPrintMedicationSchedule: 'Medicatieschema Afdrukken',
    animalDetailNoMedicationsAdded: 'Geen medicijnen toegevoegd',
    animalDetailAddMedication: 'Medicijn Toevoegen',
    animalDetailVaccinations: 'Vaccinaties',
    animalDetailDue: 'Vervaldatum:',
    animalDetailNoRenewal: 'Geen vernieuwing',
    animalDetailPrintVaccinationRecord: 'Vaccinatierecord Afdrukken',
    animalDetailNoVaccinationsRecorded: 'Geen vaccinaties vastgelegd',
    animalDetailAddVaccination: 'Vaccinatie Toevoegen',
    animalDetailMicrochipRequired: 'Microchip ID vereist voor vaccinatierecords',
    animalDetailAIAssistant: 'AI Assistent',
    animalDetailReadinessScore: 'Gereedheidscore',
    animalDetailHealthStatus: 'Gezondheidsstatus',
    animalDetailNewTips: 'Nieuwe Tips',
    animalDetailTip: 'tip',
    animalDetailTips: 'tips',
    animalDetailMoreTip: 'meer tip',
    animalDetailMoreTips: 'meer tips',
    animalDetailNoAIInsights: 'Nog geen AI-inzichten. Log een trainingssessie om te beginnen.',
    animalDetailAnalyzing: 'Analyseren...',
    animalDetailGetAIAnalysis: 'AI-Analyse Krijgen',
    animalDetailViewAllInsights: 'Alle Inzichten Bekijken',
    
    // AI Assistant Widget
    aiWidgetInsights: 'AI Inzichten',
    aiWidgetReadiness: 'Gereedheid:',
    aiWidgetNoAIData: 'Nog geen AI-gegevens',
    aiWidgetLogTraining: 'Training vastleggen',
    aiWidgetLiveStatus: 'Live Status:',
    aiWidgetAvailable: 'Beschikbaar',
    aiWidgetHealth: 'Gezondheid:',
    aiWidgetLatestTip: 'Laatste Tip:',
    aiWidgetChecking: 'Controleren...',
    aiWidgetLiveCheckIn: 'Live Check-in',
    aiWidgetViewAll: 'Alles Bekijken',
    aiWidgetNew: 'NIEUW',
    
    // Common UI
    loading: 'Laden...',
    error: 'Fout',
    success: 'Succes',
    warning: 'Waarschuwing',
    info: 'Informatie',
  },
  
  // Spanish (Español)
  es: {
    // Navigation & Headers
    settings: 'Configuración',
    advancedSettings: 'Configuración Avanzada',
    profile: 'Perfil',
    animals: 'Animales',
    devices: 'Dispositivos',
    home: 'Inicio',
    
    // Language Settings
    language: 'Idioma',
    languageSettings: 'Idioma y Región',
    selectYourLanguage: 'Selecciona tu idioma',
    languageChanged: 'Idioma cambiado exitosamente',
    
    // Settings Categories
    dataManagement: 'Gestión de Datos',
    notifications: 'Notificaciones',
    themeSettings: 'Configuración de Tema',
    darkMode: 'Modo Oscuro',
    syncSettings: 'Configuración de Sincronización',
    healthAlerts: 'Alertas de Salud',
    reminders: 'Recordatorios',
    
    // Health Alert Settings
    heartRateAlerts: 'Alertas de Frecuencia Cardíaca',
    heartRateRange: 'Rango de Frecuencia Cardíaca',
    minBpm: 'Mín (lpm)',
    maxBpm: 'Máx (lpm)',
    temperatureAlerts: 'Alertas de Temperatura',
    temperatureRange: 'Rango de Temperatura',
    minTemp: 'Mín (°C)',
    maxTemp: 'Máx (°C)',
    locationAlerts: 'Alertas de Ubicación',
    locationUpdateInterval: 'Intervalo de Actualización de Ubicación',
    alertIfNoUpdateFor: 'Alertar si no hay actualización por más de (horas):',
    
    // Reminder Settings
    reminderTimeBefore: 'Tiempo de recordatorio antes (minutos):',
    saveSettings: 'Guardar Configuración',
    settingsSaved: 'Configuración guardada exitosamente',
    fixErrorsBeforeSaving: 'Corrige los errores antes de guardar',
    maxHeartRateError: 'La frecuencia cardíaca máxima debe ser mayor que la mínima',
    maxTemperatureError: 'La temperatura máxima debe ser mayor que la mínima',
    
    // Monitoring Settings
    heartRateLimits: 'Límites de Frecuencia Cardíaca',
    temperatureAlerts: 'Alertas de Temperatura',
    locationTracking: 'Seguimiento de Ubicación',
    
    // Reminder Settings
    feedingReminders: 'Recordatorios de Alimentación',
    medicationReminders: 'Recordatorios de Medicación',
    vaccinationReminders: 'Recordatorios de Vacunación',
    
    // Actions
    saveChanges: 'Guardar Cambios',
    cancel: 'Cancelar',
    confirm: 'Confirmar',
    delete: 'Eliminar',
    edit: 'Editar',
    add: 'Agregar',
    
    // Device Management
    scanForDevices: 'Buscar Dispositivos',
    scanning: 'Buscando...',
    devicesFound: 'dispositivos encontrados',
    noDevicesFound: 'No se encontraron dispositivos',
    
    // Home Screen
    appTitle: 'HoofBeat',
    appSubtitle: 'Tu compañero ganadero',
    animalSpeedTitle: 'Velocidad de {{animalName}}',
    speedUpdatedSuccess: 'Velocidad de {{animalName}} actualizada',
    speedUpdateFailed: 'Error al actualizar velocidad',
    
    // Quick Stats
    quickOverview: 'Resumen Rápido',
    activeDevices: 'Dispositivos Activos',
    todaysFeedings: 'Alimentaciones de Hoy',
    medications: 'Medicamentos',
    
    // Animals Overview
    myAnimals: 'Mis Animales',
    seeAll: 'Ver Todo',
    addAnimal: 'Agregar Animal',
    noAnimalsYet: 'Aún no se han agregado animales',
    addFirstAnimal: 'Agrega Tu Primer Animal',
    
    // Upcoming Tasks
    upcomingTasks: 'Tareas Próximas',
    noTasksToday: 'No hay tareas programadas para hoy',
    allCaughtUp: '¡Todo al día! Tus animales están bien cuidados.',
    feedingTask: 'Alimentación: {{feedType}}',
    medicationTask: 'Medicación: {{medicationName}}',
    amount: 'Cantidad',
    dosage: 'Dosis',
    moreTasksToday: '+{{count}} tareas más hoy',
    
    // Premium Banner
    premium: 'Premium',
    upgradeToPremium: 'Actualizar a Premium',
    unlimitedAnimals: 'Animales ilimitados',
    advancedAnalytics: 'Análisis avanzados',
    upgradeNow: 'Actualizar Ahora',
    
    // AI Onboarding
    meetYourAIAssistant: 'Conoce a Tu Asistente IA',
    aiAssistantDescription: 'Obtén planes de entrenamiento personalizados, información de salud y consejos de entrenamiento impulsados por IA.',
    readinessScores: 'Puntuaciones de Preparación',
    trainingPlans: 'Planes de Entrenamiento',
    logFirstTrainingSession: 'Registra Tu Primera Sesión de Entrenamiento',
    
    // Speed Monitor
    speed: 'Velocidad',
    currentSpeed: 'Velocidad Actual',
    refresh: 'Actualizar',
    lastUpdated: 'Última actualización',
    
    // Animals Screen
    id: 'ID',
    ageYears: '{{age}} años',
    gps: 'GPS',
    selectLanguage: 'Seleccionar idioma',
    changingLanguage: 'Cambiando idioma...',
    
    // Animal Detail Screen
    animalDetailTrainingSessions: 'Sesiones de Entrenamiento',
    animalDetailLoadingSessions: 'Cargando sesiones...',
    animalDetailLogNewSession: 'Registrar Nueva Sesión',
    animalDetailNoTrainingSessions: 'Sin Sesiones de Entrenamiento',
    animalDetailTrainingDescription: 'Comienza a registrar sesiones de entrenamiento para obtener información y recomendaciones impulsadas por IA.',
    animalDetailLogFirstSession: 'Registrar Primera Sesión',
    animalDetailLatestVitals: 'Últimos Signos Vitales',
    animalDetailDeviceOnly: 'Solo dispositivo',
    animalDetailRecordedOn: 'Registrado el',
    animalDetailTemperature: 'Temperatura',
    animalDetailHeartRate: 'Frecuencia Cardíaca',
    animalDetailRespiration: 'Respiración',
    animalDetailWeight: 'Peso',
    animalDetailNotes: 'Notas',
    animalDetailNoVitalsRecorded: 'No se han registrado signos vitales',
    animalDetailConnectDevice: 'Conecta un dispositivo para registrar signos vitales automáticamente',
    animalDetailFeedingSchedule: 'Horario de Alimentación',
    animalDetailViewAll: 'Ver Todo',
    animalDetailPrintFeedingSchedule: 'Imprimir Horario de Alimentación',
    animalDetailNoFeedingSchedule: 'No hay horario de alimentación establecido',
    animalDetailSetFeedingSchedule: 'Establecer Horario de Alimentación',
    animalDetailMedications: 'Medicamentos',
    animalDetailAdd: '+ Agregar',
    animalDetailPrintMedicationSchedule: 'Imprimir Horario de Medicamentos',
    animalDetailNoMedicationsAdded: 'No se han agregado medicamentos',
    animalDetailAddMedication: 'Agregar Medicamento',
    animalDetailVaccinations: 'Vacunas',
    animalDetailDue: 'Vencimiento:',
    animalDetailNoRenewal: 'Sin renovación',
    animalDetailPrintVaccinationRecord: 'Imprimir Registro de Vacunas',
    animalDetailNoVaccinationsRecorded: 'No se han registrado vacunas',
    animalDetailAddVaccination: 'Agregar Vacuna',
    animalDetailMicrochipRequired: 'ID de microchip requerido para registros de vacunas',
    animalDetailAIAssistant: 'Asistente IA',
    animalDetailReadinessScore: 'Puntuación de Preparación',
    animalDetailHealthStatus: 'Estado de Salud',
    animalDetailNewTips: 'Nuevos Consejos',
    animalDetailTip: 'consejo',
    animalDetailTips: 'consejos',
    animalDetailMoreTip: 'consejo más',
    animalDetailMoreTips: 'consejos más',
    animalDetailNoAIInsights: 'Aún no hay información de IA. Registra una sesión de entrenamiento para comenzar.',
    animalDetailAnalyzing: 'Analizando...',
    animalDetailGetAIAnalysis: 'Obtener Análisis IA',
    animalDetailViewAllInsights: 'Ver Toda la Información',
    
    // AI Assistant Widget
    aiWidgetInsights: 'Información IA',
    aiWidgetReadiness: 'Preparación:',
    aiWidgetNoAIData: 'Aún no hay datos de IA',
    aiWidgetLogTraining: 'Registrar entrenamiento',
    aiWidgetLiveStatus: 'Estado en Vivo:',
    aiWidgetAvailable: 'Disponible',
    aiWidgetHealth: 'Salud:',
    aiWidgetLatestTip: 'Último Consejo:',
    aiWidgetChecking: 'Verificando...',
    aiWidgetLiveCheckIn: 'Check-in en Vivo',
    aiWidgetViewAll: 'Ver Todo',
    aiWidgetNew: 'NUEVO',
    
    // Common UI
    loading: 'Cargando...',
    error: 'Error',
    success: 'Éxito',
    warning: 'Advertencia',
    info: 'Información',
  },
};

// Language metadata
export const languageInfo = {
  en: { name: 'English', nativeName: 'English', isRTL: false },
  ar: { name: 'Arabic', nativeName: 'العربية', isRTL: true },
  fr: { name: 'French', nativeName: 'Français', isRTL: false },
  ja: { name: 'Japanese', nativeName: '日本語', isRTL: false },
  it: { name: 'Italian', nativeName: 'Italiano', isRTL: false },
  tr: { name: 'Turkish', nativeName: 'Türkçe', isRTL: false },
  nl: { name: 'Dutch', nativeName: 'Nederlands', isRTL: false },
  es: { name: 'Spanish', nativeName: 'Español', isRTL: false },
};

// Type definitions
export type LanguageCode = keyof typeof translations;
export type TranslationKey = keyof typeof translations.en;

// Helper function to get available languages
export const getAvailableLanguages = (): Array<{
  code: LanguageCode;
  name: string;
  nativeName: string;
  isRTL: boolean;
}> => {
  return Object.entries(languageInfo).map(([code, info]) => ({
    code: code as LanguageCode,
    ...info,
  }));
};

// Helper function to check if language is RTL
export const isRTLLanguage = (languageCode: LanguageCode): boolean => {
  return languageInfo[languageCode]?.isRTL || false;
};