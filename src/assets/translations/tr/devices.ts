export const devices = {
  // Cihaz durumu
  paired: 'Eşleştirilmiş',
  available: 'Mevcut',
  connected: '<PERSON><PERSON><PERSON><PERSON>',
  disconnected: 'Bağlantı kesildi',
  connecting: 'Bağlanıyor...',
  
  // Cihaz keşfi
  findDevices: 'Cihaz bul',
  scanForDevices: 'Cihazları tara',
  scanning: 'Taranıyor...',
  loadingDevices: 'Cihazlar yükleniyor...',
  checkingBluetooth: 'Bluetooth kontrol ediliyor...',
  noDevicesFound: 'Hiç cihaz bulunamadı',
  
  // Cihaz eşleştirme
  pairDevice: 'Cihazı eşleştir',
  pairDevicePrompt: 'Eşleştirilecek cihazı seçin',
  pairing: 'Eşleştiriliyor...',
  pairingSuccess: 'Cihaz başarıyla eşleştirildi',
  pairingFailed: 'Cihaz eşleştirme başarısız',
  noPairedDevices: 'Eşleştirilmiş cihaz bulunamadı',
  
  // Cihaz yönetimi
  unpairDevice: 'Cihaz eşleştirmesini kaldır',
  deviceInfo: 'Cihaz bilgileri',
  batteryLevel: 'Pil seviyesi',
  signalStrength: 'Sinyal gücü',
  lastSeen: 'Son görülme',
  
  // Bluetooth
  bluetoothDisabled: 'Bluetooth devre dışı',
  enableBluetooth: 'Bluetooth\'u etkinleştir',
  bluetoothUnavailable: 'Bluetooth kullanılamıyor',
  bluetoothPermission: 'Bluetooth izni gerekli',
  
  // Cihaz türleri
  heartRateMonitor: 'Kalp atış hızı monitörü',
  activityTracker: 'Aktivite takipçisi',
  smartCollar: 'Akıllı tasma',
  temperatureSensor: 'Sıcaklık sensörü',
  gpsTracker: 'GPS takipçisi',
  
  // Eylemler
  refresh: 'Yenile',
  retry: 'Tekrar dene',
  cancel: 'İptal',
  connect: 'Bağlan',
  disconnect: 'Bağlantıyı kes',
  
  // DevicesScreen'de kullanılan ek anahtar kelimeler
  initializing: 'Başlatılıyor...',
  bluetoothErrorPrefix: 'Bluetooth Hatası:',
  retryBluetoothCheck: 'Bluetooth kontrolünü tekrar dene',
  bluetoothRequiredWarning: 'Cihaz bağlantısı için Bluetooth gereklidir',
  availableToAdd: 'Eklenmeye hazır',
  addDevice: 'Cihaz Ekle',
  scanToFindDevicesPrompt: 'Yakındaki cihazları bulmak için tara',
  deviceIdPlaceholder: 'Cihaz kimliğini girin veya barkodu tarayın',
  add: 'Ekle'
};