// Modular Turkish translations
import { devices } from './devices';

export const tr = {
  ...devices,
  // Common actions
  add: '<PERSON><PERSON>',
  edit: '<PERSON><PERSON><PERSON><PERSON>',
  delete: '<PERSON><PERSON>',
  save: '<PERSON><PERSON>',
  cancel: '<PERSON><PERSON><PERSON>',
  confirm: '<PERSON><PERSON><PERSON>',
  back: '<PERSON><PERSON>',
  next: '<PERSON><PERSON><PERSON>',
  previous: '<PERSON><PERSON><PERSON>',
  loading: '<PERSON><PERSON><PERSON><PERSON>yor...',
  error: '<PERSON><PERSON>',
  success: '<PERSON><PERSON>ar<PERSON>l<PERSON>',
  warning: 'Uyar<PERSON>',
  info: '<PERSON><PERSON><PERSON>',
  retry: '<PERSON><PERSON><PERSON> dene',
  refresh: '<PERSON><PERSON><PERSON>',
  
  // Navigation
  home: 'Ana Sayfa',
  animals: '<PERSON><PERSON><PERSON>',
  devices: 'Cihazlar',
  profile: 'Profil',
  settings: '<PERSON><PERSON><PERSON>',
  advancedSettings: 'Gelişmiş Ayarlar',
  
  // Tab Navigation
  tabHome: 'Ana Sayfa',
  tabAnimals: 'Hayvanlar',
  tabDevices: 'Cihazlar',
  tabHealth: '<PERSON><PERSON>l<PERSON>k',
  tabProfile: 'Profil',
  
  // Authentication
  createAccount: 'Hesa<PERSON> o<PERSON>',
  resetPassword: '<PERSON><PERSON><PERSON><PERSON> sıfırla',
  signInToAccount: 'He<PERSON><PERSON><PERSON><PERSON><PERSON>za giriş yapın',
  login: '<PERSON><PERSON><PERSON>',
  logout: '<PERSON><PERSON><PERSON>ış',
  register: 'Kayıt ol',
  email: 'E-posta',
  password: 'Şifre',
  confirmPassword: 'Şifreyi onayla',
  forgotPassword: 'Şifrenizi mi unuttunuz?',
  
  // Biometric Authentication
  biometricLogin: 'Biyometrik giriş',
  signInWithFingerprint: 'Parmak izi ile giriş',
  signInWithFaceId: 'Face ID ile giriş',
  signInWithBiometric: 'Biyometrik ile giriş',
  biometricAuthenticationFailed: 'Biyometrik kimlik doğrulama başarısız',
  biometricNotAvailable: 'Bu cihazda biyometrik kimlik doğrulama mevcut değil',
  biometricNotEnabled: 'Biyometrik giriş etkin değil',
  biometricSetupRequired: 'Biyometrik kurulum gerekli',
  biometricSetupDescription: 'Biyometrik girişi kullanmak için önce cihaz ayarlarından {{biometricType}} kurulumunu yapın.',
  enableBiometricLogin: 'Hesabınıza hızlı ve güvenli erişim için biyometrik girişi etkinleştirin.',
  biometricLoginEnabled: 'Biyometrik giriş başarıyla etkinleştirildi!',
  biometricLoginDisabled: 'Biyometrik giriş devre dışı bırakıldı',
  disableBiometricLogin: 'Biyometrik girişi devre dışı bırak',
  disableBiometricConfirm: 'Biyometrik girişi devre dışı bırakmak istediğinizden emin misiniz? Giriş yapmak için şifrenizi kullanmanız gerekecek.',
  biometricDataSecure: 'Biyometrik verileriniz cihazınızda güvenli kalır ve asla paylaşılmaz.',
  enableBiometricPrompt: 'Biyometrik girişi etkinleştir',
  biometricSetupConfirm: 'Kurulumu onaylamak için biyometriğinizi kullanın',
  biometricSignInPrompt: 'HoofBeat\'e giriş yap',
  biometricSignInSubtitle: 'Hesabınıza erişmek için biyometriğinizi kullanın',
  biometricSetupCancelled: 'Biyometrik kurulum iptal edildi',
  biometricRefreshSession: 'Oturumunuzu yenilemek için şifrenizle giriş yapın, ardından biyometrik girişi yeniden etkinleştirin.',
  biometricCredentialsFirst: 'Biyometrik girişi etkinleştirmeden önce kimlik bilgilerinizi doğrulayın',
  biometricSignInPasswordFirst: 'Biyometrik girişi etkinleştirmek için önce şifrenizle giriş yapın',
  or: 'veya',
  
  // Error Messages
  userNotFound: 'Kullanıcı bulunamadı',
  profileCreationFailed: 'Kullanıcı profili oluşturma başarısız',
  profileLoadFailed: 'Kullanıcı profili yükleme başarısız',
  sessionExpired: 'Oturum süresi doldu. Lütfen tekrar giriş yapın.',
  networkError: 'Ağ hatası. Bağlantınızı kontrol edin.',
  unexpectedError: 'Beklenmeyen bir hata oluştu',
  
  // Biometric types
  biometric: 'Biyometrik',
  fingerprint: 'Parmak izi',
  faceId: 'Face ID',
  biometricAuthentication: 'Biyometrik kimlik doğrulama',
  
  // MFA
  mfaSettings: 'İki faktörlü kimlik doğrulama',
  mfaEnabled: 'İki faktörlü kimlik doğrulama etkinleştirildi',
  mfaDisabled: 'İki faktörlü kimlik doğrulama devre dışı bırakıldı',
  
  // Animal management
  animalName: 'Hayvan adı',
  animalType: 'Hayvan türü',
  breed: 'Cins',
  age: 'Yaş',
  gender: 'Cinsiyet',
  weight: 'Ağırlık',
  color: 'Renk',
  microchipId: 'Mikroçip ID',
  registrationNumber: 'Kayıt numarası',
  myAnimals: 'Hayvanlarım',
  seeAll: 'Tümünü gör',
  addAnimal: 'Hayvan ekle',
  noAnimalsYet: 'Henüz hayvan eklenmedi',
  addFirstAnimal: 'İlk hayvanınızı ekleyin',
  id: 'ID',
  ageYears: '{{age}} yaş',
  gps: 'GPS',
  animalNotFound: 'Hayvan bulunamadı',
  
  // AI Features
  aiAssistant: 'AI Asistan',
  aiChat: 'AI Sohbet',
  aiChatTitle: 'AI asistanla sohbet et',
  aiChatPlaceholder: 'Hayvanınızın sağlığı, eğitimi veya bakımı hakkında sorular sorun...',
  aiChatSend: 'Gönder',
  aiChatAttachment: 'Resim ekle',
  aiChatCamera: 'Fotoğraf çek',
  aiChatGallery: 'Galeriden seç',
  aiChatAnalyzing: 'Analiz ediliyor...',
  aiChatTyping: 'AI yazıyor...',
  aiChatNoMessages: 'Henüz mesaj yok',
  aiChatStartConversation: '{{animalName}} hakkında AI asistanınızla konuşmaya başlayın',
  aiChatImageAnalysis: 'Resim analizi',
  aiChatCopyMessage: 'Mesajı kopyala',
  aiChatShareMessage: 'Mesajı paylaş',
  aiChatMessageCopied: 'Mesaj panoya kopyalandı',
  aiChatUploadingImage: 'Resim yükleniyor...',
  aiChatImageUploadFailed: 'Resim yükleme başarısız',
  aiChatSendingMessage: 'Mesaj gönderiliyor...',
  aiChatMessageFailed: 'Mesaj gönderme başarısız',
  aiChatRetry: 'Tekrar dene',
  aiChatMaxFileSize: 'Dosya boyutu 10MB\'dan küçük olmalıdır',
  aiChatUnsupportedFormat: 'Desteklenmeyen dosya formatı',
  
  // Additional translations for backward compatibility
  language: 'Dil',
  languageSettings: 'Dil ve bölge',
  selectYourLanguage: 'Dilinizi seçin',
  languageChanged: 'Dil başarıyla değiştirildi',
  selectLanguage: 'Dil seç',
  changingLanguage: 'Dil değiştiriliyor...',
  
  // Profile navigation
  profileSettings: 'Ayarlar',
  profileAdvancedSettings: 'Gelişmiş ayarlar',
  profilePrivacySettings: 'Gizlilik ayarları',
  profileMfaSettings: 'İki faktörlü kimlik doğrulama',
  profileUpgradeToPremium: 'Premium\'a yükselt',
  profileHelpSupport: 'Yardım ve destek',
  profileFAQ: 'SSS',
  profileContactSupport: 'Destekle iletişim',
  profileSignOut: 'Çıkış yap',
  profileSignOutConfirm: 'Çıkış yapmak istediğinizden emin misiniz?',
  profileCancel: 'İptal',
  profileVersion: 'Sürüm',
  profileSignOutFailed: 'Çıkış başarısız',
  
  // Screen titles
  aiHealthDashboard: 'AI Sağlık Panosu',
  healthScoreDetail: 'Sağlık Skoru Detayı',
  diseaseRisk: 'Hastalık Riski',
  healthTrends: 'Sağlık Trendleri',
  stressAnalysis: 'Stres Analizi',
  sleepMonitoring: 'Uyku İzleme',
  environmentalAnalysis: 'Çevresel Analiz',
  predictiveInsights: 'Öngörülü İçgörüler',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'Çiftlik arkadaşınız',
  
  // Premium
  premium: 'Premium',
  upgradeToPremium: 'Premium\'a yükselt',
  unlimitedAnimals: 'Sınırsız hayvan',
  advancedAnalytics: 'Gelişmiş analitik',
  upgradeNow: 'Şimdi yükselt',
  
  // Health and vitals
  health: 'Sağlık',
  vitals: 'Yaşamsal belirtiler',
  temperature: 'Sıcaklık',
  heartRate: 'Kalp atış hızı',
  respirationRate: 'Solunum hızı',
  bloodPressure: 'Kan basıncı',
  
  // Additional keys
  behavioralAnalysis: 'Davranışsal analiz',
  environmentalAnalysisDescription: 'Çevrenin sağlık üzerindeki etkisini analiz et',
  environmentalImpact: 'Çevresel etki',
  viewAll: 'Tümünü gör',
  
  // Quick Actions
  quickActions: 'Hızlı İşlemler',
  healthTrends: 'Sağlık Trendleri',
  diseaseRisk: 'Hastalık Riski',
  stressAnalysis: 'Stres Analizi',
  sleepMonitoring: 'Uyku İzlemesi',
  environmental: 'Çevresel',
  predictiveInsights: 'Tahmine Dayalı İçgörüler',
  assistant: 'AI Asistanı',
  
  // Additional missing AI keys
  environmentalAnalysis: 'Çevresel Analiz',
  behavioralAnalysis: 'Davranışsal Analiz',
  aiHealthDashboard: 'AI Sağlık Paneli',
  smartAlerts: 'Akıllı Uyarılar',
  diseaseRiskAssessment: 'Hastalık Risk Değerlendirmesi',
  healthScoreDetails: 'Sağlık Skoru Detayları',
  trendsOverview: 'Trend Genel Bakış',
  riskOverview: 'Risk Genel Bakış',
  
  // Health Dashboard
  healthDashboard: 'Sağlık Paneli',
  overallHealthStatus: 'Genel Sağlık Durumu',
  totalAnimals: 'Toplam Hayvan',
  healthScore: 'Sağlık Skoru',
  activeAlerts: 'Aktif Uyarılar',
  recentAnalyses: 'Son Analizler',
  animalNotSelected: 'Hayvan seçilmedi',
  selectAnimalForDetails: 'Sağlık detaylarını görmek için bir hayvan seçin',
  viewHealthDetails: 'Sağlık Detaylarını Gör',
  lastUpdated: 'Son güncelleme',
  noHealthData: 'Sağlık verisi yok',
  tapToViewDetails: 'Detayları görmek için dokun',
  
  // Common translations
  refresh: 'Yenile',
  refreshing: 'Yenileniyor...',
  viewDetails: 'Detayları gör',
  
  // Nested objects for compatibility with modular structure
  common: {
    refresh: 'Yenile',
    refreshing: 'Yenileniyor...',
  },
  
  ai: {
    quickActions: 'Hızlı İşlemler',
    healthTrends: 'Sağlık Trendleri',
    diseaseRisk: 'Hastalık Riski',
    stressAnalysis: 'Stres Analizi',
    sleepMonitoring: 'Uyku İzlemesi',
    environmental: 'Çevresel',
    predictiveInsights: 'Tahmine Dayalı İçgörüler',
    assistant: 'AI Asistanı',
    environmentalAnalysis: 'Çevresel Analiz',
    behavioralAnalysis: 'Davranışsal Analiz',
    aiHealthDashboard: 'AI Sağlık Paneli',
    healthDashboard: 'Sağlık Paneli',
    smartAlerts: 'Akıllı Uyarılar',
    diseaseRiskAssessment: 'Hastalık Risk Değerlendirmesi',
    healthScoreDetails: 'Sağlık Skoru Detayları',
    trendsOverview: 'Trend Genel Bakış',
    riskOverview: 'Risk Genel Bakış',
    
    // AI Widget keys
    aiWidgetInsights: 'AI İçgörüler',
    aiWidgetHealth: 'Sağlık Durumu',
    aiWidgetLiveCheckIn: 'Canlı Check-in',
    aiWidgetViewAll: 'Tümünü Gör',
    aiWidgetNew: 'Yeni',
    aiWidgetLogTraining: 'Antrenman Kaydet',
    aiWidgetNoAIData: 'AI Verisi Yok',
    aiWidgetReadiness: 'Hazırlık',
    aiWidgetLiveStatus: 'Canlı Durum',
    aiWidgetAvailable: 'Mevcut',
    aiWidgetLatestTip: 'Son İpucu',
    aiWidgetChecking: 'Kontrol Ediliyor...',
  },
  
  // Root level AI widget keys for backward compatibility
  aiWidgetInsights: 'AI İçgörüler',
  aiWidgetHealth: 'Sağlık Durumu',
  aiWidgetLiveCheckIn: 'Canlı Check-in',
  aiWidgetViewAll: 'Tümünü Gör',
  aiWidgetNew: 'Yeni',
  aiWidgetLogTraining: 'Antrenman Kaydet',
  aiWidgetNoAIData: 'AI Verisi Yok',
  aiWidgetReadiness: 'Hazırlık',
  aiWidgetLiveStatus: 'Canlı Durum',
  aiWidgetAvailable: 'Mevcut',
  aiWidgetLatestTip: 'Son İpucu',
  aiWidgetChecking: 'Kontrol Ediliyor...',
  
  // Missing keys from en/common.ts - Turkish translations
  // Health Dashboard
  healthDashboard: 'Sağlık Paneli',
  overallHealthStatus: 'Genel Sağlık Durumu',
  criticalAlerts: 'Kritik Uyarılar',
  recentActivity: 'Son Aktivite',
  healthInsights: 'Sağlık İçgörüleri',
  quickActions: 'Hızlı İşlemler',
  viewHealthScore: 'Sağlık Puanunu Gör',
  analyzeRisks: 'Riskleri Analiz Et',
  viewTrends: 'Trendleri Gör',
  stressAnalysis: 'Stres Analizi',
  sleepMonitoring: 'Uyku İzlemesi',
  environmentalAnalysis: 'Çevresel Analiz',
  predictiveInsights: 'Tahmine Dayalı İçgörüler',
  noAnimalsSelected: 'Hiçbir hayvan seçilmedi',
  selectAnimalToViewHealth: 'Sağlık panosunu görmek için bir hayvan seçin',
  healthDataLoading: 'Sağlık verileri yüklüyor...',
  healthDataError: 'Sağlık verileri yüklenirken hata',
  retryLoadingHealthData: 'Sağlık verilerini tekrar yükle',
  
  // FAQ
  frequentlyAskedQuestions: 'Sık Sorulan Sorular',
  howToAddAnimal: 'Yeni hayvan nasıl eklenir?',
  howToAddAnimalAnswer: 'Yeni hayvan eklemek için Hayvanlar sekmesine gidin ve "+" düğmesine dokunun. Hayvanınızın ad, ırk, yaş ve diğer detayları dahil bilgilerini doldurun.',
  howToConnectDevice: 'İzleme cihazı nasıl bağlanır?',
  howToConnectDeviceAnswer: 'Cihazlar sekmesine gidin, "Cihaz Ekle"ye dokunun ve eşleştirme talimatlarını takip edin. Telefonunuzda Bluetooth\'un etkin olduğundan emin olun.',
  whatIsHealthScore: 'Sağlık Puanı nedir?',
  whatIsHealthScoreAnswer: 'Sağlık Puanı, hayvanınızın genel sağlığının vital belirtiler, aktivite seviyeleri ve diğer izlenen verilere dayalı AI tarafından oluşturulan bir değerlendirmesidir.',
  howToSetupAlerts: 'Sağlık uyarıları nasıl kurulur?',
  howToSetupAlertsAnswer: 'Hayvanınızın sağlık durumu hakkında ne zaman ve nasıl uyarı alacağınızı yapılandırmak için Ayarlar > Bildirimler\'e gidin.',
  whatDataIsTracked: 'Uygulama hangi verileri takip eder?',
  whatDataIsTrackedAnswer: 'Uygulama vital belirtileri (kalp hızı, sıcaklık), aktivite seviyelerini, konumu, beslenme programlarını, ilaçları ve çevresel faktörleri takip eder.',
  howToExportData: 'Hayvanımın verilerini nasıl dışa aktarabilirim?',
  howToExportDataAnswer: 'Hayvanınızın profiline gidin, menü düğmesine dokunun ve "Verileri Dışa Aktar"u seçin. Dışa aktarmak istediğiniz tarih aralığı ve veri türlerini seçebilirsiniz.',
  isPremiumWorthIt: 'Premium abonelik değer mi?',
  isPremiumWorthItAnswer: 'Premium sınırsız hayvan, gelişmiş AI içgörüleri, detaylı sağlık raporları ve öncelikli destek sunar. Profesyonel kullanım veya çoklu hayvanlar için idealdir.',
  howToContactSupport: 'Desteğe nasıl ulaşılır?',
  howToContactSupportAnswer: 'Profilinizdeki İletişim sekmesi üzerinden desteğe ulaşabilir veya doğrudan <EMAIL> adresine e-posta gönderebilirsiniz.',
  
  // Contact
  contactUs: 'Bize Ulaşın',
  messageSent: 'Mesaj başarıyla gönderildi',
  messageSentTitle: 'Mesaj Gönderildi',
  messageSentDescription: 'Mesajınız <EMAIL> adresine gönderildi. Size kısa sürede geri döneceğiz!',
  failedToSendMessage: 'Mesaj gönderme başarısız. Lütfen tekrar deneyin.',
  errorSendingMessageTitle: 'Mesaj Gönderme Hatası',
  errorSendingMessageDescription: 'Mesajınızı gönderirken bir sorun oluştu. Lütfen tekrar deneyin veya canlı sohbet seçeneğini kullanın.',
  chatError: 'Sohbet Hatası',
  failedToOpenChat: 'Sohbet açılamadı. Lütfen tekrar deneyin.',
  openingChatSupport: 'Sohbet desteği açılıyor...',
  
  // Print
  print: 'Yazdır',
  printed: 'Yazdırıldı',
  download: 'İndir',
  downloaded: 'İndirildi',
  shared: 'Paylaşıldı',
  animalNotFound: 'Hayvan bulunamadı',
  goBack: 'Geri Dön',
  
  // Barcode Scanner
  cameraPermissionRequired: 'Barkod taramak için kamera izni gerekli',
  invalidBarcodeFormat: 'Geçersiz cihaz barkod formatı',
  deviceBarcodeScanned: 'Cihaz barkodu taranıldı',
  requestingCameraPermission: 'Kamera izni isteniyor...',
  noAccessToCamera: 'Kameraya erişim yok',
  scanDeviceBarcode: 'Cihaz Barkodunu Tara',
  positionBarcodeInFrame: 'Taramak için barkodu çerçeve içine yerleştirin',
  scanAgain: 'Tekrar Tara',
  
  // Privacy Settings
  privacySettings: 'Gizlilik Ayarları',
  privacySettingsSaved: 'Gizlilik ayarları kaydedildi',
  accountDeleted: 'Hesap silindi',
  locationTracking: 'Konum Takibi',
  locationTrackingDescription: 'Uygulamaya hayvanlarınızın konumlarını takip etme izni verin. Bu konum uyarıları için gereklidir.',
  shareDataWithVets: 'Veterinerlerle Veri Paylaşımı',
  shareDataWithVetsDescription: 'Paylaştığınızda veterinerlerin hayvanlarınızın sağlık kayıtlarına erişmesine izin verin.',
  analytics: 'Analitik',
  analyticsDescription: 'Anonim kullanım verisi toplamasına izin vererek geliştirmemize yardımcı olun.',
  saveSettings: 'Ayarları Kaydet',
  
  // Alert Types
  heartRateAlert: 'Kalp Hızı Uyarısı',
  temperatureAlert: 'Sıcaklık Uyarısı',
  locationAlert: 'Konum Uyarısı',
  healthAlert: 'Sağlık Uyarısı',
  
  // Health Status
  score: 'Puan',
  alerts: 'Uyarılar',
  trends: 'Trendler',
  tracked: 'Takip Edilen',
  criticalAlertsRequireAttention: 'kritik uyarı dikkat gerektiriyor',
  lastUpdated: 'Son güncelleme',
  excellent: 'Mükemmel',
  good: 'İyi',
  fair: 'Orta',
  poor: 'Kötü',
  critical: 'Kritik',
  aiAnalysis: 'AI Analizi',
  active: 'Aktif',
  seeAll: 'Tümünü Gör',
  noAnimalsYet: 'Henüz hayvan yok',
  addFirstAnimal: 'İlk hayvanınızı ekleyin',
  
  // Missing AI & Health Analysis Keys (Turkish translations)
  diseaseRiskAssessment: 'Hastalık Risk Değerlendirmesi',
  healthTrends: 'Sağlık Trendleri',
  stressAnalysis: 'Stres Analizi',
  sleepMonitoring: 'Uyku İzlemesi',
  environmentalAnalysis: 'Çevresel Analiz',
  predictiveInsights: 'Tahmine Dayalı İçgörüler',
  aiHealthDashboard: 'AI Sağlık Paneli',
  healthDashboard: 'Sağlık Paneli',
  smartAlerts: 'Akıllı Uyarılar',
  healthScoreDetails: 'Sağlık Skoru Detayları',
  trendsOverview: 'Trend Genel Bakış',
  riskOverview: 'Risk Genel Bakış',
  analyzing: 'Analiz ediliyor...',
  analyzingEnvironment: 'Çevre analiz ediliyor...',
  analyzeEnvironment: 'Çevreyi Analiz Et',
  generatingPredictions: 'Tahminler üretiliyor...',
  generatePredictions: 'Tahmin Üret',
  predictionHorizon: 'Tahmin Ufuğu',
  oneWeek: '1 Hafta',
  oneMonth: '1 Ay',
  threeMonths: '3 Ay',
  healthPredictions: 'Sağlık Tahminleri',
  predictedHealthScore: 'Tahmini Sağlık Skoru',
  predictedWeight: 'Tahmini Ağırlık',
  predictedActivity: 'Tahmini Aktivite',
  predictedSleep: 'Tahmini Uyku',
  predictionConfidence: 'Tahmin Güveni',
  riskAssessment: 'Risk Değerlendirmesi',
  diseaseRisk: 'Hastalık Riski',
  injuryRisk: 'Yaralanma Riski',
  behavioralRisk: 'Davranışsal Risk',
  environmentalStressRisk: 'Çevresel Stres Riski',
  predictiveAlerts: 'Tahmine Dayalı Uyarılar',
  acknowledgeAlert: 'Kabul Et',
  resolveAlert: 'Çöz',
  humidity: 'Nem',
  airQuality: 'Hava Kalitesi',
  uvIndex: 'UV Endeksi',
  moderate: 'Orta',
  hazardous: 'Tehlikeli',
  optimal: 'Optimal',
};