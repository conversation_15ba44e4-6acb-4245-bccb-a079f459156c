// Turkish translations
export const tr = {
  // Navigation
  home: '<PERSON> Sayfa',
  animals: '<PERSON><PERSON><PERSON>',
  devices: '<PERSON><PERSON><PERSON><PERSON>',
  profile: 'Profil',
  settings: 'Ayar<PERSON>',
  
  // Tab Navigation
  tabHome: 'Ana Sayfa',
  tabAnimals: '<PERSON><PERSON><PERSON>',
  tabDevices: '<PERSON><PERSON><PERSON><PERSON>',
  tabHealth: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  tabProfile: 'Profil',
  
  // Common actions
  add: '<PERSON><PERSON>',
  edit: '<PERSON><PERSON><PERSON><PERSON>',
  delete: 'Sil',
  save: '<PERSON><PERSON>',
  cancel: 'İptal',
  confirm: '<PERSON><PERSON><PERSON>',
  back: '<PERSON><PERSON>',
  next: '<PERSON>leri',
  previous: '<PERSON>nceki',
  loading: 'Yükleniyor...',
  error: 'Hata',
  success: 'Başarılı',
  warning: 'Uyar<PERSON>',
  info: 'Bilgi',
  
  // Authentication
  login: '<PERSON><PERSON><PERSON>',
  logout: '<PERSON>ık<PERSON><PERSON> Yap',
  register: 'Kayıt Ol',
  email: 'E-posta',
  password: '<PERSON><PERSON><PERSON>',
  confirmPassword: '<PERSON><PERSON><PERSON><PERSON>',
  forgotPassword: '<PERSON><PERSON><PERSON><PERSON><PERSON> mi unuttunuz?',
  resetPassword: '<PERSON><PERSON><PERSON><PERSON>',
  
  // Animal management
  animalName: '<PERSON><PERSON>',
  animalType: '<PERSON><PERSON>',
  breed: 'Cins',
  age: 'Yaş',
  gender: 'Cinsiyet',
  weight: 'Ağırlık',
  color: 'Renk',
  microchipId: 'Mikroçip ID',
  registrationNumber: 'Kayıt Numarası',
  
  // Animal detail sections
  animalDetailLatestVitals: 'Son Yaşam Belirtileri',
  animalDetailDeviceOnly: 'Sadece Cihaz',
  animalDetailRecordedOn: 'Kayıt Tarihi',
  animalDetailTemperature: 'Sıcaklık',
  animalDetailHeartRate: 'Kalp Atışı',
  animalDetailRespiration: 'Solunum',
  animalDetailWeight: 'Ağırlık',
  animalDetailNotes: 'Notlar',
  animalDetailNoVitalsRecorded: 'Yaşam belirtisi kaydedilmemiş',
  animalDetailConnectDevice: 'Cihaz Bağla',
  
  animalDetailMedications: 'İlaçlar',
  animalDetailAdd: 'Ekle',
  animalDetailPrintMedicationSchedule: 'İlaç Programını Yazdır',
  animalDetailNoMedicationsAdded: 'İlaç eklenmemiş',
  animalDetailAddMedication: 'İlaç Ekle',
  
  animalDetailVaccinations: 'Aşılar',
  animalDetailDue: 'Vade',
  animalDetailNoRenewal: 'Yenileme yok',
  animalDetailPrintVaccinationRecord: 'Aşı Kaydını Yazdır',
  animalDetailNoVaccinationsRecorded: 'Aşı kaydedilmemiş',
  animalDetailAddVaccination: 'Aşı Ekle',
  animalDetailMicrochipRequired: 'Mikroçip gerekli',
  
  animalDetailFeedingSchedule: 'Beslenme Programı',
  animalDetailViewAll: 'Tümünü Gör',
  animalDetailPrintFeedingSchedule: 'Beslenme Programını Yazdır',
  animalDetailNoFeedingSchedule: 'Beslenme programı yok',
  animalDetailSetFeedingSchedule: 'Beslenme Programı Ayarla',
  
  animalDetailTrainingSessions: 'Eğitim Seansları',
  animalDetailLoadingSessions: 'Seanslar yükleniyor...',
  animalDetailLogNewSession: 'Yeni Seans Kaydet',
  animalDetailNoTrainingSessions: 'Eğitim seansı yok',
  animalDetailTrainingDescription: 'Hayvanınızın eğitim ilerlemesini takip edin',
  animalDetailLogFirstSession: 'İlk Seansı Kaydet',
  
  // Behavioral analysis
  behavioralAnalysis: 'Davranış Analizi',
  stressAnalysis: 'Stres Analizi',
  sleepMonitoring: 'Uyku İzleme',
  environmentalAnalysis: 'Çevresel Analiz',
  environmentalAnalysisDescription: 'Çevrenin sağlık üzerindeki etkisini analiz edin',
  environmentalImpact: 'Çevresel Etki',
  predictiveInsights: 'Öngörülü İçgörüler',
  
  // Health and vitals
  health: 'Sağlık',
  vitals: 'Yaşam Belirtileri',
  temperature: 'Sıcaklık',
  heartRate: 'Kalp Atışı',
  respirationRate: 'Solunum Oranı',
  bloodPressure: 'Kan Basıncı',
  
  // Time and dates
  today: 'Bugün',
  yesterday: 'Dün',
  thisWeek: 'Bu Hafta',
  thisMonth: 'Bu Ay',
  thisYear: 'Bu Yıl',
  
  // Status
  active: 'Aktif',
  inactive: 'Pasif',
  online: 'Çevrimiçi',
  offline: 'Çevrimdışı',
  connected: 'Bağlı',
  disconnected: 'Bağlantı Kesildi',
  
  // Notifications
  notifications: 'Bildirimler',
  noNotifications: 'Bildirim yok',
  markAsRead: 'Okundu olarak işaretle',
  markAllAsRead: 'Tümünü okundu işaretle',
  
  // Search and filters
  search: 'Ara',
  filter: 'Filtrele',
  sort: 'Sırala',
  sortBy: 'Şuna göre sırala',
  filterBy: 'Şuna göre filtrele',
  
  // Language settings
  language: 'Dil',
  selectLanguage: 'Dil Seç',
  
  // Device management
  deviceName: 'Cihaz Adı',
  deviceType: 'Cihaz Türü',
  batteryLevel: 'Batarya Seviyesi',
  lastSync: 'Son Senkronizasyon',
  deviceStatus: 'Cihaz Durumu',
  
  // Reports and analytics
  reports: 'Raporlar',
  analytics: 'Analitik',
  dashboard: 'Kontrol Paneli',
  export: 'Dışa Aktar',
  print: 'Yazdır',
  
  // Emergency and alerts
  emergency: 'Acil Durum',
  alert: 'Uyarı',
  critical: 'Kritik',
  urgent: 'Acil',
  normal: 'Normal',
  
  // Units
  celsius: '°C',
  fahrenheit: '°F',
  kg: 'kg',
  lbs: 'lbs',
  cm: 'cm',
  inches: 'inç',
  
  // Days of week
  monday: 'Pazartesi',
  tuesday: 'Salı',
  wednesday: 'Çarşamba',
  thursday: 'Perşembe',
  friday: 'Cuma',
  saturday: 'Cumartesi',
  sunday: 'Pazar',
  
  // Months
  january: 'Ocak',
  february: 'Şubat',
  march: 'Mart',
  april: 'Nisan',
  may: 'Mayıs',
  june: 'Haziran',
  july: 'Temmuz',
  august: 'Ağustos',
  september: 'Eylül',
  october: 'Ekim',
  november: 'Kasım',
  december: 'Aralık',
  
  // Error Messages
  animalNotFound: 'Hayvan bulunamadı',
  missingAnimalId: 'Bu ekranı görüntülemek için hayvan kimliği gerekli',
  
  // Action Labels
  goBack: 'Geri Dön',
};
