export const devices = {
  // Estado del dispositivo
  paired: 'Emparejado',
  available: 'Disponible',
  connected: '<PERSON><PERSON>tad<PERSON>',
  disconnected: 'Desconectad<PERSON>',
  connecting: 'Conectando...',
  
  // Descubrimiento de dispositivos
  findDevices: 'Buscar dispositivos',
  scanForDevices: 'Escanear dispositivos',
  scanning: 'Escaneando...',
  loadingDevices: 'Cargando dispositivos...',
  checkingBluetooth: 'Verificando Bluetooth...',
  noDevicesFound: 'No se encontraron dispositivos',
  
  // Emparejamiento de dispositivos
  pairDevice: 'Emparejar dispositivo',
  pairDevicePrompt: 'Selecciona un dispositivo para emparejar',
  pairing: 'Emparejando...',
  pairingSuccess: 'Dispositivo emparejado exitosamente',
  pairingFailed: 'Error al emparejar dispositivo',
  noPairedDevices: 'No se encontraron dispositivos emparejados',
  
  // Gestión de dispositivos
  unpairDevice: 'Desemparejar dispositivo',
  deviceInfo: 'Información del dispositivo',
  batteryLevel: 'Nivel de batería',
  signalStrength: 'Intensidad de señal',
  lastSeen: 'Visto por última vez',
  
  // Bluetooth
  bluetoothDisabled: 'Bluetooth deshabilitado',
  enableBluetooth: 'Habilitar Bluetooth',
  bluetoothUnavailable: 'Bluetooth no disponible',
  bluetoothPermission: 'Permiso de Bluetooth requerido',
  
  // Tipos de dispositivos
  heartRateMonitor: 'Monitor de frecuencia cardíaca',
  activityTracker: 'Rastreador de actividad',
  smartCollar: 'Collar inteligente',
  temperatureSensor: 'Sensor de temperatura',
  gpsTracker: 'Rastreador GPS',
  
  // Acciones
  refresh: 'Actualizar',
  retry: 'Reintentar',
  cancel: 'Cancelar',
  connect: 'Conectar',
  disconnect: 'Desconectar',
  
  // Claves adicionales usadas en DevicesScreen
  initializing: 'Inicializando...',
  bluetoothErrorPrefix: 'Error de Bluetooth:',
  retryBluetoothCheck: 'Reintentar verificación de Bluetooth',
  bluetoothRequiredWarning: 'Bluetooth es requerido para la conectividad de dispositivos',
  availableToAdd: 'Disponible para agregar',
  addDevice: 'Agregar Dispositivo',
  scanToFindDevicesPrompt: 'Toca escanear para encontrar dispositivos cercanos',
  deviceIdPlaceholder: 'Ingresa ID del dispositivo o escanea código de barras',
  add: 'Agregar'
};