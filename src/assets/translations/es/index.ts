// Modular Spanish translations
import { devices } from './devices';

export const es = {
  ...devices,
  // Common actions
  add: 'Aña<PERSON>',
  edit: 'Editar',
  delete: 'Eliminar',
  save: '<PERSON>ar',
  cancel: 'Cancelar',
  confirm: 'Confirmar',
  back: 'Atrás',
  next: 'Siguiente',
  previous: 'Anterior',
  loading: 'Cargando...',
  error: 'Error',
  success: 'Éxito',
  warning: 'Advertencia',
  info: 'Información',
  retry: 'Reintentar',
  refresh: 'Actualizar',
  
  // Navigation
  home: 'Inicio',
  animals: 'Animales',
  devices: 'Dispositivos',
  profile: 'Perfil',
  settings: 'Configuración',
  advancedSettings: 'Configuración avanzada',
  
  // Tab Navigation
  tabHome: 'Inicio',
  tabAnimals: 'Animales',
  tabDevices: 'Dispositivos',
  tabHealth: 'Salud',
  tabProfile: 'Perfil',
  
  // Authentication
  createAccount: 'Crear cuenta',
  resetPassword: 'Restablecer contraseña',
  signInToAccount: 'Iniciar sesión en tu cuenta',
  login: 'Iniciar sesión',
  logout: 'Cerrar sesión',
  register: 'Registrarse',
  email: 'Correo electrónico',
  password: 'Contraseña',
  confirmPassword: 'Confirmar contraseña',
  forgotPassword: '¿Olvidaste tu contraseña?',
  
  // Biometric Authentication
  biometricLogin: 'Inicio de sesión biométrico',
  signInWithFingerprint: 'Iniciar sesión con huella dactilar',
  signInWithFaceId: 'Iniciar sesión con Face ID',
  signInWithBiometric: 'Iniciar sesión con biometría',
  biometricAuthenticationFailed: 'Autenticación biométrica fallida',
  biometricNotAvailable: 'La autenticación biométrica no está disponible en este dispositivo',
  biometricNotEnabled: 'El inicio de sesión biométrico no está habilitado',
  biometricSetupRequired: 'Configuración biométrica requerida',
  biometricSetupDescription: 'Para usar el inicio de sesión biométrico, primero configura {{biometricType}} en la configuración del dispositivo.',
  enableBiometricLogin: 'Habilita el inicio de sesión biométrico para un acceso rápido y seguro a tu cuenta.',
  biometricLoginEnabled: '¡Inicio de sesión biométrico habilitado exitosamente!',
  biometricLoginDisabled: 'Inicio de sesión biométrico deshabilitado',
  disableBiometricLogin: 'Deshabilitar inicio de sesión biométrico',
  disableBiometricConfirm: '¿Estás seguro de que quieres deshabilitar el inicio de sesión biométrico? Necesitarás usar tu contraseña para iniciar sesión.',
  biometricDataSecure: 'Tus datos biométricos permanecen seguros en tu dispositivo y nunca se comparten.',
  enableBiometricPrompt: 'Habilitar inicio de sesión biométrico',
  biometricSetupConfirm: 'Usa tu biometría para confirmar la configuración',
  biometricSignInPrompt: 'Iniciar sesión en HoofBeat',
  biometricSignInSubtitle: 'Usa tu biometría para acceder a tu cuenta',
  biometricSetupCancelled: 'Configuración biométrica cancelada',
  biometricRefreshSession: 'Inicia sesión con tu contraseña para actualizar tu sesión, luego vuelve a habilitar el inicio de sesión biométrico.',
  biometricCredentialsFirst: 'Verifica tus credenciales antes de habilitar el inicio de sesión biométrico',
  biometricSignInPasswordFirst: 'Primero inicia sesión con tu contraseña para habilitar el inicio de sesión biométrico',
  or: 'o',
  
  // Error Messages
  userNotFound: 'Usuario no encontrado',
  profileCreationFailed: 'Fallo en la creación del perfil de usuario',
  profileLoadFailed: 'Fallo en la carga del perfil de usuario',
  sessionExpired: 'Sesión expirada. Por favor, inicia sesión de nuevo.',
  networkError: 'Error de red. Verifica tu conexión.',
  unexpectedError: 'Ocurrió un error inesperado',
  
  // Biometric types
  biometric: 'Biométrico',
  fingerprint: 'Huella dactilar',
  faceId: 'Face ID',
  biometricAuthentication: 'Autenticación biométrica',
  
  // MFA
  mfaSettings: 'Autenticación de dos factores',
  mfaEnabled: 'Autenticación de dos factores habilitada',
  mfaDisabled: 'Autenticación de dos factores deshabilitada',
  
  // Animal management
  animalName: 'Nombre del animal',
  animalType: 'Tipo de animal',
  breed: 'Raza',
  age: 'Edad',
  gender: 'Género',
  weight: 'Peso',
  color: 'Color',
  microchipId: 'ID del microchip',
  registrationNumber: 'Número de registro',
  myAnimals: 'Mis animales',
  seeAll: 'Ver todo',
  addAnimal: 'Añadir animal',
  noAnimalsYet: 'Aún no se han añadido animales',
  addFirstAnimal: 'Añade tu primer animal',
  id: 'ID',
  ageYears: '{{age}} años',
  gps: 'GPS',
  animalNotFound: 'Animal no encontrado',
  
  // AI Features
  aiAssistant: 'Asistente IA',
  aiChat: 'Chat IA',
  aiChatTitle: 'Chatea con el asistente IA',
  aiChatPlaceholder: 'Haz preguntas sobre la salud, entrenamiento o cuidado de tu animal...',
  aiChatSend: 'Enviar',
  aiChatAttachment: 'Adjuntar imagen',
  aiChatCamera: 'Tomar foto',
  aiChatGallery: 'Elegir de la galería',
  aiChatAnalyzing: 'Analizando...',
  aiChatTyping: 'IA escribiendo...',
  aiChatNoMessages: 'Aún no hay mensajes',
  aiChatStartConversation: 'Comienza una conversación con tu asistente IA sobre {{animalName}}',
  aiChatImageAnalysis: 'Análisis de imagen',
  aiChatCopyMessage: 'Copiar mensaje',
  aiChatShareMessage: 'Compartir mensaje',
  aiChatMessageCopied: 'Mensaje copiado al portapapeles',
  aiChatUploadingImage: 'Subiendo imagen...',
  aiChatImageUploadFailed: 'Fallo al subir imagen',
  aiChatSendingMessage: 'Enviando mensaje...',
  aiChatMessageFailed: 'Fallo al enviar mensaje',
  aiChatRetry: 'Reintentar',
  aiChatMaxFileSize: 'El tamaño del archivo debe ser menor a 10MB',
  aiChatUnsupportedFormat: 'Formato de archivo no soportado',
  
  // Additional translations for backward compatibility
  language: 'Idioma',
  languageSettings: 'Idioma y región',
  selectYourLanguage: 'Selecciona tu idioma',
  languageChanged: 'Idioma cambiado exitosamente',
  selectLanguage: 'Seleccionar idioma',
  changingLanguage: 'Cambiando idioma...',
  
  // Profile navigation
  profileSettings: 'Configuración',
  profileAdvancedSettings: 'Configuración avanzada',
  profilePrivacySettings: 'Configuración de privacidad',
  profileMfaSettings: 'Autenticación de dos factores',
  profileUpgradeToPremium: 'Actualizar a Premium',
  profileHelpSupport: 'Ayuda y soporte',
  profileFAQ: 'FAQ',
  profileContactSupport: 'Contactar soporte',
  profileSignOut: 'Cerrar sesión',
  profileSignOutConfirm: '¿Estás seguro de que quieres cerrar sesión?',
  profileCancel: 'Cancelar',
  profileVersion: 'Versión',
  profileSignOutFailed: 'Fallo al cerrar sesión',
  
  // Screen titles
  aiHealthDashboard: 'Panel de Salud IA',
  healthScoreDetail: 'Detalle de Puntuación de Salud',
  diseaseRisk: 'Riesgo de Enfermedad',
  healthTrends: 'Tendencias de Salud',
  stressAnalysis: 'Análisis de Estrés',
  sleepMonitoring: 'Monitoreo del Sueño',
  environmentalAnalysis: 'Análisis Ambiental',
  predictiveInsights: 'Perspectivas Predictivas',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'Tu compañero de granja',
  
  // Premium
  premium: 'Premium',
  upgradeToPremium: 'Actualizar a Premium',
  unlimitedAnimals: 'Animales ilimitados',
  advancedAnalytics: 'Análisis avanzados',
  upgradeNow: 'Actualizar ahora',
  
  // Health and vitals
  health: 'Salud',
  vitals: 'Signos vitales',
  temperature: 'Temperatura',
  heartRate: 'Frecuencia cardíaca',
  respirationRate: 'Frecuencia respiratoria',
  bloodPressure: 'Presión arterial',
  
  // Additional keys
  behavioralAnalysis: 'Análisis conductual',
  environmentalAnalysisDescription: 'Analizar el impacto del ambiente en la salud',
  environmentalImpact: 'Impacto ambiental',
  viewAll: 'Ver todo',
  
  // Quick Actions
  quickActions: 'Acciones Rápidas',
  healthTrends: 'Tendencias de Salud',
  diseaseRisk: 'Riesgo de Enfermedad',
  stressAnalysis: 'Análisis de Estrés',
  sleepMonitoring: 'Monitoreo del Sueño',
  environmental: 'Ambiental',
  predictiveInsights: 'Insights Predictivos',
  assistant: 'Asistente IA',
  
  // Additional missing AI keys
  environmentalAnalysis: 'Análisis Ambiental',
  behavioralAnalysis: 'Análisis Conductual',
  aiHealthDashboard: 'Panel de Salud IA',
  smartAlerts: 'Alertas Inteligentes',
  diseaseRiskAssessment: 'Evaluación de Riesgo de Enfermedad',
  healthScoreDetails: 'Detalles de Puntuación de Salud',
  trendsOverview: 'Resumen de Tendencias',
  riskOverview: 'Resumen de Riesgos',
  
  // Health Dashboard
  healthDashboard: 'Panel de Salud',
  overallHealthStatus: 'Estado General de Salud',
  totalAnimals: 'Total de Animales',
  healthScore: 'Puntuación de Salud',
  activeAlerts: 'Alertas Activas',
  recentAnalyses: 'Análisis Recientes',
  animalNotSelected: 'Ningún animal seleccionado',
  selectAnimalForDetails: 'Selecciona un animal para ver detalles de salud',
  viewHealthDetails: 'Ver Detalles de Salud',
  lastUpdated: 'Última actualización',
  noHealthData: 'Sin datos de salud',
  tapToViewDetails: 'Toca para ver detalles',
  
  // Common translations
  refresh: 'Actualizar',
  refreshing: 'Actualizando...',
  viewDetails: 'Ver detalles',
  
  // Nested objects for compatibility with modular structure
  common: {
    refresh: 'Actualizar',
    refreshing: 'Actualizando...',
  },
  
  ai: {
    quickActions: 'Acciones Rápidas',
    healthTrends: 'Tendencias de Salud',
    diseaseRisk: 'Riesgo de Enfermedad',
    stressAnalysis: 'Análisis de Estrés',
    sleepMonitoring: 'Monitoreo del Sueño',
    environmental: 'Ambiental',
    predictiveInsights: 'Insights Predictivos',
    assistant: 'Asistente IA',
    environmentalAnalysis: 'Análisis Ambiental',
    behavioralAnalysis: 'Análisis Conductual',
    aiHealthDashboard: 'Panel de Salud IA',
    healthDashboard: 'Panel de Salud',
    smartAlerts: 'Alertas Inteligentes',
    diseaseRiskAssessment: 'Evaluación de Riesgo de Enfermedad',
    healthScoreDetails: 'Detalles de Puntuación de Salud',
    trendsOverview: 'Resumen de Tendencias',
    riskOverview: 'Resumen de Riesgos',
    
    // AI Widget keys
    aiWidgetInsights: 'Insights IA',
    aiWidgetHealth: 'Estado de Salud',
    aiWidgetLiveCheckIn: 'Check-in en Vivo',
    aiWidgetViewAll: 'Ver Todo',
    aiWidgetNew: 'Nuevo',
    aiWidgetLogTraining: 'Registrar Entrenamiento',
    aiWidgetNoAIData: 'Sin Datos IA',
    aiWidgetReadiness: 'Preparación',
    aiWidgetLiveStatus: 'Estado en Vivo',
    aiWidgetAvailable: 'Disponible',
    aiWidgetLatestTip: 'Último Consejo',
    aiWidgetChecking: 'Verificando...',
  },
  
  // Root level AI widget keys for backward compatibility
  aiWidgetInsights: 'Insights IA',
  aiWidgetHealth: 'Estado de Salud',
  aiWidgetLiveCheckIn: 'Check-in en Vivo',
  aiWidgetViewAll: 'Ver Todo',
  aiWidgetNew: 'Nuevo',
  aiWidgetLogTraining: 'Registrar Entrenamiento',
  aiWidgetNoAIData: 'Sin Datos IA',
  aiWidgetReadiness: 'Preparación',
  aiWidgetLiveStatus: 'Estado en Vivo',
  aiWidgetAvailable: 'Disponible',
  aiWidgetLatestTip: 'Último Consejo',
  aiWidgetChecking: 'Verificando...',
  
  // Missing keys from en/common.ts - Spanish translations
  // Health Dashboard
  healthDashboard: 'Panel de Salud',
  overallHealthStatus: 'Estado General de Salud',
  criticalAlerts: 'Alertas Críticas',
  recentActivity: 'Actividad Reciente',
  healthInsights: 'Perspectivas de Salud',
  quickActions: 'Acciones Rápidas',
  viewHealthScore: 'Ver Puntuación de Salud',
  analyzeRisks: 'Analizar Riesgos',
  viewTrends: 'Ver Tendencias',
  stressAnalysis: 'Análisis de Estrés',
  sleepMonitoring: 'Monitoreo del Sueño',
  environmentalAnalysis: 'Análisis Ambiental',
  predictiveInsights: 'Perspectivas Predictivas',
  noAnimalsSelected: 'Ningún animal seleccionado',
  selectAnimalToViewHealth: 'Selecciona un animal para ver su panel de salud',
  healthDataLoading: 'Cargando datos de salud...',
  healthDataError: 'Error al cargar datos de salud',
  retryLoadingHealthData: 'Reintentar cargar datos de salud',
  
  // FAQ
  frequentlyAskedQuestions: 'Preguntas Frecuentes',
  howToAddAnimal: '¿Cómo agregar un nuevo animal?',
  howToAddAnimalAnswer: 'Para agregar un nuevo animal, ve a la pestaña Animales y toca el botón "+". Completa la información de tu animal incluyendo nombre, raza, edad y otros detalles.',
  howToConnectDevice: '¿Cómo conectar un dispositivo de monitoreo?',
  howToConnectDeviceAnswer: 'Ve a la pestaña Dispositivos, toca "Agregar Dispositivo", y sigue las instrucciones de emparejamiento. Asegúrate de que el Bluetooth esté habilitado en tu teléfono.',
  whatIsHealthScore: '¿Qué es la Puntuación de Salud?',
  whatIsHealthScoreAnswer: 'La Puntuación de Salud es una evaluación generada por IA de la salud general de tu animal basada en signos vitales, niveles de actividad y otros datos monitoreados.',
  howToSetupAlerts: '¿Cómo configurar alertas de salud?',
  howToSetupAlertsAnswer: 'Ve a Configuración > Notificaciones para configurar cuándo y cómo recibes alertas sobre el estado de salud de tu animal.',
  whatDataIsTracked: '¿Qué datos rastrea la aplicación?',
  whatDataIsTrackedAnswer: 'La aplicación rastrea signos vitales (frecuencia cardíaca, temperatura), niveles de actividad, ubicación, horarios de alimentación, medicamentos y factores ambientales.',
  howToExportData: '¿Cómo puedo exportar los datos de mi animal?',
  howToExportDataAnswer: 'Ve al perfil de tu animal, toca el botón de menú, y selecciona "Exportar Datos". Puedes elegir el rango de fechas y tipos de datos a exportar.',
  isPremiumWorthIt: '¿Vale la pena la suscripción Premium?',
  isPremiumWorthItAnswer: 'Premium ofrece animales ilimitados, perspectivas de IA avanzadas, reportes de salud detallados y soporte prioritario. Es ideal para uso profesional o múltiples animales.',
  howToContactSupport: '¿Cómo contactar soporte?',
  howToContactSupportAnswer: 'Puedes contactar soporte a través de la pestaña Contacto en tu perfil, o enviarnos un email <NAME_EMAIL>.',
  
  // Contact
  contactUs: 'Contáctanos',
  messageSent: 'Mensaje enviado exitosamente',
  messageSentTitle: 'Mensaje Enviado',
  messageSentDescription: 'Tu mensaje ha sido <NAME_EMAIL>. ¡Te responderemos pronto!',
  failedToSendMessage: 'Error al enviar mensaje. Por favor intenta de nuevo.',
  errorSendingMessageTitle: 'Error Enviando Mensaje',
  errorSendingMessageDescription: 'Hubo un problema enviando tu mensaje. Por favor intenta de nuevo o usa la opción de chat en vivo.',
  chatError: 'Error de Chat',
  failedToOpenChat: 'Error al abrir chat. Por favor intenta de nuevo.',
  openingChatSupport: 'Abriendo soporte de chat...',
  
  // Print
  print: 'Imprimir',
  printed: 'Impreso',
  download: 'Descargar',
  downloaded: 'Descargado',
  shared: 'Compartido',
  animalNotFound: 'Animal no encontrado',
  goBack: 'Volver',
  
  // Barcode Scanner
  cameraPermissionRequired: 'Se requiere permiso de cámara para escanear códigos de barras',
  invalidBarcodeFormat: 'Formato de código de barras de dispositivo inválido',
  deviceBarcodeScanned: 'Código de barras de dispositivo escaneado',
  requestingCameraPermission: 'Solicitando permiso de cámara...',
  noAccessToCamera: 'Sin acceso a la cámara',
  scanDeviceBarcode: 'Escanear Código de Barras del Dispositivo',
  positionBarcodeInFrame: 'Posiciona el código de barras dentro del marco para escanear',
  scanAgain: 'Escanear de Nuevo',
  
  // Privacy Settings
  privacySettings: 'Configuración de Privacidad',
  privacySettingsSaved: 'Configuración de privacidad guardada',
  accountDeleted: 'Cuenta eliminada',
  locationTracking: 'Seguimiento de Ubicación',
  locationTrackingDescription: 'Permitir que la aplicación rastree las ubicaciones de tus animales. Esto es requerido para alertas de ubicación.',
  shareDataWithVets: 'Compartir Datos con Veterinarios',
  shareDataWithVetsDescription: 'Permitir que los veterinarios accedan a los registros de salud de tus animales cuando los compartas.',
  analytics: 'Analíticas',
  analyticsDescription: 'Ayúdanos a mejorar permitiendo la recolección de datos de uso anónimos.',
  saveSettings: 'Guardar Configuración',
  
  // Alert Types
  heartRateAlert: 'Alerta de Frecuencia Cardíaca',
  temperatureAlert: 'Alerta de Temperatura',
  locationAlert: 'Alerta de Ubicación',
  healthAlert: 'Alerta de Salud',
  
  // Health Status
  score: 'Puntuación',
  alerts: 'Alertas',
  trends: 'Tendencias',
  tracked: 'Rastreado',
  criticalAlertsRequireAttention: 'alertas críticas requieren atención',
  lastUpdated: 'Última actualización',
  excellent: 'Excelente',
  good: 'Bueno',
  fair: 'Regular',
  poor: 'Malo',
  critical: 'Crítico',
  aiAnalysis: 'Análisis IA',
  active: 'Activo',
  seeAll: 'Ver Todo',
  noAnimalsYet: 'Aún no hay animales',
  addFirstAnimal: 'Agregar tu primer animal',
  
  // Missing AI & Health Analysis Keys (Spanish translations)
  diseaseRiskAssessment: 'Evaluación de Riesgo de Enfermedad',
  healthTrends: 'Tendencias de Salud',
  stressAnalysis: 'Análisis de Estrés',
  sleepMonitoring: 'Monitoreo del Sueño',
  environmentalAnalysis: 'Análisis Ambiental',
  predictiveInsights: 'Insights Predictivos',
  aiHealthDashboard: 'Panel de Salud IA',
  healthDashboard: 'Panel de Salud',
  smartAlerts: 'Alertas Inteligentes',
  healthScoreDetails: 'Detalles del Puntaje de Salud',
  trendsOverview: 'Resumen de Tendencias',
  riskOverview: 'Resumen de Riesgos',
  analyzing: 'Analizando...',
  analyzingEnvironment: 'Analizando ambiente...',
  analyzeEnvironment: 'Analizar Ambiente',
  generatingPredictions: 'Generando predicciones...',
  generatePredictions: 'Generar Predicciones',
  predictionHorizon: 'Horizonte de Predicción',
  oneWeek: '1 Semana',
  oneMonth: '1 Mes',
  threeMonths: '3 Meses',
  healthPredictions: 'Predicciones de Salud',
  predictedHealthScore: 'Puntaje de Salud Predicho',
  predictedWeight: 'Peso Predicho',
  predictedActivity: 'Actividad Predicha',
  predictedSleep: 'Sueño Predicho',
  predictionConfidence: 'Confianza de Predicción',
  riskAssessment: 'Evaluación de Riesgo',
  diseaseRisk: 'Riesgo de Enfermedad',
  injuryRisk: 'Riesgo de Lesión',
  behavioralRisk: 'Riesgo Conductual',
  environmentalStressRisk: 'Riesgo de Estrés Ambiental',
  predictiveAlerts: 'Alertas Predictivas',
  acknowledgeAlert: 'Reconocer',
  resolveAlert: 'Resolver',
  humidity: 'Humedad',
  airQuality: 'Calidad del Aire',
  uvIndex: 'Índice UV',
  moderate: 'Moderado',
  hazardous: 'Peligroso',
  optimal: 'Óptimo',
};