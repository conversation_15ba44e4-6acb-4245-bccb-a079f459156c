// Dutch translations
export const nl = {
  // Navigation
  home: 'Home',
  animals: '<PERSON><PERSON>',
  devices: 'Apparaten',
  profile: '<PERSON><PERSON>',
  settings: 'Instellingen',
  
  // Tab Navigation
  tabHome: 'Home',
  tabAnimals: '<PERSON><PERSON>',
  tabDevices: 'Apparaten',
  tabHealth: 'Gez<PERSON>heid',
  tabProfile: 'Profiel',
  
  // Common actions
  add: 'Toevoegen',
  edit: 'Bewerken',
  delete: 'Verwijderen',
  save: '<PERSON><PERSON><PERSON>',
  cancel: 'Annule<PERSON>',
  confirm: 'Bevestigen',
  back: 'Terug',
  next: 'Volgende',
  previous: 'Vorige',
  loading: 'Laden...',
  error: 'Fout',
  success: 'Succes',
  warning: 'Waarschuwing',
  info: 'Informatie',
  
  // Authentication
  login: 'Inloggen',
  logout: 'Uitloggen',
  register: 'Registreren',
  email: 'E-mail',
  password: 'Wachtwoord',
  confirmPassword: 'Bevestig wachtwoord',
  forgotPassword: 'Wachtwoord vergeten?',
  resetPassword: 'Wachtwoord resetten',
  
  // Animal management
  animalName: 'Dierna<PERSON>',
  animalType: 'Diertype',
  breed: 'Ras',
  age: 'Leeftijd',
  gender: 'Geslacht',
  weight: 'Gewicht',
  color: 'Kleur',
  microchipId: 'Microchip ID',
  registrationNumber: 'Registratienummer',
  
  // Animal detail sections
  animalDetailLatestVitals: 'Laatste vitale functies',
  animalDetailDeviceOnly: 'Alleen apparaat',
  animalDetailRecordedOn: 'Opgenomen op',
  animalDetailTemperature: 'Temperatuur',
  animalDetailHeartRate: 'Hartslag',
  animalDetailRespiration: 'Ademhaling',
  animalDetailWeight: 'Gewicht',
  animalDetailNotes: 'Notities',
  animalDetailNoVitalsRecorded: 'Geen vitale functies opgenomen',
  animalDetailConnectDevice: 'Apparaat verbinden',
  
  animalDetailMedications: 'Medicijnen',
  animalDetailAdd: 'Toevoegen',
  animalDetailPrintMedicationSchedule: 'Medicijnschema afdrukken',
  animalDetailNoMedicationsAdded: 'Geen medicijnen toegevoegd',
  animalDetailAddMedication: 'Medicijn toevoegen',
  
  animalDetailVaccinations: 'Vaccinaties',
  animalDetailDue: 'Vervaldatum',
  animalDetailNoRenewal: 'Geen vernieuwing',
  animalDetailPrintVaccinationRecord: 'Vaccinatierecord afdrukken',
  animalDetailNoVaccinationsRecorded: 'Geen vaccinaties opgenomen',
  animalDetailAddVaccination: 'Vaccinatie toevoegen',
  animalDetailMicrochipRequired: 'Microchip vereist',
  
  animalDetailFeedingSchedule: 'Voedingsschema',
  animalDetailViewAll: 'Alles bekijken',
  animalDetailPrintFeedingSchedule: 'Voedingsschema afdrukken',
  animalDetailNoFeedingSchedule: 'Geen voedingsschema',
  animalDetailSetFeedingSchedule: 'Voedingsschema instellen',
  
  animalDetailTrainingSessions: 'Trainingsessies',
  animalDetailLoadingSessions: 'Sessies laden...',
  animalDetailLogNewSession: 'Nieuwe sessie vastleggen',
  animalDetailNoTrainingSessions: 'Geen trainingsessies',
  animalDetailTrainingDescription: 'Volg de trainingsvoortgang van je dier',
  animalDetailLogFirstSession: 'Eerste sessie vastleggen',
  
  // Behavioral analysis
  behavioralAnalysis: 'Gedragsanalyse',
  stressAnalysis: 'Stressanalyse',
  sleepMonitoring: 'Slaapmonitoring',
  environmentalAnalysis: 'Omgevingsanalyse',
  environmentalAnalysisDescription: 'Analyseer de impact van de omgeving op de gezondheid',
  environmentalImpact: 'Omgevingsimpact',
  predictiveInsights: 'Voorspellende inzichten',
  
  // Health and vitals
  health: 'Gezondheid',
  vitals: 'Vitale functies',
  temperature: 'Temperatuur',
  heartRate: 'Hartslag',
  respirationRate: 'Ademhalingsfrequentie',
  bloodPressure: 'Bloeddruk',
  
  // Time and dates
  today: 'Vandaag',
  yesterday: 'Gisteren',
  thisWeek: 'Deze week',
  thisMonth: 'Deze maand',
  thisYear: 'Dit jaar',
  
  // Status
  active: 'Actief',
  inactive: 'Inactief',
  online: 'Online',
  offline: 'Offline',
  connected: 'Verbonden',
  disconnected: 'Niet verbonden',
  
  // Notifications
  notifications: 'Meldingen',
  noNotifications: 'Geen meldingen',
  markAsRead: 'Markeren als gelezen',
  markAllAsRead: 'Alles markeren als gelezen',
  
  // Search and filters
  search: 'Zoeken',
  filter: 'Filteren',
  sort: 'Sorteren',
  sortBy: 'Sorteren op',
  filterBy: 'Filteren op',
  
  // Language settings
  language: 'Taal',
  selectLanguage: 'Taal selecteren',
  
  // Device management
  deviceName: 'Apparaatnaam',
  deviceType: 'Apparaattype',
  batteryLevel: 'Batterijniveau',
  lastSync: 'Laatste synchronisatie',
  deviceStatus: 'Apparaatstatus',
  
  // Reports and analytics
  reports: 'Rapporten',
  analytics: 'Analytics',
  dashboard: 'Dashboard',
  export: 'Exporteren',
  print: 'Afdrukken',
  
  // Emergency and alerts
  emergency: 'Noodgeval',
  alert: 'Waarschuwing',
  critical: 'Kritiek',
  urgent: 'Urgent',
  normal: 'Normaal',
  
  // Units
  celsius: '°C',
  fahrenheit: '°F',
  kg: 'kg',
  lbs: 'lbs',
  cm: 'cm',
  inches: 'inch',
  
  // Days of week
  monday: 'Maandag',
  tuesday: 'Dinsdag',
  wednesday: 'Woensdag',
  thursday: 'Donderdag',
  friday: 'Vrijdag',
  saturday: 'Zaterdag',
  sunday: 'Zondag',
  
  // Months
  january: 'Januari',
  february: 'Februari',
  march: 'Maart',
  april: 'April',
  may: 'Mei',
  june: 'Juni',
  july: 'Juli',
  august: 'Augustus',
  september: 'September',
  october: 'Oktober',
  november: 'November',
  december: 'December',
  
  // Error Messages
  animalNotFound: 'Dier niet gevonden',
  missingAnimalId: 'Dier-ID vereist om dit scherm te bekijken',
  
  // Action Labels
  goBack: 'Ga terug',
};
