// Modular Italian translations
import { devices } from './devices';

export const it = {
  ...devices,
  // Common actions
  add: 'Aggiungi',
  edit: 'Modi<PERSON>',
  delete: '<PERSON><PERSON>',
  save: '<PERSON><PERSON>',
  cancel: '<PERSON><PERSON><PERSON>',
  confirm: '<PERSON><PERSON><PERSON>',
  back: '<PERSON><PERSON>',
  next: '<PERSON><PERSON>',
  previous: 'Precedente',
  loading: 'Caricamento...',
  error: 'Errore',
  success: '<PERSON><PERSON>',
  warning: 'Avviso',
  info: 'Informazioni',
  retry: '<PERSON><PERSON><PERSON><PERSON>',
  refresh: 'Aggiorna',
  
  // Navigation
  home: 'Home',
  animals: '<PERSON><PERSON>',
  devices: 'Dispositivi',
  profile: 'Profilo',
  settings: 'Impostazioni',
  advancedSettings: 'Impostazioni avanzate',
  
  // Tab Navigation
  tabHome: 'Home',
  tabAnimals: 'Animali',
  tabDevices: 'Dispositivi',
  tabHealth: 'Salute',
  tabProfile: 'Profilo',
  
  // Authentication
  createAccount: '<PERSON>rea account',
  resetPassword: 'Reimposta password',
  signInToAccount: 'Accedi al tuo account',
  login: 'Accedi',
  logout: 'Esci',
  register: '<PERSON><PERSON><PERSON>',
  email: 'Email',
  password: 'Password',
  confirmPassword: 'Conferma password',
  forgotPassword: 'Password dimenticata?',
  
  // Biometric Authentication
  biometricLogin: 'Accesso biometrico',
  signInWithFingerprint: 'Accedi con impronta digitale',
  signInWithFaceId: 'Accedi con Face ID',
  signInWithBiometric: 'Accedi con biometria',
  biometricAuthenticationFailed: 'Autenticazione biometrica fallita',
  biometricNotAvailable: 'L\'autenticazione biometrica non è disponibile su questo dispositivo',
  biometricNotEnabled: 'L\'accesso biometrico non è abilitato',
  biometricSetupRequired: 'Configurazione biometrica richiesta',
  biometricSetupDescription: 'Per utilizzare l\'accesso biometrico, configura prima {{biometricType}} nelle impostazioni del dispositivo.',
  enableBiometricLogin: 'Abilita l\'accesso biometrico per un accesso rapido e sicuro al tuo account.',
  biometricLoginEnabled: 'Accesso biometrico abilitato con successo!',
  biometricLoginDisabled: 'Accesso biometrico disabilitato',
  disableBiometricLogin: 'Disabilita accesso biometrico',
  disableBiometricConfirm: 'Sei sicuro di voler disabilitare l\'accesso biometrico? Dovrai usare la password per accedere.',
  biometricDataSecure: 'I tuoi dati biometrici rimangono sicuri sul tuo dispositivo e non vengono mai condivisi.',
  enableBiometricPrompt: 'Abilita accesso biometrico',
  biometricSetupConfirm: 'Usa la tua biometria per confermare la configurazione',
  biometricSignInPrompt: 'Accedi a HoofBeat',
  biometricSignInSubtitle: 'Usa la tua biometria per accedere al tuo account',
  biometricSetupCancelled: 'Configurazione biometrica annullata',
  biometricRefreshSession: 'Accedi con la password per aggiornare la sessione, poi riabilita l\'accesso biometrico.',
  biometricCredentialsFirst: 'Verifica le tue credenziali prima di abilitare l\'accesso biometrico',
  biometricSignInPasswordFirst: 'Accedi prima con la password per abilitare l\'accesso biometrico',
  or: 'o',
  
  // Error Messages
  userNotFound: 'Utente non trovato',
  profileCreationFailed: 'Creazione profilo utente fallita',
  profileLoadFailed: 'Caricamento profilo utente fallito',
  sessionExpired: 'Sessione scaduta. Accedi di nuovo.',
  networkError: 'Errore di rete. Controlla la connessione.',
  unexpectedError: 'Si è verificato un errore imprevisto',
  
  // Biometric types
  biometric: 'Biometrico',
  fingerprint: 'Impronta digitale',
  faceId: 'Face ID',
  biometricAuthentication: 'Autenticazione biometrica',
  
  // MFA
  mfaSettings: 'Autenticazione a due fattori',
  mfaEnabled: 'Autenticazione a due fattori abilitata',
  mfaDisabled: 'Autenticazione a due fattori disabilitata',
  
  // Animal management
  animalName: 'Nome animale',
  animalType: 'Tipo di animale',
  breed: 'Razza',
  age: 'Età',
  gender: 'Sesso',
  weight: 'Peso',
  color: 'Colore',
  microchipId: 'ID microchip',
  registrationNumber: 'Numero di registrazione',
  myAnimals: 'I miei animali',
  seeAll: 'Vedi tutto',
  addAnimal: 'Aggiungi animale',
  noAnimalsYet: 'Nessun animale aggiunto ancora',
  addFirstAnimal: 'Aggiungi il tuo primo animale',
  id: 'ID',
  ageYears: '{{age}} anni',
  gps: 'GPS',
  animalNotFound: 'Animale non trovato',
  
  // AI Features
  aiAssistant: 'Assistente IA',
  aiChat: 'Chat IA',
  aiChatTitle: 'Chatta con l\'assistente IA',
  aiChatPlaceholder: 'Fai domande sulla salute, addestramento o cura del tuo animale...',
  aiChatSend: 'Invia',
  aiChatAttachment: 'Allega immagine',
  aiChatCamera: 'Scatta foto',
  aiChatGallery: 'Scegli dalla galleria',
  aiChatAnalyzing: 'Analizzando...',
  aiChatTyping: 'L\'IA sta scrivendo...',
  aiChatNoMessages: 'Nessun messaggio ancora',
  aiChatStartConversation: 'Inizia una conversazione con il tuo assistente IA su {{animalName}}',
  aiChatImageAnalysis: 'Analisi immagine',
  aiChatCopyMessage: 'Copia messaggio',
  aiChatShareMessage: 'Condividi messaggio',
  aiChatMessageCopied: 'Messaggio copiato negli appunti',
  aiChatUploadingImage: 'Caricamento immagine...',
  aiChatImageUploadFailed: 'Caricamento immagine fallito',
  aiChatSendingMessage: 'Invio messaggio...',
  aiChatMessageFailed: 'Invio messaggio fallito',
  aiChatRetry: 'Riprova',
  aiChatMaxFileSize: 'La dimensione del file deve essere inferiore a 10MB',
  aiChatUnsupportedFormat: 'Formato file non supportato',
  
  // AI Onboarding
  meetYourAIAssistant: 'Incontra il tuo assistente IA',
  aiAssistantDescription: 'Ottieni piani di addestramento personalizzati, insights sulla salute e consigli di addestramento alimentati dall\'IA.',
  readinessScores: 'Punteggi di prontezza',
  trainingPlans: 'Piani di addestramento',
  logFirstTrainingSession: 'Registra la tua prima sessione di addestramento',
  
  // Additional translations for backward compatibility
  language: 'Lingua',
  languageSettings: 'Lingua e regione',
  selectYourLanguage: 'Seleziona la tua lingua',
  languageChanged: 'Lingua cambiata con successo',
  selectLanguage: 'Seleziona lingua',
  changingLanguage: 'Cambiando lingua...',
  
  // Profile navigation
  profileSettings: 'Impostazioni',
  profileAdvancedSettings: 'Impostazioni avanzate',
  profilePrivacySettings: 'Impostazioni privacy',
  profileMfaSettings: 'Autenticazione a due fattori',
  profileUpgradeToPremium: 'Passa a Premium',
  profileHelpSupport: 'Aiuto e supporto',
  profileFAQ: 'FAQ',
  profileContactSupport: 'Contatta il supporto',
  profileSignOut: 'Esci',
  profileSignOutConfirm: 'Sei sicuro di voler uscire?',
  profileCancel: 'Annulla',
  profileVersion: 'Versione',
  profileSignOutFailed: 'Uscita fallita',
  
  // Screen titles
  aiHealthDashboard: 'Dashboard salute IA',
  healthScoreDetail: 'Dettaglio punteggio salute',
  diseaseRisk: 'Rischio malattie',
  healthTrends: 'Tendenze salute',
  stressAnalysis: 'Analisi stress',
  sleepMonitoring: 'Monitoraggio sonno',
  environmentalAnalysis: 'Analisi ambientale',
  predictiveInsights: 'Insights predittivi',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'Il tuo compagno di allevamento',
  
  // Premium
  premium: 'Premium',
  upgradeToPremium: 'Passa a Premium',
  unlimitedAnimals: 'Animali illimitati',
  advancedAnalytics: 'Analisi avanzate',
  upgradeNow: 'Aggiorna ora',
  
  // Health and vitals
  health: 'Salute',
  vitals: 'Parametri vitali',
  temperature: 'Temperatura',
  heartRate: 'Frequenza cardiaca',
  respirationRate: 'Frequenza respiratoria',
  bloodPressure: 'Pressione sanguigna',
  
  // Additional keys
  behavioralAnalysis: 'Analisi comportamentale',
  environmentalAnalysisDescription: 'Analizza l\'impatto dell\'ambiente sulla salute',
  environmentalImpact: 'Impatto ambientale',
  viewAll: 'Vedi tutto',
  
  // Quick Actions
  quickActions: 'Azioni Rapide',
  healthTrends: 'Tendenze Salute',
  diseaseRisk: 'Rischio Malattie',
  stressAnalysis: 'Analisi Stress',
  sleepMonitoring: 'Monitoraggio Sonno',
  environmental: 'Ambientale',
  predictiveInsights: 'Insights Predittivi',
  assistant: 'Assistente IA',
  
  // Additional missing AI keys
  environmentalAnalysis: 'Analisi Ambientale',
  behavioralAnalysis: 'Analisi Comportamentale',
  aiHealthDashboard: 'Dashboard Salute IA',
  smartAlerts: 'Avvisi Intelligenti',
  diseaseRiskAssessment: 'Valutazione Rischio Malattie',
  healthScoreDetails: 'Dettagli Punteggio Salute',
  trendsOverview: 'Panoramica Tendenze',
  riskOverview: 'Panoramica Rischi',
  
  // Health Dashboard
  healthDashboard: 'Dashboard Salute',
  overallHealthStatus: 'Stato Salute Generale',
  totalAnimals: 'Totale Animali',
  healthScore: 'Punteggio Salute',
  activeAlerts: 'Avvisi Attivi',
  recentAnalyses: 'Analisi Recenti',
  animalNotSelected: 'Nessun animale selezionato',
  selectAnimalForDetails: 'Seleziona un animale per vedere i dettagli della salute',
  viewHealthDetails: 'Visualizza Dettagli Salute',
  lastUpdated: 'Ultimo aggiornamento',
  noHealthData: 'Nessun dato sulla salute',
  tapToViewDetails: 'Tocca per visualizzare i dettagli',
  
  // Common translations
  refresh: 'Aggiorna',
  refreshing: 'Aggiornamento...',
  viewDetails: 'Visualizza dettagli',
  
  // Nested objects for compatibility with modular structure
  common: {
    refresh: 'Aggiorna',
    refreshing: 'Aggiornamento...',
  },
  
  ai: {
    quickActions: 'Azioni Rapide',
    healthTrends: 'Tendenze Salute',
    diseaseRisk: 'Rischio Malattie',
    stressAnalysis: 'Analisi Stress',
    sleepMonitoring: 'Monitoraggio Sonno',
    environmental: 'Ambientale',
    predictiveInsights: 'Insights Predittivi',
    assistant: 'Assistente IA',
    environmentalAnalysis: 'Analisi Ambientale',
    behavioralAnalysis: 'Analisi Comportamentale',
    aiHealthDashboard: 'Dashboard Salute IA',
    healthDashboard: 'Dashboard Salute',
    smartAlerts: 'Avvisi Intelligenti',
    diseaseRiskAssessment: 'Valutazione Rischio Malattie',
    healthScoreDetails: 'Dettagli Punteggio Salute',
    trendsOverview: 'Panoramica Tendenze',
    riskOverview: 'Panoramica Rischi',
    
    // AI Widget keys
    aiWidgetInsights: 'Insights IA',
    aiWidgetHealth: 'Stato Salute',
    aiWidgetLiveCheckIn: 'Check-in Live',
    aiWidgetViewAll: 'Visualizza Tutto',
    aiWidgetNew: 'Nuovo',
    aiWidgetLogTraining: 'Registra Allenamento',
    aiWidgetNoAIData: 'Nessun Dato IA',
    aiWidgetReadiness: 'Prontezza',
    aiWidgetLiveStatus: 'Stato Live',
    aiWidgetAvailable: 'Disponibile',
    aiWidgetLatestTip: 'Ultimo Consiglio',
    aiWidgetChecking: 'Controllo...',
  },
  
  // Root level AI widget keys for backward compatibility
  aiWidgetInsights: 'Insights IA',
  aiWidgetHealth: 'Stato Salute',
  aiWidgetLiveCheckIn: 'Check-in Live',
  aiWidgetViewAll: 'Visualizza Tutto',
  aiWidgetNew: 'Nuovo',
  aiWidgetLogTraining: 'Registra Allenamento',
  aiWidgetNoAIData: 'Nessun Dato IA',
  aiWidgetReadiness: 'Prontezza',
  aiWidgetLiveStatus: 'Stato Live',
  aiWidgetAvailable: 'Disponibile',
  aiWidgetLatestTip: 'Ultimo Consiglio',
  aiWidgetChecking: 'Controllo...',
  
  // Missing keys from en/common.ts - Italian translations
  // Health Dashboard
  healthDashboard: 'Dashboard Salute',
  overallHealthStatus: 'Stato Generale di Salute',
  criticalAlerts: 'Avvisi Critici',
  recentActivity: 'Attività Recente',
  healthInsights: 'Approfondimenti Salute',
  quickActions: 'Azioni Rapide',
  viewHealthScore: 'Visualizza Punteggio Salute',
  analyzeRisks: 'Analizza Rischi',
  viewTrends: 'Visualizza Tendenze',
  stressAnalysis: 'Analisi dello Stress',
  sleepMonitoring: 'Monitoraggio del Sonno',
  environmentalAnalysis: 'Analisi Ambientale',
  predictiveInsights: 'Approfondimenti Predittivi',
  noAnimalsSelected: 'Nessun animale selezionato',
  selectAnimalToViewHealth: 'Seleziona un animale per visualizzare la sua dashboard salute',
  healthDataLoading: 'Caricamento dati salute...',
  healthDataError: 'Errore nel caricamento dati salute',
  retryLoadingHealthData: 'Riprova a caricare i dati salute',
  
  // FAQ
  frequentlyAskedQuestions: 'Domande Frequenti',
  howToAddAnimal: 'Come aggiungere un nuovo animale?',
  howToAddAnimalAnswer: 'Per aggiungere un nuovo animale, vai alla scheda Animali e tocca il pulsante "+". Compila le informazioni del tuo animale inclusi nome, razza, età e altri dettagli.',
  howToConnectDevice: 'Come connettere un dispositivo di monitoraggio?',
  howToConnectDeviceAnswer: 'Vai alla scheda Dispositivi, tocca "Aggiungi Dispositivo", e segui le istruzioni di accoppiamento. Assicurati che il Bluetooth sia abilitato sul tuo telefono.',
  whatIsHealthScore: 'Cos\'è il Punteggio Salute?',
  whatIsHealthScoreAnswer: 'Il Punteggio Salute è una valutazione generata dall\'IA della salute generale del tuo animale basata su segni vitali, livelli di attività e altri dati monitorati.',
  howToSetupAlerts: 'Come configurare gli avvisi salute?',
  howToSetupAlertsAnswer: 'Vai su Impostazioni > Notifiche per configurare quando e come ricevi avvisi sullo stato di salute del tuo animale.',
  whatDataIsTracked: 'Quali dati traccia l\'app?',
  whatDataIsTrackedAnswer: 'L\'app traccia segni vitali (frequenza cardiaca, temperatura), livelli di attività, posizione, orari di alimentazione, farmaci e fattori ambientali.',
  howToExportData: 'Come posso esportare i dati del mio animale?',
  howToExportDataAnswer: 'Vai al profilo del tuo animale, tocca il pulsante menu, e seleziona "Esporta Dati". Puoi scegliere l\'intervallo di date e i tipi di dati da esportare.',
  isPremiumWorthIt: 'Vale la pena l\'abbonamento Premium?',
  isPremiumWorthItAnswer: 'Premium offre animali illimitati, approfondimenti IA avanzati, report salute dettagliati e supporto prioritario. È ideale per uso professionale o animali multipli.',
  howToContactSupport: 'Come contattare il supporto?',
  howToContactSupportAnswer: 'Puoi contattare il supporto tramite la scheda Contatto nel tuo profilo, o inviarci un\'email <NAME_EMAIL>.',
  
  // Contact
  contactUs: 'Contattaci',
  messageSent: 'Messaggio inviato con successo',
  messageSentTitle: 'Messaggio Inviato',
  messageSentDescription: 'Il tuo messaggio è stato <NAME_EMAIL>. Ti risponderemo presto!',
  failedToSendMessage: 'Invio messaggio fallito. Riprova.',
  errorSendingMessageTitle: 'Errore Invio Messaggio',
  errorSendingMessageDescription: 'C\'è stato un problema nell\'invio del tuo messaggio. Riprova o usa l\'opzione chat dal vivo.',
  chatError: 'Errore Chat',
  failedToOpenChat: 'Apertura chat fallita. Riprova.',
  openingChatSupport: 'Apertura supporto chat...',
  
  // Print
  print: 'Stampa',
  printed: 'Stampato',
  download: 'Scarica',
  downloaded: 'Scaricato',
  shared: 'Condiviso',
  animalNotFound: 'Animale non trovato',
  goBack: 'Torna Indietro',
  
  // Barcode Scanner
  cameraPermissionRequired: 'Permesso fotocamera richiesto per scansionare codici a barre',
  invalidBarcodeFormat: 'Formato codice a barre dispositivo non valido',
  deviceBarcodeScanned: 'Codice a barre dispositivo scansionato',
  requestingCameraPermission: 'Richiesta permesso fotocamera...',
  noAccessToCamera: 'Nessun accesso alla fotocamera',
  scanDeviceBarcode: 'Scansiona Codice a Barre Dispositivo',
  positionBarcodeInFrame: 'Posiziona il codice a barre all\'interno del riquadro per scansionare',
  scanAgain: 'Scansiona di Nuovo',
  
  // Privacy Settings
  privacySettings: 'Impostazioni Privacy',
  privacySettingsSaved: 'Impostazioni privacy salvate',
  accountDeleted: 'Account eliminato',
  locationTracking: 'Tracciamento Posizione',
  locationTrackingDescription: 'Consenti all\'app di tracciare le posizioni dei tuoi animali. Questo è richiesto per gli avvisi di posizione.',
  shareDataWithVets: 'Condividi Dati con Veterinari',
  shareDataWithVetsDescription: 'Consenti ai veterinari di accedere ai record sanitari dei tuoi animali quando li condividi.',
  analytics: 'Analitiche',
  analyticsDescription: 'Aiutaci a migliorare consentendo la raccolta di dati di utilizzo anonimi.',
  saveSettings: 'Salva Impostazioni',
  
  // Alert Types
  heartRateAlert: 'Avviso Frequenza Cardiaca',
  temperatureAlert: 'Avviso Temperatura',
  locationAlert: 'Avviso Posizione',
  healthAlert: 'Avviso Salute',
  
  // Health Status
  score: 'Punteggio',
  alerts: 'Avvisi',
  trends: 'Tendenze',
  tracked: 'Tracciato',
  criticalAlertsRequireAttention: 'avvisi critici richiedono attenzione',
  lastUpdated: 'Ultimo aggiornamento',
  excellent: 'Eccellente',
  good: 'Buono',
  fair: 'Discreto',
  poor: 'Scarso',
  critical: 'Critico',
  aiAnalysis: 'Analisi IA',
  active: 'Attivo',
  seeAll: 'Vedi Tutto',
  noAnimalsYet: 'Nessun animale ancora',
  addFirstAnimal: 'Aggiungi il tuo primo animale',
  
  // Missing AI & Health Analysis Keys (Italian translations)
  diseaseRiskAssessment: 'Valutazione del Rischio di Malattia',
  healthTrends: 'Tendenze della Salute',
  stressAnalysis: 'Analisi dello Stress',
  sleepMonitoring: 'Monitoraggio del Sonno',
  environmentalAnalysis: 'Analisi Ambientale',
  predictiveInsights: 'Insights Predittivi',
  aiHealthDashboard: 'Dashboard Salute IA',
  healthDashboard: 'Dashboard Salute',
  smartAlerts: 'Avvisi Intelligenti',
  healthScoreDetails: 'Dettagli Punteggio Salute',
  trendsOverview: 'Panoramica Tendenze',
  riskOverview: 'Panoramica Rischi',
  analyzing: 'Analizzando...',
  analyzingEnvironment: 'Analizzando ambiente...',
  analyzeEnvironment: 'Analizza Ambiente',
  generatingPredictions: 'Generando previsioni...',
  generatePredictions: 'Genera Previsioni',
  predictionHorizon: 'Orizzonte di Previsione',
  oneWeek: '1 Settimana',
  oneMonth: '1 Mese',
  threeMonths: '3 Mesi',
  healthPredictions: 'Previsioni Salute',
  predictedHealthScore: 'Punteggio Salute Previsto',
  predictedWeight: 'Peso Previsto',
  predictedActivity: 'Attività Prevista',
  predictedSleep: 'Sonno Previsto',
  predictionConfidence: 'Confidenza Previsione',
  riskAssessment: 'Valutazione del Rischio',
  diseaseRisk: 'Rischio Malattia',
  injuryRisk: 'Rischio Infortunio',
  behavioralRisk: 'Rischio Comportamentale',
  environmentalStressRisk: 'Rischio Stress Ambientale',
  predictiveAlerts: 'Avvisi Predittivi',
  acknowledgeAlert: 'Riconosci',
  resolveAlert: 'Risolvi',
  humidity: 'Umidità',
  airQuality: 'Qualità dell\'Aria',
  uvIndex: 'Indice UV',
  moderate: 'Moderato',
  hazardous: 'Pericoloso',
  optimal: 'Ottimale',
};