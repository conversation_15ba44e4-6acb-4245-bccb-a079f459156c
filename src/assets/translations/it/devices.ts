export const devices = {
  // Stato del dispositivo
  paired: 'Accoppia<PERSON>',
  available: 'Disponibile',
  connected: '<PERSON><PERSON><PERSON>',
  disconnected: 'Disconnesso',
  connecting: '<PERSON>nessione...',
  
  // Scoperta dispositivi
  findDevices: 'Trova dispositivi',
  scanForDevices: 'Scansiona dispositivi',
  scanning: 'Scansione...',
  loadingDevices: 'Caricamento dispositivi...',
  checkingBluetooth: 'Controllo Bluetooth...',
  noDevicesFound: 'Nessun dispositivo trovato',
  
  // Accoppiamento dispositivi
  pairDevice: 'Accoppia dispositivo',
  pairDevicePrompt: 'Seleziona un dispositivo da accoppiare',
  pairing: 'Accoppiamento...',
  pairingSuccess: 'Dispositivo accoppiato con successo',
  pairingFailed: 'Errore nell\'accoppiamento del dispositivo',
  noPairedDevices: 'Nessun dispositivo accoppiato trovato',
  
  // Gestione dispositivi
  unpairDevice: 'Disaccoppia dispositivo',
  deviceInfo: 'Informazioni dispositivo',
  batteryLevel: 'Livello batteria',
  signalStrength: 'Intensità segnale',
  lastSeen: 'Visto l\'ultima volta',
  
  // Bluetooth
  bluetoothDisabled: 'Bluetooth disabilitato',
  enableBluetooth: 'Abilita Bluetooth',
  bluetoothUnavailable: 'Bluetooth non disponibile',
  bluetoothPermission: 'Permesso Bluetooth richiesto',
  
  // Tipi di dispositivi
  heartRateMonitor: 'Monitor frequenza cardiaca',
  activityTracker: 'Tracker attività',
  smartCollar: 'Collare intelligente',
  temperatureSensor: 'Sensore temperatura',
  gpsTracker: 'Tracker GPS',
  
  // Azioni
  refresh: 'Aggiorna',
  retry: 'Riprova',
  cancel: 'Annulla',
  connect: 'Connetti',
  disconnect: 'Disconnetti',
  
  // Chiavi aggiuntive utilizzate in DevicesScreen
  initializing: 'Inizializzazione...',
  bluetoothErrorPrefix: 'Errore Bluetooth:',
  retryBluetoothCheck: 'Riprova controllo Bluetooth',
  bluetoothRequiredWarning: 'Bluetooth è richiesto per la connettività dei dispositivi',
  availableToAdd: 'Disponibile da aggiungere',
  addDevice: 'Aggiungi Dispositivo',
  scanToFindDevicesPrompt: 'Tocca scansiona per trovare dispositivi nelle vicinanze',
  deviceIdPlaceholder: 'Inserisci ID dispositivo o scansiona codice a barre',
  add: 'Aggiungi'
};