// Spanish translations
export const es = {
  // Navigation
  home: 'Inicio',
  animals: 'Animales',
  devices: 'Dispositivos',
  profile: 'Perfil',
  settings: 'Configuración',
  
  // Tab Navigation
  tabHome: 'Inicio',
  tabAnimals: 'Animales',
  tabDevices: 'Dispositivos',
  tabHealth: 'Salud',
  tabProfile: 'Perfil',
  
  // Common actions
  add: 'Añadir',
  edit: 'Editar',
  delete: 'Eliminar',
  save: 'Guardar',
  cancel: 'Cancelar',
  confirm: 'Confirmar',
  back: 'Atrás',
  next: 'Siguiente',
  previous: 'Anterior',
  loading: 'Cargando...',
  error: 'Error',
  success: 'Éxito',
  warning: 'Advertencia',
  info: 'Información',
  
  // Authentication
  login: 'Iniciar sesión',
  logout: 'Cerrar sesión',
  register: 'Registrarse',
  email: 'Correo electrónico',
  password: 'Contraseña',
  confirmPassword: 'Confirmar contraseña',
  forgotPassword: '¿Olvidaste tu contraseña?',
  resetPassword: 'Restablecer contraseña',
  
  // Animal management
  animalName: 'Nombre del animal',
  animalType: 'Tipo de animal',
  breed: 'Raza',
  age: 'Edad',
  gender: 'Género',
  weight: 'Peso',
  color: 'Color',
  microchipId: 'ID del microchip',
  registrationNumber: 'Número de registro',
  
  // Animal detail sections
  animalDetailLatestVitals: 'Últimos signos vitales',
  animalDetailDeviceOnly: 'Solo dispositivo',
  animalDetailRecordedOn: 'Registrado el',
  animalDetailTemperature: 'Temperatura',
  animalDetailHeartRate: 'Frecuencia cardíaca',
  animalDetailRespiration: 'Respiración',
  animalDetailWeight: 'Peso',
  animalDetailNotes: 'Notas',
  animalDetailNoVitalsRecorded: 'No hay signos vitales registrados',
  animalDetailConnectDevice: 'Conectar dispositivo',
  
  animalDetailMedications: 'Medicamentos',
  animalDetailAdd: 'Añadir',
  animalDetailPrintMedicationSchedule: 'Imprimir horario de medicamentos',
  animalDetailNoMedicationsAdded: 'No se han añadido medicamentos',
  animalDetailAddMedication: 'Añadir medicamento',
  
  animalDetailVaccinations: 'Vacunas',
  animalDetailDue: 'Vencimiento',
  animalDetailNoRenewal: 'Sin renovación',
  animalDetailPrintVaccinationRecord: 'Imprimir registro de vacunación',
  animalDetailNoVaccinationsRecorded: 'No hay vacunas registradas',
  animalDetailAddVaccination: 'Añadir vacuna',
  animalDetailMicrochipRequired: 'Microchip requerido',
  
  animalDetailFeedingSchedule: 'Horario de alimentación',
  animalDetailViewAll: 'Ver todo',
  animalDetailPrintFeedingSchedule: 'Imprimir horario de alimentación',
  animalDetailNoFeedingSchedule: 'Sin horario de alimentación',
  animalDetailSetFeedingSchedule: 'Establecer horario de alimentación',
  
  animalDetailTrainingSessions: 'Sesiones de entrenamiento',
  animalDetailLoadingSessions: 'Cargando sesiones...',
  animalDetailLogNewSession: 'Registrar nueva sesión',
  animalDetailNoTrainingSessions: 'No hay sesiones de entrenamiento',
  animalDetailTrainingDescription: 'Sigue el progreso de entrenamiento de tu animal',
  animalDetailLogFirstSession: 'Registrar primera sesión',
  
  // Behavioral analysis
  behavioralAnalysis: 'Análisis de comportamiento',
  stressAnalysis: 'Análisis de estrés',
  sleepMonitoring: 'Monitoreo del sueño',
  environmentalAnalysis: 'Análisis ambiental',
  environmentalAnalysisDescription: 'Analiza el impacto del ambiente en la salud',
  environmentalImpact: 'Impacto ambiental',
  predictiveInsights: 'Insights predictivos',
  
  // Health and vitals
  health: 'Salud',
  vitals: 'Signos vitales',
  temperature: 'Temperatura',
  heartRate: 'Frecuencia cardíaca',
  respirationRate: 'Frecuencia respiratoria',
  bloodPressure: 'Presión arterial',
  
  // Time and dates
  today: 'Hoy',
  yesterday: 'Ayer',
  thisWeek: 'Esta semana',
  thisMonth: 'Este mes',
  thisYear: 'Este año',
  
  // Status
  active: 'Activo',
  inactive: 'Inactivo',
  online: 'En línea',
  offline: 'Fuera de línea',
  connected: 'Conectado',
  disconnected: 'Desconectado',
  
  // Notifications
  notifications: 'Notificaciones',
  noNotifications: 'No hay notificaciones',
  markAsRead: 'Marcar como leído',
  markAllAsRead: 'Marcar todo como leído',
  
  // Search and filters
  search: 'Buscar',
  filter: 'Filtrar',
  sort: 'Ordenar',
  sortBy: 'Ordenar por',
  filterBy: 'Filtrar por',
  
  // Language settings
  language: 'Idioma',
  selectLanguage: 'Seleccionar idioma',
  
  // Device management
  deviceName: 'Nombre del dispositivo',
  deviceType: 'Tipo de dispositivo',
  batteryLevel: 'Nivel de batería',
  lastSync: 'Última sincronización',
  deviceStatus: 'Estado del dispositivo',
  
  // Reports and analytics
  reports: 'Informes',
  analytics: 'Análisis',
  dashboard: 'Panel de control',
  export: 'Exportar',
  print: 'Imprimir',
  
  // Emergency and alerts
  emergency: 'Emergencia',
  alert: 'Alerta',
  critical: 'Crítico',
  urgent: 'Urgente',
  normal: 'Normal',
  
  // Units
  celsius: '°C',
  fahrenheit: '°F',
  kg: 'kg',
  lbs: 'lbs',
  cm: 'cm',
  inches: 'pulgadas',
  
  // Days of week
  monday: 'Lunes',
  tuesday: 'Martes',
  wednesday: 'Miércoles',
  thursday: 'Jueves',
  friday: 'Viernes',
  saturday: 'Sábado',
  sunday: 'Domingo',
  
  // Months
  january: 'Enero',
  february: 'Febrero',
  march: 'Marzo',
  april: 'Abril',
  may: 'Mayo',
  june: 'Junio',
  july: 'Julio',
  august: 'Agosto',
  september: 'Septiembre',
  october: 'Octubre',
  november: 'Noviembre',
  december: 'Diciembre',
  
  // Error Messages
  animalNotFound: 'Animal no encontrado',
  missingAnimalId: 'ID del animal requerido para ver esta pantalla',
  
  // Action Labels
  goBack: 'Volver',
};
