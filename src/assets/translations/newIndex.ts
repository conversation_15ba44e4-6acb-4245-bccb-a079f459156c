/**
 * @magic_description Refactored translation system for the HoofBeat app
 * Supports multiple languages with RTL support for Arabic
 * Contains all text strings used throughout the application
 * 
 * This file has been refactored to import translations from separate language files
 * for better maintainability and organization.
 */

import { en } from './en/index';
import { ar } from './ar/index';
import { fr } from './fr/index';
import { ja } from './ja/index';
import { it } from './it/index';
import { tr } from './tr/index';
import { nl } from './nl/index';
import { es } from './es/index';

// Type definitions
export type LanguageCode = 'en' | 'ar' | 'fr' | 'ja' | 'it' | 'tr' | 'nl' | 'es';

export interface LanguageInfo {
  name: string;
  nativeName: string;
  flag: string;
  isRTL: boolean;
}

export const languageInfo: Record<LanguageCode, LanguageInfo> = {
  en: {
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    isRTL: false,
  },
  ar: {
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    isRTL: true,
  },
  fr: {
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    isRTL: false,
  },
  es: {
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    isRTL: false,
  },
  ja: {
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    isRTL: false,
  },
  it: {
    name: 'Italian',
    nativeName: 'Italiano',
    flag: '🇮🇹',
    isRTL: false,
  },
  tr: {
    name: 'Turkish',
    nativeName: 'Türkçe',
    flag: '🇹🇷',
    isRTL: false,
  },
  nl: {
    name: 'Dutch',
    nativeName: 'Nederlands',
    flag: '🇳🇱',
    isRTL: false,
  },
};
export type TranslationKey = keyof typeof en;

// Main translations object
export const translations = {
  en,
  ar,
  fr,
  ja,
  it,
  tr,
  nl,
  es,
};





// Helper function to get available languages
export const getAvailableLanguages = (): Array<{
  code: LanguageCode;
  name: string;
  nativeName: string;
  isRTL: boolean;
}> => {
  return Object.entries(languageInfo).map(([code, info]) => ({
    code: code as LanguageCode,
    ...info,
  }));
};

// Helper function to check if language is RTL
export const isRTLLanguage = (languageCode: LanguageCode): boolean => {
  return languageInfo[languageCode]?.isRTL || false;
};