// Japanese translations
export const ja = {
  // Navigation
  home: 'ホーム',
  animals: '動物',
  devices: 'デバイス',
  profile: 'プロフィール',
  settings: '設定',
  
  // Tab Navigation
  tabHome: 'ホーム',
  tabAnimals: '動物',
  tabDevices: 'デバイス',
  tabHealth: '健康',
  tabProfile: 'プロフィール',
  
  // Common actions
  add: '追加',
  edit: '編集',
  delete: '削除',
  save: '保存',
  cancel: 'キャンセル',
  confirm: '確認',
  back: '戻る',
  next: '次へ',
  previous: '前へ',
  loading: '読み込み中...',
  error: 'エラー',
  success: '成功',
  warning: '警告',
  info: '情報',
  
  // Authentication
  login: 'ログイン',
  logout: 'ログアウト',
  register: '登録',
  email: 'メール',
  password: 'パスワード',
  confirmPassword: 'パスワード確認',
  forgotPassword: 'パスワードを忘れましたか？',
  resetPassword: 'パスワードリセット',
  
  // Animal management
  animalName: '動物名',
  animalType: '動物の種類',
  breed: '品種',
  age: '年齢',
  gender: '性別',
  weight: '体重',
  color: '色',
  microchipId: 'マイクロチップID',
  registrationNumber: '登録番号',
  
  // Animal detail sections
  animalDetailLatestVitals: '最新のバイタル',
  animalDetailDeviceOnly: 'デバイスのみ',
  animalDetailRecordedOn: '記録日',
  animalDetailTemperature: '体温',
  animalDetailHeartRate: '心拍数',
  animalDetailRespiration: '呼吸',
  animalDetailWeight: '体重',
  animalDetailNotes: 'メモ',
  animalDetailNoVitalsRecorded: 'バイタルが記録されていません',
  animalDetailConnectDevice: 'デバイスを接続',
  
  animalDetailMedications: '薬物',
  animalDetailAdd: '追加',
  animalDetailPrintMedicationSchedule: '薬物スケジュールを印刷',
  animalDetailNoMedicationsAdded: '薬物が追加されていません',
  animalDetailAddMedication: '薬物を追加',
  
  animalDetailVaccinations: 'ワクチン接種',
  animalDetailDue: '期限',
  animalDetailNoRenewal: '更新なし',
  animalDetailPrintVaccinationRecord: 'ワクチン記録を印刷',
  animalDetailNoVaccinationsRecorded: 'ワクチン接種が記録されていません',
  animalDetailAddVaccination: 'ワクチン接種を追加',
  animalDetailMicrochipRequired: 'マイクロチップが必要',
  
  animalDetailFeedingSchedule: '給餌スケジュール',
  animalDetailViewAll: 'すべて表示',
  animalDetailPrintFeedingSchedule: '給餌スケジュールを印刷',
  animalDetailNoFeedingSchedule: '給餌スケジュールなし',
  animalDetailSetFeedingSchedule: '給餌スケジュールを設定',
  
  animalDetailTrainingSessions: 'トレーニングセッション',
  animalDetailLoadingSessions: 'セッション読み込み中...',
  animalDetailLogNewSession: '新しいセッションを記録',
  animalDetailNoTrainingSessions: 'トレーニングセッションなし',
  animalDetailTrainingDescription: '動物のトレーニング進捗を追跡',
  animalDetailLogFirstSession: '最初のセッションを記録',
  
  // Behavioral analysis
  behavioralAnalysis: '行動分析',
  stressAnalysis: 'ストレス分析',
  sleepMonitoring: '睡眠監視',
  environmentalAnalysis: '環境分析',
  environmentalAnalysisDescription: '環境が健康に与える影響を分析',
  environmentalImpact: '環境への影響',
  predictiveInsights: '予測インサイト',
  
  // Health and vitals
  health: '健康',
  vitals: 'バイタル',
  temperature: '体温',
  heartRate: '心拍数',
  respirationRate: '呼吸数',
  bloodPressure: '血圧',
  
  // Time and dates
  today: '今日',
  yesterday: '昨日',
  thisWeek: '今週',
  thisMonth: '今月',
  thisYear: '今年',
  
  // Status
  active: 'アクティブ',
  inactive: '非アクティブ',
  online: 'オンライン',
  offline: 'オフライン',
  connected: '接続済み',
  disconnected: '切断済み',
  
  // Notifications
  notifications: '通知',
  noNotifications: '通知なし',
  markAsRead: '既読にする',
  markAllAsRead: 'すべて既読にする',
  
  // Search and filters
  search: '検索',
  filter: 'フィルター',
  sort: '並び替え',
  sortBy: '並び替え基準',
  filterBy: 'フィルター基準',
  
  // Language settings
  language: '言語',
  selectLanguage: '言語を選択',
  
  // Device management
  deviceName: 'デバイス名',
  deviceType: 'デバイスタイプ',
  batteryLevel: 'バッテリーレベル',
  lastSync: '最終同期',
  deviceStatus: 'デバイス状態',
  
  // Reports and analytics
  reports: 'レポート',
  analytics: '分析',
  dashboard: 'ダッシュボード',
  export: 'エクスポート',
  print: '印刷',
  
  // Emergency and alerts
  emergency: '緊急',
  alert: 'アラート',
  critical: '重要',
  urgent: '緊急',
  normal: '正常',
  
  // Units
  celsius: '°C',
  fahrenheit: '°F',
  kg: 'kg',
  lbs: 'lbs',
  cm: 'cm',
  inches: 'インチ',
  
  // Days of week
  monday: '月曜日',
  tuesday: '火曜日',
  wednesday: '水曜日',
  thursday: '木曜日',
  friday: '金曜日',
  saturday: '土曜日',
  sunday: '日曜日',
  
  // Months
  january: '1月',
  february: '2月',
  march: '3月',
  april: '4月',
  may: '5月',
  june: '6月',
  july: '7月',
  august: '8月',
  september: '9月',
  october: '10月',
  november: '11月',
  december: '12月',
  
  // Error Messages
  animalNotFound: '動物が見つかりません',
  missingAnimalId: 'この画面を表示するには動物IDが必要です',
  
  // Action Labels
  goBack: '戻る',
  
  // Missing Animal Card Keys
  id: 'ID',
  ageYears: '{{age}}歳',
  gps: 'GPS',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'あなたの家畜コンパニオン',
  animalSpeedTitle: '{{animalName}}の速度',
  speedUpdatedSuccess: '{{animalName}}の速度が更新されました',
  speedUpdateFailed: '速度の更新に失敗しました',
};
