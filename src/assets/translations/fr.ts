// French translations
export const fr = {
  // Navigation
  home: 'Accueil',
  animals: 'Animaux',
  devices: 'Appareils',
  profile: 'Profil',
  settings: 'Paramètres',
  
  // Tab Navigation
  tabHome: 'Accueil',
  tabAnimals: 'Animaux',
  tabDevices: 'Appareils',
  tabHealth: 'Santé',
  tabProfile: 'Profil',
  
  // Common actions
  add: 'Ajouter',
  edit: 'Modifier',
  delete: 'Supprimer',
  save: 'Enregistrer',
  cancel: 'Annuler',
  confirm: 'Confirmer',
  back: 'Retour',
  next: 'Suivant',
  previous: 'Précédent',
  loading: 'Chargement...',
  error: 'Erreur',
  success: 'Succès',
  warning: 'Avertissement',
  info: 'Information',
  
  // Authentication
  login: 'Connexion',
  logout: 'Déconnexion',
  register: 'S\'inscrire',
  email: 'E-mail',
  password: 'Mot de passe',
  confirmPassword: 'Confirmer le mot de passe',
  forgotPassword: 'Mot de passe oublié ?',
  resetPassword: 'Réinitialiser le mot de passe',
  
  // Animal management
  animalName: 'Nom de l\'animal',
  animalType: 'Type d\'animal',
  breed: 'Race',
  age: 'Âge',
  gender: 'Sexe',
  weight: 'Poids',
  color: 'Couleur',
  microchipId: 'ID de puce électronique',
  registrationNumber: 'Numéro d\'enregistrement',
  
  // Animal detail sections
  animalDetailLatestVitals: 'Derniers signes vitaux',
  animalDetailDeviceOnly: 'Appareil uniquement',
  animalDetailRecordedOn: 'Enregistré le',
  animalDetailTemperature: 'Température',
  animalDetailHeartRate: 'Fréquence cardiaque',
  animalDetailRespiration: 'Respiration',
  animalDetailWeight: 'Poids',
  animalDetailNotes: 'Notes',
  animalDetailNoVitalsRecorded: 'Aucun signe vital enregistré',
  animalDetailConnectDevice: 'Connecter un appareil',
  
  animalDetailMedications: 'Médicaments',
  animalDetailAdd: 'Ajouter',
  animalDetailPrintMedicationSchedule: 'Imprimer le planning des médicaments',
  animalDetailNoMedicationsAdded: 'Aucun médicament ajouté',
  animalDetailAddMedication: 'Ajouter un médicament',
  
  animalDetailVaccinations: 'Vaccinations',
  animalDetailDue: 'Échéance',
  animalDetailNoRenewal: 'Pas de renouvellement',
  animalDetailPrintVaccinationRecord: 'Imprimer le carnet de vaccination',
  animalDetailNoVaccinationsRecorded: 'Aucune vaccination enregistrée',
  animalDetailAddVaccination: 'Ajouter une vaccination',
  animalDetailMicrochipRequired: 'Puce électronique requise',
  
  animalDetailFeedingSchedule: 'Programme d\'alimentation',
  animalDetailViewAll: 'Voir tout',
  animalDetailPrintFeedingSchedule: 'Imprimer le programme d\'alimentation',
  animalDetailNoFeedingSchedule: 'Aucun programme d\'alimentation',
  animalDetailSetFeedingSchedule: 'Définir un programme d\'alimentation',
  
  animalDetailTrainingSessions: 'Séances d\'entraînement',
  animalDetailLoadingSessions: 'Chargement des séances...',
  animalDetailLogNewSession: 'Enregistrer une nouvelle séance',
  animalDetailNoTrainingSessions: 'Aucune séance d\'entraînement',
  animalDetailTrainingDescription: 'Suivez les progrès d\'entraînement de votre animal',
  animalDetailLogFirstSession: 'Enregistrer la première séance',
  
  // Behavioral analysis
  behavioralAnalysis: 'Analyse comportementale',
  stressAnalysis: 'Analyse du stress',
  sleepMonitoring: 'Surveillance du sommeil',
  environmentalAnalysis: 'Analyse environnementale',
  environmentalAnalysisDescription: 'Analysez l\'impact de l\'environnement sur la santé',
  environmentalImpact: 'Impact environnemental',
  predictiveInsights: 'Insights prédictifs',
  
  // Health and vitals
  health: 'Santé',
  vitals: 'Signes vitaux',
  temperature: 'Température',
  heartRate: 'Fréquence cardiaque',
  respirationRate: 'Fréquence respiratoire',
  bloodPressure: 'Tension artérielle',
  
  // Time and dates
  today: 'Aujourd\'hui',
  yesterday: 'Hier',
  thisWeek: 'Cette semaine',
  thisMonth: 'Ce mois',
  thisYear: 'Cette année',
  
  // Status
  active: 'Actif',
  inactive: 'Inactif',
  online: 'En ligne',
  offline: 'Hors ligne',
  connected: 'Connecté',
  disconnected: 'Déconnecté',
  
  // Notifications
  notifications: 'Notifications',
  noNotifications: 'Aucune notification',
  markAsRead: 'Marquer comme lu',
  markAllAsRead: 'Tout marquer comme lu',
  
  // Search and filters
  search: 'Rechercher',
  filter: 'Filtrer',
  sort: 'Trier',
  sortBy: 'Trier par',
  filterBy: 'Filtrer par',
  
  // Language settings
  language: 'Langue',
  selectLanguage: 'Sélectionner la langue',
  
  // Device management
  deviceName: 'Nom de l\'appareil',
  deviceType: 'Type d\'appareil',
  batteryLevel: 'Niveau de batterie',
  lastSync: 'Dernière synchronisation',
  deviceStatus: 'État de l\'appareil',
  
  // Reports and analytics
  reports: 'Rapports',
  analytics: 'Analyses',
  dashboard: 'Tableau de bord',
  export: 'Exporter',
  print: 'Imprimer',
  
  // Emergency and alerts
  emergency: 'Urgence',
  alert: 'Alerte',
  critical: 'Critique',
  urgent: 'Urgent',
  normal: 'Normal',
  
  // Units
  celsius: '°C',
  fahrenheit: '°F',
  kg: 'kg',
  lbs: 'lbs',
  cm: 'cm',
  inches: 'pouces',
  
  // Days of week
  monday: 'Lundi',
  tuesday: 'Mardi',
  wednesday: 'Mercredi',
  thursday: 'Jeudi',
  friday: 'Vendredi',
  saturday: 'Samedi',
  sunday: 'Dimanche',
  
  // Months
  january: 'Janvier',
  february: 'Février',
  march: 'Mars',
  april: 'Avril',
  may: 'Mai',
  june: 'Juin',
  july: 'Juillet',
  august: 'Août',
  september: 'Septembre',
  october: 'Octobre',
  november: 'Novembre',
  december: 'Décembre',
  
  // Missing Animal Card Keys
  id: 'ID',
  ageYears: '{{age}} ans',
  gps: 'GPS',
  
  // Home Screen
  appTitle: 'HoofBeat',
  appSubtitle: 'Votre compagnon de bétail',
  animalSpeedTitle: 'Vitesse de {{animalName}}',
  speedUpdatedSuccess: 'Vitesse de {{animalName}} mise à jour',
  speedUpdateFailed: 'Échec de la mise à jour de la vitesse',
};
