
import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, ActivityIndicator } from 'react-native';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import types
import { User, Animal, Stats } from './types';

// Import components
import AdminLoginForm from './components/AdminLoginForm';
import AdminAccessDenied from './components/AdminAccessDenied';
import AdminPanelHeader from './components/AdminPanelHeader';
import AdminTabs from './components/AdminTabs';
import DashboardView from './components/dashboard/DashboardView';
import UserListView from './components/users/UserListView';
import AnimalListView from './components/animals/AnimalListView';
import OrderListView from './components/orders/OrderListView';
import DeviceManagementView from './components/devices/DeviceManagementView';

// Initialize Supabase client
const supabaseUrl = 'https://hfqhqymuenbuzndkdcqf.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhmcWhxeW11ZW5idXpuZGtkY3FmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNDkxMTYsImV4cCI6MjA2MjgyNTExNn0.do6iCaniC596HejLA54i36fCdtH709fuEa9hZ5si09I';

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});



const AdminPanel = () => {
  const [session, setSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [loginError, setLoginError] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [stats, setStats] = useState<Stats>({
    totalUsers: 0,
    premiumUsers: 0,
    totalAnimals: 0,
    totalVitals: 0,
    totalMedications: 0,
    totalFeedings: 0,
  });
  const [activeTab, setActiveTab] = useState<'dashboard' | 'users' | 'animals' | 'devices' | 'orders'>('dashboard');
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // Check if user is logged in
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setLoading(false);
      if (session) {
        checkAdminStatus(session.user.id);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setLoading(false);
      if (session) {
        checkAdminStatus(session.user.id);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const checkAdminStatus = async (userId: string) => {
    const { data, error } = await supabase
      .from('users')
      .select('is_super_admin')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
      return;
    }

    setIsAdmin(data?.is_super_admin || false);
    if (data?.is_super_admin) {
      fetchData();
    }
  };

  const fetchData = async () => {
    setLoading(true);
    
    try {
      // Fetch users
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (usersError) throw usersError;
      setUsers(usersData || []);
      
      // Fetch animals
      const { data: animalsData, error: animalsError } = await supabase
        .from('animals')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (animalsError) throw animalsError;
      setAnimals(animalsData || []);
      
      // Calculate stats
      const premiumUsers = (usersData || []).filter(user => user.is_premium).length;
      
      // Count other entities
      const { count: vitalsCount, error: vitalsError } = await supabase
        .from('vitals')
        .select('*', { count: 'exact', head: true });
      
      if (vitalsError) throw vitalsError;
      
      const { count: medicationsCount, error: medicationsError } = await supabase
        .from('medications')
        .select('*', { count: 'exact', head: true });
      
      if (medicationsError) throw medicationsError;
      
      const { count: feedingsCount, error: feedingsError } = await supabase
        .from('feeding_schedules')
        .select('*', { count: 'exact', head: true });
      
      if (feedingsError) throw feedingsError;
      
      setStats({
        totalUsers: usersData?.length || 0,
        premiumUsers,
        totalAnimals: animalsData?.length || 0,
        totalVitals: vitalsCount || 0,
        totalMedications: medicationsCount || 0,
        totalFeedings: feedingsCount || 0,
      });
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async (email: string, password: string) => {
    setLoginError('');
    setLoading(true);
    
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) throw error;
    } catch (error: any) {
      setLoginError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await supabase.auth.signOut();
  };

  const toggleUserPremium = async (userId: string, isPremium: boolean) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ 
          is_premium: !isPremium,
          subscription_type: !isPremium ? 'monthly' : null,
          subscription_start_date: !isPremium ? new Date().toISOString() : null,
          subscription_end_date: !isPremium ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() : null,
        })
        .eq('id', userId);
      
      if (error) throw error;
      
      // Refresh users
      fetchData();
    } catch (error) {
      console.error('Error toggling premium status:', error);
    }
  };

  // Initial loading state
  if (loading && !session) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3D8C91" />
      </View>
    );
  }

  // No session - show login form
  if (!session) {
    return (
      <AdminLoginForm
        onLogin={handleLogin}
        loginError={loginError}
        isLoading={loading}
      />
    );
  }

  // Session but not admin - show access denied
  if (!isAdmin) {
    return (
      <AdminAccessDenied onLogout={handleLogout} />
    );
  }

  // Main admin panel
  return (
    <View style={styles.container}>
      <AdminPanelHeader
        title="HoofBeat Admin Panel"
        onLogout={handleLogout}
      />
      
      <AdminTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
      
      <ScrollView style={styles.content}>
        {activeTab === 'dashboard' && (
          <DashboardView
            stats={stats}
            onRefresh={fetchData}
            isLoading={loading}
          />
        )}
        
        {activeTab === 'users' && (
          <UserListView
            users={users}
            onTogglePremium={toggleUserPremium}
          />
        )}
        
        {activeTab === 'animals' && (
          <AnimalListView
            animals={animals}
          />
        )}
        
        {activeTab === 'devices' && (
          <DeviceManagementView
            onRefresh={fetchData}
            isLoading={loading}
          />
        )}
        
        {activeTab === 'orders' && (
          <OrderListView />
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
});

export default AdminPanel;
