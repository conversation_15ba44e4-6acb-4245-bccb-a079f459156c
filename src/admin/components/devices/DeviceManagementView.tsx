import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, RefreshControl, Alert } from 'react-native';
import { Wifi, WifiOff, Battery, BatteryLow, Bluetooth, MapPin, Settings, <PERSON>ert<PERSON><PERSON>gle, CheckCircle, Clock, Zap } from 'lucide-react-native';
import { supabase } from '../../../supabase/client';

interface Device {
  id: string;
  user_id: string;
  name: string;
  type: string;
  battery_level: number;
  status: 'connected' | 'disconnected' | 'paired' | 'low_battery' | 'error';
  last_sync_time: string | null;
  created_at: string;
  updated_at: string;
}

interface IoTDevice {
  id: string;
  device_name: string;
  device_type: string;
  manufacturer: string | null;
  model: string | null;
  firmware_version: string | null;
  device_identifier: string;
  connection_type: string;
  status: string;
  battery_level: number | null;
  signal_strength: number | null;
  last_seen: string | null;
  animal_id: string | null;
  user_id: string;
  enabled_sensors: any;
  device_settings: any;
}

interface DeviceStats {
  totalDevices: number;
  onlineDevices: number;
  lowBatteryDevices: number;
  errorDevices: number;
  avgBatteryLevel: number;
}

interface DeviceManagementViewProps {
  onRefresh?: () => void;
  isLoading?: boolean;
}

const DeviceManagementView: React.FC<DeviceManagementViewProps> = ({
  onRefresh,
  isLoading = false
}) => {
  const [devices, setDevices] = useState<Device[]>([]);
  const [iotDevices, setIoTDevices] = useState<IoTDevice[]>([]);
  const [stats, setStats] = useState<DeviceStats>({
    totalDevices: 0,
    onlineDevices: 0,
    lowBatteryDevices: 0,
    errorDevices: 0,
    avgBatteryLevel: 0
  });
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'online' | 'offline' | 'low_battery' | 'error'>('all');

  useEffect(() => {
    fetchDeviceData();
    
    // Set up real-time subscriptions
    const devicesSubscription = supabase
      .channel('admin-devices')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'devices' },
        () => fetchDeviceData()
      )
      .subscribe();

    const iotDevicesSubscription = supabase
      .channel('admin-iot-devices')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'iot_devices' },
        () => fetchDeviceData()
      )
      .subscribe();

    return () => {
      devicesSubscription.unsubscribe();
      iotDevicesSubscription.unsubscribe();
    };
  }, []);

  const fetchDeviceData = async () => {
    try {
      // Fetch regular devices
      const { data: devicesData, error: devicesError } = await supabase
        .from('devices')
        .select('*')
        .order('updated_at', { ascending: false });

      if (devicesError) throw devicesError;

      // Fetch IoT devices
      const { data: iotDevicesData, error: iotError } = await supabase
        .from('iot_devices')
        .select('*')
        .order('updated_at', { ascending: false });

      if (iotError) throw iotError;

      setDevices(devicesData || []);
      setIoTDevices(iotDevicesData || []);
      
      // Calculate stats
      calculateStats(devicesData || [], iotDevicesData || []);
    } catch (error) {
      console.error('Error fetching device data:', error);
    }
  };

  const calculateStats = (devicesData: Device[], iotDevicesData: IoTDevice[]) => {
    const allDevices = [...devicesData, ...iotDevicesData.map(d => ({
      ...d,
      name: d.device_name,
      type: d.device_type,
      battery_level: d.battery_level || 0,
      status: d.status as any,
      last_sync_time: d.last_seen
    }))];

    const totalDevices = allDevices.length;
    const onlineDevices = allDevices.filter(d => 
      d.status === 'connected' || d.status === 'paired'
    ).length;
    const lowBatteryDevices = allDevices.filter(d => 
      d.battery_level < 20 || d.status === 'low_battery'
    ).length;
    const errorDevices = allDevices.filter(d => 
      d.status === 'error' || d.status === 'disconnected'
    ).length;
    const avgBatteryLevel = totalDevices > 0 
      ? Math.round(allDevices.reduce((sum, d) => sum + d.battery_level, 0) / totalDevices)
      : 0;

    setStats({
      totalDevices,
      onlineDevices,
      lowBatteryDevices,
      errorDevices,
      avgBatteryLevel
    });
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDeviceData();
    if (onRefresh) onRefresh();
    setRefreshing(false);
  };

  const getStatusIcon = (status: string, batteryLevel: number) => {
    if (batteryLevel < 20) return <BatteryLow size={20} color="#ef4444" />;
    if (status === 'connected' || status === 'paired') return <CheckCircle size={20} color="#10b981" />;
    if (status === 'error') return <AlertTriangle size={20} color="#ef4444" />;
    return <WifiOff size={20} color="#6b7280" />;
  };

  const getStatusColor = (status: string, batteryLevel: number) => {
    if (batteryLevel < 20) return '#ef4444';
    if (status === 'connected' || status === 'paired') return '#10b981';
    if (status === 'error') return '#ef4444';
    return '#6b7280';
  };

  const formatLastSeen = (lastSeen: string | null) => {
    if (!lastSeen) return 'Never';
    const date = new Date(lastSeen);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  };

  const filteredDevices = () => {
    const allDevices = [...devices, ...iotDevices.map(d => ({
      ...d,
      name: d.device_name,
      type: d.device_type,
      battery_level: d.battery_level || 0,
      status: d.status as any,
      last_sync_time: d.last_seen
    }))];

    switch (selectedFilter) {
      case 'online':
        return allDevices.filter(d => d.status === 'connected' || d.status === 'paired');
      case 'offline':
        return allDevices.filter(d => d.status === 'disconnected' || d.status === 'error');
      case 'low_battery':
        return allDevices.filter(d => d.battery_level < 20 || d.status === 'low_battery');
      case 'error':
        return allDevices.filter(d => d.status === 'error');
      default:
        return allDevices;
    }
  };

  const handleDeviceAction = (deviceId: string, action: 'restart' | 'update' | 'disconnect') => {
    Alert.alert(
      'Device Action',
      `Are you sure you want to ${action} this device?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Confirm', 
          onPress: () => {
            // Implement device actions here
            console.log(`${action} device:`, deviceId);
          }
        }
      ]
    );
  };

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      {/* Stats Cards */}
      <View style={styles.statsContainer}>
        <View style={[styles.statCard, { backgroundColor: '#3b82f6' }]}>
          <Bluetooth size={24} color="white" />
          <Text style={styles.statNumber}>{stats.totalDevices}</Text>
          <Text style={styles.statLabel}>Total Devices</Text>
        </View>
        
        <View style={[styles.statCard, { backgroundColor: '#10b981' }]}>
          <Wifi size={24} color="white" />
          <Text style={styles.statNumber}>{stats.onlineDevices}</Text>
          <Text style={styles.statLabel}>Online</Text>
        </View>
        
        <View style={[styles.statCard, { backgroundColor: '#f59e0b' }]}>
          <BatteryLow size={24} color="white" />
          <Text style={styles.statNumber}>{stats.lowBatteryDevices}</Text>
          <Text style={styles.statLabel}>Low Battery</Text>
        </View>
        
        <View style={[styles.statCard, { backgroundColor: '#ef4444' }]}>
          <AlertTriangle size={24} color="white" />
          <Text style={styles.statNumber}>{stats.errorDevices}</Text>
          <Text style={styles.statLabel}>Issues</Text>
        </View>
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {[
          { key: 'all', label: 'All' },
          { key: 'online', label: 'Online' },
          { key: 'offline', label: 'Offline' },
          { key: 'low_battery', label: 'Low Battery' },
          { key: 'error', label: 'Issues' }
        ].map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterButton,
              selectedFilter === filter.key && styles.filterButtonActive
            ]}
            onPress={() => setSelectedFilter(filter.key as any)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedFilter === filter.key && styles.filterButtonTextActive
            ]}>
              {filter.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Device List */}
      <View style={styles.deviceList}>
        {filteredDevices().map((device) => (
          <View key={device.id} style={styles.deviceCard}>
            <View style={styles.deviceHeader}>
              <View style={styles.deviceInfo}>
                {getStatusIcon(device.status, device.battery_level)}
                <View style={styles.deviceDetails}>
                  <Text style={styles.deviceName}>{device.name}</Text>
                  <Text style={styles.deviceType}>{device.type}</Text>
                </View>
              </View>
              <View style={styles.deviceStatus}>
                <Text style={[styles.statusText, { color: getStatusColor(device.status, device.battery_level) }]}>
                  {device.status.toUpperCase()}
                </Text>
              </View>
            </View>
            
            <View style={styles.deviceMetrics}>
              <View style={styles.metric}>
                <Battery size={16} color="#6b7280" />
                <Text style={styles.metricText}>{device.battery_level}%</Text>
              </View>
              <View style={styles.metric}>
                <Clock size={16} color="#6b7280" />
                <Text style={styles.metricText}>{formatLastSeen(device.last_sync_time)}</Text>
              </View>
              {(device as any).signal_strength && (
                <View style={styles.metric}>
                  <Wifi size={16} color="#6b7280" />
                  <Text style={styles.metricText}>{(device as any).signal_strength}%</Text>
                </View>
              )}
            </View>

            <View style={styles.deviceActions}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => handleDeviceAction(device.id, 'restart')}
              >
                <Zap size={16} color="#3b82f6" />
                <Text style={styles.actionText}>Restart</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => handleDeviceAction(device.id, 'update')}
              >
                <Settings size={16} color="#10b981" />
                <Text style={styles.actionText}>Update</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => handleDeviceAction(device.id, 'disconnect')}
              >
                <WifiOff size={16} color="#ef4444" />
                <Text style={styles.actionText}>Disconnect</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </View>

      {filteredDevices().length === 0 && (
        <View style={styles.emptyState}>
          <Bluetooth size={48} color="#9ca3af" />
          <Text style={styles.emptyText}>No devices found</Text>
          <Text style={styles.emptySubtext}>
            {selectedFilter === 'all' 
              ? 'No devices are registered in the system'
              : `No devices match the "${selectedFilter}" filter`
            }
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    gap: 8,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  statLabel: {
    fontSize: 12,
    color: 'white',
    opacity: 0.9,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 16,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  filterButtonActive: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: 'white',
  },
  deviceList: {
    paddingHorizontal: 16,
    gap: 12,
  },
  deviceCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  deviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  deviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  deviceDetails: {
    gap: 2,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  deviceType: {
    fontSize: 14,
    color: '#6b7280',
  },
  deviceStatus: {
    alignItems: 'flex-end',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  deviceMetrics: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 12,
  },
  metric: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metricText: {
    fontSize: 14,
    color: '#6b7280',
  },
  deviceActions: {
    flexDirection: 'row',
    gap: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#f8fafc',
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
    gap: 8,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#4b5563',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#9ca3af',
    textAlign: 'center',
  },
});

export default DeviceManagementView;