import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Alert<PERSON>riangle, BatteryLow, WifiOff, Clock, X } from 'lucide-react-native';
import { supabase } from '../../../supabase/client';

interface DeviceAlert {
  id: string;
  deviceId: string;
  deviceName: string;
  type: 'low_battery' | 'offline' | 'error' | 'maintenance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

interface DeviceAlertsPanelProps {
  onAlertAction?: (alertId: string, action: 'acknowledge' | 'dismiss') => void;
}

const DeviceAlertsPanel: React.FC<DeviceAlertsPanelProps> = ({ onAlertAction }) => {
  const [alerts, setAlerts] = useState<DeviceAlert[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAlerts();
    
    // Set up real-time subscription for device changes
    const subscription = supabase
      .channel('device-alerts')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'devices' },
        () => fetchAlerts()
      )
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'iot_devices' },
        () => fetchAlerts()
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const fetchAlerts = async () => {
    try {
      setLoading(true);
      
      // Fetch devices with issues
      const { data: devices, error: devicesError } = await supabase
        .from('devices')
        .select('*')
        .or('battery_level.lt.20,status.eq.error,status.eq.disconnected');

      if (devicesError) throw devicesError;

      // Fetch IoT devices with issues
      const { data: iotDevices, error: iotError } = await supabase
        .from('iot_devices')
        .select('*')
        .or('battery_level.lt.20,status.eq.error,status.eq.inactive');

      if (iotError) throw iotError;

      // Generate alerts from device data
      const generatedAlerts: DeviceAlert[] = [];

      // Process regular devices
      devices?.forEach(device => {
        if (device.battery_level < 20) {
          generatedAlerts.push({
            id: `battery_${device.id}`,
            deviceId: device.id,
            deviceName: device.name,
            type: 'low_battery',
            severity: device.battery_level < 10 ? 'critical' : 'high',
            message: `Battery level is ${device.battery_level}%`,
            timestamp: new Date().toISOString(),
            acknowledged: false
          });
        }

        if (device.status === 'error') {
          generatedAlerts.push({
            id: `error_${device.id}`,
            deviceId: device.id,
            deviceName: device.name,
            type: 'error',
            severity: 'high',
            message: 'Device reporting error status',
            timestamp: new Date().toISOString(),
            acknowledged: false
          });
        }

        if (device.status === 'disconnected' && device.last_sync_time) {
          const lastSync = new Date(device.last_sync_time);
          const hoursSinceSync = (Date.now() - lastSync.getTime()) / (1000 * 60 * 60);
          
          if (hoursSinceSync > 24) {
            generatedAlerts.push({
              id: `offline_${device.id}`,
              deviceId: device.id,
              deviceName: device.name,
              type: 'offline',
              severity: hoursSinceSync > 72 ? 'critical' : 'medium',
              message: `Device offline for ${Math.floor(hoursSinceSync)} hours`,
              timestamp: new Date().toISOString(),
              acknowledged: false
            });
          }
        }
      });

      // Process IoT devices
      iotDevices?.forEach(device => {
        if (device.battery_level && device.battery_level < 20) {
          generatedAlerts.push({
            id: `iot_battery_${device.id}`,
            deviceId: device.id,
            deviceName: device.device_name,
            type: 'low_battery',
            severity: device.battery_level < 10 ? 'critical' : 'high',
            message: `IoT device battery level is ${device.battery_level}%`,
            timestamp: new Date().toISOString(),
            acknowledged: false
          });
        }

        if (device.status === 'error') {
          generatedAlerts.push({
            id: `iot_error_${device.id}`,
            deviceId: device.id,
            deviceName: device.device_name,
            type: 'error',
            severity: 'high',
            message: 'IoT device reporting error status',
            timestamp: new Date().toISOString(),
            acknowledged: false
          });
        }

        if (device.status === 'inactive' && device.last_seen) {
          const lastSeen = new Date(device.last_seen);
          const hoursSinceLastSeen = (Date.now() - lastSeen.getTime()) / (1000 * 60 * 60);
          
          if (hoursSinceLastSeen > 24) {
            generatedAlerts.push({
              id: `iot_offline_${device.id}`,
              deviceId: device.id,
              deviceName: device.device_name,
              type: 'offline',
              severity: hoursSinceLastSeen > 72 ? 'critical' : 'medium',
              message: `IoT device offline for ${Math.floor(hoursSinceLastSeen)} hours`,
              timestamp: new Date().toISOString(),
              acknowledged: false
            });
          }
        }
      });

      setAlerts(generatedAlerts.sort((a, b) => {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      }));
    } catch (error) {
      console.error('Error fetching device alerts:', error);
    } finally {
      setLoading(false);
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'low_battery':
        return <BatteryLow size={20} color="#f59e0b" />;
      case 'offline':
        return <WifiOff size={20} color="#ef4444" />;
      case 'error':
        return <AlertTriangle size={20} color="#ef4444" />;
      case 'maintenance':
        return <Clock size={20} color="#3b82f6" />;
      default:
        return <AlertTriangle size={20} color="#6b7280" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return '#dc2626';
      case 'high':
        return '#ef4444';
      case 'medium':
        return '#f59e0b';
      case 'low':
        return '#10b981';
      default:
        return '#6b7280';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  };

  const handleAlertAction = (alertId: string, action: 'acknowledge' | 'dismiss') => {
    if (action === 'dismiss') {
      setAlerts(prev => prev.filter(alert => alert.id !== alertId));
    } else if (action === 'acknowledge') {
      setAlerts(prev => prev.map(alert => 
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      ));
    }
    
    if (onAlertAction) {
      onAlertAction(alertId, action);
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading alerts...</Text>
      </View>
    );
  }

  if (alerts.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.noAlertsContainer}>
          <AlertTriangle size={32} color="#10b981" />
          <Text style={styles.noAlertsText}>All devices are healthy</Text>
          <Text style={styles.noAlertsSubtext}>No critical issues detected</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Device Alerts ({alerts.length})</Text>
      </View>
      
      <ScrollView style={styles.alertsList}>
        {alerts.map((alert) => (
          <View 
            key={alert.id} 
            style={[
              styles.alertCard,
              { borderLeftColor: getSeverityColor(alert.severity) },
              alert.acknowledged && styles.acknowledgedAlert
            ]}
          >
            <View style={styles.alertHeader}>
              <View style={styles.alertInfo}>
                {getAlertIcon(alert.type)}
                <View style={styles.alertDetails}>
                  <Text style={styles.alertDevice}>{alert.deviceName}</Text>
                  <Text style={styles.alertMessage}>{alert.message}</Text>
                </View>
              </View>
              <View style={styles.alertMeta}>
                <Text style={[styles.severityBadge, { color: getSeverityColor(alert.severity) }]}>
                  {alert.severity.toUpperCase()}
                </Text>
                <Text style={styles.alertTime}>{formatTimestamp(alert.timestamp)}</Text>
              </View>
            </View>
            
            <View style={styles.alertActions}>
              {!alert.acknowledged && (
                <TouchableOpacity 
                  style={styles.actionButton}
                  onPress={() => handleAlertAction(alert.id, 'acknowledge')}
                >
                  <Text style={styles.actionButtonText}>Acknowledge</Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.dismissButton]}
                onPress={() => handleAlertAction(alert.id, 'dismiss')}
              >
                <X size={16} color="#6b7280" />
                <Text style={styles.dismissButtonText}>Dismiss</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  loadingText: {
    textAlign: 'center',
    padding: 32,
    color: '#6b7280',
  },
  noAlertsContainer: {
    alignItems: 'center',
    padding: 32,
    gap: 8,
  },
  noAlertsText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#10b981',
  },
  noAlertsSubtext: {
    fontSize: 14,
    color: '#6b7280',
  },
  alertsList: {
    flex: 1,
    padding: 16,
  },
  alertCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  acknowledgedAlert: {
    opacity: 0.7,
  },
  alertHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  alertInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    flex: 1,
  },
  alertDetails: {
    flex: 1,
    gap: 4,
  },
  alertDevice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  alertMessage: {
    fontSize: 14,
    color: '#6b7280',
  },
  alertMeta: {
    alignItems: 'flex-end',
    gap: 4,
  },
  severityBadge: {
    fontSize: 12,
    fontWeight: '600',
  },
  alertTime: {
    fontSize: 12,
    color: '#9ca3af',
  },
  alertActions: {
    flexDirection: 'row',
    gap: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#3b82f6',
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: 'white',
  },
  dismissButton: {
    backgroundColor: '#f3f4f6',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dismissButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6b7280',
  },
});

export default DeviceAlertsPanel;