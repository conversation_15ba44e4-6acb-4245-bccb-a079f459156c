import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

type TabName = 'dashboard' | 'users' | 'animals' | 'devices' | 'orders';

interface AdminTabsProps {
  activeTab: TabName;
  onTabChange: (tabName: TabName) => void;
}

const AdminTabs: React.FC<AdminTabsProps> = ({ activeTab, onTabChange }) => {
  const tabs: { key: TabName; label: string }[] = [
    { key: 'dashboard', label: 'Dashboard' },
    { key: 'users', label: 'Users' },
    { key: 'animals', label: 'Animals' },
    { key: 'devices', label: 'Devices' },
    { key: 'orders', label: 'Orders' },
  ];
  
  return (
    <View style={styles.tabsContainer}>
      {tabs.map((tab) => (
        <TouchableOpacity 
          key={tab.key}
          style={[styles.tab, activeTab === tab.key && styles.activeTab]} 
          onPress={() => onTabChange(tab.key)}
        >
          <Text style={[styles.tabText, activeTab === tab.key && styles.activeTabText]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#3D8C91',
  },
  tabText: {
    fontSize: 16,
    color: '#636E72',
  },
  activeTabText: {
    color: '#3D8C91',
    fontWeight: '600',
  },
});

export default AdminTabs;