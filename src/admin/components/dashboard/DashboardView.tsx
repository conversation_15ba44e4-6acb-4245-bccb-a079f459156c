import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { Bluetooth, AlertTriangle, Battery, Wifi } from 'lucide-react-native';
import { Stats } from '../../types';
import DashboardStatsCard from './DashboardStatsCard';
import DeviceAlertsPanel from '../devices/DeviceAlertsPanel';
import { supabase } from '../../../supabase/client';

interface DashboardViewProps {
  stats: Stats;
  onRefresh: () => void;
  isLoading: boolean;
}

const DashboardView: React.FC<DashboardViewProps> = ({ stats, onRefresh, isLoading }) => {
  const [deviceStats, setDeviceStats] = useState({
    totalDevices: 0,
    onlineDevices: 0,
    lowBatteryDevices: 0,
    criticalAlerts: 0
  });
  const [showDeviceAlerts, setShowDeviceAlerts] = useState(false);

  useEffect(() => {
    fetchDeviceStats();
  }, []);

  const fetchDeviceStats = async () => {
    try {
      // Fetch regular devices
      const { data: devices, error: devicesError } = await supabase
        .from('devices')
        .select('*');

      if (devicesError) throw devicesError;

      // Fetch IoT devices
      const { data: iotDevices, error: iotError } = await supabase
        .from('iot_devices')
        .select('*');

      if (iotError) throw iotError;

      const allDevices = [...(devices || []), ...(iotDevices || [])];
      const totalDevices = allDevices.length;
      const onlineDevices = allDevices.filter(d => 
        d.status === 'connected' || d.status === 'paired'
      ).length;
      const lowBatteryDevices = allDevices.filter(d => 
        (d.battery_level && d.battery_level < 20) || d.status === 'low_battery'
      ).length;
      const criticalAlerts = allDevices.filter(d => 
        d.status === 'error' || (d.battery_level && d.battery_level < 10)
      ).length;

      setDeviceStats({
        totalDevices,
        onlineDevices,
        lowBatteryDevices,
        criticalAlerts
      });
    } catch (error) {
      console.error('Error fetching device stats:', error);
    }
  };

  const statsData = [
    { label: 'Total Users', value: stats.totalUsers },
    { label: 'Premium Users', value: stats.premiumUsers },
    { label: 'Animals', value: stats.totalAnimals },
    { label: 'Vital Records', value: stats.totalVitals },
    { label: 'Medications', value: stats.totalMedications },
    { label: 'Feeding Schedules', value: stats.totalFeedings },
  ];

  const deviceStatsData = [
    { 
      label: 'Total Devices', 
      value: deviceStats.totalDevices,
      icon: <Bluetooth size={20} color="#3b82f6" />,
      color: '#3b82f6'
    },
    { 
      label: 'Online Devices', 
      value: deviceStats.onlineDevices,
      icon: <Wifi size={20} color="#10b981" />,
      color: '#10b981'
    },
    { 
      label: 'Low Battery', 
      value: deviceStats.lowBatteryDevices,
      icon: <Battery size={20} color="#f59e0b" />,
      color: '#f59e0b'
    },
    { 
      label: 'Critical Alerts', 
      value: deviceStats.criticalAlerts,
      icon: <AlertTriangle size={20} color="#ef4444" />,
      color: '#ef4444'
    },
  ];
  
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.sectionTitle}>System Overview</Text>
      
      <View style={styles.statsContainer}>
        {statsData.map((stat, index) => (
          <DashboardStatsCard
            key={index}
            label={stat.label}
            value={stat.value}
          />
        ))}
      </View>

      {/* Device Monitoring Section */}
      <View style={styles.deviceSection}>
        <View style={styles.deviceHeader}>
          <Text style={styles.sectionTitle}>Device Monitoring</Text>
          <TouchableOpacity 
            style={styles.alertsToggle}
            onPress={() => setShowDeviceAlerts(!showDeviceAlerts)}
          >
            <AlertTriangle size={16} color={deviceStats.criticalAlerts > 0 ? '#ef4444' : '#6b7280'} />
            <Text style={[styles.alertsToggleText, { 
              color: deviceStats.criticalAlerts > 0 ? '#ef4444' : '#6b7280' 
            }]}>
              {deviceStats.criticalAlerts > 0 ? `${deviceStats.criticalAlerts} Alerts` : 'No Alerts'}
            </Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.deviceStatsContainer}>
          {deviceStatsData.map((stat, index) => (
            <View key={index} style={styles.deviceStatCard}>
              <View style={styles.deviceStatHeader}>
                {stat.icon}
                <Text style={[styles.deviceStatValue, { color: stat.color }]}>
                  {stat.value}
                </Text>
              </View>
              <Text style={styles.deviceStatLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        {showDeviceAlerts && (
          <View style={styles.alertsContainer}>
            <DeviceAlertsPanel />
          </View>
        )}
      </View>
      
      <TouchableOpacity 
        style={[styles.refreshButton, isLoading && styles.refreshButtonDisabled]} 
        onPress={() => {
          onRefresh();
          fetchDeviceStats();
        }}
        disabled={isLoading}
      >
        <Text style={styles.refreshButtonText}>
          {isLoading ? 'Refreshing...' : 'Refresh Data'}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    color: '#2D3436',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  refreshButton: {
    backgroundColor: '#3D8C91',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  refreshButtonDisabled: {
    opacity: 0.7,
  },
  refreshButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
});

export default DashboardView;