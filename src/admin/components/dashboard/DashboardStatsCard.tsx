import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface DashboardStatsCardProps {
  label: string;
  value: number;
}

const DashboardStatsCard: React.FC<DashboardStatsCardProps> = ({ label, value }) => {
  return (
    <View style={styles.statCard}>
      <Text style={styles.statNumber}>{value}</Text>
      <Text style={styles.statLabel}>{label}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  statCard: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3D8C91',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#636E72',
  },
});

export default DashboardStatsCard;