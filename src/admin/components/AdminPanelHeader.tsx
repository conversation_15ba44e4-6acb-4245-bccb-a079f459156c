import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface AdminPanelHeaderProps {
  title: string;
  onLogout: () => void;
}

const AdminPanelHeader: React.FC<AdminPanelHeaderProps> = ({ title, onLogout }) => {
  return (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>{title}</Text>
      <TouchableOpacity style={styles.logoutButton} onPress={onLogout}>
        <Text style={styles.logoutText}>Sign Out</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#3D8C91',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  logoutButton: {
    padding: 8,
  },
  logoutText: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
});

export default AdminPanelHeader;