import React from 'react';
import { View, Text, FlatList, StyleSheet } from 'react-native';
import { User } from '../../types';
import UserListItem from './UserListItem';

interface UserListViewProps {
  users: User[];
  onTogglePremium: (userId: string, currentPremiumStatus: boolean) => void;
}

const UserListView: React.FC<UserListViewProps> = ({ users, onTogglePremium }) => {
  return (
    <View>
      <Text style={styles.sectionTitle}>User Management</Text>
      
      <FlatList
        data={users}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <UserListItem
            user={item}
            onTogglePremium={onTogglePremium}
          />
        )}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    color: '#2D3436',
  },
});

export default UserListView;