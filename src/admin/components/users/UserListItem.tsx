import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { User } from '../../types';

interface UserListItemProps {
  user: User;
  onTogglePremium: (userId: string, currentPremiumStatus: boolean) => void;
}

const UserListItem: React.FC<UserListItemProps> = ({ user, onTogglePremium }) => {
  return (
    <View style={styles.userCard}>
      <View style={styles.userInfo}>
        <Text style={styles.userName}>{user.name || 'No Name'}</Text>
        <Text style={styles.userEmail}>{user.email}</Text>
        <Text style={[
          styles.userStatus, 
          user.is_premium ? styles.premiumStatus : styles.freeStatus
        ]}>
          {user.is_premium ? 'Premium' : 'Free'}
        </Text>
        {user.is_premium && user.subscription_type && (
          <Text style={styles.subscriptionInfo}>
            {user.subscription_type.charAt(0).toUpperCase() + user.subscription_type.slice(1)} Plan
            {user.subscription_end_date && ` (Expires: ${new Date(user.subscription_end_date).toLocaleDateString()})`}
          </Text>
        )}
      </View>
      
      <TouchableOpacity 
        style={[
          styles.togglePremiumButton, 
          user.is_premium ? styles.removePremiumButton : styles.addPremiumButton
        ]}
        onPress={() => onTogglePremium(user.id, user.is_premium)}
      >
        <Text style={styles.togglePremiumText}>
          {user.is_premium ? 'Remove Premium' : 'Add Premium'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  userCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3436',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#636E72',
    marginBottom: 4,
  },
  userStatus: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  premiumStatus: {
    color: '#38A169',
  },
  freeStatus: {
    color: '#636E72',
  },
  subscriptionInfo: {
    fontSize: 12,
    color: '#636E72',
  },
  togglePremiumButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
  },
  addPremiumButton: {
    backgroundColor: '#38A169',
  },
  removePremiumButton: {
    backgroundColor: '#E53E3E',
  },
  togglePremiumText: {
    color: '#FFFFFF',
    fontWeight: '500',
    fontSize: 12,
  },
});

export default UserListItem;