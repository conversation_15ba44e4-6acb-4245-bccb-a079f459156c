import React from 'react';
import { View, Text, FlatList, StyleSheet } from 'react-native';
import { Animal } from '../../types';
import AnimalListItem from './AnimalListItem';

interface AnimalListViewProps {
  animals: Animal[];
}

const AnimalListView: React.FC<AnimalListViewProps> = ({ animals }) => {
  return (
    <View>
      <Text style={styles.sectionTitle}>Animal Management</Text>
      
      <FlatList
        data={animals}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <AnimalListItem animal={item} />
        )}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    color: '#2D3436',
  },
});

export default AnimalListView;