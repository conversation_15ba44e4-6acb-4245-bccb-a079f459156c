import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Animal } from '../../types';

interface AnimalListItemProps {
  animal: Animal;
}

const AnimalListItem: React.FC<AnimalListItemProps> = ({ animal }) => {
  return (
    <View style={styles.animalCard}>
      <Text style={styles.animalName}>{animal.name}</Text>
      <Text style={styles.animalDetails}>
        {animal.type.charAt(0).toUpperCase() + animal.type.slice(1)} • {animal.breed}
      </Text>
      <Text style={styles.animalOwner}>Owner ID: {animal.user_id}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  animalCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  animalName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3436',
    marginBottom: 4,
  },
  animalDetails: {
    fontSize: 14,
    color: '#636E72',
    marginBottom: 4,
  },
  animalOwner: {
    fontSize: 12,
    color: '#636E72',
  },
});

export default AnimalListItem;