import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';

interface Order {
  id: string;
  user_id: string;
  total_amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  shipping_address_line1: string;
  shipping_address_line2?: string;
  shipping_city: string;
  shipping_state_province: string;
  shipping_postal_code: string;
  shipping_country: string;
  payment_method?: string;
  tracking_number?: string;
  created_at: string;
  order_items?: OrderItem[];
  user?: {
    email: string;
    name?: string;
  };
}

interface OrderItem {
  id: string;
  product_id: string;
  quantity: number;
  price_at_purchase: number;
  product?: {
    name: string;
  };
}

interface EditOrderModalProps {
  visible: boolean;
  order: Order;
  onUpdate: (orderId: string, updates: Partial<Order>) => Promise<void>;
  onClose: () => void;
}

const EditOrderModal: React.FC<EditOrderModalProps> = ({
  visible,
  order,
  onUpdate,
  onClose,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    status: order.status,
    tracking_number: order.tracking_number || '',
    shipping_address_line1: order.shipping_address_line1,
    shipping_address_line2: order.shipping_address_line2 || '',
    shipping_city: order.shipping_city,
    shipping_state_province: order.shipping_state_province,
    shipping_postal_code: order.shipping_postal_code,
    shipping_country: order.shipping_country,
  });

  useEffect(() => {
    setFormData({
      status: order.status,
      tracking_number: order.tracking_number || '',
      shipping_address_line1: order.shipping_address_line1,
      shipping_address_line2: order.shipping_address_line2 || '',
      shipping_city: order.shipping_city,
      shipping_state_province: order.shipping_state_province,
      shipping_postal_code: order.shipping_postal_code,
      shipping_country: order.shipping_country,
    });
  }, [order]);

  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'paid', label: 'Paid' },
    { value: 'processing', label: 'Processing' },
    { value: 'shipped', label: 'Shipped' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'refunded', label: 'Refunded' },
  ];

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await onUpdate(order.id, formData);
    } catch (error) {
      Alert.alert('Error', 'Failed to update order');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // Reset form data to original values
    setFormData({
      status: order.status,
      tracking_number: order.tracking_number || '',
      shipping_address_line1: order.shipping_address_line1,
      shipping_address_line2: order.shipping_address_line2 || '',
      shipping_city: order.shipping_city,
      shipping_state_province: order.shipping_state_province,
      shipping_postal_code: order.shipping_postal_code,
      shipping_country: order.shipping_country,
    });
    onClose();
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel}>
            <Text style={styles.cancelButton}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Edit Order</Text>
          <TouchableOpacity onPress={handleSave} disabled={isLoading}>
            {isLoading ? (
              <ActivityIndicator size="small" color="#3D8C91" />
            ) : (
              <Text style={styles.saveButton}>Save</Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Order Info */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Order Information</Text>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Order ID:</Text>
              <Text style={styles.infoValue}>#{order.id.substring(0, 8).toUpperCase()}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Customer:</Text>
              <Text style={styles.infoValue}>
                {order.user?.name || order.user?.email || 'Unknown'}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Total Amount:</Text>
              <Text style={styles.infoValue}>
                {formatPrice(order.total_amount, order.currency)}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Created:</Text>
              <Text style={styles.infoValue}>{formatDate(order.created_at)}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Payment Method:</Text>
              <Text style={styles.infoValue}>
                {order.payment_method || 'Not specified'}
              </Text>
            </View>
          </View>

          {/* Order Items */}
          {order.order_items && order.order_items.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Order Items</Text>
              {order.order_items.map((item, index) => (
                <View key={item.id} style={styles.orderItem}>
                  <Text style={styles.itemName}>
                    {item.product?.name || `Product ${item.product_id}`}
                  </Text>
                  <Text style={styles.itemDetails}>
                    Qty: {item.quantity} • {formatPrice(item.price_at_purchase, order.currency)} each
                  </Text>
                </View>
              ))}
            </View>
          )}

          {/* Status */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Order Status</Text>
            <View style={styles.statusContainer}>
              {statusOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.statusOption,
                    formData.status === option.value && styles.statusOptionSelected,
                  ]}
                  onPress={() => setFormData({ ...formData, status: option.value as any })}
                >
                  <Text
                    style={[
                      styles.statusOptionText,
                      formData.status === option.value && styles.statusOptionTextSelected,
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Tracking Number */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Tracking Information</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter tracking number"
              value={formData.tracking_number}
              onChangeText={(text) => setFormData({ ...formData, tracking_number: text })}
            />
          </View>

          {/* Shipping Address */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Shipping Address</Text>
            
            <Text style={styles.inputLabel}>Address Line 1</Text>
            <TextInput
              style={styles.input}
              placeholder="Street address"
              value={formData.shipping_address_line1}
              onChangeText={(text) => setFormData({ ...formData, shipping_address_line1: text })}
            />
            
            <Text style={styles.inputLabel}>Address Line 2 (Optional)</Text>
            <TextInput
              style={styles.input}
              placeholder="Apartment, suite, etc."
              value={formData.shipping_address_line2}
              onChangeText={(text) => setFormData({ ...formData, shipping_address_line2: text })}
            />
            
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>City</Text>
                <TextInput
                  style={styles.input}
                  placeholder="City"
                  value={formData.shipping_city}
                  onChangeText={(text) => setFormData({ ...formData, shipping_city: text })}
                />
              </View>
              
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>State/Province</Text>
                <TextInput
                  style={styles.input}
                  placeholder="State/Province"
                  value={formData.shipping_state_province}
                  onChangeText={(text) => setFormData({ ...formData, shipping_state_province: text })}
                />
              </View>
            </View>
            
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>Postal Code</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Postal Code"
                  value={formData.shipping_postal_code}
                  onChangeText={(text) => setFormData({ ...formData, shipping_postal_code: text })}
                />
              </View>
              
              <View style={styles.halfWidth}>
                <Text style={styles.inputLabel}>Country</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Country"
                  value={formData.shipping_country}
                  onChangeText={(text) => setFormData({ ...formData, shipping_country: text })}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A202C',
  },
  cancelButton: {
    fontSize: 16,
    color: '#718096',
  },
  saveButton: {
    fontSize: 16,
    color: '#3D8C91',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1A202C',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F7FAFC',
  },
  infoLabel: {
    fontSize: 14,
    color: '#718096',
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    color: '#1A202C',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  orderItem: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F7FAFC',
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1A202C',
    marginBottom: 2,
  },
  itemDetails: {
    fontSize: 12,
    color: '#718096',
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F7FAFC',
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  statusOptionSelected: {
    backgroundColor: '#3D8C91',
    borderColor: '#3D8C91',
  },
  statusOptionText: {
    fontSize: 12,
    color: '#718096',
    fontWeight: '500',
  },
  statusOptionTextSelected: {
    color: '#FFFFFF',
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1A202C',
    marginBottom: 6,
    marginTop: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#1A202C',
    backgroundColor: '#FFFFFF',
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
});

export default EditOrderModal;