import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

interface Order {
  id: string;
  user_id: string;
  total_amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  shipping_address_line1: string;
  shipping_city: string;
  shipping_state_province: string;
  shipping_country: string;
  payment_method?: string;
  tracking_number?: string;
  created_at: string;
  order_items?: OrderItem[];
  user?: {
    email: string;
    name?: string;
  };
}

interface OrderItem {
  id: string;
  product_id: string;
  quantity: number;
  price_at_purchase: number;
  product?: {
    name: string;
  };
}

interface OrderListItemProps {
  order: Order;
  onEdit: () => void;
}

const OrderListItem: React.FC<OrderListItemProps> = ({ order, onEdit }) => {
  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#F59E0B';
      case 'paid':
      case 'processing':
        return '#3B82F6';
      case 'shipped':
        return '#8B5CF6';
      case 'delivered':
        return '#10B981';
      case 'cancelled':
      case 'refunded':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const getStatusText = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const getTotalItems = () => {
    if (!order.order_items) return 0;
    return order.order_items.reduce((total, item) => total + item.quantity, 0);
  };

  const getFirstProductName = () => {
    if (!order.order_items || order.order_items.length === 0) return 'No items';
    const firstItem = order.order_items[0];
    return firstItem.product?.name || `Product ${firstItem.product_id}`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.orderInfo}>
          <Text style={styles.orderId}>#{order.id.substring(0, 8).toUpperCase()}</Text>
          <Text style={styles.orderDate}>{formatDate(order.created_at)}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) + '20' }]}>
          <Text style={[styles.statusText, { color: getStatusColor(order.status) }]}>
            {getStatusText(order.status)}
          </Text>
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.customerInfo}>
          <Text style={styles.customerLabel}>Customer:</Text>
          <Text style={styles.customerValue}>
            {order.user?.name || order.user?.email || 'Unknown'}
          </Text>
        </View>

        <View style={styles.orderDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Items:</Text>
            <Text style={styles.detailValue}>
              {getTotalItems()} item{getTotalItems() !== 1 ? 's' : ''}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Product:</Text>
            <Text style={styles.detailValue} numberOfLines={1}>
              {getFirstProductName()}
              {order.order_items && order.order_items.length > 1 && 
                ` +${order.order_items.length - 1} more`
              }
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Total:</Text>
            <Text style={[styles.detailValue, styles.totalAmount]}>
              {formatPrice(order.total_amount, order.currency)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Shipping:</Text>
            <Text style={styles.detailValue} numberOfLines={1}>
              {order.shipping_city}, {order.shipping_state_province}
            </Text>
          </View>

          {order.tracking_number && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Tracking:</Text>
              <Text style={styles.detailValue}>{order.tracking_number}</Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.actions}>
        <TouchableOpacity style={styles.editButton} onPress={onEdit}>
          <Text style={styles.editButtonText}>Edit Order</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1A202C',
    marginBottom: 2,
  },
  orderDate: {
    fontSize: 12,
    color: '#718096',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  content: {
    marginBottom: 16,
  },
  customerInfo: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  customerLabel: {
    fontSize: 14,
    color: '#718096',
    width: 80,
  },
  customerValue: {
    fontSize: 14,
    color: '#1A202C',
    fontWeight: '500',
    flex: 1,
  },
  orderDetails: {
    gap: 6,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 13,
    color: '#718096',
    flex: 1,
  },
  detailValue: {
    fontSize: 13,
    color: '#1A202C',
    flex: 2,
    textAlign: 'right',
  },
  totalAmount: {
    fontWeight: 'bold',
    color: '#3D8C91',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  editButton: {
    backgroundColor: '#3D8C91',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default OrderListItem;