import { supabase } from '../supabase/client';
import { toast } from 'sonner-native';

/**
 * Initialize Supabase Storage for profile images
 * This function calls the setup-storage edge function to create the bucket and policies
 */
export const initializeStorage = async (): Promise<boolean> => {
  try {
    console.log('Initializing Supabase Storage for profile images...');
    
    // Call the setup-storage edge function
    const { data, error } = await supabase.functions.invoke('setup-storage', {
      body: {}
    });

    if (error) {
      console.error('Error calling setup-storage function:', error);
      toast.error('Failed to initialize storage. Please try again.');
      return false;
    }

    if (data?.success) {
      console.log('Storage initialized successfully:', data);
      toast.success('Storage initialized successfully!');
      return true;
    } else {
      console.error('Storage setup failed:', data);
      toast.error('Storage setup failed. Please contact support.');
      return false;
    }
  } catch (error) {
    console.error('Error initializing storage:', error);
    toast.error('Failed to initialize storage. Please try again.');
    return false;
  }
};

/**
 * Check if the profile-images bucket exists
 */
export const checkStorageSetup = async (): Promise<boolean> => {
  try {
    console.log('Checking if profile-images bucket exists...');
    
    // Use getBucket for a more reliable check
    const { data: bucket, error } = await supabase.storage.getBucket('profile-images');

    if (error) {
      if (error.message.includes('Bucket not found') || error.message.includes('not found')) {
        console.log('Profile images bucket not found');
        return false;
      }
      // Other errors might be permission-related but bucket likely exists
      console.log('Bucket check got error (but may exist):', error.message);
      // Fall back to list check
      const { error: listError } = await supabase.storage
        .from('profile-images')
        .list('', { limit: 1 });
      
      if (listError && listError.message.includes('Bucket not found')) {
        return false;
      }
      return true;
    }

    if (bucket) {
      console.log('Profile images bucket exists:', bucket.name);
      return true;
    }

    console.log('Profile images bucket not found (no data returned)');
    return false;
  } catch (error) {
    console.error('Error checking storage setup:', error);
    // Fall back to list check as final attempt
    try {
      const { error: listError } = await supabase.storage
        .from('profile-images')
        .list('', { limit: 1 });
      
      return !listError || !listError.message.includes('Bucket not found');
    } catch {
      return false;
    }
  }
};