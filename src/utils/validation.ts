// Comprehensive validation utilities for form inputs
// Provides reusable validation functions with consistent error messages

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

// Email validation with comprehensive pattern matching
export const validateEmail = (email: string): ValidationResult => {
  if (!email || !email.trim()) {
    return { isValid: false, error: 'Email is required' };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email.trim())) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }
  
  return { isValid: true };
};

// Phone number validation (flexible format)
export const validatePhone = (phone: string): ValidationResult => {
  if (!phone || !phone.trim()) {
    return { isValid: false, error: 'Phone number is required' };
  }
  
  // Remove all non-digit characters for validation
  const digitsOnly = phone.replace(/\D/g, '');
  
  if (digitsOnly.length < 10) {
    return { isValid: false, error: 'Phone number must be at least 10 digits' };
  }
  
  if (digitsOnly.length > 15) {
    return { isValid: false, error: 'Phone number cannot exceed 15 digits' };
  }
  
  return { isValid: true };
};

// Required field validation
export const validateRequired = (value: string, fieldName: string): ValidationResult => {
  if (!value || !value.trim()) {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  return { isValid: true };
};

// Text length validation
export const validateLength = (
  value: string, 
  fieldName: string, 
  minLength?: number, 
  maxLength?: number
): ValidationResult => {
  const trimmedValue = value?.trim() || '';
  
  if (minLength && trimmedValue.length < minLength) {
    return { 
      isValid: false, 
      error: `${fieldName} must be at least ${minLength} characters` 
    };
  }
  
  if (maxLength && trimmedValue.length > maxLength) {
    return { 
      isValid: false, 
      error: `${fieldName} cannot exceed ${maxLength} characters` 
    };
  }
  
  return { isValid: true };
};

// Numeric validation with range checking
export const validateNumber = (
  value: string, 
  fieldName: string, 
  min?: number, 
  max?: number,
  allowDecimals: boolean = false
): ValidationResult => {
  if (!value || !value.trim()) {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  const numericRegex = allowDecimals ? /^\d*\.?\d+$/ : /^\d+$/;
  if (!numericRegex.test(value.trim())) {
    return { 
      isValid: false, 
      error: `${fieldName} must be a valid ${allowDecimals ? 'number' : 'whole number'}` 
    };
  }
  
  const numValue = parseFloat(value.trim());
  
  if (min !== undefined && numValue < min) {
    return { 
      isValid: false, 
      error: `${fieldName} must be at least ${min}` 
    };
  }
  
  if (max !== undefined && numValue > max) {
    return { 
      isValid: false, 
      error: `${fieldName} cannot exceed ${max}` 
    };
  }
  
  return { isValid: true };
};

// Age validation (specific for animals)
export const validateAge = (age: string): ValidationResult => {
  if (!age || !age.trim()) {
    return { isValid: true }; // Age is optional
  }
  
  return validateNumber(age, 'Age', 0, 50, false);
};

// Microchip ID validation
export const validateMicrochipId = (microchipId: string): ValidationResult => {
  if (!microchipId || !microchipId.trim()) {
    return { isValid: true }; // Microchip ID is optional
  }
  
  const trimmedId = microchipId.trim();
  
  // Microchip IDs are typically 15 digits, but can vary
  if (trimmedId.length < 10 || trimmedId.length > 20) {
    return { 
      isValid: false, 
      error: 'Microchip ID must be between 10 and 20 characters' 
    };
  }
  
  // Allow alphanumeric characters
  const alphanumericRegex = /^[A-Za-z0-9]+$/;
  if (!alphanumericRegex.test(trimmedId)) {
    return { 
      isValid: false, 
      error: 'Microchip ID can only contain letters and numbers' 
    };
  }
  
  return { isValid: true };
};

// Dosage validation for medications
export const validateDosage = (dosage: string): ValidationResult => {
  if (!dosage || !dosage.trim()) {
    return { isValid: false, error: 'Dosage is required' };
  }
  
  return validateNumber(dosage, 'Dosage', 0.01, 10000, true);
};

// Temperature validation (for animal health)
export const validateTemperature = (temperature: string): ValidationResult => {
  if (!temperature || !temperature.trim()) {
    return { isValid: false, error: 'Temperature is required' };
  }
  
  return validateNumber(temperature, 'Temperature', 30, 45, true);
};

// Heart rate validation (for animal health)
export const validateHeartRate = (heartRate: string): ValidationResult => {
  if (!heartRate || !heartRate.trim()) {
    return { isValid: false, error: 'Heart rate is required' };
  }
  
  return validateNumber(heartRate, 'Heart rate', 20, 200, false);
};

// Date validation
export const validateDate = (date: Date | null, fieldName: string, required: boolean = true): ValidationResult => {
  if (!date) {
    if (required) {
      return { isValid: false, error: `${fieldName} is required` };
    }
    return { isValid: true };
  }
  
  const now = new Date();
  const oneYearFromNow = new Date();
  oneYearFromNow.setFullYear(now.getFullYear() + 1);
  
  if (date < now) {
    return { 
      isValid: false, 
      error: `${fieldName} cannot be in the past` 
    };
  }
  
  if (date > oneYearFromNow) {
    return { 
      isValid: false, 
      error: `${fieldName} cannot be more than one year in the future` 
    };
  }
  
  return { isValid: true };
};

// Date range validation
export const validateDateRange = (
  startDate: Date | null, 
  endDate: Date | null
): ValidationResult => {
  if (!startDate || !endDate) {
    return { isValid: true }; // Individual date validation handles required checks
  }
  
  if (endDate <= startDate) {
    return { 
      isValid: false, 
      error: 'End date must be after start date' 
    };
  }
  
  return { isValid: true };
};

// Combine multiple validation results
export const combineValidations = (...validations: ValidationResult[]): ValidationResult => {
  for (const validation of validations) {
    if (!validation.isValid) {
      return validation;
    }
  }
  
  return { isValid: true };
};

// Validation for form objects
export const validateFormFields = (validations: Record<string, ValidationResult>): {
  isValid: boolean;
  errors: Record<string, string>;
  firstError?: string;
} => {
  const errors: Record<string, string> = {};
  let firstError: string | undefined;
  
  for (const [field, validation] of Object.entries(validations)) {
    if (!validation.isValid && validation.error) {
      errors[field] = validation.error;
      if (!firstError) {
        firstError = validation.error;
      }
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    firstError
  };
};