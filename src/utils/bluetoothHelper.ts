
/**
 * Bluetooth Helper Utility
 * 
 * This is a simplified implementation for demo purposes.
 * In a real app, you would use a library like react-native-ble-plx or expo-bluetooth.
 */

// Mock device types for our app
export type BluetoothDeviceStatus = 'connected' | 'disconnected' | 'pairing' | 'low_battery';

export interface BluetoothDevice {
  id: string;
  name: string;
  type: string;
  batteryLevel: number;
  status: BluetoothDeviceStatus;
  lastSyncTime?: number;
}

// Mock available devices
const mockAvailableDevices: BluetoothDevice[] = [
  {
    id: 'bt-001',
    name: 'HoofMonitor Ultra',
    type: 'Multi-sensor',
    batteryLevel: 100,
    status: 'disconnected',
  },
  {
    id: 'bt-002',
    name: 'EquiTrack Pro',
    type: 'GPS Tracker',
    batteryLevel: 95,
    status: 'disconnected',
  },
  {
    id: 'bt-003',
    name: 'VitalSense 2000',
    type: 'Heart Rate Monitor',
    batteryLevel: 85,
    status: 'disconnected',
  }
];

// Mock paired devices
let mockPairedDevices: BluetoothDevice[] = [];

// Bluetooth scanning state
let isScanning = false;

/**
 * Start scanning for Bluetooth devices
 * @returns Promise that resolves when scanning starts
 */
export const startScan = async (): Promise<void> => {
  isScanning = true;
  
  // In a real app, this would use the device's Bluetooth API
  console.log('Started scanning for Bluetooth devices');
  
  // Return a promise that resolves after a delay to simulate scanning
  return new Promise(resolve => {
    setTimeout(() => {
      isScanning = false;
      resolve();
    }, 2000);
  });
};

/**
 * Stop scanning for Bluetooth devices
 */
export const stopScan = (): void => {
  isScanning = false;
  console.log('Stopped scanning for Bluetooth devices');
};

/**
 * Get scanning status
 * @returns boolean indicating if scanning is in progress
 */
export const getIsScanning = (): boolean => {
  return isScanning;
};

// Check if Bluetooth is available on the device
export const isBluetoothAvailable = async (): Promise<boolean> => {
  // In a real app, this would check actual Bluetooth availability
  // For now, we'll simulate Bluetooth availability based on platform
  try {
    // Simulate checking device capabilities
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // For web/expo preview, we'll always return true to allow testing
    // In a real native app, this would use expo-bluetooth or similar
    return true;
  } catch (error) {
    console.error('Error checking Bluetooth availability:', error);
    return false;
  }
};

/**
 * Get available devices
 * @returns Array of available Bluetooth devices
 */
export const getAvailableDevices = (): BluetoothDevice[] => {
  return mockAvailableDevices;
};

/**
 * Get paired devices
 * @returns Array of paired Bluetooth devices
 */
export const getPairedDevices = (): BluetoothDevice[] => {
  return mockPairedDevices;
};

/**
 * Pair a device
 * @param deviceId ID of the device to pair
 * @returns Promise that resolves when pairing is complete
 */
export const pairDevice = async (deviceId: string): Promise<BluetoothDevice | null> => {
  const device = mockAvailableDevices.find(d => d.id === deviceId);
  
  if (!device) {
    return null;
  }
  
  // Update device status
  const updatedDevice = {
    ...device,
    status: 'connected' as BluetoothDeviceStatus,
    lastSyncTime: Date.now()
  };
  
  // Add to paired devices
  mockPairedDevices = [...mockPairedDevices, updatedDevice];
  
  console.log(`Paired device: ${device.name}`);
  
  return updatedDevice;
};

export default {
  startScan,
  stopScan,
  getIsScanning,
  getAvailableDevices,
  getPairedDevices,
  pairDevice,
  isBluetoothAvailable
};
