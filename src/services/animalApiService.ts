import { supabase, logApiCall } from '../supabase/client';
import { Animal } from '../mocks/animals';
import { toast } from 'sonner-native';

// Database schema type for animals table
interface AnimalDbRecord {
  id: string;
  user_id: string;
  name: string;
  type: string;
  breed: string;
  age: number;
  gender: string;
  color: string;
  image_url: string | null;
  microchip_id: string | null;
  dam: string | null;
  sire: string | null;
  notes: string | null;
  device_id: string | null;
  device_name: string | null;
  device_status: string | null;
  last_sync_time: string | null;
  location_latitude: number | null;
  location_longitude: number | null;
  location_timestamp: string | null;
  passport_image_url: string | null;
  speed: number;
  speed_unit: string;
  speed_updated_at: string | null;
}

// Data transformation utilities
const transformAnimalToDb = (animal: Omit<Animal, 'id'>): Omit<AnimalDbRecord, 'id' | 'user_id'> => {
  return {
    name: animal.name,
    type: animal.type,
    breed: animal.breed,
    age: animal.age,
    gender: animal.gender,
    color: animal.color,
    image_url: animal.imageUrl,
    microchip_id: animal.microchipId,
    dam: animal.dam,
    sire: animal.sire,
    notes: animal.notes,
    device_id: animal.deviceId,
    device_name: animal.deviceName,
    device_status: animal.deviceStatus,
    last_sync_time: animal.lastSyncTime ? new Date(animal.lastSyncTime).toISOString() : null,
    location_latitude: animal.location?.latitude || null,
    location_longitude: animal.location?.longitude || null,
    location_timestamp: animal.location?.timestamp ? new Date(animal.location.timestamp).toISOString() : null,
    passport_image_url: animal.passportImageUrl,
    speed: animal.speed || 0,
    speed_unit: animal.speedUnit || 'km/h',
    speed_updated_at: animal.speedUpdatedAt || new Date().toISOString()
  };
};

const transformDbToAnimal = (dbRecord: AnimalDbRecord): Animal => {
  return {
    id: dbRecord.id,
    name: dbRecord.name,
    type: dbRecord.type,
    breed: dbRecord.breed,
    age: dbRecord.age,
    gender: dbRecord.gender,
    color: dbRecord.color,
    imageUrl: dbRecord.image_url,
    microchipId: dbRecord.microchip_id,
    dam: dbRecord.dam,
    sire: dbRecord.sire,
    notes: dbRecord.notes,
    deviceId: dbRecord.device_id,
    deviceName: dbRecord.device_name,
    deviceStatus: dbRecord.device_status,
    lastSyncTime: dbRecord.last_sync_time ? new Date(dbRecord.last_sync_time).getTime() : undefined,
    location: dbRecord.location_latitude && dbRecord.location_longitude ? {
      latitude: dbRecord.location_latitude,
      longitude: dbRecord.location_longitude,
      timestamp: dbRecord.location_timestamp ? new Date(dbRecord.location_timestamp).getTime() : Date.now()
    } : undefined,
    passportImageUrl: dbRecord.passport_image_url,
    speed: dbRecord.speed || 0,
    speedUnit: dbRecord.speed_unit || 'km/h',
    speedUpdatedAt: dbRecord.speed_updated_at || undefined
  };
};

const transformAnimalUpdatesToDb = (updates: Partial<Animal>): Partial<AnimalDbRecord> => {
  const updateData: Partial<AnimalDbRecord> = {};
  
  if (updates.name) updateData.name = updates.name;
  if (updates.type) updateData.type = updates.type;
  if (updates.breed) updateData.breed = updates.breed;
  if (updates.age !== undefined) updateData.age = updates.age;
  if (updates.gender) updateData.gender = updates.gender;
  if (updates.color) updateData.color = updates.color;
  if (updates.imageUrl) updateData.image_url = updates.imageUrl;
  if (updates.microchipId) updateData.microchip_id = updates.microchipId;
  if (updates.dam) updateData.dam = updates.dam;
  if (updates.sire) updateData.sire = updates.sire;
  if (updates.notes) updateData.notes = updates.notes;
  if (updates.deviceId) updateData.device_id = updates.deviceId;
  if (updates.deviceName) updateData.device_name = updates.deviceName;
  if (updates.deviceStatus) updateData.device_status = updates.deviceStatus;
  if (updates.lastSyncTime) updateData.last_sync_time = new Date(updates.lastSyncTime).toISOString();
  if (updates.passportImageUrl) updateData.passport_image_url = updates.passportImageUrl;
  if (updates.speed !== undefined) updateData.speed = updates.speed;
  if (updates.speedUnit) updateData.speed_unit = updates.speedUnit;
  if (updates.speedUpdatedAt) updateData.speed_updated_at = updates.speedUpdatedAt;
  if (updates.location) {
    updateData.location_latitude = updates.location.latitude;
    updateData.location_longitude = updates.location.longitude;
    updateData.location_timestamp = new Date(updates.location.timestamp).toISOString();
  }
  
  return updateData;
};

// API Service Class
export class AnimalApiService {
  /**
   * Fetch all animals from the database
   */
  static async fetchAnimals(): Promise<Animal[]> {
    // Get current user for defense-in-depth filtering
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const { data, error } = await supabase
      .from('animals')
      .select('*')
      .eq('user_id', user.id); // Defense-in-depth: explicit user filtering
      
    logApiCall('fetchAnimals', data, error);
    
    if (error) {
      console.error('Error fetching animals:', error);
      throw new Error(`Failed to fetch animals: ${error.message}`);
    }
    
    if (!data) {
      return [];
    }
    
    return data.map(transformDbToAnimal);
  }
  
  /**
   * Add a new animal to the database
   */
  static async addAnimal(animal: Omit<Animal, 'id'>): Promise<Animal> {
    try {
      // Get the current user ID
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        console.error('Error getting current user:', userError);
        toast.error('Authentication required. Please log in again.');
        throw new Error('User not authenticated');
      }
      
      const animalData = {
        ...transformAnimalToDb(animal),
        user_id: user.id // Add the current user's ID
      };
      
      const { data, error } = await supabase
        .from('animals')
        .insert(animalData)
        .select()
        .single();
        
      logApiCall('addAnimal', data, error);
      
      if (error) {
        console.error('Error adding animal:', error);
        toast.error(`Failed to add animal: ${error.message}`);
        throw new Error(`Failed to add animal: ${error.message}`);
      }
      
      toast.success(`${animal.name} has been added successfully!`);
      return transformDbToAnimal(data);
    } catch (error: any) {
      console.error('Error adding animal:', error);
      if (!error.message.includes('Failed to add animal')) {
        toast.error('Failed to add animal. Please try again.');
      }
      throw error;
    }
  }
  
  /**
   * Update an existing animal in the database
   */
  static async updateAnimal(id: string, updates: Partial<Animal>): Promise<void> {
    // Get current user for defense-in-depth filtering
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const updateData = transformAnimalUpdatesToDb(updates);
    
    const { error } = await supabase
      .from('animals')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id); // Defense-in-depth: ensure user owns animal
      
    logApiCall('updateAnimal', { id, ...updateData }, error);
    
    if (error) {
      console.error('Error updating animal:', error);
      throw new Error(`Failed to update animal: ${error.message}`);
    }
  }
  
  /**
   * Delete an animal from the database
   */
  static async deleteAnimal(id: string): Promise<void> {
    // Get current user for defense-in-depth filtering
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const { error } = await supabase
      .from('animals')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id); // Defense-in-depth: ensure user owns animal
      
    logApiCall('deleteAnimal', { id }, error);
    
    if (error) {
      console.error('Error deleting animal:', error);
      throw new Error(`Failed to delete animal: ${error.message}`);
    }
  }
  
  /**
   * Update device status for an animal
   */
  static async updateDeviceStatus(animalId: string, deviceStatus: Animal['deviceStatus']): Promise<void> {
    const updateData = {
      device_status: deviceStatus,
      last_sync_time: deviceStatus === 'connected' ? new Date().toISOString() : undefined
    };
    
    const { error } = await supabase
      .from('animals')
      .update(updateData)
      .eq('id', animalId);
      
    logApiCall('updateDeviceStatus', { animalId, ...updateData }, error);
    
    if (error) {
      console.error('Error updating device status:', error);
      throw new Error(`Failed to update device status: ${error.message}`);
    }
  }
  
  /**
   * Record animal speed and update the animal record
   */
  static async updateAnimalSpeed(animalId: string, speed: number, speedUnit: string = 'km/h'): Promise<void> {
    const now = new Date().toISOString();
    
    // Create a new speed record
    const speedRecord = {
      animal_id: animalId,
      user_id: (await supabase.auth.getUser()).data.user?.id,
      speed,
      speed_unit: speedUnit,
      recorded_at: now
    };
    
    const { error } = await supabase
      .from('speed_records')
      .insert(speedRecord);
      
    logApiCall('updateAnimalSpeed', { animalId, speed, speedUnit }, error);
    
    if (error) {
      console.error('Error updating animal speed:', error);
      throw new Error(`Failed to update animal speed: ${error.message}`);
    }
  }
}

export default AnimalApiService;