import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../supabase/client';
import { toast } from 'sonner-native';

// Note: This service is ready for expo-notifications integration
// When expo-notifications is added to dependencies, uncomment the import below:
// import * as Notifications from 'expo-notifications';

interface PushNotificationData {
  title: string;
  body: string;
  data?: Record<string, any>;
  priority: 'low' | 'normal' | 'high' | 'critical';
  animalId?: string;
  alertType?: string;
}

class NotificationService {
  private isInitialized = false;
  private pushToken: string | null = null;
  
  /**
   * Initialize the notification service
   * Requests permissions and registers for push notifications
   */
  async initialize(): Promise<boolean> {
    try {
      // For now, we'll simulate the initialization
      // When expo-notifications is available, uncomment the code below:
      
      /*
      // Configure notification behavior
      await Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        }),
      });
      
      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        console.log('Push notification permissions not granted');
        return false;
      }
      
      // Get push token
      const token = (await Notifications.getExpoPushTokenAsync()).data;
      this.pushToken = token;
      
      // Store token in database
      await this.storePushToken(token);
      */
      
      // Simulate successful initialization
      this.isInitialized = true;
      console.log('✅ Notification service initialized (simulation mode)');
      
      // Store a mock token for development
      this.pushToken = `mock-token-${Date.now()}`;
      await this.storePushToken(this.pushToken);
      
      return true;
    } catch (error) {
      console.error('Error initializing notifications:', error);
      return false;
    }
  }
  
  /**
   * Store push token in user profile
   */
  private async storePushToken(token: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;
      
      const { error } = await supabase
        .from('users')
        .update({ 
          push_token: token,
          push_notifications_enabled: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);
      
      if (error) {
        console.error('Error storing push token:', error);
      } else {
        console.log('✅ Push token stored successfully');
      }
    } catch (error) {
      console.error('Error storing push token:', error);
    }
  }
  
  /**
   * Send a local notification (for testing)
   */
  async sendLocalNotification(data: PushNotificationData): Promise<void> {
    try {
      // For now, show a toast notification as a fallback
      // When expo-notifications is available, uncomment the code below:
      
      /*
      await Notifications.scheduleNotificationAsync({
        content: {
          title: data.title,
          body: data.body,
          data: data.data || {},
          priority: this.mapPriorityToExpo(data.priority),
        },
        trigger: null, // Show immediately
      });
      */
      
      // Fallback: Show toast notification
      const priorityEmoji = this.getPriorityEmoji(data.priority);
      toast.error(`${priorityEmoji} ${data.title}: ${data.body}`);
      
    } catch (error) {
      console.error('Error sending local notification:', error);
    }
  }
  
  /**
   * Trigger push notification via backend
   */
  async triggerPushNotification(data: PushNotificationData): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;
      
      // Call backend function to send push notification
      const { error } = await supabase.functions.invoke('send-push-notification', {
        body: {
          user_id: user.id,
          title: data.title,
          body: data.body,
          data: data.data || {},
          priority: data.priority,
          animal_id: data.animalId,
          alert_type: data.alertType
        }
      });
      
      if (error) {
        console.error('Error triggering push notification:', error);
        // Fallback to local notification
        await this.sendLocalNotification(data);
      } else {
        console.log('✅ Push notification triggered successfully');
      }
    } catch (error) {
      console.error('Error triggering push notification:', error);
      // Fallback to local notification
      await this.sendLocalNotification(data);
    }
  }
  
  /**
   * Handle critical health alerts
   */
  async sendCriticalHealthAlert(animalName: string, alertType: string, message: string, animalId: string): Promise<void> {
    const data: PushNotificationData = {
      title: `🚨 Critical Alert - ${animalName}`,
      body: message,
      priority: 'critical',
      animalId,
      alertType,
      data: {
        type: 'health_alert',
        animal_id: animalId,
        alert_type: alertType,
        action: 'open_animal_detail'
      }
    };
    
    await this.triggerPushNotification(data);
  }
  
  /**
   * Handle high priority alerts
   */
  async sendHighPriorityAlert(animalName: string, alertType: string, message: string, animalId: string): Promise<void> {
    const data: PushNotificationData = {
      title: `⚠️ Alert - ${animalName}`,
      body: message,
      priority: 'high',
      animalId,
      alertType,
      data: {
        type: 'health_alert',
        animal_id: animalId,
        alert_type: alertType,
        action: 'open_animal_detail'
      }
    };
    
    await this.triggerPushNotification(data);
  }
  
  /**
   * Handle medication reminders
   */
  async sendMedicationReminder(animalName: string, medicationName: string, animalId: string): Promise<void> {
    const data: PushNotificationData = {
      title: `💊 Medication Reminder`,
      body: `Time to give ${animalName} their ${medicationName}`,
      priority: 'high',
      animalId,
      alertType: 'medication_reminder',
      data: {
        type: 'medication_reminder',
        animal_id: animalId,
        medication_name: medicationName,
        action: 'open_animal_detail'
      }
    };
    
    await this.triggerPushNotification(data);
  }
  
  /**
   * Handle feeding reminders
   */
  async sendFeedingReminder(animalName: string, feedType: string, animalId: string): Promise<void> {
    const data: PushNotificationData = {
      title: `🍽️ Feeding Time`,
      body: `Time to feed ${animalName} - ${feedType}`,
      priority: 'normal',
      animalId,
      alertType: 'feeding_reminder',
      data: {
        type: 'feeding_reminder',
        animal_id: animalId,
        feed_type: feedType,
        action: 'open_animal_detail'
      }
    };
    
    await this.triggerPushNotification(data);
  }
  
  /**
   * Check notification permissions
   */
  async checkPermissions(): Promise<boolean> {
    try {
      // When expo-notifications is available, uncomment:
      /*
      const { status } = await Notifications.getPermissionsAsync();
      return status === 'granted';
      */
      
      // For now, return true (simulation)
      return this.isInitialized;
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      return false;
    }
  }
  
  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      // When expo-notifications is available, uncomment:
      /*
      const { status } = await Notifications.requestPermissionsAsync();
      return status === 'granted';
      */
      
      // For now, simulate permission request
      return await this.initialize();
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }
  
  /**
   * Disable notifications for user
   */
  async disableNotifications(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;
      
      const { error } = await supabase
        .from('users')
        .update({ 
          push_notifications_enabled: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);
      
      if (error) {
        console.error('Error disabling notifications:', error);
      } else {
        console.log('✅ Notifications disabled');
        toast.success('Notifications disabled');
      }
    } catch (error) {
      console.error('Error disabling notifications:', error);
    }
  }
  
  /**
   * Enable notifications for user
   */
  async enableNotifications(): Promise<void> {
    try {
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        toast.error('Notification permissions required');
        return;
      }
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;
      
      const { error } = await supabase
        .from('users')
        .update({ 
          push_notifications_enabled: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);
      
      if (error) {
        console.error('Error enabling notifications:', error);
      } else {
        console.log('✅ Notifications enabled');
        toast.success('Notifications enabled');
      }
    } catch (error) {
      console.error('Error enabling notifications:', error);
    }
  }
  
  // Helper methods
  private getPriorityEmoji(priority: string): string {
    switch (priority) {
      case 'critical': return '🚨';
      case 'high': return '⚠️';
      case 'normal': return '🔔';
      case 'low': return '💬';
      default: return '📱';
    }
  }
  
  /*
  // Uncomment when expo-notifications is available
  private mapPriorityToExpo(priority: string): Notifications.AndroidNotificationPriority {
    switch (priority) {
      case 'critical': return Notifications.AndroidNotificationPriority.MAX;
      case 'high': return Notifications.AndroidNotificationPriority.HIGH;
      case 'normal': return Notifications.AndroidNotificationPriority.DEFAULT;
      case 'low': return Notifications.AndroidNotificationPriority.LOW;
      default: return Notifications.AndroidNotificationPriority.DEFAULT;
    }
  }
  */
  
  // Getters
  get isReady(): boolean {
    return this.isInitialized;
  }
  
  get token(): string | null {
    return this.pushToken;
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
export default notificationService;