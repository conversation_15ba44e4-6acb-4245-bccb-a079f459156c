/**
 * Automated Translation Services Integration
 * 
 * Supports multiple translation providers:
 * - Google Translate API
 * - DeepL API
 * - Azure Translator
 * - OpenAI GPT (for context-aware translations)
 */

import { supabase } from '../supabase/client';
import { toast } from 'sonner-native';

// Translation service types
export type TranslationProvider = 'google' | 'deepl' | 'azure' | 'openai';

export interface TranslationConfig {
  provider: TranslationProvider;
  apiKey: string;
  region?: string; // For Azure
  model?: string; // For OpenAI
  maxRequestsPerMinute?: number;
  maxCharactersPerRequest?: number;
  costPerCharacter?: number;
}

export interface TranslationRequest {
  text: string;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
  domain?: string; // 'medical', 'technical', 'general'
}

export interface TranslationResult {
  translatedText: string;
  confidence: number;
  provider: TranslationProvider;
  cost: number;
  charactersUsed: number;
  detectedLanguage?: string;
}

export interface TranslationBatch {
  id: string;
  requests: TranslationRequest[];
  results: TranslationResult[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  totalCost: number;
  createdAt: Date;
  completedAt?: Date;
}

// Language code mappings for different services
const LANGUAGE_MAPPINGS = {
  google: {
    'en': 'en',
    'ar': 'ar',
    'fr': 'fr',
    'ja': 'ja',
    'it': 'it',
    'tr': 'tr',
    'nl': 'nl',
    'es': 'es'
  },
  deepl: {
    'en': 'EN',
    'ar': 'AR', // Note: DeepL has limited Arabic support
    'fr': 'FR',
    'ja': 'JA',
    'it': 'IT',
    'tr': 'TR',
    'nl': 'NL',
    'es': 'ES'
  },
  azure: {
    'en': 'en',
    'ar': 'ar',
    'fr': 'fr',
    'ja': 'ja',
    'it': 'it',
    'tr': 'tr',
    'nl': 'nl',
    'es': 'es'
  },
  openai: {
    'en': 'English',
    'ar': 'Arabic',
    'fr': 'French',
    'ja': 'Japanese',
    'it': 'Italian',
    'tr': 'Turkish',
    'nl': 'Dutch',
    'es': 'Spanish'
  }
};

// Context templates for better translations
const CONTEXT_TEMPLATES = {
  ui: 'This is a user interface element in a mobile app for animal health monitoring.',
  medical: 'This is medical terminology related to animal health and veterinary care.',
  navigation: 'This is a navigation label or menu item in a mobile application.',
  action: 'This is an action button or command in a mobile app interface.',
  status: 'This describes a status or state in an animal health monitoring system.',
  alert: 'This is an alert or notification message for users.',
  form: 'This is a form label or input field description.',
  general: 'This is general text in a mobile application for animal health monitoring.'
};

/**
 * Google Translate Service
 */
class GoogleTranslateService {
  private apiKey: string;
  private baseUrl = 'https://translation.googleapis.com/language/translate/v2';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async translate(request: TranslationRequest): Promise<TranslationResult> {
    try {
      const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: request.text,
          source: LANGUAGE_MAPPINGS.google[request.sourceLanguage as keyof typeof LANGUAGE_MAPPINGS.google],
          target: LANGUAGE_MAPPINGS.google[request.targetLanguage as keyof typeof LANGUAGE_MAPPINGS.google],
          format: 'text'
        })
      });

      if (!response.ok) {
        throw new Error(`Google Translate API error: ${response.statusText}`);
      }

      const data = await response.json();
      const translation = data.data.translations[0];

      return {
        translatedText: translation.translatedText,
        confidence: 0.9, // Google doesn't provide confidence scores
        provider: 'google',
        cost: request.text.length * 0.00002, // $20 per 1M characters
        charactersUsed: request.text.length,
        detectedLanguage: translation.detectedSourceLanguage
      };
    } catch (error) {
      console.error('Google Translate error:', error);
      throw error;
    }
  }

  async translateBatch(requests: TranslationRequest[]): Promise<TranslationResult[]> {
    // Google Translate supports batch requests
    const batchRequest = {
      q: requests.map(r => r.text),
      source: requests[0].sourceLanguage,
      target: requests[0].targetLanguage,
      format: 'text'
    };

    try {
      const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(batchRequest)
      });

      if (!response.ok) {
        throw new Error(`Google Translate API error: ${response.statusText}`);
      }

      const data = await response.json();
      
      return data.data.translations.map((translation: any, index: number) => ({
        translatedText: translation.translatedText,
        confidence: 0.9,
        provider: 'google' as TranslationProvider,
        cost: requests[index].text.length * 0.00002,
        charactersUsed: requests[index].text.length,
        detectedLanguage: translation.detectedSourceLanguage
      }));
    } catch (error) {
      console.error('Google Translate batch error:', error);
      throw error;
    }
  }
}

/**
 * DeepL Translation Service
 */
class DeepLService {
  private apiKey: string;
  private baseUrl = 'https://api-free.deepl.com/v2/translate'; // Use api.deepl.com for pro

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async translate(request: TranslationRequest): Promise<TranslationResult> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `DeepL-Auth-Key ${this.apiKey}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          text: request.text,
          source_lang: LANGUAGE_MAPPINGS.deepl[request.sourceLanguage as keyof typeof LANGUAGE_MAPPINGS.deepl],
          target_lang: LANGUAGE_MAPPINGS.deepl[request.targetLanguage as keyof typeof LANGUAGE_MAPPINGS.deepl],
          formality: 'default'
        })
      });

      if (!response.ok) {
        throw new Error(`DeepL API error: ${response.statusText}`);
      }

      const data = await response.json();
      const translation = data.translations[0];

      return {
        translatedText: translation.text,
        confidence: 0.95, // DeepL generally has high quality
        provider: 'deepl',
        cost: request.text.length * 0.00002, // Similar pricing to Google
        charactersUsed: request.text.length,
        detectedLanguage: translation.detected_source_language
      };
    } catch (error) {
      console.error('DeepL error:', error);
      throw error;
    }
  }

  async translateBatch(requests: TranslationRequest[]): Promise<TranslationResult[]> {
    // DeepL supports multiple texts in one request
    const texts = requests.map(r => r.text);
    
    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `DeepL-Auth-Key ${this.apiKey}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          text: texts,
          source_lang: LANGUAGE_MAPPINGS.deepl[requests[0].sourceLanguage as keyof typeof LANGUAGE_MAPPINGS.deepl],
          target_lang: LANGUAGE_MAPPINGS.deepl[requests[0].targetLanguage as keyof typeof LANGUAGE_MAPPINGS.deepl],
          formality: 'default'
        })
      });

      if (!response.ok) {
        throw new Error(`DeepL API error: ${response.statusText}`);
      }

      const data = await response.json();
      
      return data.translations.map((translation: any, index: number) => ({
        translatedText: translation.text,
        confidence: 0.95,
        provider: 'deepl' as TranslationProvider,
        cost: requests[index].text.length * 0.00002,
        charactersUsed: requests[index].text.length,
        detectedLanguage: translation.detected_source_language
      }));
    } catch (error) {
      console.error('DeepL batch error:', error);
      throw error;
    }
  }
}

/**
 * OpenAI GPT Translation Service (Context-aware)
 */
class OpenAITranslationService {
  private apiKey: string;
  private baseUrl = 'https://api.openai.com/v1/chat/completions';
  private model: string;

  constructor(apiKey: string, model = 'gpt-3.5-turbo') {
    this.apiKey = apiKey;
    this.model = model;
  }

  async translate(request: TranslationRequest): Promise<TranslationResult> {
    try {
      const context = request.context || CONTEXT_TEMPLATES.general;
      const sourceLanguage = LANGUAGE_MAPPINGS.openai[request.sourceLanguage as keyof typeof LANGUAGE_MAPPINGS.openai];
      const targetLanguage = LANGUAGE_MAPPINGS.openai[request.targetLanguage as keyof typeof LANGUAGE_MAPPINGS.openai];

      const prompt = `You are a professional translator specializing in mobile app localization for animal health applications.

Context: ${context}

Translate the following ${sourceLanguage} text to ${targetLanguage}. Maintain the tone, style, and technical accuracy. If it's a UI element, keep it concise and user-friendly.

Text to translate: "${request.text}"

Provide only the translation without any explanations or additional text.`;

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 200,
          temperature: 0.3 // Lower temperature for more consistent translations
        })
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.statusText}`);
      }

      const data = await response.json();
      const translatedText = data.choices[0].message.content.trim();

      // Estimate cost based on tokens (rough approximation)
      const estimatedTokens = prompt.length / 4 + translatedText.length / 4;
      const cost = estimatedTokens * 0.000002; // Approximate cost per token

      return {
        translatedText,
        confidence: 0.85, // Generally good but may need review
        provider: 'openai',
        cost,
        charactersUsed: request.text.length
      };
    } catch (error) {
      console.error('OpenAI translation error:', error);
      throw error;
    }
  }

  async translateBatch(requests: TranslationRequest[]): Promise<TranslationResult[]> {
    // Process requests sequentially to avoid rate limits
    const results: TranslationResult[] = [];
    
    for (const request of requests) {
      try {
        const result = await this.translate(request);
        results.push(result);
        
        // Add small delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`Failed to translate: ${request.text}`, error);
        // Add a fallback result
        results.push({
          translatedText: request.text, // Fallback to original
          confidence: 0,
          provider: 'openai',
          cost: 0,
          charactersUsed: request.text.length
        });
      }
    }
    
    return results;
  }
}

/**
 * Translation Service Manager
 */
export class TranslationServiceManager {
  private services: Map<TranslationProvider, any> = new Map();
  private config: Map<TranslationProvider, TranslationConfig> = new Map();
  private usageStats = {
    totalRequests: 0,
    totalCost: 0,
    totalCharacters: 0,
    requestsByProvider: {} as Record<TranslationProvider, number>,
    costByProvider: {} as Record<TranslationProvider, number>
  };

  constructor() {
    this.loadConfiguration();
  }

  private async loadConfiguration() {
    try {
      // Load API keys from Supabase secrets
      const { data: secrets } = await supabase.rpc('get_translation_secrets');
      
      if (secrets) {
        // Initialize services based on available API keys
        if (secrets.google_translate_api_key) {
          this.services.set('google', new GoogleTranslateService(secrets.google_translate_api_key));
          this.config.set('google', {
            provider: 'google',
            apiKey: secrets.google_translate_api_key,
            maxRequestsPerMinute: 100,
            maxCharactersPerRequest: 5000,
            costPerCharacter: 0.00002
          });
        }

        if (secrets.deepl_api_key) {
          this.services.set('deepl', new DeepLService(secrets.deepl_api_key));
          this.config.set('deepl', {
            provider: 'deepl',
            apiKey: secrets.deepl_api_key,
            maxRequestsPerMinute: 50,
            maxCharactersPerRequest: 5000,
            costPerCharacter: 0.00002
          });
        }

        if (secrets.openai_api_key) {
          this.services.set('openai', new OpenAITranslationService(secrets.openai_api_key));
          this.config.set('openai', {
            provider: 'openai',
            apiKey: secrets.openai_api_key,
            model: 'gpt-3.5-turbo',
            maxRequestsPerMinute: 20,
            maxCharactersPerRequest: 2000,
            costPerCharacter: 0.00005
          });
        }
      }
    } catch (error) {
      console.error('Failed to load translation configuration:', error);
    }
  }

  async translate(
    text: string,
    sourceLanguage: string,
    targetLanguage: string,
    options: {
      provider?: TranslationProvider;
      context?: string;
      domain?: string;
      fallbackProviders?: TranslationProvider[];
    } = {}
  ): Promise<TranslationResult> {
    const { provider, context, domain, fallbackProviders = ['google', 'deepl', 'openai'] } = options;
    
    // Determine which provider to use
    const providersToTry = provider ? [provider] : fallbackProviders;
    
    for (const providerName of providersToTry) {
      const service = this.services.get(providerName);
      if (!service) continue;

      try {
        const request: TranslationRequest = {
          text,
          sourceLanguage,
          targetLanguage,
          context: context || this.getContextForDomain(domain),
          domain
        };

        const result = await service.translate(request);
        
        // Update usage stats
        this.updateUsageStats(providerName, result);
        
        // Log translation for audit
        await this.logTranslation(request, result);
        
        return result;
      } catch (error) {
        console.error(`Translation failed with ${providerName}:`, error);
        continue; // Try next provider
      }
    }

    throw new Error('All translation providers failed');
  }

  async translateBatch(
    requests: TranslationRequest[],
    provider?: TranslationProvider
  ): Promise<TranslationResult[]> {
    const providerName = provider || this.getBestProviderForBatch(requests);
    const service = this.services.get(providerName);
    
    if (!service) {
      throw new Error(`Translation provider ${providerName} not available`);
    }

    try {
      const results = await service.translateBatch(requests);
      
      // Update usage stats
      results.forEach(result => this.updateUsageStats(providerName, result));
      
      // Log batch translation
      await this.logBatchTranslation(requests, results, providerName);
      
      return results;
    } catch (error) {
      console.error(`Batch translation failed with ${providerName}:`, error);
      throw error;
    }
  }

  private getContextForDomain(domain?: string): string {
    switch (domain) {
      case 'ui': return CONTEXT_TEMPLATES.ui;
      case 'medical': return CONTEXT_TEMPLATES.medical;
      case 'navigation': return CONTEXT_TEMPLATES.navigation;
      case 'action': return CONTEXT_TEMPLATES.action;
      case 'status': return CONTEXT_TEMPLATES.status;
      case 'alert': return CONTEXT_TEMPLATES.alert;
      case 'form': return CONTEXT_TEMPLATES.form;
      default: return CONTEXT_TEMPLATES.general;
    }
  }

  private getBestProviderForBatch(requests: TranslationRequest[]): TranslationProvider {
    const totalCharacters = requests.reduce((sum, req) => sum + req.text.length, 0);
    
    // Choose provider based on batch size and availability
    if (totalCharacters > 10000 && this.services.has('google')) {
      return 'google'; // Best for large batches
    } else if (this.services.has('deepl')) {
      return 'deepl'; // Best quality
    } else if (this.services.has('google')) {
      return 'google'; // Reliable fallback
    } else {
      return 'openai'; // Context-aware fallback
    }
  }

  private updateUsageStats(provider: TranslationProvider, result: TranslationResult) {
    this.usageStats.totalRequests++;
    this.usageStats.totalCost += result.cost;
    this.usageStats.totalCharacters += result.charactersUsed;
    
    if (!this.usageStats.requestsByProvider[provider]) {
      this.usageStats.requestsByProvider[provider] = 0;
      this.usageStats.costByProvider[provider] = 0;
    }
    
    this.usageStats.requestsByProvider[provider]++;
    this.usageStats.costByProvider[provider] += result.cost;
  }

  private async logTranslation(request: TranslationRequest, result: TranslationResult) {
    try {
      await supabase.from('translation_logs').insert({
        source_text: request.text,
        translated_text: result.translatedText,
        source_language: request.sourceLanguage,
        target_language: request.targetLanguage,
        provider: result.provider,
        confidence: result.confidence,
        cost: result.cost,
        characters_used: result.charactersUsed,
        context: request.context,
        domain: request.domain,
        created_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to log translation:', error);
    }
  }

  private async logBatchTranslation(
    requests: TranslationRequest[],
    results: TranslationResult[],
    provider: TranslationProvider
  ) {
    try {
      const batchLog = {
        id: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        provider,
        request_count: requests.length,
        total_cost: results.reduce((sum, r) => sum + r.cost, 0),
        total_characters: results.reduce((sum, r) => sum + r.charactersUsed, 0),
        average_confidence: results.reduce((sum, r) => sum + r.confidence, 0) / results.length,
        created_at: new Date().toISOString()
      };

      await supabase.from('translation_batch_logs').insert(batchLog);
    } catch (error) {
      console.error('Failed to log batch translation:', error);
    }
  }

  getUsageStats() {
    return { ...this.usageStats };
  }

  getAvailableProviders(): TranslationProvider[] {
    return Array.from(this.services.keys());
  }

  isProviderAvailable(provider: TranslationProvider): boolean {
    return this.services.has(provider);
  }
}

// Export singleton instance
export const translationManager = new TranslationServiceManager();