
import React, { useEffect } from 'react';
import { Navigation<PERSON>ontainer, DefaultTheme, DarkTheme } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Toaster } from 'sonner-native';
import RootNavigator from './navigation';
import { COLORS } from './constants/colors';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { useAnimalStore } from './store/animalStore';
import { useVitalsStore } from './store/vitalsStore';
import { useFeedingStore } from './store/feedingStore';
import { useMedicationStore } from './store/medicationStore';
import { useVaccinationStore } from './store/vaccinationStore';
import { useAuth } from './contexts/AuthContext';
import { useUserStore } from './store/userStore';
import { ActivityIndicator, View } from 'react-native';
import notificationService from './services/NotificationService';

const AppContent = () => {
  const { isDarkMode, colors } = useTheme();
  const { session, isLoading } = useAuth();
  
  const fetchAnimals = useAnimalStore(state => state.fetchAnimals);
  const fetchVitals = useVitalsStore(state => state.fetchVitals);
  const fetchFeedings = useFeedingStore(state => state.fetchFeedings);
  const fetchMedications = useMedicationStore(state => state.fetchMedications);
  const fetchVaccinations = useVaccinationStore(state => state.fetchVaccinations);
  const fetchProfile = useUserStore(state => state.fetchProfile);
  
  // Fetch data when authenticated - but only once when session changes
  useEffect(() => {
    if (session) {
      const loadData = async () => {
        try {
          await fetchProfile();
          await Promise.all([
            fetchAnimals(),
            fetchVitals(),
            fetchFeedings(),
            fetchMedications(),
            fetchVaccinations()
          ]);
        } catch (error) {
          console.error('Error loading data:', error);
        }
      };
      
      loadData();
    }
  }, [session?.user?.id]); // Only depend on the user ID, not the entire session object
  
  // Initialize notification service when user is authenticated
  useEffect(() => {
    if (session) {
      notificationService.initialize().then((success) => {
        if (success) {
          console.log('✅ Notification service initialized successfully');
        } else {
          console.log('⚠️ Notification service initialization failed');
        }
      });
    }
  }, [session?.user?.id]);

  // Create a custom theme that properly extends the base theme
  const navigationTheme = isDarkMode 
    ? {
        ...DefaultTheme,
        ...DarkTheme,
        colors: {
          ...DarkTheme.colors,
          primary: colors.primary,
          background: colors.background,
          card: colors.card,
          text: colors.text,
          border: colors.border,
        },
      }
    : {
        ...DefaultTheme,
        colors: {
          ...DefaultTheme.colors,
          primary: colors.primary,
          background: colors.background,
          card: colors.card,
          text: colors.text,
          border: colors.border,
        },
      };

  // Show loading indicator while authentication is being checked
  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <NavigationContainer theme={navigationTheme}>
      <StatusBar
        style={isDarkMode ? 'light' : 'dark'}
      />
      <Toaster theme={isDarkMode ? 'dark' : 'light'} richColors />
      <RootNavigator />
    </NavigationContainer>
  );
};

/**
 * Do not add navigation stack here. Add it in the navigation folder.
 */
export default function App() {
  return (
    <SafeAreaProvider>
      <LanguageProvider>
        <ThemeProvider>
          <AuthProvider>
            <AppContent />
          </AuthProvider>
        </ThemeProvider>
      </LanguageProvider>
    </SafeAreaProvider>
  );
}
