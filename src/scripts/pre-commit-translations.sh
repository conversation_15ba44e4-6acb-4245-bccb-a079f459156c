#!/bin/bash

# Pre-commit hook for translation validation
# This script runs before each commit to ensure translation consistency

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🌍 Pre-commit Translation Check${NC}"
echo ""

# Check if translation files are being modified
translation_files_changed=$(git diff --cached --name-only | grep -E "assets/translations/|components/|screens/|hooks/|contexts/" | wc -l)

if [ "$translation_files_changed" -eq 0 ]; then
    echo -e "${GREEN}✅ No translation-related files changed, skipping validation${NC}"
    exit 0
fi

echo -e "${YELLOW}🔍 Translation-related files detected, running validation...${NC}"
echo ""

# Run translation validation
echo -e "${BLUE}Running translation structure validation...${NC}"
if node scripts/translation-utils.js validate > /tmp/translation-validation.log 2>&1; then
    echo -e "${GREEN}✅ Translation structure validation passed${NC}"
else
    echo -e "${RED}❌ Translation structure validation failed${NC}"
    echo ""
    echo -e "${YELLOW}Validation output:${NC}"
    cat /tmp/translation-validation.log
    echo ""
    echo -e "${RED}Commit blocked due to translation validation failure${NC}"
    echo -e "${YELLOW}Please fix the translation issues and try again${NC}"
    exit 1
fi

# Check for missing translations
echo -e "${BLUE}Checking for missing translation keys...${NC}"
node scripts/translation-utils.js check > /tmp/translation-check.log 2>&1

# Extract missing count
missing_count=$(grep "Total missing keys:" /tmp/translation-check.log | grep -o '[0-9]\+' || echo "0")

if [ "$missing_count" -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Found $missing_count missing translation keys${NC}"
    echo ""
    echo -e "${YELLOW}Missing keys summary:${NC}"
    cat /tmp/translation-check.log
    echo ""
    
    # Ask user what to do
    echo -e "${YELLOW}Options:${NC}"
    echo "1. Auto-fix by adding English placeholders (recommended)"
    echo "2. Commit anyway (not recommended)"
    echo "3. Cancel commit"
    echo ""
    read -p "Choose option (1/2/3): " choice
    
    case $choice in
        1)
            echo -e "${BLUE}🔧 Auto-fixing missing translations...${NC}"
            if node scripts/translation-utils.js fix; then
                echo -e "${GREEN}✅ Missing translations fixed with English placeholders${NC}"
                echo -e "${YELLOW}📝 Note: Keys marked with [EN] need manual translation${NC}"
                
                # Stage the fixed files
                git add assets/translations/
                echo -e "${GREEN}✅ Translation files staged for commit${NC}"
            else
                echo -e "${RED}❌ Failed to auto-fix translations${NC}"
                exit 1
            fi
            ;;
        2)
            echo -e "${YELLOW}⚠️  Proceeding with missing translations (not recommended)${NC}"
            echo -e "${YELLOW}Please run 'npm run translations:fix' after commit${NC}"
            ;;
        3)
            echo -e "${BLUE}Commit cancelled${NC}"
            exit 1
            ;;
        *)
            echo -e "${RED}Invalid option, cancelling commit${NC}"
            exit 1
            ;;
    esac
else
    echo -e "${GREEN}✅ No missing translation keys found${NC}"
fi

# Final validation after potential fixes
echo -e "${BLUE}Running final validation...${NC}"
if node scripts/translation-utils.js validate > /dev/null 2>&1; then
    echo -e "${GREEN}✅ All translation validations passed${NC}"
    echo -e "${GREEN}🎉 Ready to commit!${NC}"
else
    echo -e "${RED}❌ Final validation failed${NC}"
    echo -e "${RED}Commit blocked${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ Pre-commit translation check completed successfully${NC}"
