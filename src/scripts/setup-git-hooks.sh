#!/bin/bash

# Setup script for Git hooks and CI/CD translation validation

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🔧 Setting up Translation Validation Git Hooks${NC}"
echo ""

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo -e "${YELLOW}⚠️  Not in a Git repository. Please run this from the project root.${NC}"
    exit 1
fi

# Create .git/hooks directory if it doesn't exist
mkdir -p .git/hooks

# Make the pre-commit script executable
chmod +x scripts/pre-commit-translations.sh

# Create or update pre-commit hook
echo -e "${BLUE}Installing pre-commit hook...${NC}"

cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash

# Translation validation pre-commit hook
# This hook runs translation validation before each commit

# Check if translation validation script exists
if [ -f "scripts/pre-commit-translations.sh" ]; then
    echo "Running translation validation..."
    ./scripts/pre-commit-translations.sh
else
    echo "Warning: Translation validation script not found"
    echo "Skipping translation validation"
fi

# You can add other pre-commit checks here
# For example: linting, formatting, tests, etc.

EOF

# Make the hook executable
chmod +x .git/hooks/pre-commit

echo -e "${GREEN}✅ Pre-commit hook installed${NC}"

# Create commit-msg hook for translation-related commits
echo -e "${BLUE}Installing commit-msg hook...${NC}"

cat > .git/hooks/commit-msg << 'EOF'
#!/bin/bash

# Commit message hook for translation-related commits
# Adds helpful information for translation commits

commit_file="$1"
commit_msg=$(cat "$commit_file")

# Check if this is a translation-related commit
if git diff --cached --name-only | grep -q "assets/translations/"; then
    # Check if commit message already mentions translations
    if ! echo "$commit_msg" | grep -qi "translation\|translate\|🌍"; then
        # Add translation emoji if not present
        if ! echo "$commit_msg" | grep -q "🌍"; then
            echo "🌍 $commit_msg" > "$commit_file"
        fi
    fi
fi

EOF

chmod +x .git/hooks/commit-msg

echo -e "${GREEN}✅ Commit-msg hook installed${NC}"

# Test the setup
echo -e "${BLUE}Testing translation validation setup...${NC}"

if node scripts/translation-utils.js validate > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Translation validation test passed${NC}"
else
    echo -e "${YELLOW}⚠️  Translation validation test failed (this is expected if there are issues)${NC}"
fi

# Create .gitignore entries for reports
echo -e "${BLUE}Adding report directories to .gitignore...${NC}"

if [ -f ".gitignore" ]; then
    # Add translation report directories if not already present
    if ! grep -q "reports/" .gitignore; then
        echo "" >> .gitignore
        echo "# Translation validation reports" >> .gitignore
        echo "reports/" >> .gitignore
        echo "translation-*.log" >> .gitignore
        echo "translation-*.md" >> .gitignore
    fi
fi

echo ""
echo -e "${GREEN}🎉 Git hooks setup complete!${NC}"
echo ""
echo -e "${BLUE}What's been installed:${NC}"
echo "  ✅ Pre-commit hook for translation validation"
echo "  ✅ Commit-msg hook for translation commits"
echo "  ✅ .gitignore entries for reports"
echo ""
echo -e "${BLUE}Available commands:${NC}"
echo "  npm run translations:check    - Check for missing keys"
echo "  npm run translations:fix      - Add missing keys with placeholders"
echo "  npm run translations:validate - Validate translation structure"
echo ""
echo -e "${BLUE}CI/CD Integration:${NC}"
echo "  ✅ GitHub Actions workflow configured"
echo "  ✅ Pre-commit validation enabled"
echo "  ✅ Automatic PR comments for missing translations"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "  1. Add the NPM scripts to package.json (see scripts/package-scripts.json)"
echo "  2. Test by making a commit that touches translation files"
echo "  3. Configure CI environment variables if needed"
echo ""
