# CI/CD Environment Configuration for Translation Validation
# Copy this file and customize for your CI/CD environment

# Translation validation mode
# Set to 'true' for strict validation (fails on any missing keys)
# Set to 'false' for lenient validation (allows some missing keys)
TRANSLATION_STRICT_MODE=false

# Maximum allowed missing translation keys before failing
# Only applies when STRICT_MODE is false
MAX_MISSING_KEYS=0

# Output format for CI reports
# Options: 'text' or 'json'
TRANSLATION_OUTPUT_FORMAT=text

# Save detailed reports to files
# Set to 'true' to save JSON reports for analysis
SAVE_TRANSLATION_REPORT=true

# Directory for saving translation reports
TRANSLATION_REPORT_DIR=./reports

# GitHub Actions specific settings
# These are automatically set by GitHub Actions
# GITHUB_TOKEN=<automatically_provided>
# GITHUB_REPOSITORY=<automatically_provided>
# GITHUB_EVENT_NAME=<automatically_provided>

# Slack/Discord webhook for notifications (optional)
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
# DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/...

# Email notifications (optional)
# NOTIFICATION_EMAIL=<EMAIL>

# Custom validation rules (advanced)
# REQUIRE_ALL_LANGUAGES=true
# ALLOW_EXTRA_KEYS=false
# VALIDATE_NESTED_STRUCTURE=true

# Performance settings
# VALIDATION_TIMEOUT=30000  # 30 seconds
# PARALLEL_VALIDATION=true

# Development vs Production settings
# In development, you might want more lenient validation
# In production, you want strict validation
# NODE_ENV=production

# Branch-specific settings
# You can have different validation rules for different branches
# MAIN_BRANCH_STRICT=true
# DEVELOP_BRANCH_STRICT=false
# FEATURE_BRANCH_STRICT=false
