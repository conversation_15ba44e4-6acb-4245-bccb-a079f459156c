#!/usr/bin/env node

/**
 * Automated Translation Script
 * 
 * This script automatically translates [EN] prefixed keys using
 * various translation services (Google Translate, DeepL, OpenAI)
 */

const fs = require('fs');
const path = require('path');
const { extractKeys, loadTranslationFile, saveTranslationFile } = require('./translation-utils');

// Configuration
const TRANSLATIONS_DIR = path.join(__dirname, '../assets/translations');
const LANGUAGES = ['ar', 'fr', 'ja', 'it', 'tr', 'nl', 'es'];
const SOURCE_LANGUAGE = 'en';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Translation service configurations
const TRANSLATION_SERVICES = {
  google: {
    name: 'Google Translate',
    endpoint: 'https://translation.googleapis.com/language/translate/v2',
    keyEnvVar: 'GOOGLE_TRANSLATE_API_KEY',
    costPerChar: 0.00002,
    maxCharsPerRequest: 5000,
    rateLimitPerMinute: 100
  },
  deepl: {
    name: 'DeepL',
    endpoint: 'https://api-free.deepl.com/v2/translate',
    keyEnvVar: 'DEEPL_API_KEY',
    costPerChar: 0.00002,
    maxCharsPerRequest: 5000,
    rateLimitPerMinute: 50
  },
  openai: {
    name: 'OpenAI GPT',
    endpoint: 'https://api.openai.com/v1/chat/completions',
    keyEnvVar: 'OPENAI_API_KEY',
    costPerChar: 0.00005,
    maxCharsPerRequest: 2000,
    rateLimitPerMinute: 20
  }
};

// Language mappings for different services
const LANGUAGE_MAPPINGS = {
  google: {
    'ar': 'ar', 'fr': 'fr', 'ja': 'ja', 'it': 'it', 'tr': 'tr', 'nl': 'nl', 'es': 'es'
  },
  deepl: {
    'ar': 'AR', 'fr': 'FR', 'ja': 'JA', 'it': 'IT', 'tr': 'TR', 'nl': 'NL', 'es': 'ES'
  },
  openai: {
    'ar': 'Arabic', 'fr': 'French', 'ja': 'Japanese', 'it': 'Italian', 
    'tr': 'Turkish', 'nl': 'Dutch', 'es': 'Spanish'
  }
};

// Context detection patterns
const CONTEXT_PATTERNS = {
  ui: /button|menu|tab|screen|view|modal|dialog/i,
  medical: /health|vital|medication|vaccine|symptom|diagnosis/i,
  navigation: /home|back|next|previous|navigate|go to/i,
  action: /add|edit|delete|save|cancel|submit|create/i,
  status: /status|state|condition|active|inactive|pending/i,
  alert: /alert|warning|error|success|notification/i,
  form: /input|field|label|placeholder|required|optional/i
};

/**
 * Detect context from translation key and text
 */
function detectContext(key, text) {
  const combined = `${key} ${text}`.toLowerCase();
  
  for (const [context, pattern] of Object.entries(CONTEXT_PATTERNS)) {
    if (pattern.test(combined)) {
      return context;
    }
  }
  
  return 'general';
}

/**
 * Get nested value from object using dot notation
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Set nested value in object using dot notation
 */
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  target[lastKey] = value;
}

/**
 * Find keys that need translation (marked with [EN] prefix)
 */
function findKeysNeedingTranslation() {
  console.log(`${colors.blue}🔍 Scanning for keys needing translation...${colors.reset}\n`);
  
  const keysToTranslate = {};
  let totalKeys = 0;
  
  for (const language of LANGUAGES) {
    console.log(`${colors.cyan}Checking ${language}...${colors.reset}`);
    
    const translations = loadTranslationFile(language);
    if (!translations) {
      console.log(`${colors.yellow}  ⚠️  Could not load ${language} translations${colors.reset}`);
      continue;
    }
    
    const keys = extractKeys(translations);
    const needsTranslation = [];
    
    for (const key of keys) {
      const value = getNestedValue(translations, key);
      if (typeof value === 'string' && value.startsWith('[EN] ')) {
        needsTranslation.push({
          key,
          originalText: value.substring(5), // Remove [EN] prefix
          context: detectContext(key, value)
        });
      }
    }
    
    if (needsTranslation.length > 0) {
      keysToTranslate[language] = needsTranslation;
      totalKeys += needsTranslation.length;
      console.log(`${colors.yellow}  📝 Found ${needsTranslation.length} keys needing translation${colors.reset}`);
    } else {
      console.log(`${colors.green}  ✅ All keys translated${colors.reset}`);
    }
  }
  
  console.log(`\n${colors.magenta}📊 SUMMARY${colors.reset}`);
  console.log(`${colors.magenta}Total keys needing translation: ${totalKeys}${colors.reset}`);
  
  return keysToTranslate;
}

/**
 * Google Translate API integration
 */
async function translateWithGoogle(text, targetLanguage) {
  const apiKey = process.env.GOOGLE_TRANSLATE_API_KEY;
  if (!apiKey) {
    throw new Error('Google Translate API key not found in environment variables');
  }
  
  const url = `${TRANSLATION_SERVICES.google.endpoint}?key=${apiKey}`;
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        q: text,
        source: 'en',
        target: LANGUAGE_MAPPINGS.google[targetLanguage],
        format: 'text'
      })
    });
    
    if (!response.ok) {
      throw new Error(`Google Translate API error: ${response.statusText}`);
    }
    
    const data = await response.json();
    return {
      translatedText: data.data.translations[0].translatedText,
      confidence: 0.9,
      provider: 'google',
      cost: text.length * TRANSLATION_SERVICES.google.costPerChar
    };
  } catch (error) {
    console.error('Google Translate error:', error);
    throw error;
  }
}

/**
 * DeepL API integration
 */
async function translateWithDeepL(text, targetLanguage) {
  const apiKey = process.env.DEEPL_API_KEY;
  if (!apiKey) {
    throw new Error('DeepL API key not found in environment variables');
  }
  
  try {
    const response = await fetch(TRANSLATION_SERVICES.deepl.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `DeepL-Auth-Key ${apiKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        text: text,
        source_lang: 'EN',
        target_lang: LANGUAGE_MAPPINGS.deepl[targetLanguage],
        formality: 'default'
      })
    });
    
    if (!response.ok) {
      throw new Error(`DeepL API error: ${response.statusText}`);
    }
    
    const data = await response.json();
    return {
      translatedText: data.translations[0].text,
      confidence: 0.95,
      provider: 'deepl',
      cost: text.length * TRANSLATION_SERVICES.deepl.costPerChar
    };
  } catch (error) {
    console.error('DeepL error:', error);
    throw error;
  }
}

/**
 * OpenAI GPT translation (context-aware)
 */
async function translateWithOpenAI(text, targetLanguage, context = 'general') {
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    throw new Error('OpenAI API key not found in environment variables');
  }
  
  const contextPrompts = {
    ui: 'This is a user interface element in a mobile app for animal health monitoring.',
    medical: 'This is medical terminology related to animal health and veterinary care.',
    navigation: 'This is a navigation label or menu item in a mobile application.',
    action: 'This is an action button or command in a mobile app interface.',
    status: 'This describes a status or state in an animal health monitoring system.',
    alert: 'This is an alert or notification message for users.',
    form: 'This is a form label or input field description.',
    general: 'This is general text in a mobile application for animal health monitoring.'
  };
  
  const contextDescription = contextPrompts[context] || contextPrompts.general;
  const targetLanguageName = LANGUAGE_MAPPINGS.openai[targetLanguage];
  
  const prompt = `You are a professional translator specializing in mobile app localization for animal health applications.

Context: ${contextDescription}

Translate the following English text to ${targetLanguageName}. Maintain the tone, style, and technical accuracy. If it's a UI element, keep it concise and user-friendly.

Text to translate: "${text}"

Provide only the translation without any explanations or additional text.`;
  
  try {
    const response = await fetch(TRANSLATION_SERVICES.openai.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 200,
        temperature: 0.3
      })
    });
    
    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }
    
    const data = await response.json();
    const translatedText = data.choices[0].message.content.trim();
    
    // Estimate cost based on tokens
    const estimatedTokens = prompt.length / 4 + translatedText.length / 4;
    const cost = estimatedTokens * 0.000002;
    
    return {
      translatedText,
      confidence: 0.85,
      provider: 'openai',
      cost
    };
  } catch (error) {
    console.error('OpenAI translation error:', error);
    throw error;
  }
}

/**
 * Translate text using the specified provider
 */
async function translateText(text, targetLanguage, provider, context) {
  switch (provider) {
    case 'google':
      return await translateWithGoogle(text, targetLanguage);
    case 'deepl':
      return await translateWithDeepL(text, targetLanguage);
    case 'openai':
      return await translateWithOpenAI(text, targetLanguage, context);
    default:
      throw new Error(`Unknown translation provider: ${provider}`);
  }
}

/**
 * Get the best available translation provider
 */
function getBestAvailableProvider() {
  // Check for API keys in order of preference
  if (process.env.DEEPL_API_KEY) return 'deepl'; // Best quality
  if (process.env.GOOGLE_TRANSLATE_API_KEY) return 'google'; // Most reliable
  if (process.env.OPENAI_API_KEY) return 'openai'; // Context-aware
  
  throw new Error('No translation API keys found. Please set DEEPL_API_KEY, GOOGLE_TRANSLATE_API_KEY, or OPENAI_API_KEY');
}

/**
 * Perform automated translation
 */
async function performAutomatedTranslation(keysToTranslate, options = {}) {
  const {
    provider = getBestAvailableProvider(),
    dryRun = false,
    batchSize = 10,
    delayBetweenRequests = 1000
  } = options;
  
  console.log(`\n${colors.blue}🤖 Starting automated translation...${colors.reset}`);
  console.log(`${colors.cyan}Provider: ${TRANSLATION_SERVICES[provider].name}${colors.reset}`);
  console.log(`${colors.cyan}Dry run: ${dryRun ? 'Yes' : 'No'}${colors.reset}\n`);
  
  const results = {
    totalTranslated: 0,
    totalCost: 0,
    byLanguage: {},
    errors: []
  };
  
  for (const [language, keys] of Object.entries(keysToTranslate)) {
    console.log(`${colors.magenta}Translating ${language.toUpperCase()} (${keys.length} keys)...${colors.reset}`);
    
    const languageResults = {
      translated: 0,
      cost: 0,
      errors: []
    };
    
    // Load current translations
    const translations = loadTranslationFile(language);
    if (!translations) {
      console.log(`${colors.red}  ❌ Could not load ${language} translations${colors.reset}`);
      continue;
    }
    
    // Process keys in batches
    for (let i = 0; i < keys.length; i += batchSize) {
      const batch = keys.slice(i, i + batchSize);
      
      for (const { key, originalText, context } of batch) {
        try {
          console.log(`${colors.yellow}  🔄 Translating: ${key}${colors.reset}`);
          
          if (!dryRun) {
            const result = await translateText(originalText, language, provider, context);
            
            // Update the translation file
            setNestedValue(translations, key, result.translatedText);
            
            languageResults.translated++;
            languageResults.cost += result.cost;
            
            console.log(`${colors.green}    ✅ "${originalText}" → "${result.translatedText}"${colors.reset}`);
            
            // Add delay to respect rate limits
            await new Promise(resolve => setTimeout(resolve, delayBetweenRequests));
          } else {
            console.log(`${colors.blue}    🔍 Would translate: "${originalText}"${colors.reset}`);
            languageResults.translated++;
          }
        } catch (error) {
          console.log(`${colors.red}    ❌ Failed: ${error.message}${colors.reset}`);
          languageResults.errors.push({ key, error: error.message });
        }
      }
    }
    
    // Save updated translations
    if (!dryRun && languageResults.translated > 0) {
      saveTranslationFile(language, translations);
      console.log(`${colors.green}  💾 Saved ${language} translations${colors.reset}`);
    }
    
    results.byLanguage[language] = languageResults;
    results.totalTranslated += languageResults.translated;
    results.totalCost += languageResults.cost;
    results.errors.push(...languageResults.errors);
    
    console.log(`${colors.green}  ✅ Completed ${language}: ${languageResults.translated} translated, $${languageResults.cost.toFixed(4)} cost${colors.reset}\n`);
  }
  
  return results;
}

/**
 * Generate translation report
 */
function generateReport(results, keysToTranslate) {
  console.log(`${colors.magenta}📊 TRANSLATION REPORT${colors.reset}`);
  console.log('='.repeat(50));
  console.log(`${colors.cyan}Total keys translated: ${results.totalTranslated}${colors.reset}`);
  console.log(`${colors.cyan}Total cost: $${results.totalCost.toFixed(4)}${colors.reset}`);
  console.log(`${colors.cyan}Total errors: ${results.errors.length}${colors.reset}\n`);
  
  // Language breakdown
  console.log(`${colors.blue}By Language:${colors.reset}`);
  for (const [language, stats] of Object.entries(results.byLanguage)) {
    const total = keysToTranslate[language]?.length || 0;
    const percentage = total > 0 ? ((stats.translated / total) * 100).toFixed(1) : '0';
    console.log(`  ${language.toUpperCase()}: ${stats.translated}/${total} (${percentage}%) - $${stats.cost.toFixed(4)}`);
  }
  
  // Errors
  if (results.errors.length > 0) {
    console.log(`\n${colors.red}Errors:${colors.reset}`);
    results.errors.forEach(error => {
      console.log(`  ${colors.red}❌ ${error.key}: ${error.error}${colors.reset}`);
    });
  }
  
  console.log(`\n${colors.green}🎉 Translation completed!${colors.reset}`);
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'translate';
  
  console.log(`${colors.magenta}🤖 Automated Translation System${colors.reset}\n`);
  
  try {
    switch (command) {
      case 'scan':
      case 'check':
        findKeysNeedingTranslation();
        break;
        
      case 'translate':
      case 'auto':
        const keysToTranslate = findKeysNeedingTranslation();
        
        if (Object.keys(keysToTranslate).length === 0) {
          console.log(`\n${colors.green}🎉 No keys need translation!${colors.reset}`);
          return;
        }
        
        const options = {
          provider: args.find(arg => arg.startsWith('--provider='))?.split('=')[1],
          dryRun: args.includes('--dry-run'),
          batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || 10,
          delayBetweenRequests: parseInt(args.find(arg => arg.startsWith('--delay='))?.split('=')[1]) || 1000
        };
        
        const results = await performAutomatedTranslation(keysToTranslate, options);
        generateReport(results, keysToTranslate);
        break;
        
      case 'help':
      default:
        console.log(`${colors.cyan}Usage:${colors.reset}`);
        console.log(`  node auto-translate.js [command] [options]\n`);
        console.log(`${colors.cyan}Commands:${colors.reset}`);
        console.log(`  scan          - Scan for keys needing translation`);
        console.log(`  translate     - Perform automated translation (default)`);
        console.log(`  help          - Show this help message\n`);
        console.log(`${colors.cyan}Options:${colors.reset}`);
        console.log(`  --provider=<name>     - Translation provider (google|deepl|openai)`);
        console.log(`  --dry-run             - Show what would be translated without doing it`);
        console.log(`  --batch-size=<n>      - Number of translations per batch (default: 10)`);
        console.log(`  --delay=<ms>          - Delay between requests in ms (default: 1000)\n`);
        console.log(`${colors.cyan}Environment Variables:${colors.reset}`);
        console.log(`  GOOGLE_TRANSLATE_API_KEY  - Google Translate API key`);
        console.log(`  DEEPL_API_KEY             - DeepL API key`);
        console.log(`  OPENAI_API_KEY            - OpenAI API key`);
        break;
    }
  } catch (error) {
    console.error(`${colors.red}❌ Error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  findKeysNeedingTranslation,
  performAutomatedTranslation,
  translateText,
  getBestAvailableProvider
};