{"description": "Add these scripts to your package.json scripts section", "scripts": {"translations:check": "node scripts/translation-utils.js check", "translations:fix": "node scripts/translation-utils.js fix", "translations:validate": "node scripts/translation-utils.js validate", "translations:help": "node scripts/translation-utils.js help", "translations:ci": "node scripts/ci-translation-check.js", "translations:setup": "chmod +x scripts/setup-git-hooks.sh && ./scripts/setup-git-hooks.sh", "translations:test": "node scripts/test-translations.js", "translate:scan": "node scripts/auto-translate.js scan", "translate:auto": "node scripts/auto-translate.js translate", "translate:dry-run": "node scripts/auto-translate.js translate --dry-run", "translate:google": "node scripts/auto-translate.js translate --provider=google", "translate:deepl": "node scripts/auto-translate.js translate --provider=deepl", "translate:openai": "node scripts/auto-translate.js translate --provider=openai"}, "usage": {"check": "Scan for missing translation keys across all languages", "fix": "Automatically add missing keys with English placeholders", "validate": "Ensure all translation files have consistent structure", "help": "Show detailed usage information", "ci": "Run CI-specific validation with strict checking and reports", "setup": "Install Git hooks and configure local development environment", "test": "Test the translation utility system", "translate:scan": "Scan for keys needing automated translation ([EN] prefixed)", "translate:auto": "Automatically translate missing keys using best available provider", "translate:dry-run": "Show what would be translated without actually doing it", "translate:google": "Translate using Google Translate API", "translate:deepl": "Translate using DeepL API (highest quality)", "translate:openai": "Translate using OpenAI GPT (context-aware)"}, "workflow": {"development": ["1. Add new features with English translations", "2. Run 'npm run translations:fix' to add placeholders", "3. Run 'npm run translations:validate' before committing"], "translation": ["1. Look for keys marked with [EN] prefix", "2. Replace with proper translations", "3. Remove [EN] prefix when complete"]}}