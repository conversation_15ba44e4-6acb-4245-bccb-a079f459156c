#!/usr/bin/env node

/**
 * CI-specific translation validation script
 * 
 * This script is designed for CI/CD environments and provides:
 * - Strict validation with exit codes
 * - JSON output for parsing
 * - Detailed reporting
 * - Integration with CI systems
 */

const { findMissingKeys, validateTranslations, extractKeys, loadTranslationFile } = require('./translation-utils');
const fs = require('fs');
const path = require('path');

// Configuration
const STRICT_MODE = process.env.TRANSLATION_STRICT_MODE === 'true';
const OUTPUT_FORMAT = process.env.TRANSLATION_OUTPUT_FORMAT || 'text'; // 'text' or 'json'
const MAX_MISSING_KEYS = parseInt(process.env.MAX_MISSING_KEYS || '0');
const LANGUAGES = ['en', 'ar', 'fr', 'ja', 'it', 'tr', 'nl', 'es'];

/**
 * Generate comprehensive translation report
 */
function generateReport() {
  const report = {
    timestamp: new Date().toISOString(),
    status: 'unknown',
    summary: {
      totalLanguages: LANGUAGES.length - 1, // Exclude English
      totalKeys: 0,
      missingKeys: 0,
      validLanguages: 0,
      invalidLanguages: 0
    },
    languages: {},
    errors: [],
    warnings: []
  };

  try {
    // Load English as source
    const englishTranslations = loadTranslationFile('en');
    if (!englishTranslations) {
      report.errors.push('Could not load English translations (source)');
      report.status = 'error';
      return report;
    }

    const englishKeys = extractKeys(englishTranslations);
    report.summary.totalKeys = englishKeys.length;

    // Check each language
    for (const language of LANGUAGES) {
      if (language === 'en') continue;

      const languageReport = {
        language,
        status: 'unknown',
        totalKeys: 0,
        missingKeys: 0,
        extraKeys: 0,
        missingKeysList: [],
        extraKeysList: [],
        errors: []
      };

      try {
        const translations = loadTranslationFile(language);
        
        if (!translations) {
          languageReport.status = 'error';
          languageReport.errors.push('Could not load translation file');
          report.summary.invalidLanguages++;
        } else {
          const keys = extractKeys(translations);
          languageReport.totalKeys = keys.length;
          
          // Find missing and extra keys
          const missing = englishKeys.filter(key => !keys.includes(key));
          const extra = keys.filter(key => !englishKeys.includes(key));
          
          languageReport.missingKeys = missing.length;
          languageReport.extraKeys = extra.length;
          languageReport.missingKeysList = missing;
          languageReport.extraKeysList = extra;
          
          if (missing.length === 0 && extra.length === 0) {
            languageReport.status = 'perfect';
            report.summary.validLanguages++;
          } else if (missing.length === 0) {
            languageReport.status = 'extra_keys';
            report.summary.validLanguages++;
            report.warnings.push(`${language}: Has ${extra.length} extra keys`);
          } else {
            languageReport.status = 'missing_keys';
            report.summary.invalidLanguages++;
            report.summary.missingKeys += missing.length;
          }
        }
      } catch (error) {
        languageReport.status = 'error';
        languageReport.errors.push(error.message);
        report.summary.invalidLanguages++;
      }

      report.languages[language] = languageReport;
    }

    // Determine overall status
    if (report.summary.invalidLanguages === 0 && report.summary.missingKeys === 0) {
      report.status = 'success';
    } else if (report.summary.missingKeys <= MAX_MISSING_KEYS && !STRICT_MODE) {
      report.status = 'warning';
    } else {
      report.status = 'failure';
    }

  } catch (error) {
    report.errors.push(`Critical error: ${error.message}`);
    report.status = 'error';
  }

  return report;
}

/**
 * Output report in specified format
 */
function outputReport(report) {
  if (OUTPUT_FORMAT === 'json') {
    console.log(JSON.stringify(report, null, 2));
    return;
  }

  // Text format output
  console.log('🌍 CI Translation Validation Report');
  console.log('=' .repeat(50));
  console.log(`Timestamp: ${report.timestamp}`);
  console.log(`Status: ${getStatusEmoji(report.status)} ${report.status.toUpperCase()}`);
  console.log('');

  // Summary
  console.log('📊 Summary:');
  console.log(`  Total Languages: ${report.summary.totalLanguages}`);
  console.log(`  Total Keys: ${report.summary.totalKeys}`);
  console.log(`  Missing Keys: ${report.summary.missingKeys}`);
  console.log(`  Valid Languages: ${report.summary.validLanguages}`);
  console.log(`  Invalid Languages: ${report.summary.invalidLanguages}`);
  console.log('');

  // Language details
  console.log('🔍 Language Details:');
  for (const [lang, details] of Object.entries(report.languages)) {
    const statusEmoji = getStatusEmoji(details.status);
    console.log(`  ${statusEmoji} ${lang.toUpperCase()}: ${details.status}`);
    
    if (details.missingKeys > 0) {
      console.log(`    Missing: ${details.missingKeys} keys`);
      if (details.missingKeysList.length <= 5) {
        details.missingKeysList.forEach(key => console.log(`      - ${key}`));
      } else {
        details.missingKeysList.slice(0, 3).forEach(key => console.log(`      - ${key}`));
        console.log(`      ... and ${details.missingKeys - 3} more`);
      }
    }
    
    if (details.extraKeys > 0) {
      console.log(`    Extra: ${details.extraKeys} keys`);
    }
    
    if (details.errors.length > 0) {
      console.log(`    Errors: ${details.errors.join(', ')}`);
    }
  }

  // Errors and warnings
  if (report.errors.length > 0) {
    console.log('');
    console.log('❌ Errors:');
    report.errors.forEach(error => console.log(`  - ${error}`));
  }

  if (report.warnings.length > 0) {
    console.log('');
    console.log('⚠️  Warnings:');
    report.warnings.forEach(warning => console.log(`  - ${warning}`));
  }

  // Recommendations
  console.log('');
  console.log('📝 Recommendations:');
  
  if (report.summary.missingKeys > 0) {
    console.log('  1. Run "npm run translations:fix" to add English placeholders');
    console.log('  2. Translate keys marked with [EN] prefix');
    console.log('  3. Run "npm run translations:validate" to verify');
  } else {
    console.log('  ✅ All translations are complete!');
  }

  console.log('');
}

/**
 * Get emoji for status
 */
function getStatusEmoji(status) {
  const emojis = {
    success: '✅',
    perfect: '✅',
    warning: '⚠️',
    extra_keys: '⚠️',
    missing_keys: '❌',
    failure: '❌',
    error: '❌',
    unknown: '❓'
  };
  return emojis[status] || '❓';
}

/**
 * Save report to file
 */
function saveReport(report) {
  const outputDir = process.env.TRANSLATION_REPORT_DIR || './reports';
  
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `translation-report-${timestamp}.json`;
  const filepath = path.join(outputDir, filename);

  fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
  console.log(`💾 Report saved to: ${filepath}`);
}

/**
 * Main execution
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'check';

  console.log('🤖 CI Translation Validation');
  console.log(`Mode: ${STRICT_MODE ? 'STRICT' : 'NORMAL'}`);
  console.log(`Max Missing Keys: ${MAX_MISSING_KEYS}`);
  console.log('');

  const report = generateReport();

  // Save report if requested
  if (process.env.SAVE_TRANSLATION_REPORT === 'true') {
    saveReport(report);
  }

  // Output report
  outputReport(report);

  // Set exit code based on status
  const exitCodes = {
    success: 0,
    warning: STRICT_MODE ? 1 : 0,
    failure: 1,
    error: 1
  };

  const exitCode = exitCodes[report.status] || 1;
  
  if (exitCode !== 0) {
    console.log('');
    console.log(`❌ CI validation failed with status: ${report.status}`);
    console.log('Build will be blocked until translation issues are resolved.');
  } else {
    console.log('');
    console.log('✅ CI validation passed!');
  }

  process.exit(exitCode);
}

// Export for testing
module.exports = {
  generateReport,
  outputReport,
  saveReport
};

// Run if called directly
if (require.main === module) {
  main();
}
