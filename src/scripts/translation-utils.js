#!/usr/bin/env node

/**
 * Translation Utility Script
 * 
 * This script helps automate translation management by:
 * 1. Finding missing translation keys across all language files
 * 2. Adding placeholder translations with English text
 * 3. Generating reports of missing translations
 * 4. Validating translation file structure
 */

const fs = require('fs');
const path = require('path');

// Configuration
const TRANSLATIONS_DIR = path.join(__dirname, '../assets/translations');
const LANGUAGES = ['en', 'ar', 'fr', 'ja', 'it', 'tr', 'nl', 'es'];
const SOURCE_LANGUAGE = 'en';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Recursively extract all keys from a nested object
 */
function extractKeys(obj, prefix = '') {
  const keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...extractKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

/**
 * Get value from nested object using dot notation
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Set value in nested object using dot notation
 */
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  target[lastKey] = value;
}

/**
 * Load translation file
 */
function loadTranslationFile(language) {
  try {
    const filePath = path.join(TRANSLATIONS_DIR, language, 'index.ts');
    
    if (!fs.existsSync(filePath)) {
      console.log(`${colors.yellow}Warning: Translation file not found for ${language}${colors.reset}`);
      return null;
    }
    
    // Read and parse the TypeScript file
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Extract the exported object (simplified parsing)
    const match = content.match(/export const \w+ = ({[\s\S]*});/);
    if (!match) {
      console.log(`${colors.red}Error: Could not parse translation file for ${language}${colors.reset}`);
      return null;
    }
    
    // Convert to JSON-like format for parsing
    const objectStr = match[1]
      .replace(/'/g, '"')
      .replace(/([a-zA-Z_$][a-zA-Z0-9_$]*):(?=\s*[^/])/g, '"$1":')
      .replace(/,\s*}/g, '}')
      .replace(/,\s*]/g, ']');
    
    try {
      return JSON.parse(objectStr);
    } catch (parseError) {
      console.log(`${colors.red}Error parsing ${language} translations: ${parseError.message}${colors.reset}`);
      return null;
    }
  } catch (error) {
    console.log(`${colors.red}Error loading ${language} translations: ${error.message}${colors.reset}`);
    return null;
  }
}

/**
 * Save translation file
 */
function saveTranslationFile(language, translations) {
  const filePath = path.join(TRANSLATIONS_DIR, language, 'index.ts');
  
  // Convert object to TypeScript format
  const content = `export const ${language} = ${JSON.stringify(translations, null, 2)
    .replace(/"/g, "'")
    .replace(/'/g, "'")};
`;
  
  fs.writeFileSync(filePath, content, 'utf8');
}

/**
 * Find missing translation keys
 */
function findMissingKeys() {
  console.log(`${colors.blue}🔍 Scanning for missing translation keys...${colors.reset}\n`);
  
  // Load source language (English)
  const sourceTranslations = loadTranslationFile(SOURCE_LANGUAGE);
  if (!sourceTranslations) {
    console.log(`${colors.red}❌ Could not load source language (${SOURCE_LANGUAGE})${colors.reset}`);
    return;
  }
  
  const sourceKeys = extractKeys(sourceTranslations);
  console.log(`${colors.green}✅ Found ${sourceKeys.length} keys in ${SOURCE_LANGUAGE}${colors.reset}`);
  
  const missingReport = {};
  let totalMissing = 0;
  
  // Check each target language
  for (const language of LANGUAGES) {
    if (language === SOURCE_LANGUAGE) continue;
    
    console.log(`\n${colors.cyan}Checking ${language}...${colors.reset}`);
    
    const targetTranslations = loadTranslationFile(language);
    if (!targetTranslations) {
      missingReport[language] = sourceKeys;
      totalMissing += sourceKeys.length;
      continue;
    }
    
    const targetKeys = extractKeys(targetTranslations);
    const missing = sourceKeys.filter(key => !targetKeys.includes(key));
    
    if (missing.length > 0) {
      missingReport[language] = missing;
      totalMissing += missing.length;
      console.log(`${colors.yellow}  ⚠️  Missing ${missing.length} keys${colors.reset}`);
      missing.slice(0, 5).forEach(key => {
        console.log(`${colors.yellow}    - ${key}${colors.reset}`);
      });
      if (missing.length > 5) {
        console.log(`${colors.yellow}    ... and ${missing.length - 5} more${colors.reset}`);
      }
    } else {
      console.log(`${colors.green}  ✅ All keys present${colors.reset}`);
    }
  }
  
  console.log(`\n${colors.magenta}📊 SUMMARY${colors.reset}`);
  console.log(`${colors.magenta}Total missing keys: ${totalMissing}${colors.reset}`);
  
  return { sourceTranslations, missingReport, totalMissing };
}

/**
 * Add missing keys with English placeholders
 */
function addMissingKeys(sourceTranslations, missingReport) {
  console.log(`\n${colors.blue}🔧 Adding missing keys with English placeholders...${colors.reset}\n`);
  
  let totalAdded = 0;
  
  for (const [language, missingKeys] of Object.entries(missingReport)) {
    if (missingKeys.length === 0) continue;
    
    console.log(`${colors.cyan}Processing ${language}...${colors.reset}`);
    
    // Load existing translations or create new object
    let targetTranslations = loadTranslationFile(language) || {};
    
    // Add missing keys
    for (const key of missingKeys) {
      const englishValue = getNestedValue(sourceTranslations, key);
      if (englishValue) {
        setNestedValue(targetTranslations, key, `[EN] ${englishValue}`);
        totalAdded++;
      }
    }
    
    // Save updated translations
    saveTranslationFile(language, targetTranslations);
    console.log(`${colors.green}  ✅ Added ${missingKeys.length} keys${colors.reset}`);
  }
  
  console.log(`\n${colors.green}🎉 Successfully added ${totalAdded} missing translation keys!${colors.reset}`);
  console.log(`${colors.yellow}📝 Note: Keys marked with [EN] need manual translation${colors.reset}`);
}

/**
 * Validate translation file structure
 */
function validateTranslations() {
  console.log(`${colors.blue}🔍 Validating translation file structure...${colors.reset}\n`);
  
  const sourceTranslations = loadTranslationFile(SOURCE_LANGUAGE);
  if (!sourceTranslations) return;
  
  const sourceKeys = extractKeys(sourceTranslations);
  let allValid = true;
  
  for (const language of LANGUAGES) {
    if (language === SOURCE_LANGUAGE) continue;
    
    const targetTranslations = loadTranslationFile(language);
    if (!targetTranslations) {
      console.log(`${colors.red}❌ ${language}: File not found or invalid${colors.reset}`);
      allValid = false;
      continue;
    }
    
    const targetKeys = extractKeys(targetTranslations);
    const missing = sourceKeys.filter(key => !targetKeys.includes(key));
    const extra = targetKeys.filter(key => !sourceKeys.includes(key));
    
    if (missing.length === 0 && extra.length === 0) {
      console.log(`${colors.green}✅ ${language}: Perfect match${colors.reset}`);
    } else {
      console.log(`${colors.yellow}⚠️  ${language}: ${missing.length} missing, ${extra.length} extra keys${colors.reset}`);
      allValid = false;
    }
  }
  
  if (allValid) {
    console.log(`\n${colors.green}🎉 All translation files are valid!${colors.reset}`);
  } else {
    console.log(`\n${colors.yellow}⚠️  Some translation files need attention${colors.reset}`);
  }
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'check';
  
  console.log(`${colors.magenta}🌍 Translation Utility${colors.reset}\n`);
  
  switch (command) {
    case 'check':
    case 'scan':
      findMissingKeys();
      break;
      
    case 'fix':
    case 'add':
      const result = findMissingKeys();
      if (result && result.totalMissing > 0) {
        addMissingKeys(result.sourceTranslations, result.missingReport);
      } else {
        console.log(`\n${colors.green}🎉 No missing keys found!${colors.reset}`);
      }
      break;
      
    case 'validate':
      validateTranslations();
      break;
      
    case 'help':
    default:
      console.log(`${colors.cyan}Usage:${colors.reset}`);
      console.log(`  node translation-utils.js [command]\n`);
      console.log(`${colors.cyan}Commands:${colors.reset}`);
      console.log(`  check     - Scan for missing translation keys (default)`);
      console.log(`  fix       - Add missing keys with English placeholders`);
      console.log(`  validate  - Validate all translation files`);
      console.log(`  help      - Show this help message`);
      break;
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  findMissingKeys,
  addMissingKeys,
  validateTranslations,
  extractKeys,
  loadTranslationFile,
  saveTranslationFile
};