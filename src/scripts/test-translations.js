#!/usr/bin/env node

/**
 * Quick test script to verify translation utility works correctly
 */

const { findMissingKeys, validateTranslations } = require('./translation-utils');

console.log('🧪 Testing Translation Utility...\n');

// Test 1: Check for missing keys
console.log('🔍 Test 1: Checking for missing keys...');
const result = findMissingKeys();

if (result) {
  console.log(`\n✅ Test 1 passed: Found ${result.totalMissing} missing keys`);
} else {
  console.log('\n❌ Test 1 failed: Could not scan translations');
}

// Test 2: Validate structure
console.log('\n🔍 Test 2: Validating translation structure...');
validateTranslations();

console.log('\n🎉 Translation utility test complete!');
console.log('\n📝 Next steps:');
console.log('  1. Add the scripts to package.json (see scripts/package-scripts.json)');
console.log('  2. Run "npm run translations:fix" to add any missing keys');
console.log('  3. Use "npm run translations:check" regularly during development');
