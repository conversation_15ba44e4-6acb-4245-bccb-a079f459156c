# Translation Management Scripts

This directory contains utility scripts for managing translations in the magically app.

## Translation Utility (`translation-utils.js`)

A comprehensive script for automating translation management.

### Features

- **🔍 Scan for Missing Keys**: Automatically finds translation keys that exist in English but are missing in other languages
- **🔧 Auto-Fix Missing Keys**: Adds missing keys with English placeholders marked with `[EN]`
- **✅ Validate Structure**: Ensures all translation files have consistent structure
- **📊 Detailed Reports**: Provides clear reports of what's missing and what was added

### Usage

```bash
# Check for missing translation keys
node scripts/translation-utils.js check

# Automatically add missing keys with English placeholders
node scripts/translation-utils.js fix

# Validate all translation files
node scripts/translation-utils.js validate

# Show help
node scripts/translation-utils.js help
```

### NPM Scripts

Add these to your `package.json` scripts section:

```json
{
  "scripts": {
    "translations:check": "node scripts/translation-utils.js check",
    "translations:fix": "node scripts/translation-utils.js fix",
    "translations:validate": "node scripts/translation-utils.js validate"
  }
}
```

### Workflow Integration

#### For Developers

1. **Before Adding New Features**: Run `npm run translations:check` to see current status
2. **After Adding New Translation Keys**: Run `npm run translations:fix` to add placeholders
3. **Before Committing**: Run `npm run translations:validate` to ensure consistency

#### For Translators

1. Look for keys marked with `[EN]` prefix
2. Replace the English text with proper translations
3. Remove the `[EN]` prefix when done

### Example Output

```
🌍 Translation Utility

🔍 Scanning for missing translation keys...

✅ Found 156 keys in en

Checking ar...
  ⚠️  Missing 12 keys
    - quickActions
    - healthDashboard
    - overallHealthStatus
    ... and 9 more

Checking fr...
  ✅ All keys present

📊 SUMMARY
Total missing keys: 12

🔧 Adding missing keys with English placeholders...

Processing ar...
  ✅ Added 12 keys

🎉 Successfully added 12 missing translation keys!
📝 Note: Keys marked with [EN] need manual translation
```

### Supported Languages

- English (en) - Source language
- Arabic (ar)
- French (fr)
- Japanese (ja)
- Italian (it)
- Turkish (tr)
- Dutch (nl)
- Spanish (es)

### File Structure

The script expects translations to be organized as:

```
assets/translations/
├── en/index.ts
├── ar/index.ts
├── fr/index.ts
├── ja/index.ts
├── it/index.ts
├── tr/index.ts
├── nl/index.ts
└── es/index.ts
```

### Best Practices

1. **Always use English as the source of truth**
2. **Run the fix command after adding new features**
3. **Validate before releases**
4. **Keep translation keys descriptive and hierarchical**
5. **Use the `[EN]` prefix system for tracking untranslated content**

### Troubleshooting

#### "Could not parse translation file"
- Check for syntax errors in the TypeScript file
- Ensure proper object structure
- Verify all strings are properly quoted

#### "File not found"
- Ensure the translation file exists in the correct directory
- Check file permissions

#### "Missing keys not detected"
- Verify the English file is the complete source
- Check for nested object structure issues
- Ensure key names match exactly (case-sensitive)

### Contributing

When adding new translation keys:

1. Add them to the English file first
2. Run `npm run translations:fix`
3. Commit the placeholder additions
4. Create issues for translators to fill in the actual translations

This ensures no features break due to missing translations while maintaining a clear workflow for translation updates.