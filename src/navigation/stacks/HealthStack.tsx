import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import HealthDashboardScreen from '../../screens/HealthDashboardScreen';
import AIAssistantScreen from '../../screens/AIAssistantScreen';
import AIHealthDashboardScreen from '../../screens/AIHealthDashboardScreen';
import HealthScoreDetailScreen from '../../screens/HealthScoreDetailScreen';
import DiseaseRiskScreen from '../../screens/DiseaseRiskScreen';
import HealthTrendsScreen from '../../screens/HealthTrendsScreen';
import StressAnalysisScreen from '../../screens/StressAnalysisScreen';
import SleepMonitoringScreen from '../../screens/SleepMonitoringScreen';
import EnvironmentalAnalysisScreen from '../../screens/EnvironmentalAnalysisScreen';
import PredictiveInsightsScreen from '../../screens/PredictiveInsightsScreen';
import AnimalsScreen from '../../screens/AnimalsScreen';

export type HealthStackParamList = {
  HealthDashboard: undefined;
  AIAssistant: { animalId?: string };
  AIHealthDashboard: { animalId: string };
  HealthScoreDetail: { animalId: string; scoreId: string };
  DiseaseRisk: { animalId: string };
  HealthTrends: { animalId: string };
  StressAnalysis: { animalId: string };
  SleepMonitoring: { animalId: string };
  EnvironmentalAnalysis: { animalId: string };
  PredictiveInsights: { animalId: string };
  Animals: undefined;
};

const Stack = createNativeStackNavigator<HealthStackParamList>();

/**
 * @magic_description Health stack navigator
 * Handles all health and AI-powered medical analysis screens
 */
const HealthStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right',
      }}
      initialRouteName="HealthDashboard"
    >
      <Stack.Screen name="HealthDashboard" component={HealthDashboardScreen} />
      <Stack.Screen name="AIAssistant" component={AIAssistantScreen} />
      <Stack.Screen name="AIHealthDashboard" component={AIHealthDashboardScreen} />
      <Stack.Screen name="HealthScoreDetail" component={HealthScoreDetailScreen} />
      <Stack.Screen name="DiseaseRisk" component={DiseaseRiskScreen} />
      <Stack.Screen name="HealthTrends" component={HealthTrendsScreen} />
      <Stack.Screen name="StressAnalysis" component={StressAnalysisScreen} />
      <Stack.Screen name="SleepMonitoring" component={SleepMonitoringScreen} />
      <Stack.Screen name="EnvironmentalAnalysis" component={EnvironmentalAnalysisScreen} />
      <Stack.Screen name="PredictiveInsights" component={PredictiveInsightsScreen} />
      <Stack.Screen name="Animals" component={AnimalsScreen} />
    </Stack.Navigator>
  );
};

export default HealthStack;