import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import ProfileScreen from '../../screens/ProfileScreen';
import SubscriptionScreen from '../../screens/SubscriptionScreen';
import AdvancedSettingsScreen from '../../screens/AdvancedSettingsScreen';
import EditProfileScreen from '../../screens/EditProfileScreen';
import PrivacySettingsScreen from '../../screens/PrivacySettingsScreen';
import ContactScreen from '../../screens/ContactScreen';
import FAQScreen from '../../screens/FAQScreen';
import MfaScreen from '../../screens/MfaScreen';

export type ProfileStackParamList = {
  Profile: undefined;
  Subscription: undefined;
  AdvancedSettings: undefined;
  EditProfile: undefined;
  PrivacySettings: undefined;
  Contact: undefined;
  FAQ: undefined;
  MfaSettings: undefined;
};

const Stack = createNativeStackNavigator<ProfileStackParamList>();

/**
 * @magic_description Profile stack navigator
 * Handles all profile, settings, and account management screens
 */
const ProfileStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="Profile" component={ProfileScreen} />
      <Stack.Screen name="Subscription" component={SubscriptionScreen} />
      <Stack.Screen name="AdvancedSettings" component={AdvancedSettingsScreen} />
      <Stack.Screen name="EditProfile" component={EditProfileScreen} />
      <Stack.Screen name="PrivacySettings" component={PrivacySettingsScreen} />
      <Stack.Screen name="Contact" component={ContactScreen} />
      <Stack.Screen name="FAQ" component={FAQScreen} />
      <Stack.Screen name="MfaSettings" component={MfaScreen} />
    </Stack.Navigator>
  );
};

export default ProfileStack;