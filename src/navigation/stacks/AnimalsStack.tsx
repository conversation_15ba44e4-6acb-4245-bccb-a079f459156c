import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import AnimalsScreen from '../../screens/AnimalsScreen';
import AnimalDetailScreen from '../../screens/AnimalDetailScreen';
import AddAnimalScreen from '../../screens/AddAnimalScreen';
import RecordVitalsScreen from '../../screens/RecordVitalsScreen';
import LogTrainingSessionScreen from '../../screens/LogTrainingSessionScreen';
import AIAssistantScreen from '../../screens/AIAssistantScreen';
import AIChatScreen from '../../screens/AIChatScreen';
import AIHealthDashboardScreen from '../../screens/AIHealthDashboardScreen';
import HealthScoreDetailScreen from '../../screens/HealthScoreDetailScreen';
import DiseaseRiskScreen from '../../screens/DiseaseRiskScreen';
import HealthTrendsScreen from '../../screens/HealthTrendsScreen';
import StressAnalysisScreen from '../../screens/StressAnalysisScreen';
import SleepMonitoringScreen from '../../screens/SleepMonitoringScreen';
import EnvironmentalAnalysisScreen from '../../screens/EnvironmentalAnalysisScreen';
import PredictiveInsightsScreen from '../../screens/PredictiveInsightsScreen';

export type AnimalsStackParamList = {
  Animals: undefined;
  AnimalDetail: { id: string };
  AddAnimal: undefined;
  RecordVitals: { animalId?: string };
  LogTrainingSession: { animalId: string };
  AIAssistant: { animalId: string };
  AIChat: { animalId: string };
  AIHealthDashboard: { animalId: string };
  HealthScoreDetail: { animalId: string; scoreId: string };
  DiseaseRisk: { animalId: string };
  HealthTrends: { animalId: string };
  StressAnalysis: { animalId: string };
  SleepMonitoring: { animalId: string };
  EnvironmentalAnalysis: { animalId: string };
  PredictiveInsights: { animalId: string };
};

const Stack = createNativeStackNavigator<AnimalsStackParamList>();

/**
 * @magic_description Animals stack navigator
 * Handles all animal-related screens and management
 */
const AnimalsStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="Animals" component={AnimalsScreen} />
      <Stack.Screen name="AnimalDetail" component={AnimalDetailScreen} />
      <Stack.Screen name="AddAnimal" component={AddAnimalScreen} />
      <Stack.Screen name="RecordVitals" component={RecordVitalsScreen} />
      <Stack.Screen name="LogTrainingSession" component={LogTrainingSessionScreen} />
      <Stack.Screen name="AIAssistant" component={AIAssistantScreen} />
      <Stack.Screen name="AIChat" component={AIChatScreen} />
      <Stack.Screen name="AIHealthDashboard" component={AIHealthDashboardScreen} />
      <Stack.Screen name="HealthScoreDetail" component={HealthScoreDetailScreen} />
      <Stack.Screen name="DiseaseRisk" component={DiseaseRiskScreen} />
      <Stack.Screen name="HealthTrends" component={HealthTrendsScreen} />
      <Stack.Screen name="StressAnalysis" component={StressAnalysisScreen} />
      <Stack.Screen name="SleepMonitoring" component={SleepMonitoringScreen} />
      <Stack.Screen name="EnvironmentalAnalysis" component={EnvironmentalAnalysisScreen} />
      <Stack.Screen name="PredictiveInsights" component={PredictiveInsightsScreen} />
    </Stack.Navigator>
  );
};

export default AnimalsStack;