import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import HomeScreen from '../../screens/HomeScreen';
import AnimalDetailScreen from '../../screens/AnimalDetailScreen';
import RecordVitalsScreen from '../../screens/RecordVitalsScreen';
import PrintScreen from '../../screens/PrintScreen';
import BarcodeScannerScreen from '../../screens/BarcodeScannerScreen';
import FeedScheduleScreen from '../../screens/FeedScheduleScreen';
import AddMedicationScreen from '../../screens/AddMedicationScreen';
import LogTrainingSessionScreen from '../../screens/LogTrainingSessionScreen';
import AIAssistantScreen from '../../screens/AIAssistantScreen';
import AIChatScreen from '../../screens/AIChatScreen';
import LocationScreen from '../../screens/LocationScreen';
import AIHealthDashboardScreen from '../../screens/AIHealthDashboardScreen';
import HealthScoreDetailScreen from '../../screens/HealthScoreDetailScreen';
import DiseaseRiskScreen from '../../screens/DiseaseRiskScreen';
import HealthTrendsScreen from '../../screens/HealthTrendsScreen';
import StressAnalysisScreen from '../../screens/StressAnalysisScreen';
import SleepMonitoringScreen from '../../screens/SleepMonitoringScreen';
import EnvironmentalAnalysisScreen from '../../screens/EnvironmentalAnalysisScreen';
import PredictiveInsightsScreen from '../../screens/PredictiveInsightsScreen';

export type HomeStackParamList = {
  Home: undefined;
  AnimalDetail: { id: string };
  RecordVitals: { animalId?: string };
  Print: { type: 'feeding' | 'medication' | 'vitals'; animalId: string };
  BarcodeScanner: { animalId: string };
  FeedSchedule: { animalId: string };
  AddMedication: { animalId: string };
  LogTrainingSession: { animalId: string };
  AIAssistant: { animalId: string };
  AIChat: { animalId: string };
  Location: { animalId: string };
  AIHealthDashboard: { animalId: string };
  HealthScoreDetail: { animalId: string; scoreId: string };
  DiseaseRisk: { animalId: string };
  HealthTrends: { animalId: string };
  StressAnalysis: { animalId: string };
  SleepMonitoring: { animalId: string };
  EnvironmentalAnalysis: { animalId: string };
  PredictiveInsights: { animalId: string };
};

const Stack = createNativeStackNavigator<HomeStackParamList>();

/**
 * @magic_description Home stack navigator
 * Handles main app screens and AI-powered features
 */
const HomeStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="AnimalDetail" component={AnimalDetailScreen} />
      <Stack.Screen name="RecordVitals" component={RecordVitalsScreen} />
      <Stack.Screen name="Print" component={PrintScreen} />
      <Stack.Screen name="BarcodeScanner" component={BarcodeScannerScreen} />
      <Stack.Screen name="FeedSchedule" component={FeedScheduleScreen} />
      <Stack.Screen name="AddMedication" component={AddMedicationScreen} />
      <Stack.Screen name="LogTrainingSession" component={LogTrainingSessionScreen} />
      <Stack.Screen name="AIAssistant" component={AIAssistantScreen} />
      <Stack.Screen name="AIChat" component={AIChatScreen} />
      <Stack.Screen name="Location" component={LocationScreen} />
      <Stack.Screen name="AIHealthDashboard" component={AIHealthDashboardScreen} />
      <Stack.Screen name="HealthScoreDetail" component={HealthScoreDetailScreen} />
      <Stack.Screen name="DiseaseRisk" component={DiseaseRiskScreen} />
      <Stack.Screen name="HealthTrends" component={HealthTrendsScreen} />
      <Stack.Screen name="StressAnalysis" component={StressAnalysisScreen} />
      <Stack.Screen name="SleepMonitoring" component={SleepMonitoringScreen} />
      <Stack.Screen name="EnvironmentalAnalysis" component={EnvironmentalAnalysisScreen} />
      <Stack.Screen name="PredictiveInsights" component={PredictiveInsightsScreen} />
    </Stack.Navigator>
  );
};

export default HomeStack;