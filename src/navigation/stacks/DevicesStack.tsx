import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import DevicesScreen from '../../screens/DevicesScreen';
import OrderDeviceScreen from '../../screens/OrderDeviceScreen';
import CheckoutScreen from '../../screens/CheckoutScreen';
import OrderHistoryScreen from '../../screens/OrderHistoryScreen';

export type DevicesStackParamList = {
  Devices: undefined;
  OrderDevice: undefined;
  Checkout: { productId: string; quantity: number };
  OrderHistory: undefined;
};

const Stack = createNativeStackNavigator<DevicesStackParamList>();

/**
 * @magic_description Devices stack navigator
 * Handles all device management and ordering screens
 */
const DevicesStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen name="Devices" component={DevicesScreen} />
      <Stack.Screen name="OrderDevice" component={OrderDeviceScreen} />
      <Stack.Screen name="Checkout" component={CheckoutScreen} />
      <Stack.Screen name="OrderHistory" component={OrderHistoryScreen} />
    </Stack.Navigator>
  );
};

export default DevicesStack;