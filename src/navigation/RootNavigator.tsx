import React from 'react';
import { Platform } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Feather } from '@expo/vector-icons';
import { COLORS } from '../constants/colors';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

// Import stack navigators
import HomeStack from './stacks/HomeStack';
import AnimalsStack from './stacks/AnimalsStack';
import DevicesStack from './stacks/DevicesStack';
import ProfileStack from './stacks/ProfileStack';
import AuthStack from './stacks/AuthStack';
import HealthStack from './stacks/HealthStack';

// Import individual screens that need to be at root level
import VerifyMfaScreen from '../screens/VerifyMfaScreen';

// Type definitions
export type RootStackParamList = {
  AuthStack: undefined;
  MainTabs: undefined;
  VerifyMfa: { challengeId: string };
};

export type MainTabParamList = {
  HomeTab: undefined;
  AnimalsTab: undefined;
  DevicesTab: undefined;
  HealthTab: undefined;
  ProfileTab: undefined;
};

const RootStack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

/**
 * @magic_description Main tab navigator
 * Bottom tab navigation for authenticated users
 */
const MainTabNavigator = () => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Feather.glyphMap;

          switch (route.name) {
            case 'HomeTab':
              iconName = 'home';
              break;
            case 'AnimalsTab':
              iconName = 'heart';
              break;
            case 'DevicesTab':
              iconName = 'bluetooth';
              break;
            case 'HealthTab':
              iconName = 'activity';
              break;
            case 'ProfileTab':
              iconName = 'user';
              break;
            default:
              iconName = 'circle';
          }

          return (
            <Feather 
              name={iconName} 
              size={20} 
              color={color} 
            />
          );
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textLight,
        tabBarStyle: {
          backgroundColor: colors.card,
          borderTopColor: colors.border,
          borderTopWidth: 1,
          paddingBottom: Platform.OS === 'ios' ? 20 : 5,
          height: Platform.OS === 'ios' ? 85 : 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      })}
    >
      <Tab.Screen 
        name="HomeTab" 
        component={HomeStack}
        options={{
          tabBarLabel: t('tabHome'),
        }}
      />
      <Tab.Screen 
        name="AnimalsTab" 
        component={AnimalsStack}
        options={{
          tabBarLabel: t('tabAnimals'),
        }}
      />
      <Tab.Screen 
        name="DevicesTab" 
        component={DevicesStack}
        options={{
          tabBarLabel: t('tabDevices'),
        }}
      />
      <Tab.Screen 
        name="HealthTab" 
        component={HealthStack}
        options={{
          tabBarLabel: t('tabHealth'),
        }}
      />
      <Tab.Screen 
        name="ProfileTab" 
        component={ProfileStack}
        options={{
          tabBarLabel: t('tabProfile'),
        }}
      />
    </Tab.Navigator>
  );
};

/**
 * @magic_description Root navigator
 * Main navigation controller that handles authentication flow
 */
const RootNavigator = () => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return null; // Or loading screen
  }

  return (
    <RootStack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        animation: 'slide_from_right',
      }}
    >
      {user ? (
        // Authenticated user screens
        <>
          <RootStack.Screen name="MainTabs" component={MainTabNavigator} />
          <RootStack.Screen name="VerifyMfa" component={VerifyMfaScreen} />
        </>
      ) : (
        // Unauthenticated user screens
        <RootStack.Screen name="AuthStack" component={AuthStack} />
      )}
    </RootStack.Navigator>
  );
};

export default RootNavigator;