/**
 * @magic_description Main navigation entry point
 * Re-exports the refactored RootNavigator and type definitions
 */
import RootNavigator from './RootNavigator';

// Re-export type definitions for backward compatibility
export type { RootStackParamList, MainTabParamList } from './RootNavigator';
export type { HomeStackParamList } from './stacks/HomeStack';
export type { AnimalsStackParamList } from './stacks/AnimalsStack';
export type { DevicesStackParamList } from './stacks/DevicesStack';
export type { ProfileStackParamList } from './stacks/ProfileStack';
export type { AuthStackParamList } from './stacks/AuthStack';
export type { HealthStackParamList } from './stacks/HealthStack';

// Export the main navigator as default
export default RootNavigator;