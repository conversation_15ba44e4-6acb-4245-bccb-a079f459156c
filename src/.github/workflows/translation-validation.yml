name: Translation Validation

on:
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'assets/translations/**'
      - 'components/**'
      - 'screens/**'
      - 'hooks/**'
      - 'contexts/**'
  push:
    branches: [ main, develop ]
    paths:
      - 'assets/translations/**'
      - 'components/**'
      - 'screens/**'
      - 'hooks/**'
      - 'contexts/**'
  workflow_dispatch:

jobs:
  validate-translations:
    name: Validate Translation Files
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run translation validation
      id: validation
      run: |
        echo "🔍 Running translation validation..."
        node scripts/translation-utils.js validate > validation-output.txt 2>&1
        
        # Check if validation passed
        if grep -q "All translation files are valid!" validation-output.txt; then
          echo "✅ Translation validation passed"
          echo "validation_status=success" >> $GITHUB_OUTPUT
        else
          echo "❌ Translation validation failed"
          echo "validation_status=failed" >> $GITHUB_OUTPUT
          exit 1
        fi
        
    - name: Check for missing translations
      id: missing-check
      run: |
        echo "🔍 Checking for missing translation keys..."
        node scripts/translation-utils.js check > missing-output.txt 2>&1
        
        # Extract missing count
        missing_count=$(grep "Total missing keys:" missing-output.txt | grep -o '[0-9]\+' || echo "0")
        echo "missing_count=$missing_count" >> $GITHUB_OUTPUT
        
        if [ "$missing_count" -gt 0 ]; then
          echo "⚠️ Found $missing_count missing translation keys"
          echo "missing_status=warning" >> $GITHUB_OUTPUT
        else
          echo "✅ No missing translation keys found"
          echo "missing_status=success" >> $GITHUB_OUTPUT
        fi
        
    - name: Generate translation report
      if: always()
      run: |
        echo "📊 Generating comprehensive translation report..."
        
        cat > translation-report.md << 'EOF'
        # 🌍 Translation Validation Report
        
        ## Validation Results
        
        **Status**: ${{ steps.validation.outputs.validation_status == 'success' && '✅ PASSED' || '❌ FAILED' }}
        **Missing Keys**: ${{ steps.missing-check.outputs.missing_count }}
        **Date**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
        
        ## Detailed Output
        
        ### Structure Validation
        ```
        EOF
        
        cat validation-output.txt >> translation-report.md
        
        cat >> translation-report.md << 'EOF'
        ```
        
        ### Missing Keys Check
        ```
        EOF
        
        cat missing-output.txt >> translation-report.md
        
        cat >> translation-report.md << 'EOF'
        ```
        
        ## Next Steps
        
        EOF
        
        if [ "${{ steps.missing-check.outputs.missing_count }}" -gt 0 ]; then
          cat >> translation-report.md << 'EOF'
        ⚠️ **Action Required**: Missing translation keys detected
        
        1. Run `npm run translations:fix` to add English placeholders
        2. Translate the keys marked with `[EN]` prefix
        3. Commit the updated translation files
        
        EOF
        else
          cat >> translation-report.md << 'EOF'
        ✅ **All Good**: No action required - all translations are complete
        
        EOF
        fi
        
        cat >> translation-report.md << 'EOF'
        ## Available Commands
        
        ```bash
        # Check for missing keys
        npm run translations:check
        
        # Add missing keys with placeholders
        npm run translations:fix
        
        # Validate translation structure
        npm run translations:validate
        ```
        
        ---
        *Generated by Translation Validation CI*
        EOF
        
    - name: Upload translation report
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: translation-report
        path: translation-report.md
        retention-days: 30
        
    - name: Comment on PR (if missing translations)
      if: github.event_name == 'pull_request' && steps.missing-check.outputs.missing_count > 0
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('translation-report.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: report
          });
          
    - name: Fail if validation failed
      if: steps.validation.outputs.validation_status == 'failed'
      run: |
        echo "❌ Translation validation failed - blocking merge"
        exit 1
        
  auto-fix-translations:
    name: Auto-fix Missing Translations (Optional)
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Auto-fix missing translations
      run: |
        echo "🔧 Auto-fixing missing translations..."
        node scripts/translation-utils.js fix
        
    - name: Check for changes
      id: changes
      run: |
        if git diff --quiet; then
          echo "No changes made"
          echo "has_changes=false" >> $GITHUB_OUTPUT
        else
          echo "Changes detected"
          echo "has_changes=true" >> $GITHUB_OUTPUT
        fi
        
    - name: Commit and push changes
      if: steps.changes.outputs.has_changes == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "Translation Auto-fix"
        git add assets/translations/
        git commit -m "🌍 Auto-fix: Add missing translation placeholders
        
        - Added English placeholders for missing translation keys
        - Keys marked with [EN] prefix need manual translation
        - Run 'npm run translations:check' to verify"
        git push
        
    - name: Create issue for manual translation
      if: steps.changes.outputs.has_changes == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          const title = '🌍 Translation Update Required';
          const body = `## Missing Translations Auto-Fixed
          
          The CI system has automatically added English placeholders for missing translation keys.
          
          ### Next Steps
          1. Look for keys marked with \`[EN]\` prefix in translation files
          2. Replace English text with proper translations
          3. Remove the \`[EN]\` prefix when translation is complete
          
          ### Files Updated
          - Translation files in \`assets/translations/\`
          
          ### Commands
          \`\`\`bash
          # Check current status
          npm run translations:check
          
          # Validate after translation
          npm run translations:validate
          \`\`\`
          
          ### Languages Needing Attention
          Please check all non-English language files for \`[EN]\` prefixed keys.
          
          ---
          *Auto-generated by Translation CI*`;
          
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: title,
            body: body,
            labels: ['translations', 'enhancement']
          });