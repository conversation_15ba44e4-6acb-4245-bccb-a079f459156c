
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FeedingEntry } from '../mocks/feeding';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

interface FeedingState {
  feedings: FeedingEntry[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  getFeedings: () => FeedingEntry[];
  getFeedingsByAnimalId: (animalId: string) => FeedingEntry[];
  addFeedingEntry: (entry: Omit<FeedingEntry, 'id'>) => Promise<void>;
  updateFeedingEntry: (id: string, updates: Partial<FeedingEntry>) => Promise<void>;
  deleteFeedingEntry: (id: string) => Promise<void>;
  getTodayFeedings: (animalId: string) => FeedingEntry[];
  fetchFeedings: () => Promise<void>;
}

export const useFeedingStore = create<FeedingState>()(
  persist(
    (set, get) => ({
      feedings: [], // Initialize as empty array - will be populated by fetchFeedings()
      isLoading: false,
      error: null,
      
      getFeedings: () => get().feedings,
      
      getFeedingsByAnimalId: (animalId) => {
        return get().feedings
          .filter(feeding => feeding.animalId === animalId)
          .sort((a, b) => {
            const timeA = parseInt(a.time.replace(':', ''));
            const timeB = parseInt(b.time.replace(':', ''));
            return timeA - timeB;
          });
      },
      
      addFeedingEntry: async (entry) => {
        set({ isLoading: true, error: null });
        
        try {
          // Convert the entry to match database schema
          const feedingData = {
            animal_id: entry.animalId,
            feed_type: entry.feedType,
            amount: entry.amount,
            time: entry.time,
            notes: entry.notes,
            reminder: entry.reminder,
            reminder_time: entry.reminderTime
          };
          
          const { data, error } = await supabase
            .from('feeding_schedules')
            .insert(feedingData)
            .select()
            .single();
            
          logApiCall('addFeedingEntry', data, error);
          
          if (error) {
            console.error('Error adding feeding schedule:', error);
            throw new Error(`Failed to add feeding schedule: ${error.message}`);
          }
          
          // Convert the response back to our FeedingEntry type
          const newFeeding: FeedingEntry = {
            id: data.id,
            animalId: data.animal_id,
            feedType: data.feed_type,
            amount: data.amount,
            time: data.time,
            notes: data.notes,
            reminder: data.reminder,
            reminderTime: data.reminder_time
          };
          
          set(state => ({
            feedings: [...state.feedings, newFeeding],
            isLoading: false
          }));
          
          toast.success(`Feeding schedule for ${entry.feedType} added successfully!`);
        } catch (error: any) {
          console.error('Error adding feeding schedule:', error);
          const errorMessage = error.message || 'Failed to add feeding schedule';
          set({ error: errorMessage, isLoading: false });
          toast.error(errorMessage);
          throw error;
        }
      },
      
      updateFeedingEntry: async (id, updates) => {
        set({ isLoading: true, error: null });
        
        try {
          // Convert updates to match database schema
          const updateData: any = {};
          
          if (updates.feedType !== undefined) updateData.feed_type = updates.feedType;
          if (updates.amount !== undefined) updateData.amount = updates.amount;
          if (updates.time !== undefined) updateData.time = updates.time;
          if (updates.notes !== undefined) updateData.notes = updates.notes;
          if (updates.reminder !== undefined) updateData.reminder = updates.reminder;
          if (updates.reminderTime !== undefined) updateData.reminder_time = updates.reminderTime;
          
          // Get current user for defense-in-depth filtering
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ isLoading: false, error: 'User not authenticated' });
            return;
          }
          
          const { error } = await supabase
            .from('feeding_schedules')
            .update(updateData)
            .eq('id', id)
            .eq('user_id', user.id); // Defense-in-depth: ensure user owns record
            
          logApiCall('updateFeedingEntry', { id, ...updateData }, error);
          
          if (error) {
            console.error('Error updating feeding schedule:', error);
            throw new Error(`Failed to update feeding schedule: ${error.message}`);
          }
          
          set(state => ({
            feedings: state.feedings.map(feeding => 
              feeding.id === id ? { ...feeding, ...updates } : feeding
            ),
            isLoading: false
          }));
          
          toast.success('Feeding schedule updated successfully!');
        } catch (error: any) {
          console.error('Error updating feeding schedule:', error);
          const errorMessage = error.message || 'Failed to update feeding schedule';
          set({ error: errorMessage, isLoading: false });
          toast.error(errorMessage);
          throw error;
        }
      },
      
      deleteFeedingEntry: async (id) => {
        set({ isLoading: true, error: null });
        
        try {
          // Get current user for defense-in-depth filtering
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ isLoading: false, error: 'User not authenticated' });
            return;
          }
          
          const { error } = await supabase
            .from('feeding_schedules')
            .delete()
            .eq('id', id)
            .eq('user_id', user.id); // Defense-in-depth: ensure user owns record
            
          logApiCall('deleteFeedingEntry', { id }, error);
          
          if (error) {
            console.error('Error deleting feeding schedule:', error);
            throw new Error(`Failed to delete feeding schedule: ${error.message}`);
          }
          
          set(state => ({
            feedings: state.feedings.filter(feeding => feeding.id !== id),
            isLoading: false
          }));
          
          toast.success('Feeding schedule deleted successfully!');
        } catch (error: any) {
          console.error('Error deleting feeding schedule:', error);
          const errorMessage = error.message || 'Failed to delete feeding schedule';
          set({ error: errorMessage, isLoading: false });
          toast.error(errorMessage);
          throw error;
        }
      },
      
      getTodayFeedings: (animalId) => {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        const currentTime = currentHour * 60 + currentMinute;
        
        return get().feedings
          .filter(feeding => {
            if (feeding.animalId !== animalId) return false;
            
            const [hours, minutes] = feeding.time.split(':').map(Number);
            const feedingTime = hours * 60 + minutes;
            
            // Return feedings that are upcoming today (within next 2 hours)
            return feedingTime > currentTime && feedingTime <= currentTime + 120;
          })
          .sort((a, b) => {
            const timeA = parseInt(a.time.replace(':', ''));
            const timeB = parseInt(b.time.replace(':', ''));
            return timeA - timeB;
          });
      },
      
      fetchFeedings: async () => {
        set({ isLoading: true, error: null });
        
        try {
          // Get current user for defense-in-depth filtering
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ isLoading: false, error: 'User not authenticated' });
            return;
          }
          
          const { data, error } = await supabase
            .from('feeding_schedules')
            .select('*')
            .eq('user_id', user.id) // Defense-in-depth: explicit user filtering
            .order('time', { ascending: true });
            
          logApiCall('fetchFeedings', data, error);
          
          if (error) {
            console.error('Error fetching feeding schedules:', error);
            throw new Error(`Failed to fetch feeding schedules: ${error.message}`);
          }
          
          if (data) {
            // Convert database records to our FeedingEntry type
            const feedings: FeedingEntry[] = data.map(item => ({
              id: item.id,
              animalId: item.animal_id,
              feedType: item.feed_type,
              amount: item.amount,
              time: item.time,
              notes: item.notes,
              reminder: item.reminder,
              reminderTime: item.reminder_time
            }));
            
            set({ feedings, isLoading: false });
            console.log(`Successfully fetched ${feedings.length} feeding schedules`);
          } else {
            set({ feedings: [], isLoading: false });
          }
        } catch (error: any) {
          console.error('Error fetching feeding schedules:', error);
          const errorMessage = error.message || 'Failed to fetch feeding schedules';
          set({ error: errorMessage, isLoading: false });
          // Note: No toast for fetch errors as they're usually silent background operations
        }
      }
    }),
    {
      name: 'hoofbeat-feeding-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
