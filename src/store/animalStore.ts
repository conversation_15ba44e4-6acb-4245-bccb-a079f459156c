
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Animal } from '../mocks/animals';
import { AnimalApiService } from '../services/animalApiService';
import { toast } from 'sonner-native';

interface AnimalState {
  animals: Animal[];
  selectedAnimalId: string | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  getAnimals: () => Promise<Animal[]>;
  getAnimalById: (id: string) => Animal | undefined;
  addAnimal: (animal: Omit<Animal, 'id'>) => Promise<void>;
  updateAnimal: (id: string, updates: Partial<Animal>) => Promise<void>;
  deleteAnimal: (id: string) => Promise<void>;
  setSelectedAnimal: (id: string | null) => void;
  updateDeviceStatus: (animalId: string, deviceStatus: Animal['deviceStatus']) => Promise<void>;
  fetchAnimals: () => Promise<void>;
  updateAnimalSpeed: (animalId: string, speed: number, speedUnit?: string) => Promise<void>;
  getActiveAnimal: () => Animal | undefined;
}

export const useAnimalStore = create<AnimalState>()(
  persist(
    (set, get) => ({
      animals: [], // Initialize as empty array - will be populated by fetchAnimals()
      selectedAnimalId: null,
      isLoading: false,
      error: null,
      
      getAnimals: async () => {
        const { animals } = get();
        return animals;
      },
      
      getAnimalById: (id) => {
        return get().animals.find(animal => animal.id === id);
      },
      
      addAnimal: async (animal) => {
        set({ isLoading: true, error: null });
        
        try {
          const newAnimal = await AnimalApiService.addAnimal(animal);
          
          set(state => ({
            animals: [...state.animals, newAnimal],
            isLoading: false
          }));
          
          toast.success(`${newAnimal.name} added successfully`);
        } catch (error: any) {
          console.error('Error adding animal:', error);
          const errorMessage = error.message || 'Failed to add animal';
          set({ error: errorMessage, isLoading: false });
          toast.error(`Failed to add animal: ${errorMessage}`);
          throw error;
        }
      },
      
      updateAnimal: async (id, updates) => {
        set({ isLoading: true, error: null });
        
        try {
          await AnimalApiService.updateAnimal(id, updates);
          
          const updatedAnimal = { ...get().animals.find(a => a.id === id), ...updates };
          
          set(state => ({
            animals: state.animals.map(animal => 
              animal.id === id ? { ...animal, ...updates } : animal
            ),
            isLoading: false
          }));
          
          toast.success(`${updatedAnimal.name || 'Animal'} updated successfully`);
        } catch (error: any) {
          console.error('Error updating animal:', error);
          const errorMessage = error.message || 'Failed to update animal';
          set({ error: errorMessage, isLoading: false });
          toast.error(`Failed to update animal: ${errorMessage}`);
          throw error;
        }
      },
      
      deleteAnimal: async (id) => {
        const animalToDelete = get().animals.find(animal => animal.id === id);
        const animalName = animalToDelete?.name || 'Animal';
        
        set({ isLoading: true, error: null });
        
        try {
          await AnimalApiService.deleteAnimal(id);
          
          set(state => ({
            animals: state.animals.filter(animal => animal.id !== id),
            selectedAnimalId: state.selectedAnimalId === id ? null : state.selectedAnimalId,
            isLoading: false
          }));
          
          toast.success(`${animalName} deleted successfully`);
        } catch (error: any) {
          console.error('Error deleting animal:', error);
          const errorMessage = error.message || 'Failed to delete animal';
          set({ error: errorMessage, isLoading: false });
          toast.error(`Failed to delete ${animalName}: ${errorMessage}`);
          throw error;
        }
      },
      
      setSelectedAnimal: (id) => {
        set({ selectedAnimalId: id });
      },
      
      updateDeviceStatus: async (animalId, deviceStatus) => {
        set({ isLoading: true, error: null });
        
        try {
          await AnimalApiService.updateDeviceStatus(animalId, deviceStatus);
          
          set(state => ({
            animals: state.animals.map(animal => 
              animal.id === animalId 
                ? { ...animal, deviceStatus, lastSyncTime: deviceStatus === 'connected' ? Date.now() : animal.lastSyncTime } 
                : animal
            ),
            isLoading: false
          }));
          // Device status updates are typically automatic - no success toast needed
        } catch (error: any) {
          console.error('Error updating device status:', error);
          const errorMessage = error.message || 'Failed to update device status';
          set({ error: errorMessage, isLoading: false });
          toast.error(`Device status update failed: ${errorMessage}`);
          throw error;
        }
      },
      
      fetchAnimals: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const animals = await AnimalApiService.fetchAnimals();
          set({ animals, isLoading: false });
          // No toast for successful fetch - this is typically a background operation
        } catch (error: any) {
          console.error('Error fetching animals:', error);
          const errorMessage = error.message || 'Failed to fetch animals';
          set({ error: errorMessage, isLoading: false });
          // Only show error toast for fetch failures that might affect user experience
          toast.error(`Failed to load animals: ${errorMessage}`);
        }
      },
      
      updateAnimalSpeed: async (animalId, speed, speedUnit = 'km/h') => {
        set({ isLoading: true, error: null });
        
        try {
          await AnimalApiService.updateAnimalSpeed(animalId, speed, speedUnit);
          
          const now = new Date().toISOString();
          
          // Update the animal in the local state
          set(state => ({
            animals: state.animals.map(animal => 
              animal.id === animalId 
                ? { 
                    ...animal, 
                    speed, 
                    speedUnit, 
                    speedUpdatedAt: now 
                  } 
                : animal
            ),
            isLoading: false
          }));
          // Speed updates are typically automatic from devices - no success toast needed
        } catch (error: any) {
          console.error('Error updating animal speed:', error);
          const errorMessage = error.message || 'Failed to update animal speed';
          set({ error: errorMessage, isLoading: false });
          toast.error(`Speed update failed: ${errorMessage}`);
          throw error;
        }
      },
      
      getActiveAnimal: () => {
        const { animals, selectedAnimalId } = get();
        
        // If there's a selected animal, return it
        if (selectedAnimalId) {
          return animals.find(animal => animal.id === selectedAnimalId);
        }
        
        // Otherwise, find the animal with the most recent speed update
        return animals
          .filter(animal => animal.speedUpdatedAt && animal.deviceStatus === 'connected')
          .sort((a, b) => {
            const dateA = a.speedUpdatedAt ? new Date(a.speedUpdatedAt).getTime() : 0;
            const dateB = b.speedUpdatedAt ? new Date(b.speedUpdatedAt).getTime() : 0;
            return dateB - dateA;
          })[0];
      }
    }),
    {
      name: 'hoofbeat-animal-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
