
import { create } from 'zustand';
import { supabase } from '../supabase/client';
import { toast } from 'sonner-native';

export interface MedicationRecord {
  id: string;
  animalId: string;
  medicationName: string;
  dosage: string;
  dosageUnit: string;
  frequency: string;
  startDate: string;
  endDate?: string;
  time: string;
  notes?: string;
  reminder: boolean;
  reminderTime?: string;
  completed?: boolean;
}

interface MedicationStore {
  medications: MedicationRecord[];
  isLoading: boolean;
  error: string | null;
  fetchMedications: () => Promise<void>;
  addMedication: (medication: MedicationRecord) => Promise<void>;
  updateMedication: (id: string, medication: Partial<MedicationRecord>) => Promise<void>;
  deleteMedication: (id: string) => Promise<void>;
  getTodayMedications: (animalId: string) => MedicationRecord[];
  getMedicationsByAnimalId: (animalId: string) => MedicationRecord[];
}

export const useMedicationStore = create<MedicationStore>((set, get) => ({
  medications: [],
  isLoading: false,
  error: null,
  
  fetchMedications: async () => {
    set({ isLoading: true, error: null });
    
    try {
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return;
      }
      
      const { data, error } = await supabase
        .from('medications')
        .select('*')
        .eq('user_id', user.id); // Defense-in-depth: explicit user filtering
      
      if (error) {
        throw error;
      }
      
      console.log('API Success (fetchMedications):', data);
      
      // Convert database fields to match our interface
      const formattedData = data.map((item: any) => ({
        id: item.id,
        animalId: item.animal_id,
        medicationName: item.medication_name,
        dosage: item.dosage,
        dosageUnit: item.dosage_unit,
        frequency: item.frequency,
        startDate: item.start_date,
        endDate: item.end_date,
        time: item.time,
        notes: item.notes,
        reminder: item.reminder,
        reminderTime: item.reminder_time,
        completed: item.completed,
      }));
      
      set({ medications: formattedData, isLoading: false });
    } catch (error: any) {
      console.error('Error fetching medications:', error);
      set({ error: error.message, isLoading: false });
    }
  },
  
  addMedication: async (medication) => {
    set({ isLoading: true, error: null });
    
    try {
      // Convert to database field names
      const dbMedication = {
        animal_id: medication.animalId,
        medication_name: medication.medicationName,
        dosage: medication.dosage,
        dosage_unit: medication.dosageUnit,
        frequency: medication.frequency,
        start_date: medication.startDate,
        end_date: medication.endDate,
        time: medication.time,
        notes: medication.notes,
        reminder: medication.reminder,
        reminder_time: medication.reminderTime,
        completed: medication.completed || false,
      };
      
      const { data, error } = await supabase
        .from('medications')
        .insert(dbMedication)
        .select();
      
      if (error) {
        throw error;
      }
      
      // For now, just add to local state
      // In a real app with Supabase, we'd use the returned data
      const newMedication = {
        ...medication,
        id: data?.[0]?.id || medication.id,
      };
      
      set(state => ({
        medications: [...state.medications, newMedication],
        isLoading: false
      }));
      
      toast.success('Medication added successfully');
    } catch (error: any) {
      console.error('Error adding medication:', error);
      set({ error: error.message, isLoading: false });
      toast.error('Failed to add medication');
      throw error;
    }
  },
  
  updateMedication: async (id, medication) => {
    set({ isLoading: true, error: null });
    
    try {
      // Convert to database field names
      const dbMedication: any = {};
      
      if (medication.animalId) dbMedication.animal_id = medication.animalId;
      if (medication.medicationName) dbMedication.medication_name = medication.medicationName;
      if (medication.dosage) dbMedication.dosage = medication.dosage;
      if (medication.dosageUnit) dbMedication.dosage_unit = medication.dosageUnit;
      if (medication.frequency) dbMedication.frequency = medication.frequency;
      if (medication.startDate) dbMedication.start_date = medication.startDate;
      if (medication.endDate !== undefined) dbMedication.end_date = medication.endDate;
      if (medication.time) dbMedication.time = medication.time;
      if (medication.notes !== undefined) dbMedication.notes = medication.notes;
      if (medication.reminder !== undefined) dbMedication.reminder = medication.reminder;
      if (medication.reminderTime !== undefined) dbMedication.reminder_time = medication.reminderTime;
      if (medication.completed !== undefined) dbMedication.completed = medication.completed;
      
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return;
      }
      
      const { error } = await supabase
        .from('medications')
        .update(dbMedication)
        .eq('id', id)
        .eq('user_id', user.id); // Defense-in-depth: ensure user owns record
      
      if (error) {
        throw error;
      }
      
      set(state => ({
        medications: state.medications.map(med => 
          med.id === id ? { ...med, ...medication } : med
        ),
        isLoading: false
      }));
      
      toast.success('Medication updated successfully');
    } catch (error: any) {
      console.error('Error updating medication:', error);
      set({ error: error.message, isLoading: false });
      toast.error('Failed to update medication');
    }
  },
  
  deleteMedication: async (id) => {
    set({ isLoading: true, error: null });
    
    try {
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return;
      }
      
      const { error } = await supabase
        .from('medications')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id); // Defense-in-depth: ensure user owns record
      
      if (error) {
        throw error;
      }
      
      set(state => ({
        medications: state.medications.filter(med => med.id !== id),
        isLoading: false
      }));
      
      toast.success('Medication deleted successfully');
    } catch (error: any) {
      console.error('Error deleting medication:', error);
      set({ error: error.message, isLoading: false });
      toast.error('Failed to delete medication');
    }
  },
  
  getTodayMedications: (animalId) => {
    const { medications } = get();
    const today = new Date();
    
    return medications.filter(med => {
      if (med.animalId !== animalId) return false;
      
      const startDate = new Date(med.startDate);
      const endDate = med.endDate ? new Date(med.endDate) : null;
      
      // Check if today is within the medication date range
      if (today < startDate) return false;
      if (endDate && today > endDate) return false;
      
      // For weekly medications, check if today is the same day of the week as the start date
      if (med.frequency === 'weekly') {
        return today.getDay() === startDate.getDay();
      }
      
      // For one-time medications, check if today is the same date as the start date
      if (med.frequency === 'once') {
        return today.toDateString() === startDate.toDateString();
      }
      
      // For daily medications, always return true if within date range
      return true;
    });
  },
  
  // Add the missing function
  getMedicationsByAnimalId: (animalId) => {
    const { medications } = get();
    return medications.filter(med => med.animalId === animalId);
  }
}));
