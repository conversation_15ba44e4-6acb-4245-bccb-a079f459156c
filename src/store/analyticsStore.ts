import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

export interface CustomDashboard {
  id: string;
  user_id: string;
  dashboard_name: string;
  dashboard_description?: string;
  dashboard_type: string;
  dashboard_category?: string;
  layout_config: any;
  widget_configs: any;
  filter_config?: any;
  refresh_interval: number;
  is_public: boolean;
  sharing_token?: string;
  shared_with?: any;
  access_level: string;
  last_viewed_at?: string;
  view_count: number;
  is_favorite: boolean;
  tags?: any;
  created_at: string;
  updated_at: string;
}

export interface DashboardWidget {
  id: string;
  dashboard_id: string;
  widget_name: string;
  widget_type: string;
  widget_category?: string;
  data_source: string;
  query_config: any;
  visualization_config: any;
  display_config?: any;
  aggregation_type?: string;
  time_range: string;
  grouping_config?: any;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface ScheduledReport {
  id: string;
  user_id: string;
  report_name: string;
  report_description?: string;
  report_type: string;
  report_template?: string;
  data_filters?: any;
  report_config: any;
  format_config?: any;
  schedule_type: string;
  schedule_config?: any;
  timezone: string;
  is_active: boolean;
  delivery_method: string;
  email_recipients?: any;
  last_generated_at?: string;
  next_generation_at?: string;
  generation_count: number;
  last_error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface ReportInstance {
  id: string;
  scheduled_report_id?: string;
  user_id: string;
  report_name: string;
  report_type: string;
  generation_trigger?: string;
  report_data: any;
  report_metadata?: any;
  file_url?: string;
  file_format?: string;
  file_size_bytes?: number;
  generation_status: string;
  generation_started_at?: string;
  generation_completed_at?: string;
  generation_duration_ms?: number;
  error_message?: string;
  download_count: number;
  last_downloaded_at?: string;
  expires_at?: string;
  created_at: string;
}

export interface DataVisualization {
  id: string;
  user_id: string;
  visualization_name: string;
  visualization_type: string;
  visualization_category?: string;
  data_source: string;
  data_query: any;
  data_filters?: any;
  chart_config: any;
  styling_config?: any;
  interaction_config?: any;
  is_public: boolean;
  sharing_token?: string;
  view_count: number;
  last_viewed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface AnalyticsInsight {
  id: string;
  user_id: string;
  animal_id?: string;
  insight_type: string;
  insight_category?: string;
  insight_title: string;
  insight_description: string;
  statistical_significance?: number;
  confidence_interval?: any;
  trend_direction?: string;
  trend_strength?: number;
  data_points?: any;
  statistical_tests?: any;
  correlation_coefficients?: any;
  analysis_period_start?: string;
  analysis_period_end?: string;
  forecast_period_start?: string;
  forecast_period_end?: string;
  recommended_actions?: any;
  priority_level?: string;
  insight_accuracy?: number;
  validation_date?: string;
  created_at: string;
  updated_at: string;
}

export interface BenchmarkData {
  id: string;
  benchmark_type: string;
  benchmark_category: string;
  classification_criteria: any;
  sample_size: number;
  metric_name: string;
  metric_unit?: string;
  mean_value?: number;
  median_value?: number;
  std_deviation?: number;
  min_value?: number;
  max_value?: number;
  percentile_25?: number;
  percentile_75?: number;
  percentile_90?: number;
  percentile_95?: number;
  confidence_level?: number;
  data_quality_score?: number;
  last_updated?: string;
  data_source?: string;
  calculation_method?: string;
  created_at: string;
  updated_at: string;
}

interface AnalyticsStore {
  // Dashboards
  dashboards: CustomDashboard[];
  currentDashboard: CustomDashboard | null;
  dashboardWidgets: DashboardWidget[];
  dashboardData: any;
  isLoadingDashboards: boolean;
  
  // Reports
  scheduledReports: ScheduledReport[];
  reportInstances: ReportInstance[];
  isLoadingReports: boolean;
  
  // Visualizations
  visualizations: DataVisualization[];
  isLoadingVisualizations: boolean;
  
  // Insights
  analyticsInsights: AnalyticsInsight[];
  isLoadingInsights: boolean;
  
  // Benchmarks
  benchmarkData: BenchmarkData[];
  isLoadingBenchmarks: boolean;
  
  // Actions - Dashboards
  fetchDashboards: () => Promise<void>;
  createDashboard: (dashboard: Partial<CustomDashboard>) => Promise<CustomDashboard | null>;
  updateDashboard: (dashboardId: string, updates: Partial<CustomDashboard>) => Promise<void>;
  deleteDashboard: (dashboardId: string) => Promise<void>;
  setCurrentDashboard: (dashboard: CustomDashboard | null) => void;
  generateDashboard: (dashboardId: string, filters?: any) => Promise<any>;
  
  // Actions - Widgets
  fetchDashboardWidgets: (dashboardId: string) => Promise<void>;
  addWidget: (dashboardId: string, widget: Partial<DashboardWidget>) => Promise<DashboardWidget | null>;
  updateWidget: (widgetId: string, updates: Partial<DashboardWidget>) => Promise<void>;
  removeWidget: (widgetId: string) => Promise<void>;
  
  // Actions - Reports
  fetchScheduledReports: () => Promise<void>;
  createScheduledReport: (report: Partial<ScheduledReport>) => Promise<ScheduledReport | null>;
  updateScheduledReport: (reportId: string, updates: Partial<ScheduledReport>) => Promise<void>;
  deleteScheduledReport: (reportId: string) => Promise<void>;
  generateReport: (reportConfig: any) => Promise<ReportInstance | null>;
  fetchReportInstances: () => Promise<void>;
  downloadReport: (reportId: string) => Promise<string | null>;
  
  // Actions - Visualizations
  fetchVisualizations: () => Promise<void>;
  createVisualization: (visualization: Partial<DataVisualization>) => Promise<DataVisualization | null>;
  updateVisualization: (visualizationId: string, updates: Partial<DataVisualization>) => Promise<void>;
  deleteVisualization: (visualizationId: string) => Promise<void>;
  
  // Actions - Insights
  fetchAnalyticsInsights: (animalId?: string) => Promise<void>;
  generateInsights: (animalId: string, analysisType: string) => Promise<void>;
  
  // Actions - Benchmarks
  fetchBenchmarkData: (benchmarkType?: string, category?: string) => Promise<void>;
  compareToBenchmark: (animalId: string, metric: string) => Promise<any>;
  
  // Utility Actions
  clearAnalyticsData: () => void;
}

export const useAnalyticsStore = create<AnalyticsStore>()(persist(
  (set, get) => ({
    // Initial state
    dashboards: [],
    currentDashboard: null,
    dashboardWidgets: [],
    dashboardData: null,
    isLoadingDashboards: false,
    scheduledReports: [],
    reportInstances: [],
    isLoadingReports: false,
    visualizations: [],
    isLoadingVisualizations: false,
    analyticsInsights: [],
    isLoadingInsights: false,
    benchmarkData: [],
    isLoadingBenchmarks: false,

    // Dashboard Actions
    fetchDashboards: async () => {
      set({ isLoadingDashboards: true });
      
      try {
        const { data, error } = await supabase
          .from('custom_dashboards')
          .select('*')
          .order('last_viewed_at', { ascending: false, nullsFirst: false })
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching dashboards:', error);
          toast.error('Failed to load dashboards');
          return;
        }

        set({ dashboards: data || [] });
        
        logApiCall('custom_dashboards', 'fetch', {
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchDashboards:', error);
        toast.error('Failed to load dashboards');
      } finally {
        set({ isLoadingDashboards: false });
      }
    },

    createDashboard: async (dashboard: Partial<CustomDashboard>) => {
      try {
        const { data, error } = await supabase
          .from('custom_dashboards')
          .insert({
            dashboard_name: dashboard.dashboard_name,
            dashboard_description: dashboard.dashboard_description,
            dashboard_type: dashboard.dashboard_type || 'custom',
            dashboard_category: dashboard.dashboard_category,
            layout_config: dashboard.layout_config || {},
            widget_configs: dashboard.widget_configs || {},
            filter_config: dashboard.filter_config,
            refresh_interval: dashboard.refresh_interval || 300,
            is_public: dashboard.is_public || false,
            access_level: dashboard.access_level || 'private',
            view_count: 0,
            is_favorite: dashboard.is_favorite || false,
            tags: dashboard.tags
          })
          .select()
          .single();

        if (error) {
          console.error('Error creating dashboard:', error);
          toast.error('Failed to create dashboard');
          return null;
        }

        const currentDashboards = get().dashboards;
        set({ dashboards: [data, ...currentDashboards] });
        
        logApiCall('custom_dashboards', 'create', {
          dashboardType: dashboard.dashboard_type,
          category: dashboard.dashboard_category
        });
        
        toast.success('Dashboard created successfully');
        return data;
        
      } catch (error) {
        console.error('Error in createDashboard:', error);
        toast.error('Failed to create dashboard');
        return null;
      }
    },

    updateDashboard: async (dashboardId: string, updates: Partial<CustomDashboard>) => {
      try {
        const { data, error } = await supabase
          .from('custom_dashboards')
          .update({
            ...updates,
            updated_at: new Date().toISOString()
          })
          .eq('id', dashboardId)
          .select()
          .single();

        if (error) {
          console.error('Error updating dashboard:', error);
          toast.error('Failed to update dashboard');
          return;
        }

        const currentDashboards = get().dashboards;
        const updatedDashboards = currentDashboards.map(dashboard => 
          dashboard.id === dashboardId ? data : dashboard
        );
        set({ dashboards: updatedDashboards });
        
        // Update current dashboard if it's the one being updated
        const currentDashboard = get().currentDashboard;
        if (currentDashboard && currentDashboard.id === dashboardId) {
          set({ currentDashboard: data });
        }
        
        logApiCall('custom_dashboards', 'update', { dashboardId });
        toast.success('Dashboard updated successfully');
        
      } catch (error) {
        console.error('Error in updateDashboard:', error);
        toast.error('Failed to update dashboard');
      }
    },

    deleteDashboard: async (dashboardId: string) => {
      try {
        const { error } = await supabase
          .from('custom_dashboards')
          .delete()
          .eq('id', dashboardId);

        if (error) {
          console.error('Error deleting dashboard:', error);
          toast.error('Failed to delete dashboard');
          return;
        }

        const currentDashboards = get().dashboards;
        const filteredDashboards = currentDashboards.filter(dashboard => dashboard.id !== dashboardId);
        set({ dashboards: filteredDashboards });
        
        // Clear current dashboard if it's the one being deleted
        const currentDashboard = get().currentDashboard;
        if (currentDashboard && currentDashboard.id === dashboardId) {
          set({ currentDashboard: null, dashboardData: null });
        }
        
        logApiCall('custom_dashboards', 'delete', { dashboardId });
        toast.success('Dashboard deleted successfully');
        
      } catch (error) {
        console.error('Error in deleteDashboard:', error);
        toast.error('Failed to delete dashboard');
      }
    },

    setCurrentDashboard: (dashboard: CustomDashboard | null) => {
      set({ currentDashboard: dashboard, dashboardData: null });
      
      if (dashboard) {
        // Update last viewed timestamp
        get().updateDashboard(dashboard.id, {
          last_viewed_at: new Date().toISOString(),
          view_count: dashboard.view_count + 1
        });
        
        // Fetch widgets for this dashboard
        get().fetchDashboardWidgets(dashboard.id);
      } else {
        set({ dashboardWidgets: [] });
      }
    },

    generateDashboard: async (dashboardId: string, filters?: any) => {
      set({ isLoadingDashboards: true });
      
      try {
        const { data, error } = await supabase.functions.invoke('generate-analytics-dashboard', {
          body: {
            dashboardId,
            filters,
            refreshCache: true
          }
        });

        if (error) {
          console.error('Error generating dashboard:', error);
          toast.error('Failed to generate dashboard');
          return null;
        }

        if (!data.success) {
          console.error('Dashboard generation failed:', data.error);
          toast.error(data.error || 'Dashboard generation failed');
          return null;
        }

        set({ dashboardData: data });
        
        logApiCall('analytics_dashboard', 'generate', {
          dashboardId,
          widgetCount: data.widgets?.length || 0,
          generationTime: data.metadata?.generationTime
        });
        
        return data;
        
      } catch (error) {
        console.error('Error in generateDashboard:', error);
        toast.error('Failed to generate dashboard');
        return null;
      } finally {
        set({ isLoadingDashboards: false });
      }
    },

    // Widget Actions
    fetchDashboardWidgets: async (dashboardId: string) => {
      try {
        const { data, error } = await supabase
          .from('dashboard_widgets')
          .select('*')
          .eq('dashboard_id', dashboardId)
          .eq('is_active', true)
          .order('sort_order');

        if (error) {
          console.error('Error fetching dashboard widgets:', error);
          return;
        }

        set({ dashboardWidgets: data || [] });
        
      } catch (error) {
        console.error('Error in fetchDashboardWidgets:', error);
      }
    },

    addWidget: async (dashboardId: string, widget: Partial<DashboardWidget>) => {
      try {
        const { data, error } = await supabase
          .from('dashboard_widgets')
          .insert({
            dashboard_id: dashboardId,
            widget_name: widget.widget_name,
            widget_type: widget.widget_type,
            widget_category: widget.widget_category,
            data_source: widget.data_source,
            query_config: widget.query_config || {},
            visualization_config: widget.visualization_config || {},
            display_config: widget.display_config,
            aggregation_type: widget.aggregation_type,
            time_range: widget.time_range || '30d',
            grouping_config: widget.grouping_config,
            is_active: true,
            sort_order: widget.sort_order || 0
          })
          .select()
          .single();

        if (error) {
          console.error('Error adding widget:', error);
          toast.error('Failed to add widget');
          return null;
        }

        const currentWidgets = get().dashboardWidgets;
        set({ dashboardWidgets: [...currentWidgets, data] });
        
        logApiCall('dashboard_widgets', 'create', {
          dashboardId,
          widgetType: widget.widget_type
        });
        
        toast.success('Widget added successfully');
        return data;
        
      } catch (error) {
        console.error('Error in addWidget:', error);
        toast.error('Failed to add widget');
        return null;
      }
    },

    updateWidget: async (widgetId: string, updates: Partial<DashboardWidget>) => {
      try {
        const { data, error } = await supabase
          .from('dashboard_widgets')
          .update({
            ...updates,
            updated_at: new Date().toISOString()
          })
          .eq('id', widgetId)
          .select()
          .single();

        if (error) {
          console.error('Error updating widget:', error);
          toast.error('Failed to update widget');
          return;
        }

        const currentWidgets = get().dashboardWidgets;
        const updatedWidgets = currentWidgets.map(widget => 
          widget.id === widgetId ? data : widget
        );
        set({ dashboardWidgets: updatedWidgets });
        
        toast.success('Widget updated successfully');
        
      } catch (error) {
        console.error('Error in updateWidget:', error);
        toast.error('Failed to update widget');
      }
    },

    removeWidget: async (widgetId: string) => {
      try {
        const { error } = await supabase
          .from('dashboard_widgets')
          .delete()
          .eq('id', widgetId);

        if (error) {
          console.error('Error removing widget:', error);
          toast.error('Failed to remove widget');
          return;
        }

        const currentWidgets = get().dashboardWidgets;
        const filteredWidgets = currentWidgets.filter(widget => widget.id !== widgetId);
        set({ dashboardWidgets: filteredWidgets });
        
        toast.success('Widget removed successfully');
        
      } catch (error) {
        console.error('Error in removeWidget:', error);
        toast.error('Failed to remove widget');
      }
    },

    // Report Actions
    fetchScheduledReports: async () => {
      set({ isLoadingReports: true });
      
      try {
        const { data, error } = await supabase
          .from('scheduled_reports')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching scheduled reports:', error);
          toast.error('Failed to load scheduled reports');
          return;
        }

        set({ scheduledReports: data || [] });
        
        logApiCall('scheduled_reports', 'fetch', {
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchScheduledReports:', error);
        toast.error('Failed to load scheduled reports');
      } finally {
        set({ isLoadingReports: false });
      }
    },

    createScheduledReport: async (report: Partial<ScheduledReport>) => {
      try {
        const { data, error } = await supabase
          .from('scheduled_reports')
          .insert(report)
          .select()
          .single();

        if (error) {
          console.error('Error creating scheduled report:', error);
          toast.error('Failed to create scheduled report');
          return null;
        }

        const currentReports = get().scheduledReports;
        set({ scheduledReports: [data, ...currentReports] });
        
        logApiCall('scheduled_reports', 'create', {
          reportType: report.report_type,
          scheduleType: report.schedule_type
        });
        
        toast.success('Scheduled report created successfully');
        return data;
        
      } catch (error) {
        console.error('Error in createScheduledReport:', error);
        toast.error('Failed to create scheduled report');
        return null;
      }
    },

    updateScheduledReport: async (reportId: string, updates: Partial<ScheduledReport>) => {
      try {
        const { data, error } = await supabase
          .from('scheduled_reports')
          .update({
            ...updates,
            updated_at: new Date().toISOString()
          })
          .eq('id', reportId)
          .select()
          .single();

        if (error) {
          console.error('Error updating scheduled report:', error);
          toast.error('Failed to update scheduled report');
          return;
        }

        const currentReports = get().scheduledReports;
        const updatedReports = currentReports.map(report => 
          report.id === reportId ? data : report
        );
        set({ scheduledReports: updatedReports });
        
        toast.success('Scheduled report updated successfully');
        
      } catch (error) {
        console.error('Error in updateScheduledReport:', error);
        toast.error('Failed to update scheduled report');
      }
    },

    deleteScheduledReport: async (reportId: string) => {
      try {
        const { error } = await supabase
          .from('scheduled_reports')
          .delete()
          .eq('id', reportId);

        if (error) {
          console.error('Error deleting scheduled report:', error);
          toast.error('Failed to delete scheduled report');
          return;
        }

        const currentReports = get().scheduledReports;
        const filteredReports = currentReports.filter(report => report.id !== reportId);
        set({ scheduledReports: filteredReports });
        
        toast.success('Scheduled report deleted successfully');
        
      } catch (error) {
        console.error('Error in deleteScheduledReport:', error);
        toast.error('Failed to delete scheduled report');
      }
    },

    generateReport: async (reportConfig: any) => {
      set({ isLoadingReports: true });
      
      try {
        const { data, error } = await supabase.functions.invoke('generate-analytics-report', {
          body: reportConfig
        });

        if (error) {
          console.error('Error generating report:', error);
          toast.error('Failed to generate report');
          return null;
        }

        if (!data.success) {
          console.error('Report generation failed:', data.error);
          toast.error(data.error || 'Report generation failed');
          return null;
        }

        // Refresh report instances
        await get().fetchReportInstances();
        
        logApiCall('analytics_report', 'generate', {
          reportType: reportConfig.reportType,
          format: reportConfig.format,
          generationTime: data.generationTime
        });
        
        toast.success('Report generated successfully');
        return data;
        
      } catch (error) {
        console.error('Error in generateReport:', error);
        toast.error('Failed to generate report');
        return null;
      } finally {
        set({ isLoadingReports: false });
      }
    },

    fetchReportInstances: async () => {
      try {
        const { data, error } = await supabase
          .from('report_instances')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(50);

        if (error) {
          console.error('Error fetching report instances:', error);
          return;
        }

        set({ reportInstances: data || [] });
        
      } catch (error) {
        console.error('Error in fetchReportInstances:', error);
      }
    },

    downloadReport: async (reportId: string) => {
      try {
        const report = get().reportInstances.find(r => r.id === reportId);
        if (!report || !report.file_url) {
          toast.error('Report file not available');
          return null;
        }

        // Update download count
        await supabase
          .from('report_instances')
          .update({ 
            download_count: report.download_count + 1,
            last_downloaded_at: new Date().toISOString()
          })
          .eq('id', reportId);

        // Refresh report instances
        await get().fetchReportInstances();
        
        logApiCall('report_instances', 'download', { reportId });
        return report.file_url;
        
      } catch (error) {
        console.error('Error in downloadReport:', error);
        toast.error('Failed to download report');
        return null;
      }
    },

    // Visualization Actions
    fetchVisualizations: async () => {
      set({ isLoadingVisualizations: true });
      
      try {
        const { data, error } = await supabase
          .from('data_visualizations')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching visualizations:', error);
          toast.error('Failed to load visualizations');
          return;
        }

        set({ visualizations: data || [] });
        
        logApiCall('data_visualizations', 'fetch', {
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchVisualizations:', error);
        toast.error('Failed to load visualizations');
      } finally {
        set({ isLoadingVisualizations: false });
      }
    },

    createVisualization: async (visualization: Partial<DataVisualization>) => {
      try {
        const { data, error } = await supabase
          .from('data_visualizations')
          .insert(visualization)
          .select()
          .single();

        if (error) {
          console.error('Error creating visualization:', error);
          toast.error('Failed to create visualization');
          return null;
        }

        const currentVisualizations = get().visualizations;
        set({ visualizations: [data, ...currentVisualizations] });
        
        logApiCall('data_visualizations', 'create', {
          visualizationType: visualization.visualization_type
        });
        
        toast.success('Visualization created successfully');
        return data;
        
      } catch (error) {
        console.error('Error in createVisualization:', error);
        toast.error('Failed to create visualization');
        return null;
      }
    },

    updateVisualization: async (visualizationId: string, updates: Partial<DataVisualization>) => {
      try {
        const { data, error } = await supabase
          .from('data_visualizations')
          .update({
            ...updates,
            updated_at: new Date().toISOString()
          })
          .eq('id', visualizationId)
          .select()
          .single();

        if (error) {
          console.error('Error updating visualization:', error);
          toast.error('Failed to update visualization');
          return;
        }

        const currentVisualizations = get().visualizations;
        const updatedVisualizations = currentVisualizations.map(viz => 
          viz.id === visualizationId ? data : viz
        );
        set({ visualizations: updatedVisualizations });
        
        toast.success('Visualization updated successfully');
        
      } catch (error) {
        console.error('Error in updateVisualization:', error);
        toast.error('Failed to update visualization');
      }
    },

    deleteVisualization: async (visualizationId: string) => {
      try {
        const { error } = await supabase
          .from('data_visualizations')
          .delete()
          .eq('id', visualizationId);

        if (error) {
          console.error('Error deleting visualization:', error);
          toast.error('Failed to delete visualization');
          return;
        }

        const currentVisualizations = get().visualizations;
        const filteredVisualizations = currentVisualizations.filter(viz => viz.id !== visualizationId);
        set({ visualizations: filteredVisualizations });
        
        toast.success('Visualization deleted successfully');
        
      } catch (error) {
        console.error('Error in deleteVisualization:', error);
        toast.error('Failed to delete visualization');
      }
    },

    // Insights Actions
    fetchAnalyticsInsights: async (animalId?: string) => {
      set({ isLoadingInsights: true });
      
      try {
        let query = supabase
          .from('analytics_insights')
          .select('*')
          .order('created_at', { ascending: false });
        
        if (animalId) {
          query = query.eq('animal_id', animalId);
        }
        
        const { data, error } = await query;

        if (error) {
          console.error('Error fetching analytics insights:', error);
          toast.error('Failed to load insights');
          return;
        }

        set({ analyticsInsights: data || [] });
        
        logApiCall('analytics_insights', 'fetch', {
          animalId,
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchAnalyticsInsights:', error);
        toast.error('Failed to load insights');
      } finally {
        set({ isLoadingInsights: false });
      }
    },

    generateInsights: async (animalId: string, analysisType: string) => {
      try {
        // This would call an AI service to generate insights
        // For now, we'll create a mock insight
        const mockInsight = {
          animal_id: animalId,
          insight_type: analysisType,
          insight_category: 'health',
          insight_title: 'Health Trend Analysis',
          insight_description: 'Based on recent data, your pet shows stable health indicators with minor improvements in activity levels.',
          statistical_significance: 0.85,
          trend_direction: 'stable',
          trend_strength: 0.7,
          priority_level: 'medium',
          recommended_actions: [
            'Continue current care routine',
            'Monitor activity levels',
            'Schedule regular check-ups'
          ]
        };
        
        const { data, error } = await supabase
          .from('analytics_insights')
          .insert(mockInsight)
          .select()
          .single();

        if (error) {
          console.error('Error generating insight:', error);
          toast.error('Failed to generate insight');
          return;
        }

        const currentInsights = get().analyticsInsights;
        set({ analyticsInsights: [data, ...currentInsights] });
        
        toast.success('Insight generated successfully');
        
      } catch (error) {
        console.error('Error in generateInsights:', error);
        toast.error('Failed to generate insight');
      }
    },

    // Benchmark Actions
    fetchBenchmarkData: async (benchmarkType?: string, category?: string) => {
      set({ isLoadingBenchmarks: true });
      
      try {
        let query = supabase
          .from('benchmark_data')
          .select('*')
          .order('last_updated', { ascending: false, nullsFirst: false })
          .order('created_at', { ascending: false });
        
        if (benchmarkType) {
          query = query.eq('benchmark_type', benchmarkType);
        }
        
        if (category) {
          query = query.eq('benchmark_category', category);
        }
        
        const { data, error } = await query;

        if (error) {
          console.error('Error fetching benchmark data:', error);
          toast.error('Failed to load benchmark data');
          return;
        }

        set({ benchmarkData: data || [] });
        
        logApiCall('benchmark_data', 'fetch', {
          benchmarkType,
          category,
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchBenchmarkData:', error);
        toast.error('Failed to load benchmark data');
      } finally {
        set({ isLoadingBenchmarks: false });
      }
    },

    compareToBenchmark: async (animalId: string, metric: string) => {
      try {
        // This would perform actual benchmark comparison
        // For now, we'll return mock comparison data
        const comparison = {
          animalValue: Math.random() * 50 + 50,
          benchmarkMean: Math.random() * 50 + 50,
          percentile: Math.floor(Math.random() * 100),
          comparison: 'above_average', // 'above_average', 'average', 'below_average'
          confidence: 0.85
        };
        
        logApiCall('benchmark_comparison', 'compare', {
          animalId,
          metric
        });
        
        return comparison;
        
      } catch (error) {
        console.error('Error in compareToBenchmark:', error);
        toast.error('Failed to compare to benchmark');
        return null;
      }
    },

    // Utility Actions
    clearAnalyticsData: () => {
      set({
        dashboards: [],
        currentDashboard: null,
        dashboardWidgets: [],
        dashboardData: null,
        scheduledReports: [],
        reportInstances: [],
        visualizations: [],
        analyticsInsights: [],
        benchmarkData: [],
        isLoadingDashboards: false,
        isLoadingReports: false,
        isLoadingVisualizations: false,
        isLoadingInsights: false,
        isLoadingBenchmarks: false
      });
    }
  }),
  {
    name: `analytics-store-2b237ff5-0e25-49db-b0c2-56f261d0a573`,
    storage: createJSONStorage(() => AsyncStorage),
    partialize: (state) => ({
      // Only persist non-sensitive data
      dashboards: state.dashboards,
      currentDashboard: state.currentDashboard,
      scheduledReports: state.scheduledReports,
      visualizations: state.visualizations.filter(viz => !viz.is_public),
      analyticsInsights: state.analyticsInsights
    })
  }
));

// Helper functions for analytics features
export const getDashboardTypeColor = (type: string): string => {
  switch (type) {
    case 'health': return '#10B981';
    case 'activity': return '#3B82F6';
    case 'nutrition': return '#F59E0B';
    case 'comprehensive': return '#8B5CF6';
    case 'custom': return '#6B7280';
    default: return '#6B7280';
  }
};

export const getReportStatusColor = (status: string): string => {
  switch (status) {
    case 'completed': return '#10B981';
    case 'generating': return '#F59E0B';
    case 'pending': return '#6B7280';
    case 'failed': return '#EF4444';
    default: return '#6B7280';
  }
};

export const getInsightPriorityColor = (priority: string): string => {
  switch (priority) {
    case 'critical': return '#DC2626';
    case 'high': return '#EF4444';
    case 'medium': return '#F59E0B';
    case 'low': return '#10B981';
    default: return '#6B7280';
  }
};

export const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return 'N/A';
  
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

export const formatDuration = (ms: number | undefined): string => {
  if (!ms) return 'N/A';
  
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  return `${(ms / 60000).toFixed(1)}m`;
};

export const getVisualizationIcon = (type: string): string => {
  const icons = {
    'line_chart': '📈',
    'bar_chart': '📊',
    'pie_chart': '🥧',
    'heatmap': '🔥',
    'gauge': '⏱️',
    'table': '📋',
    'kpi': '🎯'
  };
  
  return icons[type] || '📊';
};