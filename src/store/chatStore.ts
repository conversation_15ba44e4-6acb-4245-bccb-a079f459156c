import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../supabase/client';
import { toast } from 'sonner-native';

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  attachments?: {
    type: 'image' | 'file';
    url: string;
    name: string;
    size?: number;
  }[];
  isLoading?: boolean;
  isError?: boolean;
  conversationId: string;
  animalId?: string;
}

export interface Conversation {
  id: string;
  title: string;
  animalId?: string;
  animalName?: string;
  lastMessage?: string;
  lastMessageAt: string;
  createdAt: string;
  messageCount: number;
}

interface ChatState {
  // Data
  conversations: Conversation[];
  messages: ChatMessage[];
  currentConversationId: string | null;
  
  // Loading states
  isLoading: boolean;
  isSending: boolean;
  isLoadingConversations: boolean;
  
  // Actions
  sendMessage: (content: string, attachments?: File[], animalId?: string) => Promise<void>;
  loadConversations: () => Promise<void>;
  loadMessages: (conversationId: string) => Promise<void>;
  createNewConversation: (animalId?: string, animalName?: string) => Promise<string>;
  deleteConversation: (conversationId: string) => Promise<void>;
  setCurrentConversation: (conversationId: string | null) => void;
  clearMessages: () => void;
  
  // Getters
  getCurrentConversation: () => Conversation | null;
  getConversationMessages: (conversationId: string) => ChatMessage[];
}

export const useChatStore = create<ChatState>()(persist(
  (set, get) => ({
    // Initial state
    conversations: [],
    messages: [],
    currentConversationId: null,
    isLoading: false,
    isSending: false,
    isLoadingConversations: false,

    // Send Message
    sendMessage: async (content: string, attachments?: File[], animalId?: string) => {
      const state = get();
      let conversationId = state.currentConversationId;
      
      // Create new conversation if none exists
      if (!conversationId) {
        conversationId = await get().createNewConversation(animalId);
      }
      
      if (!conversationId) {
        toast.error('Failed to create conversation');
        return;
      }

      set({ isSending: true });
      
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
          throw new Error('No active session');
        }

        // Create user message
        const userMessageId = `user_${Date.now()}`;
        const userMessage: ChatMessage = {
          id: userMessageId,
          content,
          role: 'user',
          timestamp: new Date().toISOString(),
          conversationId,
          animalId,
          attachments: attachments?.map(file => ({
            type: file.type?.startsWith('image/') ? 'image' : 'file',
            url: file.uri || '',
            name: file.name || 'file',
            size: file.size
          })) as any
        };

        // Add user message to state immediately
        set(state => ({
          messages: [...state.messages, userMessage]
        }));

        // Create loading message for AI response
        const aiMessageId = `ai_${Date.now()}`;
        const loadingMessage: ChatMessage = {
          id: aiMessageId,
          content: '',
          role: 'assistant',
          timestamp: new Date().toISOString(),
          conversationId,
          animalId,
          isLoading: true
        };

        set(state => ({
          messages: [...state.messages, loadingMessage]
        }));

        // Prepare request payload
        const payload: any = {
          message: content,
          conversationId,
          animalId
        };

        // Handle file uploads if any
        if (attachments && attachments.length > 0) {
          const uploadedFiles = [];
          
          for (const file of attachments) {
            try {
              // Upload file to Supabase storage
              const fileName = `${Date.now()}_${file.name}`;
              const { data: uploadData, error: uploadError } = await supabase.storage
                .from('ai_uploads')
                .upload(fileName, file as any, {
                  cacheControl: '3600',
                  upsert: false
                });

              if (uploadError) {
                console.error('Upload error:', uploadError);
                continue;
              }

              // Get public URL
              const { data: { publicUrl } } = supabase.storage
                .from('ai_uploads')
                .getPublicUrl(fileName);

              uploadedFiles.push({
                url: publicUrl,
                type: file.type?.startsWith('image/') ? 'image' : 'file',
                name: file.name || 'file'
              });
            } catch (error) {
              console.error('File upload error:', error);
            }
          }
          
          payload.attachments = uploadedFiles;
        }

        // Call AI chat function
        const response = await fetch(
          `${supabase.supabaseUrl}/functions/v1/handle-ai-chat`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${session.access_token}`,
            },
            body: JSON.stringify(payload),
          }
        );

        if (!response.ok) {
          let errorMessage = 'Failed to send message';
          try {
            const errorData = await response.json();
            errorMessage = errorData.error || errorMessage;
            
            // Provide user-friendly error messages
            if (errorMessage.includes('API key not configured')) {
              errorMessage = 'AI service is temporarily unavailable. Please try again later.';
            } else if (errorMessage.includes('Unauthorized')) {
              errorMessage = 'Please log in again to continue chatting.';
            } else if (errorMessage.includes('rate limit')) {
              errorMessage = 'Too many requests. Please wait a moment and try again.';
            }
          } catch (parseError) {
            console.error('Error parsing error response:', parseError);
          }
          throw new Error(errorMessage);
        }

        const result = await response.json();
        
        // Update AI message with response
        const aiResponse: ChatMessage = {
          id: aiMessageId,
          content: result.response,
          role: 'assistant',
          timestamp: new Date().toISOString(),
          conversationId,
          animalId,
          isLoading: false
        };

        // Update messages in state
        set(state => ({
          messages: state.messages.map(msg => 
            msg.id === aiMessageId ? aiResponse : msg
          )
        }));

        // Update conversation with last message
        set(state => ({
          conversations: state.conversations.map(conv => 
            conv.id === conversationId 
              ? {
                  ...conv,
                  lastMessage: result.response.substring(0, 100),
                  lastMessageAt: new Date().toISOString(),
                  messageCount: conv.messageCount + 2
                }
              : conv
          )
        }));
        
      } catch (error) {
        console.error('Send message error:', error);
        
        // Show user-friendly error message
        const errorMessage = error.message || 'Failed to get AI response';
        toast.error(errorMessage);
        
        // Replace loading message with error message
        const errorResponse: ChatMessage = {
          id: `error_${Date.now()}`,
          content: `⚠️ Sorry, I'm having trouble responding right now. ${errorMessage.includes('temporarily unavailable') ? 'Please try again in a few moments.' : 'Please try again or contact support if the issue persists.'}`,
          role: 'assistant',
          timestamp: new Date().toISOString(),
          conversationId,
          animalId,
          isError: true
        };
        
        set(state => ({
          messages: state.messages.map(msg => 
            msg.isLoading ? errorResponse : msg
          )
        }));
      } finally {
        set({ isSending: false });
      }
    },

    // Load Conversations
    loadConversations: async () => {
      set({ isLoadingConversations: true });
      
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
          throw new Error('No active session');
        }

        const { data, error } = await supabase
          .from('ai_conversations')
          .select(`
            id,
            title,
            animal_id,
            last_message,
            last_message_at,
            created_at,
            message_count,
            animals(name)
          `)
          .eq('user_id', session.user.id)
          .order('last_message_at', { ascending: false });
        
        if (error) throw error;
        
        const conversations: Conversation[] = (data || []).map(conv => ({
          id: conv.id,
          title: conv.title,
          animalId: conv.animal_id,
          animalName: (conv as any).animals?.name,
          lastMessage: conv.last_message,
          lastMessageAt: conv.last_message_at,
          createdAt: conv.created_at,
          messageCount: conv.message_count || 0
        }));
        
        set({ conversations });
        
      } catch (error) {
        console.error('Load conversations error:', error);
        toast.error('Failed to load conversations');
      } finally {
        set({ isLoadingConversations: false });
      }
    },

    // Load Messages
    loadMessages: async (conversationId: string) => {
      set({ isLoading: true });
      
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
          throw new Error('No active session');
        }

        const { data, error } = await supabase
          .from('ai_messages')
          .select('*')
          .eq('conversation_id', conversationId)
          .eq('user_id', session.user.id)
          .order('created_at', { ascending: true });
        
        if (error) throw error;
        
        const messages: ChatMessage[] = (data || []).map(msg => ({
          id: msg.id,
          content: msg.content,
          role: msg.role,
          timestamp: msg.created_at,
          conversationId: msg.conversation_id,
          animalId: msg.animal_id,
          attachments: msg.attachments
        }));
        
        set({ messages, currentConversationId: conversationId });
        
      } catch (error) {
        console.error('Load messages error:', error);
        toast.error('Failed to load messages');
      } finally {
        set({ isLoading: false });
      }
    },

    // Create New Conversation
    createNewConversation: async (animalId?: string, animalName?: string) => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
          throw new Error('No active session');
        }

        const title = animalName ? `Chat about ${animalName}` : 'New Chat';
        
        const { data, error } = await supabase
          .from('ai_conversations')
          .insert({
            user_id: session.user.id,
            animal_id: animalId,
            title,
            created_at: new Date().toISOString(),
            last_message_at: new Date().toISOString(),
            message_count: 0
          })
          .select()
          .single();
        
        if (error) throw error;
        
        const newConversation: Conversation = {
          id: data.id,
          title: data.title,
          animalId: data.animal_id,
          animalName,
          lastMessage: '',
          lastMessageAt: data.last_message_at,
          createdAt: data.created_at,
          messageCount: 0
        };
        
        set(state => ({
          conversations: [newConversation, ...state.conversations],
          currentConversationId: data.id,
          messages: []
        }));
        
        return data.id;
        
      } catch (error) {
        console.error('Create conversation error:', error);
        toast.error('Failed to create conversation');
        return null;
      }
    },

    // Delete Conversation
    deleteConversation: async (conversationId: string) => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
          throw new Error('No active session');
        }

        const { error } = await supabase
          .from('ai_conversations')
          .delete()
          .eq('id', conversationId)
          .eq('user_id', session.user.id);
        
        if (error) throw error;
        
        set(state => ({
          conversations: state.conversations.filter(conv => conv.id !== conversationId),
          currentConversationId: state.currentConversationId === conversationId ? null : state.currentConversationId,
          messages: state.currentConversationId === conversationId ? [] : state.messages
        }));
        
        toast.success('Conversation deleted');
        
      } catch (error) {
        console.error('Delete conversation error:', error);
        toast.error('Failed to delete conversation');
      }
    },

    // Set Current Conversation
    setCurrentConversation: (conversationId: string | null) => {
      set({ currentConversationId: conversationId });
      if (conversationId) {
        get().loadMessages(conversationId);
      } else {
        set({ messages: [] });
      }
    },

    // Clear Messages
    clearMessages: () => {
      set({ messages: [], currentConversationId: null });
    },

    // Getters
    getCurrentConversation: () => {
      const state = get();
      return state.conversations.find(conv => conv.id === state.currentConversationId) || null;
    },

    getConversationMessages: (conversationId: string) => {
      const state = get();
      return state.messages.filter(msg => msg.conversationId === conversationId);
    },
  }),
  {
    name: 'chat-store',
    storage: createJSONStorage(() => AsyncStorage),
    partialize: (state) => ({
      conversations: state.conversations,
      currentConversationId: state.currentConversationId
    })
  }
));