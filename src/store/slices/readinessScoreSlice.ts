/**
 * @magic_description Readiness Score Store Slice
 * Handles readiness score data and operations
 * Extracted from aiStore for better maintainability
 */

import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface ReadinessScore {
  score_id: string;
  animal_id: string;
  user_id: string;
  score_value: number;
  score_category: string;
  generated_at: string;
}

export interface ReadinessScoreState {
  readinessScores: ReadinessScore[];
  isLoadingReadinessScores: boolean;
}

export const readinessScoreOperations = {
  // Fetch Readiness Scores
  fetchReadinessScores: async (animalId?: string) => {
    try {
      let query = supabase
        .from('ai_readiness_scores')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      console.log(`Fetched ${data?.length || 0} readiness scores`);
      return data || [];
      
    } catch (error) {
      console.error('Error fetching readiness scores:', error);
      toast.error('Failed to fetch readiness scores');
      throw error;
    }
  },

  // Get Latest Readiness Score
  getLatestReadinessScore: (scores: ReadinessScore[], animalId: string) => {
    const filtered = scores.filter(s => s.animal_id === animalId);
    return filtered.length > 0 ? filtered[0] : null;
  },
};

export const readinessScoreInitialState: ReadinessScoreState = {
  readinessScores: [],
  isLoadingReadinessScores: false,
};