import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface ChatConversation {
  id: string;
  user_id: string;
  animal_id?: string;
  title: string;
  last_message_at: string;
  message_count: number;
  created_at: string;
  updated_at: string;
}

export interface ChatConversationsState {
  conversations: ChatConversation[];
  currentConversationId: string | null;
  isLoading: boolean;
  error: string | null;
}

export const chatConversationsInitialState: ChatConversationsState = {
  conversations: [],
  currentConversationId: null,
  isLoading: false,
  error: null,
};

export const chatConversationsOperations = {
  fetchConversations: async (): Promise<ChatConversation[]> => {
    try {
      const { data, error } = await supabase
        .from('ai_chat_conversations')
        .select('*')
        .order('last_message_at', { ascending: false });

      if (error) {
        console.error('Error fetching conversations:', error);
        toast.error('Failed to load conversations');
        throw error;
      }

      return data || [];
    } catch (error: any) {
      console.error('Error in fetchConversations:', error);
      throw error;
    }
  },

  createConversation: async (title: string, animalId?: string): Promise<ChatConversation> => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      const conversationData = {
        user_id: session.user.id,
        animal_id: animalId,
        title,
        last_message_at: new Date().toISOString(),
        message_count: 0,
      };

      const { data, error } = await supabase
        .from('ai_chat_conversations')
        .insert(conversationData)
        .select()
        .single();

      if (error) {
        console.error('Error creating conversation:', error);
        toast.error('Failed to create conversation');
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error in createConversation:', error);
      throw error;
    }
  },

  updateConversation: async (conversationId: string, updates: Partial<ChatConversation>): Promise<void> => {
    try {
      const { error } = await supabase
        .from('ai_chat_conversations')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', conversationId);

      if (error) {
        console.error('Error updating conversation:', error);
        toast.error('Failed to update conversation');
        throw error;
      }
    } catch (error: any) {
      console.error('Error in updateConversation:', error);
      throw error;
    }
  },

  deleteConversation: async (conversationId: string): Promise<void> => {
    try {
      const { error } = await supabase
        .from('ai_chat_conversations')
        .delete()
        .eq('id', conversationId);

      if (error) {
        console.error('Error deleting conversation:', error);
        toast.error('Failed to delete conversation');
        throw error;
      }

      toast.success('Conversation deleted');
    } catch (error: any) {
      console.error('Error in deleteConversation:', error);
      throw error;
    }
  },
};