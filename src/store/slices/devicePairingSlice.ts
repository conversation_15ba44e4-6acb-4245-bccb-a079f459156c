import { Device } from '../../mocks/devices';
import { toast } from 'sonner-native';
import { useAnimalStore } from '../animalStore';
import { deviceApiOperations } from './deviceApiSlice';

export const devicePairingOperations = {
  pairDevice: async (set: any, get: any, deviceId: string, animalId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const device = get().devices.find((d: any) => d.id === deviceId);
      if (!device) {
        throw new Error('Device not found');
      }

      // Optimistic UI update
      const updatedDevice = { ...device, status: 'paired' as const, last_sync_time: new Date().toISOString() };
      set((state: any) => ({
        devices: state.devices.map((d: any) => 
          d.id === deviceId ? updatedDevice : d
        ),
        pairedDevices: [
          ...state.pairedDevices.filter((d: any) => d.id !== deviceId),
          updatedDevice
        ],
      }));
      
      // Update device status in Supabase
      const deviceUpdateResult = await deviceApiOperations.updateDevice(set, get, deviceId, {
        status: 'paired',
        last_sync_time: new Date().toISOString()
      });
      
      if (!deviceUpdateResult) {
        throw new Error('Failed to update device status');
      }
      
      // Update animal's device info via animalStore
      await useAnimalStore.getState().updateAnimal(animalId, {
        deviceId: deviceId,
        deviceName: device.name,
        deviceStatus: 'paired',
        lastSyncTime: Date.now(),
      });

      set({ isLoading: false });
      toast.success(`${device.name} paired successfully with animal!`);
    } catch (error: any) {
      console.error('Error pairing device:', error);
      const errorMessage = error.message || 'Failed to pair device';
      
      // Revert optimistic update on error
      const device = get().devices.find((d: any) => d.id === deviceId);
      if (device) {
        set((state: any) => ({
          devices: state.devices.map((d: any) => 
            d.id === deviceId 
              ? { ...d, status: 'disconnected' } 
              : d
          ),
          pairedDevices: state.pairedDevices.filter((d: any) => d.id !== deviceId),
          error: errorMessage,
          isLoading: false
        }));
      } else {
        set({ error: errorMessage, isLoading: false });
      }
      
      toast.error(`Pairing failed: ${errorMessage}`);
      throw error;
    }
  },
  
  unpairDevice: async (set: any, get: any, deviceId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const device = get().devices.find((d: any) => d.id === deviceId);
      if (!device) {
        throw new Error('Device not found');
      }

      // Find the animal this device is paired with
      const animalStore = useAnimalStore.getState();
      const pairedAnimal = animalStore.animals.find(animal => animal.deviceId === deviceId);
      
      // Optimistic UI update
      const updatedDevice = { ...device, status: 'disconnected' as const, last_sync_time: new Date().toISOString() };
      set((state: any) => ({
        devices: state.devices.map((d: any) => 
          d.id === deviceId ? updatedDevice : d
        ),
        pairedDevices: state.pairedDevices.filter((d: any) => d.id !== deviceId),
      }));

      // Update device status in Supabase
      const deviceUpdateResult = await deviceApiOperations.updateDevice(set, get, deviceId, {
        status: 'disconnected',
        last_sync_time: new Date().toISOString()
      });
      
      if (!deviceUpdateResult) {
        throw new Error('Failed to update device status');
      }

      // Update the animal to remove device association
      if (pairedAnimal) {
        await useAnimalStore.getState().updateAnimal(pairedAnimal.id, {
          deviceId: null,
          deviceName: null,
          deviceStatus: 'disconnected',
          lastSyncTime: null,
        });
      }

      set({ isLoading: false });
      toast.success(`${device.name} unpaired successfully!`);
    } catch (error: any) {
      console.error('Error unpairing device:', error);
      const errorMessage = error.message || 'Failed to unpair device';
      
      // Revert optimistic update on error
      const device = get().devices.find((d: any) => d.id === deviceId);
      if (device) {
        set((state: any) => ({
          devices: state.devices.map((d: any) => 
            d.id === deviceId 
              ? { ...d, status: 'paired' } 
              : d
          ),
          pairedDevices: [
            ...state.pairedDevices.filter((d: any) => d.id !== deviceId),
            { ...device, status: 'paired' }
          ],
          error: errorMessage,
          isLoading: false
        }));
      } else {
        set({ error: errorMessage, isLoading: false });
      }
      
      toast.error(`Unpairing failed: ${errorMessage}`);
      throw error;
    }
  },
};