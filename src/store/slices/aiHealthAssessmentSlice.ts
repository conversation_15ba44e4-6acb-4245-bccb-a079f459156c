import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface HealthAssessment {
  assessment_id: string;
  animal_id: string;
  user_id: string;
  assessment_text: string;
  severity_level: string;
  generated_at: string;
}

export interface HealthAssessmentState {
  healthAssessments: HealthAssessment[];
  isLoadingHealthAssessments: boolean;
}

export const healthAssessmentInitialState: HealthAssessmentState = {
  healthAssessments: [],
  isLoadingHealthAssessments: false,
};

export const healthAssessmentOperations = {
  // Fetch Health Assessments
  fetchHealthAssessments: async (set: any, get: any, animalId?: string) => {
    set({ isLoadingHealthAssessments: true });
    
    try {
      let query = supabase
        .from('ai_health_assessments')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      set({ healthAssessments: data || [] });
      console.log(`Fetched ${data?.length || 0} health assessments`);
      
    } catch (error) {
      console.error('Error fetching health assessments:', error);
      toast.error('Failed to fetch health assessments');
    } finally {
      set({ isLoadingHealthAssessments: false });
    }
  },
  
  // Get latest health assessment for an animal
  getLatestHealthAssessment: (get: any, animalId: string) => {
    const assessments = get().healthAssessments.filter((a: HealthAssessment) => a.animal_id === animalId);
    return assessments.length > 0 ? assessments[0] : null;
  },
};