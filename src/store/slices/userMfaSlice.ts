import { supabase } from '../../supabase/client';

export interface UserMfaState {
  // MFA state is managed in the user profile
}

export const userMfaInitialState: UserMfaState = {};

export const userMfaOperations = {
  checkMfaStatus: async (set: any, get: any) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return false;
      }
      
      // Check if user has MFA enabled
      const { data: mfaData, error } = await supabase
        .from('user_mfa_settings')
        .select('is_enabled')
        .eq('user_id', user.id)
        .eq('is_enabled', true)
        .single();
      
      const mfaEnabled = !error && mfaData?.is_enabled === true;
      
      // Update local state
      set((state: any) => ({
        user: state.user ? { ...state.user, mfaEnabled } : null
      }));
      
      return mfaEnabled;
    } catch (error) {
      console.error('Error checking MFA status:', error);
      return false;
    }
  },
  
  updateMfaStatus: (set: any, enabled: boolean) => {
    set((state: any) => ({
      user: state.user ? { ...state.user, mfaEnabled: enabled } : null
    }));
  },
};