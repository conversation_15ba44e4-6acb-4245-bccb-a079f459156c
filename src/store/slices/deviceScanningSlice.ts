import { StateCreator } from 'zustand';
import { DeviceStore } from '../deviceStore';
import { Device } from '../../types/device';

export interface DeviceScanningSlice {
  // Scanning state
  isScanning: boolean;
  scanError: string | null;
  discoveredDevices: Device[];
  scanDuration: number;
  
  // Scanning actions
  startScanning: () => Promise<void>;
  stopScanning: () => void;
  addDiscoveredDevice: (device: Device) => void;
  clearDiscoveredDevices: () => void;
  setScanError: (error: string | null) => void;
}

export const createDeviceScanningSlice: StateCreator<
  DeviceStore,
  [],
  [],
  DeviceScanningSlice
> = (set, get) => ({
  // Initial state
  isScanning: false,
  scanError: null,
  discoveredDevices: [],
  scanDuration: 0,
  
  // Actions
  startScanning: async () => {
    set({ 
      isScanning: true, 
      scanError: null, 
      discoveredDevices: [],
      scanDuration: 0
    });
    
    try {
      // Simulate device discovery
      const mockDevices: Device[] = [
        {
          id: 'collar-001',
          name: 'Smart Collar Pro',
          type: 'collar',
          status: 'discovered',
          batteryLevel: 85,
          signalStrength: -45,
          lastSeen: new Date().toISOString(),
          firmwareVersion: '2.1.0',
          features: ['gps', 'activity', 'health'],
          animalId: undefined
        },
        {
          id: 'tracker-002',
          name: 'GPS Tracker Mini',
          type: 'tracker',
          status: 'discovered',
          batteryLevel: 92,
          signalStrength: -38,
          lastSeen: new Date().toISOString(),
          firmwareVersion: '1.8.2',
          features: ['gps', 'geofencing'],
          animalId: undefined
        }
      ];
      
      // Simulate progressive discovery
      for (let i = 0; i < mockDevices.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        if (get().isScanning) {
          set(state => ({
            discoveredDevices: [...state.discoveredDevices, mockDevices[i]],
            scanDuration: state.scanDuration + 2
          }));
        }
      }
      
    } catch (error) {
      console.error('Scanning failed:', error);
      set({ 
        scanError: 'Failed to scan for devices',
        isScanning: false 
      });
    }
  },
  
  stopScanning: () => {
    set({ isScanning: false });
  },
  
  addDiscoveredDevice: (device: Device) => {
    const { discoveredDevices } = get();
    const exists = discoveredDevices.find(d => d.id === device.id);
    
    if (!exists) {
      set({ 
        discoveredDevices: [...discoveredDevices, device]
      });
    }
  },
  
  clearDiscoveredDevices: () => {
    set({ discoveredDevices: [] });
  },
  
  setScanError: (error: string | null) => {
    set({ scanError: error });
  }
});