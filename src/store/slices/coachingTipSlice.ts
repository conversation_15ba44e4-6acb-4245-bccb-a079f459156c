/**
 * @magic_description Coaching Tip Store Slice
 * Handles coaching tip data and operations
 * Extracted from aiStore for better maintainability
 */

import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface CoachingTip {
  tip_id: string;
  animal_id: string;
  user_id: string;
  tip_text: string;
  category: string;
  priority: string;
  is_read: boolean;
  generated_at: string;
}

export interface CoachingTipState {
  coachingTips: CoachingTip[];
  isLoadingCoachingTips: boolean;
}

export const coachingTipOperations = {
  // Fetch Coaching Tips
  fetchCoachingTips: async (animalId?: string) => {
    try {
      let query = supabase
        .from('ai_coaching_tips')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      console.log(`Fetched ${data?.length || 0} coaching tips`);
      return data || [];
      
    } catch (error) {
      console.error('Error fetching coaching tips:', error);
      toast.error('Failed to fetch coaching tips');
      throw error;
    }
  },

  // Mark Tip as Read
  markTipAsRead: async (tipId: string) => {
    try {
      const { error } = await supabase
        .from('ai_coaching_tips')
        .update({ is_read: true })
        .eq('tip_id', tipId);
      
      if (error) throw error;
      
      return true;
      
    } catch (error) {
      console.error('Error marking tip as read:', error);
      toast.error('Failed to mark tip as read');
      throw error;
    }
  },

  // Get Unread Tips
  getUnreadTips: (tips: CoachingTip[], animalId?: string) => {
    let filtered = tips.filter(tip => !tip.is_read);
    if (animalId) {
      filtered = filtered.filter(tip => tip.animal_id === animalId);
    }
    return filtered;
  },
};

export const coachingTipInitialState: CoachingTipState = {
  coachingTips: [],
  isLoadingCoachingTips: false,
};