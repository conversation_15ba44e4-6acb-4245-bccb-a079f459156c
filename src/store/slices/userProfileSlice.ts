import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  farmName?: string;
  address?: string;
  phone?: string;
  isPremium: boolean;
  subscriptionType?: string;
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;
  imageUrl?: string;
  createdAt?: string;
  updatedAt?: string;
  mfaEnabled?: boolean;
}

export interface UserProfileState {
  user: UserProfile | null;
  isLoading: boolean;
  error: string | null;
}

export const userProfileInitialState: UserProfileState = {
  user: null,
  isLoading: false,
  error: null,
};

export const userProfileOperations = {
  fetchProfile: async (set: any, get: any) => {
    set({ isLoading: true, error: null });
    
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        console.log('No authenticated user found');
        set({ user: null, isLoading: false });
        return;
      }
      
      console.log('Fetching profile for user:', user.id);
      
      // Try to get existing user profile
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (error && error.code === 'PGRST116') {
        // User profile doesn't exist, create one
        console.log('User profile not found, creating new profile for:', user.email);
        
        const newUserProfile = {
          id: user.id,
          email: user.email,
          name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
          farm_name: null,
          address: null,
          phone: null,
          is_premium: false,
          subscription_type: null,
          subscription_start_date: null,
          subscription_end_date: null,
          image_url: null
        };
        
        const { data: newData, error: insertError } = await supabase
          .from('users')
          .insert([newUserProfile])
          .select()
          .single();
        
        if (insertError) {
          console.error('Error creating user profile:', insertError);
          throw new Error(`Failed to create user profile: ${insertError.message}`);
        }
        
        console.log('User profile created successfully:', newData);
        
        // Use the newly created profile
        const formattedData: UserProfile = {
          id: newData.id,
          email: newData.email,
          name: newData.name,
          farmName: newData.farm_name,
          address: newData.address,
          phone: newData.phone,
          isPremium: newData.is_premium || false,
          subscriptionType: newData.subscription_type,
          subscriptionStartDate: newData.subscription_start_date,
          subscriptionEndDate: newData.subscription_end_date,
          imageUrl: newData.image_url,
          createdAt: newData.created_at,
          updatedAt: newData.updated_at
        };
        
        set({ user: formattedData, isLoading: false });
        return;
      }
      
      if (error) {
        console.error('Database error fetching profile:', error);
        throw new Error(`Database error: ${error.message}`);
      }
      
      if (!data) {
        console.error('No user data returned from database');
        throw new Error('User profile not found in database');
      }
      
      console.log('API Success (fetchProfile):', data);
      
      // Convert database fields to match our interface
      const formattedData: UserProfile = {
        id: data.id,
        email: data.email,
        name: data.name,
        farmName: data.farm_name,
        address: data.address,
        phone: data.phone,
        isPremium: data.is_premium || false,
        subscriptionType: data.subscription_type,
        subscriptionStartDate: data.subscription_start_date,
        subscriptionEndDate: data.subscription_end_date,
        imageUrl: data.image_url,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
      
      set({ user: formattedData, isLoading: false });
    } catch (error: any) {
      console.error('Error fetching profile:', error);
      set({ error: error.message, isLoading: false });
      toast.error('Failed to load user profile');
    }
  },
  
  updateProfile: async (set: any, get: any, profile: Partial<UserProfile>) => {
    set({ isLoading: true, error: null });
    
    try {
      const { user } = get();
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Convert to database field names
      const dbProfile: any = {};
      
      if (profile.name !== undefined) dbProfile.name = profile.name;
      if (profile.farmName !== undefined) dbProfile.farm_name = profile.farmName;
      if (profile.address !== undefined) dbProfile.address = profile.address;
      if (profile.phone !== undefined) dbProfile.phone = profile.phone;
      if (profile.imageUrl !== undefined) dbProfile.image_url = profile.imageUrl;
      
      const { error } = await supabase
        .from('users')
        .update(dbProfile)
        .eq('id', user.id);
      
      if (error) {
        throw error;
      }
      
      set((state: any) => ({
        user: state.user ? { ...state.user, ...profile } : null,
        isLoading: false
      }));
      
      toast.success('Profile updated successfully');
    } catch (error: any) {
      console.error('Error updating profile:', error);
      set({ error: error.message, isLoading: false });
      toast.error('Failed to update profile');
      throw error;
    }
  },
};