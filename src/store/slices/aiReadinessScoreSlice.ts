import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface ReadinessScore {
  score_id: string;
  animal_id: string;
  user_id: string;
  score_value: number;
  score_category: string;
  generated_at: string;
}

export interface ReadinessScoreState {
  readinessScores: ReadinessScore[];
  isLoadingReadinessScores: boolean;
}

export const readinessScoreInitialState: ReadinessScoreState = {
  readinessScores: [],
  isLoadingReadinessScores: false,
};

export const readinessScoreOperations = {
  // Fetch Readiness Scores
  fetchReadinessScores: async (set: any, get: any, animalId?: string) => {
    set({ isLoadingReadinessScores: true });
    
    try {
      let query = supabase
        .from('ai_readiness_scores')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      set({ readinessScores: data || [] });
      console.log(`Fetched ${data?.length || 0} readiness scores`);
      
    } catch (error) {
      console.error('Error fetching readiness scores:', error);
      toast.error('Failed to fetch readiness scores');
    } finally {
      set({ isLoadingReadinessScores: false });
    }
  },
  
  // Get latest readiness score for an animal
  getLatestReadinessScore: (get: any, animalId: string) => {
    const scores = get().readinessScores.filter((s: ReadinessScore) => s.animal_id === animalId);
    return scores.length > 0 ? scores[0] : null;
  },
};