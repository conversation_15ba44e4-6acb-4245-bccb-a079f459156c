import { StateCreator } from 'zustand';
import { DeviceStore } from '../deviceStore';
import { Device } from '../../types/device';

export interface DeviceManagementSlice {
  // Device management state
  devices: Device[];
  selectedDevice: Device | null;
  isLoading: boolean;
  error: string | null;
  
  // Device management actions
  addDevice: (device: Device) => void;
  removeDevice: (deviceId: string) => void;
  updateDevice: (deviceId: string, updates: Partial<Device>) => void;
  setSelectedDevice: (device: Device | null) => void;
  assignDeviceToAnimal: (deviceId: string, animalId: string) => void;
  unassignDevice: (deviceId: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const createDeviceManagementSlice: StateCreator<
  DeviceStore,
  [],
  [],
  DeviceManagementSlice
> = (set, get) => ({
  // Initial state
  devices: [],
  selectedDevice: null,
  isLoading: false,
  error: null,
  
  // Actions
  addDevice: (device: Device) => {
    const { devices } = get();
    const exists = devices.find(d => d.id === device.id);
    
    if (!exists) {
      set({ 
        devices: [...devices, device],
        error: null
      });
    } else {
      set({ error: 'Device already exists' });
    }
  },
  
  removeDevice: (deviceId: string) => {
    const { devices, selectedDevice } = get();
    
    set({ 
      devices: devices.filter(d => d.id !== deviceId),
      selectedDevice: selectedDevice?.id === deviceId ? null : selectedDevice,
      error: null
    });
  },
  
  updateDevice: (deviceId: string, updates: Partial<Device>) => {
    const { devices, selectedDevice } = get();
    
    const updatedDevices = devices.map(device => 
      device.id === deviceId 
        ? { ...device, ...updates }
        : device
    );
    
    set({ 
      devices: updatedDevices,
      selectedDevice: selectedDevice?.id === deviceId 
        ? { ...selectedDevice, ...updates }
        : selectedDevice,
      error: null
    });
  },
  
  setSelectedDevice: (device: Device | null) => {
    set({ selectedDevice: device });
  },
  
  assignDeviceToAnimal: (deviceId: string, animalId: string) => {
    const { updateDevice } = get();
    updateDevice(deviceId, { animalId });
  },
  
  unassignDevice: (deviceId: string) => {
    const { updateDevice } = get();
    updateDevice(deviceId, { animalId: undefined });
  },
  
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },
  
  setError: (error: string | null) => {
    set({ error });
  }
});