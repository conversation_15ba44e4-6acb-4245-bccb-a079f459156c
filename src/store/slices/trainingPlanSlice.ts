/**
 * @magic_description Training Plan Store Slice
 * Handles training plan data and operations
 * Extracted from aiStore for better maintainability
 */

import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface TrainingPlan {
  plan_id: string;
  animal_id: string;
  user_id: string;
  plan_text: string;
  plan_type: string;
  plan_data: any;
  generated_at: string;
}

export interface TrainingPlanState {
  trainingPlans: TrainingPlan[];
  isLoadingTrainingPlans: boolean;
}

export const trainingPlanOperations = {
  // Fetch Training Plans
  fetchTrainingPlans: async (animalId?: string) => {
    try {
      let query = supabase
        .from('ai_training_plans')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      console.log(`Fetched ${data?.length || 0} training plans`);
      return data || [];
      
    } catch (error) {
      console.error('Error fetching training plans:', error);
      toast.error('Failed to fetch training plans');
      throw error;
    }
  },

  // Get Latest Training Plan
  getLatestTrainingPlan: (plans: TrainingPlan[], animalId: string) => {
    const filtered = plans.filter(p => p.animal_id === animalId);
    return filtered.length > 0 ? filtered[0] : null;
  },
};

export const trainingPlanInitialState: TrainingPlanState = {
  trainingPlans: [],
  isLoadingTrainingPlans: false,
};