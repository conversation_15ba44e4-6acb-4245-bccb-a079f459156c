import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface TrainingPlan {
  plan_id: string;
  animal_id: string;
  user_id: string;
  plan_text: string;
  plan_type: string;
  plan_data: any;
  generated_at: string;
}

export interface TrainingPlanState {
  trainingPlans: TrainingPlan[];
  isLoadingTrainingPlans: boolean;
}

export const trainingPlanInitialState: TrainingPlanState = {
  trainingPlans: [],
  isLoadingTrainingPlans: false,
};

export const trainingPlanOperations = {
  // Fetch Training Plans
  fetchTrainingPlans: async (set: any, get: any, animalId?: string) => {
    set({ isLoadingTrainingPlans: true });
    
    try {
      let query = supabase
        .from('ai_training_plans')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      set({ trainingPlans: data || [] });
      console.log(`Fetched ${data?.length || 0} training plans`);
      
    } catch (error) {
      console.error('Error fetching training plans:', error);
      toast.error('Failed to fetch training plans');
    } finally {
      set({ isLoadingTrainingPlans: false });
    }
  },
  
  // Get latest training plan for an animal
  getLatestTrainingPlan: (get: any, animalId: string) => {
    const plans = get().trainingPlans.filter((p: TrainingPlan) => p.animal_id === animalId);
    return plans.length > 0 ? plans[0] : null;
  },
};