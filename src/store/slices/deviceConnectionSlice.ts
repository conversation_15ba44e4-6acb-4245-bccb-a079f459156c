import { StateCreator } from 'zustand';
import { DeviceStore } from '../deviceStore';

export interface DeviceConnectionSlice {
  // Connection state
  isConnecting: boolean;
  connectionError: string | null;
  connectedDevices: string[];
  
  // Connection actions
  connectDevice: (deviceId: string) => Promise<boolean>;
  disconnectDevice: (deviceId: string) => Promise<boolean>;
  reconnectDevice: (deviceId: string) => Promise<boolean>;
  setConnectionError: (error: string | null) => void;
  clearConnectionError: () => void;
}

export const createDeviceConnectionSlice: StateCreator<
  DeviceStore,
  [],
  [],
  DeviceConnectionSlice
> = (set, get) => ({
  // Initial state
  isConnecting: false,
  connectionError: null,
  connectedDevices: [],
  
  // Actions
  connectDevice: async (deviceId: string) => {
    const { devices } = get();
    const device = devices.find(d => d.id === deviceId);
    
    if (!device) {
      set({ connectionError: 'Device not found' });
      return false;
    }
    
    set({ isConnecting: true, connectionError: null });
    
    try {
      // Simulate connection process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update device status
      const updatedDevices = devices.map(d => 
        d.id === deviceId 
          ? { ...d, status: 'connected' as const, lastSeen: new Date().toISOString() }
          : d
      );
      
      set({
        devices: updatedDevices,
        connectedDevices: [...get().connectedDevices, deviceId],
        isConnecting: false
      });
      
      return true;
    } catch (error) {
      console.error('Connection failed:', error);
      set({ 
        connectionError: 'Failed to connect to device',
        isConnecting: false 
      });
      return false;
    }
  },
  
  disconnectDevice: async (deviceId: string) => {
    const { devices, connectedDevices } = get();
    
    set({ isConnecting: true });
    
    try {
      // Simulate disconnection
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update device status
      const updatedDevices = devices.map(d => 
        d.id === deviceId 
          ? { ...d, status: 'disconnected' as const }
          : d
      );
      
      set({
        devices: updatedDevices,
        connectedDevices: connectedDevices.filter(id => id !== deviceId),
        isConnecting: false
      });
      
      return true;
    } catch (error) {
      console.error('Disconnection failed:', error);
      set({ 
        connectionError: 'Failed to disconnect device',
        isConnecting: false 
      });
      return false;
    }
  },
  
  reconnectDevice: async (deviceId: string) => {
    const { disconnectDevice, connectDevice } = get();
    
    // First disconnect, then reconnect
    const disconnected = await disconnectDevice(deviceId);
    if (disconnected) {
      await new Promise(resolve => setTimeout(resolve, 500));
      return await connectDevice(deviceId);
    }
    
    return false;
  },
  
  setConnectionError: (error: string | null) => {
    set({ connectionError: error });
  },
  
  clearConnectionError: () => {
    set({ connectionError: null });
  }
});