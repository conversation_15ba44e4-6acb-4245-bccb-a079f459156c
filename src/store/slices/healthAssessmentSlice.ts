/**
 * @magic_description Health Assessment Store Slice
 * Handles health assessment data and operations
 * Extracted from aiStore for better maintainability
 */

import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface HealthAssessment {
  assessment_id: string;
  animal_id: string;
  user_id: string;
  assessment_text: string;
  severity_level: string;
  generated_at: string;
}

export interface HealthAssessmentState {
  healthAssessments: HealthAssessment[];
  isLoadingHealthAssessments: boolean;
}

export const healthAssessmentOperations = {
  // Fetch Health Assessments
  fetchHealthAssessments: async (animalId?: string) => {
    try {
      let query = supabase
        .from('ai_health_assessments')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      console.log(`Fetched ${data?.length || 0} health assessments`);
      return data || [];
      
    } catch (error) {
      console.error('Error fetching health assessments:', error);
      toast.error('Failed to fetch health assessments');
      throw error;
    }
  },

  // Get Latest Health Assessment
  getLatestHealthAssessment: (assessments: HealthAssessment[], animalId: string) => {
    const filtered = assessments.filter(a => a.animal_id === animalId);
    return filtered.length > 0 ? filtered[0] : null;
  },
};

export const healthAssessmentInitialState: HealthAssessmentState = {
  healthAssessments: [],
  isLoadingHealthAssessments: false,
};