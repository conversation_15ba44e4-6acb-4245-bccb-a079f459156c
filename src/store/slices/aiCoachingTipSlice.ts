import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface CoachingTip {
  tip_id: string;
  animal_id: string;
  user_id: string;
  tip_text: string;
  category: string;
  priority: string;
  is_read: boolean;
  generated_at: string;
}

export interface CoachingTipState {
  coachingTips: CoachingTip[];
  isLoadingCoachingTips: boolean;
}

export const coachingTipInitialState: CoachingTipState = {
  coachingTips: [],
  isLoadingCoachingTips: false,
};

export const coachingTipOperations = {
  // Fetch Coaching Tips
  fetchCoachingTips: async (set: any, get: any, animalId?: string) => {
    set({ isLoadingCoachingTips: true });
    
    try {
      let query = supabase
        .from('ai_coaching_tips')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      set({ coachingTips: data || [] });
      console.log(`Fetched ${data?.length || 0} coaching tips`);
      
    } catch (error) {
      console.error('Error fetching coaching tips:', error);
      toast.error('Failed to fetch coaching tips');
    } finally {
      set({ isLoadingCoachingTips: false });
    }
  },
  
  // Mark Tip as Read
  markTipAsRead: async (set: any, get: any, tipId: string) => {
    try {
      const { error } = await supabase
        .from('ai_coaching_tips')
        .update({ is_read: true })
        .eq('tip_id', tipId);
      
      if (error) throw error;
      
      // Update local state
      set((state: any) => ({
        coachingTips: state.coachingTips.map((tip: CoachingTip) => 
          tip.tip_id === tipId ? { ...tip, is_read: true } : tip
        )
      }));
      
    } catch (error) {
      console.error('Error marking tip as read:', error);
      toast.error('Failed to mark tip as read');
    }
  },
  
  // Get unread tips
  getUnreadTips: (get: any, animalId?: string) => {
    let tips = get().coachingTips.filter((tip: CoachingTip) => !tip.is_read);
    if (animalId) {
      tips = tips.filter((tip: CoachingTip) => tip.animal_id === animalId);
    }
    return tips;
  },
};