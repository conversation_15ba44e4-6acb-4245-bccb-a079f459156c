import { supabase, logApiCall } from '../../supabase/client';
import { Device } from '../../mocks/devices';
import { toast } from 'sonner-native';

export const deviceApiOperations = {
  // Fetch devices from Supabase
  fetchDevices: async (set: any, get: any) => {
    try {
      set({ isLoading: true, error: null });
      
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.warn('fetchDevices: User not authenticated');
        set({ 
          devices: [], 
          pairedDevices: [], 
          isLoading: false, 
          error: 'User not authenticated' 
        });
        return;
      }
      
      const { data, error } = await supabase
        .from('devices')
        .select('*')
        .eq('user_id', user.id) // Defense-in-depth: explicit user filtering
        .order('created_at', { ascending: false });
      
      logApiCall('fetchDevices', data, error);
      
      if (error) {
        console.error('Supabase error in fetchDevices:', error);
        // Ensure we always set valid arrays even on error
        set({ 
          devices: [], 
          pairedDevices: [], 
          isLoading: false, 
          error: `Database error: ${error.message}` 
        });
        return;
      }
      
      // Defensive data validation - ensure data is always an array
      const validDevices = Array.isArray(data) ? data : [];
      
      // Safely derive paired devices with comprehensive validation
      const pairedDevices = validDevices.filter(device => {
        return device && 
               typeof device === 'object' && 
               device.id && 
               device.name && 
               device.status && 
               (device.status === 'connected' || 
                device.status === 'paired' || 
                device.status === 'low_battery');
      });
      
      set({ 
        devices: validDevices, 
        pairedDevices,
        isLoading: false, 
        error: null 
      });
      
      console.log(`fetchDevices success: ${validDevices.length} devices, ${pairedDevices.length} paired`);
      
    } catch (error: any) {
      console.error('Unexpected error in fetchDevices:', error);
      // Always ensure valid state even on unexpected errors
      set({ 
        devices: [], 
        pairedDevices: [], 
        isLoading: false, 
        error: error.message || 'Failed to fetch devices' 
      });
    }
  },
  
  // Add device to Supabase
  addDevice: async (set: any, get: any, deviceData: Omit<Device, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    try {
      set({ isLoading: true, error: null });
      
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error('User not found. Please log in.');
        set({ isLoading: false, error: 'User not found' });
        return null;
      }
      
      // Store the original scan ID for removing from availableDevices
      const originalScanId = deviceData.id;
      
      // Remove the temporary scan ID before sending to Supabase
      const { id, ...deviceDataWithoutId } = deviceData;
      const newDevice = {
        ...deviceDataWithoutId,
        user_id: user.id,
      };
      
      logApiCall('addDevice', 'devices', 'INSERT', newDevice);
      const { data, error } = await supabase
        .from('devices')
        .insert(newDevice)
        .select()
        .single();
      
      if (error) {
        console.error('Error adding device:', error);
        set({ isLoading: false, error: error.message });
        toast.error(`Failed to add device: ${error.message}`);
        throw error;
      }
      
      // Update local state and remove from availableDevices using original scan ID
      set((state: any) => ({
        devices: [data, ...state.devices],
        availableDevices: state.availableDevices.filter((d: any) => d.id !== originalScanId),
        pairedDevices: data.status === 'connected' || data.status === 'paired' || data.status === 'low_battery'
          ? [data, ...state.pairedDevices]
          : state.pairedDevices,
        isLoading: false,
        error: null
      }));
      
      toast.success(`Device "${data.name}" added successfully!`);
      return data;
      
    } catch (error: any) {
      console.error('Error in addDevice:', error);
      set({ isLoading: false, error: error.message || 'Failed to add device' });
      return null;
    }
  },
  
  // Update device in Supabase
  updateDevice: async (set: any, get: any, id: string, updates: Partial<Omit<Device, 'id' | 'user_id' | 'created_at' | 'updated_at'>>) => {
    try {
      set({ isLoading: true, error: null });
      
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return null;
      }
      
      logApiCall('updateDevice', 'devices', 'UPDATE', { id, ...updates });
      const { data, error } = await supabase
        .from('devices')
        .update(updates)
        .eq('id', id)
        .eq('user_id', user.id) // Defense-in-depth: ensure user owns device
        .select()
        .single();
      
      if (error) {
        console.error('Error updating device:', error);
        set({ isLoading: false, error: error.message });
        toast.error(`Failed to update device: ${error.message}`);
        throw error;
      }
      
      // Update local state
      set((state: any) => ({
        devices: state.devices.map((device: any) => 
          device.id === id ? data : device
        ),
        pairedDevices: state.pairedDevices.map((device: any) => 
          device.id === id ? data : device
        ),
        isLoading: false,
        error: null
      }));
      
      toast.success(`Device "${data.name}" updated successfully!`);
      return data;
      
    } catch (error: any) {
      console.error('Error in updateDevice:', error);
      set({ isLoading: false, error: error.message || 'Failed to update device' });
      return null;
    }
  },
  
  // Delete device from Supabase
  deleteDevice: async (set: any, get: any, id: string) => {
    try {
      set({ isLoading: true, error: null });
      
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return false;
      }
      
      const device = get().devices.find((d: any) => d.id === id);
      if (!device) {
        toast.error('Device not found');
        set({ isLoading: false, error: 'Device not found' });
        return false;
      }
      
      logApiCall('deleteDevice', 'devices', 'DELETE', { id });
      const { error } = await supabase
        .from('devices')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id); // Defense-in-depth: ensure user owns device
      
      if (error) {
        console.error('Error deleting device:', error);
        set({ isLoading: false, error: error.message });
        toast.error(`Failed to delete device: ${error.message}`);
        throw error;
      }
      
      // Update local state
      set((state: any) => ({
        devices: state.devices.filter((device: any) => device.id !== id),
        pairedDevices: state.pairedDevices.filter((device: any) => device.id !== id),
        isLoading: false,
        error: null
      }));
      
      toast.success(`Device "${device.name}" deleted successfully!`);
      return true;
      
    } catch (error: any) {
      console.error('Error in deleteDevice:', error);
      set({ isLoading: false, error: error.message || 'Failed to delete device' });
      return false;
    }
  },
};