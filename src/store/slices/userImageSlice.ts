import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface UserImageState {
  isLoading: boolean;
  error: string | null;
}

export const userImageInitialState: UserImageState = {
  isLoading: false,
  error: null,
};

export const userImageOperations = {
  uploadProfileImage: async (set: any, get: any, imageUri: string) => {
    const { user } = get();
    
    if (!user) {
      toast.error('User not found. Please log in.');
      throw new Error('User not found');
    }

    set({ isLoading: true, error: null });
    
    let uploadedImageUrl = '';
    const originalImageUrl = user.imageUrl;
    
    try {
      console.log('Starting profile image upload for user:', user.id);
      
      // Fetch the image blob from the local URI
      const response = await fetch(imageUri);
      if (!response.ok) {
        throw new Error('Failed to fetch image data');
      }
      
      const blob = await response.blob();
      
      // Determine file extension and create unique filename
      // Use user.id folder structure to match RLS policies
      const fileExt = imageUri.split('.').pop() || 'jpg';
      const fileName = `${user.id}/profile.${fileExt}`;
      
      console.log('Uploading image to Supabase Storage:', fileName);
      
      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('profile-images')
        .upload(fileName, blob, {
          contentType: blob.type,
          upsert: true // Overwrite existing profile image
        });

      if (uploadError) {
        console.error('Error uploading image to Supabase Storage:', uploadError);
        
        // Check if bucket doesn't exist and provide helpful error
        if (uploadError.message.includes('Bucket not found')) {
          toast.error('Storage not configured. Please contact support.');
          throw new Error('Storage bucket not found. Please contact support.');
        }
        
        toast.error(`Image upload failed: ${uploadError.message}`);
        throw uploadError;
      }

      console.log('Image uploaded successfully:', uploadData);

      // Get public URL for the uploaded image
      const { data: urlData } = supabase.storage
        .from('profile-images')
        .getPublicUrl(fileName);
      
      if (!urlData || !urlData.publicUrl) {
        console.error('Error getting public URL for image');
        toast.error('Failed to get image URL. Please try again.');
        throw new Error('Failed to get image URL');
      }

      uploadedImageUrl = urlData.publicUrl;
      console.log('Got public URL:', uploadedImageUrl);

      // Optimistically update local state
      set((state: any) => ({ 
        user: state.user ? { ...state.user, imageUrl: uploadedImageUrl } : null
      }));

      // Update user profile in the database with the new public URL
      const updateProfile = get().updateProfile;
      if (updateProfile) {
        await updateProfile({ imageUrl: uploadedImageUrl });
      }
      
      set({ isLoading: false });
      toast.success('Profile image updated successfully!');
      
      return uploadedImageUrl;

    } catch (error: any) {
      console.error('Error in uploadProfileImage:', error);
      
      // Revert optimistic update if we had set the new URL
      if (uploadedImageUrl && user.imageUrl === uploadedImageUrl) {
        set((state: any) => ({ 
          user: state.user ? { ...state.user, imageUrl: originalImageUrl } : null
        }));
      }
      
      set({ isLoading: false, error: error.message || 'Failed to upload profile image' });
      
      // Only show toast if we haven't already shown a specific error
      if (!error.message?.includes('Image upload failed') && 
          !error.message?.includes('Failed to get image URL') &&
          !error.message?.includes('Storage not configured')) {
        toast.error(error.message || 'Failed to update profile image');
      }
      
      throw error;
    }
  },
};