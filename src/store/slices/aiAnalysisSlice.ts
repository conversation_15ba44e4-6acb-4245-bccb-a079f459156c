import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

interface RealtimeAnalysis {
  analysis: string;
  timestamp: string;
  dataPoints: {
    speed: number | null;
    location: string | null;
    heartRate: number | null;
    temperature: number | null;
    speedHistory: Array<{ timestamp: string; speed: number }>;
  };
}

interface DehydrationAnalysis {
  id?: string;
  animal_id: string;
  overall_status: 'excellent' | 'good' | 'concerning' | 'critical';
  trend: 'improving' | 'stable' | 'declining';
  average_hydration: number;
  hydration_variance: number;
  risk_factors: string[];
  recommendations: string[];
  alerts: {
    type: 'dehydration_risk' | 'trend_warning' | 'data_quality';
    severity: 'low' | 'medium' | 'high';
    message: string;
  }[];
  insights: {
    category: 'hydration_patterns' | 'environmental_factors' | 'health_indicators';
    finding: string;
    confidence: number;
  }[];
  next_actions: string[];
  generated_at: string;
}

export interface AIAnalysisState {
  realtimeAnalyses: RealtimeAnalysis[];
  dehydrationAnalyses: DehydrationAnalysis[];
  isLoadingAnalysis: boolean;
  isLoadingRealtimeAnalysis: boolean;
  isLoadingDehydrationAnalysis: boolean;
}

export const aiAnalysisInitialState: AIAnalysisState = {
  realtimeAnalyses: [],
  dehydrationAnalyses: [],
  isLoadingAnalysis: false,
  isLoadingRealtimeAnalysis: false,
  isLoadingDehydrationAnalysis: false,
};

export const aiAnalysisOperations = {
  // Request AI Analysis
  requestAIAnalysis: async (set: any, get: any, animalId: string) => {
    set({ isLoadingAnalysis: true });
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      const response = await fetch(
        `${supabase.supabaseUrl}/functions/v1/get-assistant-trainer-analysis`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ animalId }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get AI analysis');
      }

      const result = await response.json();
      console.log('AI Analysis completed:', result);
      
      toast.success('AI analysis completed successfully!');
      
      // Refresh all AI data for this animal
      const { fetchHealthAssessments, fetchTrainingPlans, fetchReadinessScores, fetchCoachingTips } = get();
      await Promise.all([
        fetchHealthAssessments?.(animalId),
        fetchTrainingPlans?.(animalId),
        fetchReadinessScores?.(animalId),
        fetchCoachingTips?.(animalId),
        get().requestDehydrationAnalysis?.(animalId),
      ]);
      
    } catch (error: any) {
      console.error('AI Analysis error:', error);
      toast.error(`Failed to get AI analysis: ${error.message}`);
      throw error;
    } finally {
      set({ isLoadingAnalysis: false });
    }
  },

  // Request Real-time Analysis
  requestRealtimeAnalysis: async (set: any, get: any, animalId: string) => {
    set({ isLoadingRealtimeAnalysis: true });
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      const response = await fetch(
        `${supabase.supabaseUrl}/functions/v1/get-realtime-animal-analysis`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ animalId }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get real-time analysis');
      }

      const result = await response.json();
      console.log('Real-time Analysis completed:', result);
      
      if (result.success && result.data) {
        // Add the new analysis to the array
        set((state: any) => ({
          realtimeAnalyses: [result.data, ...state.realtimeAnalyses.slice(0, 9)] // Keep last 10
        }));
        
        toast.success('Live check-in completed!');
        
        // Refresh health assessments to include the new analysis
        const { fetchHealthAssessments } = get();
        await fetchHealthAssessments?.(animalId);
      }
      
    } catch (error: any) {
      console.error('Real-time Analysis error:', error);
      toast.error(`Failed to get live check-in: ${error.message}`);
      throw error;
    } finally {
      set({ isLoadingRealtimeAnalysis: false });
    }
  },

  // Request Dehydration Analysis
  requestDehydrationAnalysis: async (set: any, get: any, animalId: string, days: number = 7) => {
    set({ isLoadingDehydrationAnalysis: true });
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      const response = await fetch(
        `${supabase.supabaseUrl}/functions/v1/get-dehydration-analysis`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ animal_id: animalId, days }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get dehydration analysis');
      }

      const analysis = await response.json();
      console.log('Dehydration Analysis completed:', analysis);
      
      // Add the new analysis to the array
      const newAnalysis: DehydrationAnalysis = {
        ...analysis,
        animal_id: animalId,
        generated_at: new Date().toISOString()
      };
      
      set((state: any) => ({
        dehydrationAnalyses: [newAnalysis, ...state.dehydrationAnalyses.filter((a: any) => a.animal_id !== animalId).slice(0, 9)]
      }));
      
      toast.success('Dehydration analysis completed!');
      
    } catch (error: any) {
      console.error('Dehydration Analysis error:', error);
      toast.error(`Failed to get dehydration analysis: ${error.message}`);
      throw error;
    } finally {
      set({ isLoadingDehydrationAnalysis: false });
    }
  },
  
  // Getters
  getLatestRealtimeAnalysis: (get: any, animalId: string) => {
    const analyses = get().realtimeAnalyses;
    return analyses.length > 0 ? analyses[0] : null;
  },

  getLatestDehydrationAnalysis: (get: any, animalId: string) => {
    const analyses = get().dehydrationAnalyses.filter((a: any) => a.animal_id === animalId);
    return analyses.length > 0 ? analyses[0] : null;
  },
};