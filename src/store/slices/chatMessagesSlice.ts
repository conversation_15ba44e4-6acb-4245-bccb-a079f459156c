import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface ChatMessage {
  id: string;
  conversation_id: string;
  user_id: string;
  content: string;
  role: 'user' | 'assistant';
  metadata?: {
    animal_id?: string;
    context_type?: string;
    attachments?: string[];
  };
  created_at: string;
  updated_at: string;
}

export interface ChatMessagesState {
  messages: ChatMessage[];
  isLoading: boolean;
  isSending: boolean;
  error: string | null;
}

export const chatMessagesInitialState: ChatMessagesState = {
  messages: [],
  isLoading: false,
  isSending: false,
  error: null,
};

export const chatMessagesOperations = {
  fetchMessages: async (conversationId: string): Promise<ChatMessage[]> => {
    try {
      const { data, error } = await supabase
        .from('ai_chat_messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching messages:', error);
        toast.error('Failed to load messages');
        throw error;
      }

      return data || [];
    } catch (error: any) {
      console.error('Error in fetchMessages:', error);
      throw error;
    }
  },

  sendMessage: async (conversationId: string, content: string, animalId?: string): Promise<ChatMessage> => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      // Create user message
      const userMessage = {
        conversation_id: conversationId,
        user_id: session.user.id,
        content,
        role: 'user' as const,
        metadata: animalId ? { animal_id: animalId } : undefined,
      };

      const { data, error } = await supabase
        .from('ai_chat_messages')
        .insert(userMessage)
        .select()
        .single();

      if (error) {
        console.error('Error sending message:', error);
        toast.error('Failed to send message');
        throw error;
      }

      return data;
    } catch (error: any) {
      console.error('Error in sendMessage:', error);
      throw error;
    }
  },

  deleteMessage: async (messageId: string): Promise<void> => {
    try {
      const { error } = await supabase
        .from('ai_chat_messages')
        .delete()
        .eq('id', messageId);

      if (error) {
        console.error('Error deleting message:', error);
        toast.error('Failed to delete message');
        throw error;
      }

      toast.success('Message deleted');
    } catch (error: any) {
      console.error('Error in deleteMessage:', error);
      throw error;
    }
  },

  clearMessages: async (conversationId: string): Promise<void> => {
    try {
      const { error } = await supabase
        .from('ai_chat_messages')
        .delete()
        .eq('conversation_id', conversationId);

      if (error) {
        console.error('Error clearing messages:', error);
        toast.error('Failed to clear messages');
        throw error;
      }

      toast.success('Messages cleared');
    } catch (error: any) {
      console.error('Error in clearMessages:', error);
      throw error;
    }
  },
};