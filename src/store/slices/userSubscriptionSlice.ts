import { supabase } from '../../supabase/client';
import { toast } from 'sonner-native';

export interface UserSubscriptionState {
  isLoading: boolean;
  error: string | null;
}

export const userSubscriptionInitialState: UserSubscriptionState = {
  isLoading: false,
  error: null,
};

export const userSubscriptionOperations = {
  updateSubscription: async (set: any, get: any, isPremium: boolean, subscriptionType: 'monthly' | 'yearly') => {
    set({ isLoading: true, error: null });
    
    try {
      const { user } = get();
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Calculate subscription dates
      const startDate = new Date();
      const endDate = new Date();
      
      if (subscriptionType === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1);
      }
      
      // Update subscription in Supabase
      const { error } = await supabase
        .from('users')
        .update({
          is_premium: isPremium,
          subscription_type: subscriptionType,
          subscription_start_date: startDate.toISOString(),
          subscription_end_date: endDate.toISOString()
        })
        .eq('id', user.id);
      
      if (error) {
        throw error;
      }
      
      // Update local state
      set((state: any) => ({
        user: state.user ? {
          ...state.user,
          isPremium,
          subscriptionType,
          subscriptionStartDate: startDate.toISOString(),
          subscriptionEndDate: endDate.toISOString()
        } : null,
        isLoading: false
      }));
      
      console.log('Subscription updated successfully:', { isPremium, subscriptionType });
      
    } catch (error: any) {
      console.error('Error updating subscription:', error);
      set({ error: error.message, isLoading: false });
      toast.error('Failed to update subscription');
      throw error;
    }
  },
};