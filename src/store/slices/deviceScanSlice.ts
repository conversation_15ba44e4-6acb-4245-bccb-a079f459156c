import { Device } from '../../mocks/devices';
import { toast } from 'sonner-native';

export const deviceScanOperations = {
  startScan: (set: any, get: any) => {
    set({ isScanning: true, isLoading: true, error: null });
    
    // Simulate finding devices (in real implementation, this would use Bluetooth scanning)
    setTimeout(() => {
      try {
        // Simulate discovering new devices that aren't in the user's registered devices list
        const currentDeviceIds = get().devices.map((d: any) => d.id);
        const simulatedDevices = [
          {
            id: 'scan_device_1',
            name: 'HoofMonitor Ultra',
            type: 'Multi-sensor',
            battery_level: 100,
            status: 'disconnected' as const,
          },
          {
            id: 'scan_device_2',
            name: 'EquiTrack Pro',
            type: 'GPS Tracker',
            battery_level: 95,
            status: 'disconnected' as const,
          },
          {
            id: 'scan_device_3',
            name: 'SmartCollar V2',
            type: 'Collar',
            battery_level: 78,
            status: 'disconnected' as const,
          },
        ];
        
        // Only show devices that aren't already registered
        const availableDevices = simulatedDevices.filter(d => !currentDeviceIds.includes(d.id));
        
        set({ 
          isScanning: false,
          isLoading: false,
          availableDevices
        });
        
        toast.success(`Scan complete. Found ${availableDevices.length} available devices.`);
      } catch (error: any) {
        console.error('Error during device scan:', error);
        const errorMessage = error.message || 'Device scan failed';
        set({ 
          isScanning: false,
          isLoading: false,
          error: errorMessage,
          availableDevices: []
        });
        toast.error(`Scan failed: ${errorMessage}`);
      }
    }, 3000);
  },
  
  stopScan: (set: any) => {
    set({ isScanning: false });
  },
};