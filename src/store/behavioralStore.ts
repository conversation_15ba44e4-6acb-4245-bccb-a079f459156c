import { create } from 'zustand';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

// Types
export interface StressAnalysis {
  id: string;
  animal_id: string;
  user_id: string;
  analysis_timestamp: string;
  stress_level: 'very_low' | 'low' | 'moderate' | 'high' | 'very_high';
  stress_score: number;
  hrv_metrics: {
    rmssd: number;
    pnn50: number;
    heartRateVariability: number;
    averageHeartRate: number;
    stressIndicator: number;
  };
  activity_indicators: {
    restlessness: number;
    movementPatterns: string;
    activityLevel: number;
    unusualBehaviors: string[];
  };
  environmental_factors: {
    temperature: number;
    humidity: number;
    noiseLevel: number;
    locationChanges: number;
    weatherConditions: string;
    timeOfDay: string;
  };
  stress_triggers: Array<{
    type: string;
    confidence: number;
    description: string;
    timestamp: string;
  }>;
  confidence_level: number;
  analysis_duration_minutes: number;
  data_quality_score: number;
  recommendations: string;
  created_at: string;
  updated_at: string;
}

export interface SleepAnalysis {
  id: string;
  animal_id: string;
  user_id: string;
  sleep_date: string;
  sleep_start_time: string | null;
  sleep_end_time: string | null;
  total_sleep_duration_minutes: number;
  sleep_efficiency_percentage: number;
  deep_sleep_minutes: number;
  light_sleep_minutes: number;
  rem_sleep_minutes: number;
  wake_episodes: number;
  time_to_sleep_minutes: number;
  sleep_quality_score: number;
  sleep_disturbances: Array<{
    timestamp: string;
    type: string;
    severity: 'mild' | 'moderate' | 'severe';
    cause?: string;
    durationMinutes: number;
  }>;
  environmental_conditions: {
    averageTemperature: number;
    temperatureVariation: number;
    noiseLevel: number;
    lightExposure: number;
    humidity: number;
  };
  circadian_rhythm_alignment: number;
  sleep_recommendations: string;
  data_sources: {
    vitals_sensors: number;
    activity_monitors: number;
    environmental_sensors: number;
    total_data_points: number;
  };
  created_at: string;
  updated_at: string;
}

export interface BehavioralPattern {
  id: string;
  animal_id: string;
  user_id: string;
  pattern_type: 'activity' | 'feeding' | 'social' | 'location';
  pattern_category: 'daily' | 'weekly' | 'seasonal';
  pattern_start_date: string;
  pattern_end_date: string | null;
  pattern_strength: number;
  pattern_consistency: number;
  pattern_data: any;
  baseline_comparison: any;
  anomalies_detected: any;
  pattern_insights: string;
  confidence_level: number;
  created_at: string;
  updated_at: string;
}

export interface EnvironmentalImpact {
  id: string;
  animal_id: string;
  user_id: string;
  analysis_date: string;
  weather_conditions: any;
  location_factors: any;
  seasonal_factors: any;
  environmental_stress_score: number;
  adaptation_score: number;
  optimal_conditions: any;
  impact_on_behavior: any;
  impact_on_health: any;
  recommendations: string;
  created_at: string;
  updated_at: string;
}

export interface StressEvent {
  id: string;
  animal_id: string;
  user_id: string;
  event_timestamp: string;
  event_type: 'acute' | 'chronic' | 'environmental' | 'social';
  severity_level: 'mild' | 'moderate' | 'severe' | 'critical';
  duration_minutes: number | null;
  trigger_identified: boolean;
  trigger_description: string | null;
  physiological_response: any;
  behavioral_response: any;
  recovery_time_minutes: number | null;
  intervention_applied: string | null;
  resolution_status: 'ongoing' | 'resolved' | 'chronic';
  created_at: string;
  updated_at: string;
}

interface BehavioralState {
  // Stress Analysis
  stressAnalyses: StressAnalysis[];
  isLoadingStressAnalysis: boolean;
  stressAnalysisError: string | null;
  
  // Sleep Analysis
  sleepAnalyses: SleepAnalysis[];
  isLoadingSleepAnalysis: boolean;
  sleepAnalysisError: string | null;
  
  // Behavioral Patterns
  behavioralPatterns: BehavioralPattern[];
  isLoadingPatterns: boolean;
  patternsError: string | null;
  
  // Environmental Impact
  environmentalImpacts: EnvironmentalImpact[];
  isLoadingEnvironmental: boolean;
  environmentalError: string | null;
  
  // Stress Events
  stressEvents: StressEvent[];
  isLoadingStressEvents: boolean;
  stressEventsError: string | null;
  
  // Actions
  fetchStressAnalyses: (animalId: string, limit?: number) => Promise<void>;
  requestStressAnalysis: (animalId: string, analysisHours?: number) => Promise<void>;
  
  fetchSleepAnalyses: (animalId: string, limit?: number) => Promise<void>;
  requestSleepAnalysis: (animalId: string, analysisDate?: string) => Promise<void>;
  
  fetchBehavioralPatterns: (animalId: string, patternType?: string) => Promise<void>;
  
  fetchEnvironmentalImpacts: (animalId: string, limit?: number) => Promise<void>;
  
  fetchStressEvents: (animalId: string, limit?: number) => Promise<void>;
  resolveStressEvent: (eventId: string, interventionApplied?: string) => Promise<void>;
  
  // Getters
  getLatestStressAnalysis: (animalId: string) => StressAnalysis | null;
  getLatestSleepAnalysis: (animalId: string) => SleepAnalysis | null;
  getActiveStressEvents: (animalId: string) => StressEvent[];
  getPatternsByType: (animalId: string, patternType: string) => BehavioralPattern[];
  
  // Clear functions
  clearStressAnalyses: () => void;
  clearSleepAnalyses: () => void;
  clearBehavioralPatterns: () => void;
  clearEnvironmentalImpacts: () => void;
  clearStressEvents: () => void;
}

export const useBehavioralStore = create<BehavioralState>((set, get) => ({
  // Initial state
  stressAnalyses: [],
  isLoadingStressAnalysis: false,
  stressAnalysisError: null,
  
  sleepAnalyses: [],
  isLoadingSleepAnalysis: false,
  sleepAnalysisError: null,
  
  behavioralPatterns: [],
  isLoadingPatterns: false,
  patternsError: null,
  
  environmentalImpacts: [],
  isLoadingEnvironmental: false,
  environmentalError: null,
  
  stressEvents: [],
  isLoadingStressEvents: false,
  stressEventsError: null,
  
  // Stress Analysis Actions
  fetchStressAnalyses: async (animalId: string, limit = 10) => {
    set({ isLoadingStressAnalysis: true, stressAnalysisError: null });
    
    try {
      logApiCall('fetchStressAnalyses', { animalId, limit });
      
      const { data, error } = await supabase
        .from('stress_analysis')
        .select('*')
        .eq('animal_id', animalId)
        .order('analysis_timestamp', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      
      set({ 
        stressAnalyses: data || [],
        isLoadingStressAnalysis: false 
      });
      
    } catch (error: any) {
      console.error('Error fetching stress analyses:', error);
      set({ 
        stressAnalysisError: error.message,
        isLoadingStressAnalysis: false 
      });
      toast.error('Failed to load stress analyses');
    }
  },
  
  requestStressAnalysis: async (animalId: string, analysisHours = 24) => {
    set({ isLoadingStressAnalysis: true, stressAnalysisError: null });
    
    try {
      logApiCall('requestStressAnalysis', { animalId, analysisHours });
      
      const { data, error } = await supabase.functions.invoke('analyze-stress-levels', {
        body: { animalId, analysisHours }
      });
      
      if (error) throw error;
      
      if (data?.success) {
        // Refresh stress analyses
        await get().fetchStressAnalyses(animalId);
        toast.success('Stress analysis completed successfully');
      } else {
        throw new Error(data?.error || 'Analysis failed');
      }
      
    } catch (error: any) {
      console.error('Error requesting stress analysis:', error);
      set({ 
        stressAnalysisError: error.message,
        isLoadingStressAnalysis: false 
      });
      toast.error('Failed to analyze stress levels');
    }
  },
  
  // Sleep Analysis Actions
  fetchSleepAnalyses: async (animalId: string, limit = 10) => {
    set({ isLoadingSleepAnalysis: true, sleepAnalysisError: null });
    
    try {
      logApiCall('fetchSleepAnalyses', { animalId, limit });
      
      const { data, error } = await supabase
        .from('sleep_analysis')
        .select('*')
        .eq('animal_id', animalId)
        .order('sleep_date', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      
      set({ 
        sleepAnalyses: data || [],
        isLoadingSleepAnalysis: false 
      });
      
    } catch (error: any) {
      console.error('Error fetching sleep analyses:', error);
      set({ 
        sleepAnalysisError: error.message,
        isLoadingSleepAnalysis: false 
      });
      toast.error('Failed to load sleep analyses');
    }
  },
  
  requestSleepAnalysis: async (animalId: string, analysisDate?: string) => {
    set({ isLoadingSleepAnalysis: true, sleepAnalysisError: null });
    
    try {
      logApiCall('requestSleepAnalysis', { animalId, analysisDate });
      
      const { data, error } = await supabase.functions.invoke('analyze-sleep-patterns', {
        body: { animalId, analysisDate }
      });
      
      if (error) throw error;
      
      if (data?.success) {
        // Refresh sleep analyses
        await get().fetchSleepAnalyses(animalId);
        toast.success('Sleep analysis completed successfully');
      } else {
        throw new Error(data?.error || 'Analysis failed');
      }
      
    } catch (error: any) {
      console.error('Error requesting sleep analysis:', error);
      set({ 
        sleepAnalysisError: error.message,
        isLoadingSleepAnalysis: false 
      });
      toast.error('Failed to analyze sleep patterns');
    }
  },
  
  // Behavioral Patterns Actions
  fetchBehavioralPatterns: async (animalId: string, patternType?: string) => {
    set({ isLoadingPatterns: true, patternsError: null });
    
    try {
      logApiCall('fetchBehavioralPatterns', { animalId, patternType });
      
      let query = supabase
        .from('behavioral_patterns')
        .select('*')
        .eq('animal_id', animalId)
        .order('pattern_start_date', { ascending: false });
      
      if (patternType) {
        query = query.eq('pattern_type', patternType);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      set({ 
        behavioralPatterns: data || [],
        isLoadingPatterns: false 
      });
      
    } catch (error: any) {
      console.error('Error fetching behavioral patterns:', error);
      set({ 
        patternsError: error.message,
        isLoadingPatterns: false 
      });
      toast.error('Failed to load behavioral patterns');
    }
  },
  
  // Environmental Impact Actions
  fetchEnvironmentalImpacts: async (animalId: string, limit = 10) => {
    set({ isLoadingEnvironmental: true, environmentalError: null });
    
    try {
      logApiCall('fetchEnvironmentalImpacts', { animalId, limit });
      
      const { data, error } = await supabase
        .from('environmental_impact')
        .select('*')
        .eq('animal_id', animalId)
        .order('analysis_date', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      
      set({ 
        environmentalImpacts: data || [],
        isLoadingEnvironmental: false 
      });
      
    } catch (error: any) {
      console.error('Error fetching environmental impacts:', error);
      set({ 
        environmentalError: error.message,
        isLoadingEnvironmental: false 
      });
      toast.error('Failed to load environmental impacts');
    }
  },
  
  // Stress Events Actions
  fetchStressEvents: async (animalId: string, limit = 20) => {
    set({ isLoadingStressEvents: true, stressEventsError: null });
    
    try {
      logApiCall('fetchStressEvents', { animalId, limit });
      
      const { data, error } = await supabase
        .from('stress_events')
        .select('*')
        .eq('animal_id', animalId)
        .order('event_timestamp', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      
      set({ 
        stressEvents: data || [],
        isLoadingStressEvents: false 
      });
      
    } catch (error: any) {
      console.error('Error fetching stress events:', error);
      set({ 
        stressEventsError: error.message,
        isLoadingStressEvents: false 
      });
      toast.error('Failed to load stress events');
    }
  },
  
  resolveStressEvent: async (eventId: string, interventionApplied?: string) => {
    try {
      logApiCall('resolveStressEvent', { eventId, interventionApplied });
      
      const { error } = await supabase
        .from('stress_events')
        .update({
          resolution_status: 'resolved',
          intervention_applied: interventionApplied || 'Manual resolution',
          updated_at: new Date().toISOString()
        })
        .eq('id', eventId);
      
      if (error) throw error;
      
      // Update local state
      set(state => ({
        stressEvents: state.stressEvents.map(event => 
          event.id === eventId 
            ? { 
                ...event, 
                resolution_status: 'resolved',
                intervention_applied: interventionApplied || 'Manual resolution'
              }
            : event
        )
      }));
      
      toast.success('Stress event resolved');
      
    } catch (error: any) {
      console.error('Error resolving stress event:', error);
      toast.error('Failed to resolve stress event');
    }
  },
  
  // Getters
  getLatestStressAnalysis: (animalId: string) => {
    const analyses = get().stressAnalyses.filter(analysis => analysis.animal_id === animalId);
    return analyses.length > 0 ? analyses[0] : null;
  },
  
  getLatestSleepAnalysis: (animalId: string) => {
    const analyses = get().sleepAnalyses.filter(analysis => analysis.animal_id === animalId);
    return analyses.length > 0 ? analyses[0] : null;
  },
  
  getActiveStressEvents: (animalId: string) => {
    return get().stressEvents.filter(event => 
      event.animal_id === animalId && event.resolution_status === 'ongoing'
    );
  },
  
  getPatternsByType: (animalId: string, patternType: string) => {
    return get().behavioralPatterns.filter(pattern => 
      pattern.animal_id === animalId && pattern.pattern_type === patternType
    );
  },
  
  // Clear functions
  clearStressAnalyses: () => set({ stressAnalyses: [] }),
  clearSleepAnalyses: () => set({ sleepAnalyses: [] }),
  clearBehavioralPatterns: () => set({ behavioralPatterns: [] }),
  clearEnvironmentalImpacts: () => set({ environmentalImpacts: [] }),
  clearStressEvents: () => set({ stressEvents: [] })
}));