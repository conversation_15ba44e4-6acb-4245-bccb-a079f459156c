import { create } from 'zustand';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

export interface DehydrationReading {
  id: string;
  animal_id: string;
  user_id: string;
  hydration_level: number; // 0-100%
  hydration_status: 'optimal' | 'mild_dehydration' | 'moderate_dehydration' | 'severe_dehydration';
  bioimpedance_reading: number; // Raw sensor value in ohms
  skin_conductance?: number; // microsiemens
  body_temperature?: number; // Celsius
  ambient_temperature?: number; // Celsius
  humidity_level?: number; // percentage
  device_id?: string;
  measurement_duration?: number; // seconds
  signal_quality?: 'excellent' | 'good' | 'fair' | 'poor';
  notes?: string;
  is_manual_entry: boolean;
  created_at: string;
  updated_at: string;
}

export interface DehydrationTrend {
  date: string;
  avg_hydration: number;
  min_hydration: number;
  max_hydration: number;
  reading_count: number;
  status_distribution: Record<string, number>;
}

export interface DehydrationAlert {
  id: string;
  animal_id: string;
  alert_type: 'dehydration_warning' | 'dehydration_critical' | 'sensor_error' | 'trend_concern';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  reading_id?: string;
  created_at: string;
  acknowledged: boolean;
}

interface DehydrationState {
  // Data
  readings: DehydrationReading[];
  trends: DehydrationTrend[];
  alerts: DehydrationAlert[];
  
  // Loading states
  isLoading: boolean;
  isRecording: boolean;
  isSyncing: boolean;
  
  // Error handling
  error: string | null;
  
  // Current session (for live monitoring)
  currentSession: {
    animal_id: string;
    start_time: string;
    readings: DehydrationReading[];
  } | null;
  
  // Actions
  fetchReadings: (animalId: string, limit?: number) => Promise<void>;
  addReading: (reading: Omit<DehydrationReading, 'id' | 'created_at' | 'updated_at'>) => Promise<DehydrationReading | null>;
  updateReading: (id: string, updates: Partial<DehydrationReading>) => Promise<void>;
  deleteReading: (id: string) => Promise<void>;
  
  // Live monitoring
  startMonitoringSession: (animalId: string) => Promise<void>;
  stopMonitoringSession: () => Promise<void>;
  simulateReading: (animalId: string) => Promise<DehydrationReading | null>;
  
  // Analytics
  fetchTrends: (animalId: string, days?: number) => Promise<void>;
  fetchAlerts: (animalId: string) => Promise<void>;
  acknowledgeAlert: (alertId: string) => Promise<void>;
  
  // Utilities
  getLatestReading: (animalId: string) => DehydrationReading | null;
  getHydrationStatus: (animalId: string) => string;
  clearError: () => void;
  reset: () => void;
}

export const useDehydrationStore = create<DehydrationState>((set, get) => ({
  // Initial state
  readings: [],
  trends: [],
  alerts: [],
  isLoading: false,
  isRecording: false,
  isSyncing: false,
  error: null,
  currentSession: null,
  
  // Fetch readings for an animal
  fetchReadings: async (animalId: string, limit = 50) => {
    set({ isLoading: true, error: null });
    
    try {
      const { data, error } = await supabase
        .from('dehydration_logs')
        .select('*')
        .eq('animal_id', animalId)
        .order('created_at', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      
      logApiCall('fetchDehydrationReadings', data);
      set({ readings: data || [], isLoading: false });
      
    } catch (error: any) {
      console.error('Error fetching dehydration readings:', error);
      logApiCall('fetchDehydrationReadings', null, error);
      set({ error: error.message, isLoading: false });
      toast.error('Failed to load hydration data');
    }
  },
  
  // Add new reading
  addReading: async (reading) => {
    set({ isSyncing: true, error: null });
    
    try {
      const { data, error } = await supabase
        .from('dehydration_logs')
        .insert([reading])
        .select()
        .single();
      
      if (error) throw error;
      
      logApiCall('addDehydrationReading', data);
      
      // Update local state
      set(state => ({
        readings: [data, ...state.readings],
        isSyncing: false
      }));
      
      // Check for alerts
      await get().checkForAlerts(data);
      
      toast.success('Hydration reading recorded');
      return data;
      
    } catch (error: any) {
      console.error('Error adding dehydration reading:', error);
      logApiCall('addDehydrationReading', null, error);
      set({ error: error.message, isSyncing: false });
      toast.error('Failed to save hydration reading');
      return null;
    }
  },
  
  // Update reading
  updateReading: async (id: string, updates) => {
    set({ isSyncing: true, error: null });
    
    try {
      const { data, error } = await supabase
        .from('dehydration_logs')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      
      logApiCall('updateDehydrationReading', data);
      
      // Update local state
      set(state => ({
        readings: state.readings.map(r => r.id === id ? { ...r, ...data } : r),
        isSyncing: false
      }));
      
      toast.success('Reading updated');
      
    } catch (error: any) {
      console.error('Error updating dehydration reading:', error);
      logApiCall('updateDehydrationReading', null, error);
      set({ error: error.message, isSyncing: false });
      toast.error('Failed to update reading');
    }
  },
  
  // Delete reading
  deleteReading: async (id: string) => {
    set({ isSyncing: true, error: null });
    
    try {
      const { error } = await supabase
        .from('dehydration_logs')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      logApiCall('deleteDehydrationReading', { id });
      
      // Update local state
      set(state => ({
        readings: state.readings.filter(r => r.id !== id),
        isSyncing: false
      }));
      
      toast.success('Reading deleted');
      
    } catch (error: any) {
      console.error('Error deleting dehydration reading:', error);
      logApiCall('deleteDehydrationReading', null, error);
      set({ error: error.message, isSyncing: false });
      toast.error('Failed to delete reading');
    }
  },
  
  // Start live monitoring session
  startMonitoringSession: async (animalId: string) => {
    set({ 
      isRecording: true, 
      error: null,
      currentSession: {
        animal_id: animalId,
        start_time: new Date().toISOString(),
        readings: []
      }
    });
    
    toast.success('Started hydration monitoring');
  },
  
  // Stop monitoring session
  stopMonitoringSession: async () => {
    const { currentSession } = get();
    
    if (currentSession && currentSession.readings.length > 0) {
      // Calculate session summary
      const avgHydration = currentSession.readings.reduce((sum, r) => sum + r.hydration_level, 0) / currentSession.readings.length;
      const duration = Math.round((new Date().getTime() - new Date(currentSession.start_time).getTime()) / 1000 / 60); // minutes
      
      toast.success(`Monitoring session completed\nDuration: ${duration} min\nAvg Hydration: ${avgHydration.toFixed(1)}%`);
    }
    
    set({ 
      isRecording: false, 
      currentSession: null 
    });
  },
  
  // Simulate MAX30009 sensor reading
  simulateReading: async (animalId: string) => {
    const { currentSession } = get();
    
    try {
      // Simulate sensor data (in real app, this would come from actual MAX30009 device)
      const baseImpedance = 350 + Math.random() * 200; // 350-550 ohms
      const hydrationLevel = Math.max(0, Math.min(100, 100 - ((baseImpedance - 200) / 4)));
      
      let hydrationStatus: DehydrationReading['hydration_status'];
      if (baseImpedance <= 300) hydrationStatus = 'optimal';
      else if (baseImpedance <= 400) hydrationStatus = 'mild_dehydration';
      else if (baseImpedance <= 500) hydrationStatus = 'moderate_dehydration';
      else hydrationStatus = 'severe_dehydration';
      
      const reading: Omit<DehydrationReading, 'id' | 'created_at' | 'updated_at'> = {
        animal_id: animalId,
        user_id: (await supabase.auth.getUser()).data.user?.id || '',
        hydration_level: Math.round(hydrationLevel * 100) / 100,
        hydration_status: hydrationStatus,
        bioimpedance_reading: Math.round(baseImpedance * 100) / 100,
        skin_conductance: Math.round((15 + Math.random() * 10) * 100) / 100,
        body_temperature: Math.round((37.5 + Math.random() * 2) * 100) / 100,
        ambient_temperature: Math.round((20 + Math.random() * 15) * 100) / 100,
        humidity_level: Math.round((40 + Math.random() * 40) * 100) / 100,
        device_id: `MAX30009-${Math.floor(Math.random() * 9999).toString().padStart(4, '0')}`,
        measurement_duration: 30,
        signal_quality: Math.random() > 0.8 ? 'excellent' : Math.random() > 0.6 ? 'good' : Math.random() > 0.3 ? 'fair' : 'poor',
        is_manual_entry: false
      };
      
      // Add to current session if monitoring
      if (currentSession) {
        const newReading = await get().addReading(reading);
        if (newReading) {
          set(state => ({
            currentSession: state.currentSession ? {
              ...state.currentSession,
              readings: [...state.currentSession.readings, newReading]
            } : null
          }));
        }
        return newReading;
      } else {
        return await get().addReading(reading);
      }
      
    } catch (error: any) {
      console.error('Error simulating sensor reading:', error);
      set({ error: error.message });
      toast.error('Failed to capture sensor reading');
      return null;
    }
  },
  
  // Fetch trends
  fetchTrends: async (animalId: string, days = 7) => {
    try {
      const { data, error } = await supabase
        .from('dehydration_logs')
        .select('*')
        .eq('animal_id', animalId)
        .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      
      // Process data into daily trends
      const trendMap = new Map<string, DehydrationTrend>();
      
      data?.forEach(reading => {
        const date = reading.created_at.split('T')[0];
        const existing = trendMap.get(date);
        
        if (existing) {
          existing.reading_count++;
          existing.avg_hydration = (existing.avg_hydration * (existing.reading_count - 1) + reading.hydration_level) / existing.reading_count;
          existing.min_hydration = Math.min(existing.min_hydration, reading.hydration_level);
          existing.max_hydration = Math.max(existing.max_hydration, reading.hydration_level);
          existing.status_distribution[reading.hydration_status] = (existing.status_distribution[reading.hydration_status] || 0) + 1;
        } else {
          trendMap.set(date, {
            date,
            avg_hydration: reading.hydration_level,
            min_hydration: reading.hydration_level,
            max_hydration: reading.hydration_level,
            reading_count: 1,
            status_distribution: { [reading.hydration_status]: 1 }
          });
        }
      });
      
      set({ trends: Array.from(trendMap.values()) });
      
    } catch (error: any) {
      console.error('Error fetching dehydration trends:', error);
      set({ error: error.message });
    }
  },
  
  // Fetch alerts (placeholder - would be implemented with real alert system)
  fetchAlerts: async (animalId: string) => {
    // This would typically fetch from an alerts table
    // For now, generate alerts based on recent readings
    const { readings } = get();
    const recentReadings = readings.filter(r => r.animal_id === animalId).slice(0, 5);
    
    const alerts: DehydrationAlert[] = [];
    
    recentReadings.forEach(reading => {
      if (reading.hydration_status === 'severe_dehydration') {
        alerts.push({
          id: `alert-${reading.id}`,
          animal_id: animalId,
          alert_type: 'dehydration_critical',
          severity: 'critical',
          message: `Critical dehydration detected: ${reading.hydration_level.toFixed(1)}%`,
          reading_id: reading.id,
          created_at: reading.created_at,
          acknowledged: false
        });
      } else if (reading.hydration_status === 'moderate_dehydration') {
        alerts.push({
          id: `alert-${reading.id}`,
          animal_id: animalId,
          alert_type: 'dehydration_warning',
          severity: 'high',
          message: `Moderate dehydration detected: ${reading.hydration_level.toFixed(1)}%`,
          reading_id: reading.id,
          created_at: reading.created_at,
          acknowledged: false
        });
      }
    });
    
    set({ alerts });
  },
  
  // Acknowledge alert
  acknowledgeAlert: async (alertId: string) => {
    set(state => ({
      alerts: state.alerts.map(alert => 
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      )
    }));
  },
  
  // Check for alerts after new reading
  checkForAlerts: async (reading: DehydrationReading) => {
    // This would typically trigger server-side alert processing
    // For now, just update local alerts
    await get().fetchAlerts(reading.animal_id);
  },
  
  // Get latest reading for an animal
  getLatestReading: (animalId: string) => {
    const { readings } = get();
    return readings.find(r => r.animal_id === animalId) || null;
  },
  
  // Get hydration status for an animal
  getHydrationStatus: (animalId: string) => {
    const latest = get().getLatestReading(animalId);
    return latest?.hydration_status || 'unknown';
  },
  
  // Clear error
  clearError: () => set({ error: null }),
  
  // Reset store
  reset: () => set({
    readings: [],
    trends: [],
    alerts: [],
    isLoading: false,
    isRecording: false,
    isSyncing: false,
    error: null,
    currentSession: null
  })
}));