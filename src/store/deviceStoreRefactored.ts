import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Device } from '../mocks/devices';
import { deviceApiOperations } from './slices/deviceApiSlice';
import { deviceScanOperations } from './slices/deviceScanSlice';
import { devicePairingOperations } from './slices/devicePairingSlice';
import { toast } from 'sonner-native';
import { useAnimalStore } from './animalStore';

interface DeviceState {
  devices: Device[];
  pairedDevices: Device[];
  availableDevices: Device[];
  isScanning: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchDevices: () => Promise<void>;
  getDevices: () => Device[];
  getDeviceById: (id: string) => Device | undefined;
  addDevice: (device: Omit<Device, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => Promise<Device | null>;
  updateDevice: (id: string, updates: Partial<Omit<Device, 'id' | 'user_id' | 'created_at' | 'updated_at'>>) => Promise<Device | null>;
  deleteDevice: (id: string) => Promise<boolean>;
  pairDevice: (deviceId: string, animalId: string) => Promise<void>;
  unpairDevice: (deviceId: string) => Promise<void>;
  startScan: () => void;
  stopScan: () => void;
  updateDeviceStatus: (id: string, status: Device['status']) => Promise<void>;
  updateBatteryLevel: (id: string, batteryLevel: number) => Promise<void>;
}

export const useDeviceStoreRefactored = create<DeviceState>()(
  persist(
    (set, get) => ({
      // Initial state
      devices: [],
      pairedDevices: [],
      availableDevices: [],
      isScanning: false,
      isLoading: false,
      error: null,
      
      // API Operations
      fetchDevices: () => deviceApiOperations.fetchDevices(set, get),
      addDevice: (deviceData) => deviceApiOperations.addDevice(set, get, deviceData),
      updateDevice: (id, updates) => deviceApiOperations.updateDevice(set, get, id, updates),
      deleteDevice: (id) => deviceApiOperations.deleteDevice(set, get, id),
      
      // Scanning Operations
      startScan: () => deviceScanOperations.startScan(set, get),
      stopScan: () => deviceScanOperations.stopScan(set),
      
      // Pairing Operations
      pairDevice: (deviceId, animalId) => devicePairingOperations.pairDevice(set, get, deviceId, animalId),
      unpairDevice: (deviceId) => devicePairingOperations.unpairDevice(set, get, deviceId),
      
      // Utility Methods
      getDevices: () => get().devices,
      
      getDeviceById: (id) => {
        return get().devices.find(device => device.id === id);
      },
      
      // Update device status in Supabase
      updateDeviceStatus: async (id, status) => {
        try {
          const device = get().devices.find(d => d.id === id);
          if (!device) {
            console.error('Device not found for status update:', id);
            toast.error('Device not found');
            return;
          }
          
          // Use the existing updateDevice method to persist to Supabase
          const result = await get().updateDevice(id, {
            status,
            last_sync_time: new Date().toISOString()
          });
          
          if (!result) {
            throw new Error('Failed to update device status');
          }
          
          // Update pairedDevices list based on new status
          set(state => ({
            pairedDevices: status === 'connected' || status === 'paired' || status === 'low_battery'
              ? [...state.pairedDevices.filter(d => d.id !== id), result]
              : state.pairedDevices.filter(d => d.id !== id),
          }));
          
        } catch (error: any) {
          console.error('Error updating device status:', error);
          // Error toast is already handled by updateDevice
        }
      },
      
      // Update device battery level in Supabase
      updateBatteryLevel: async (id, batteryLevel) => {
        try {
          const device = get().devices.find(d => d.id === id);
          if (!device) {
            console.error('Device not found for battery update:', id);
            return;
          }
          
          // Use the existing updateDevice method to persist to Supabase
          const result = await get().updateDevice(id, {
            battery_level: batteryLevel,
            last_sync_time: new Date().toISOString()
          });
          
          if (!result) {
            throw new Error('Failed to update battery level');
          }
          
          // Update pairedDevices if this device is in the list
          set(state => ({
            pairedDevices: state.pairedDevices.map(d => 
              d.id === id ? result : d
            ),
          }));
          
        } catch (error: any) {
          console.error('Error updating battery level:', error);
          // Error toast is already handled by updateDevice
        }
      },
    }),
    {
      name: 'hoofbeat-device-storage-refactored',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);