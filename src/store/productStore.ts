import { create } from 'zustand';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  image_url: string;
  stock_quantity: number;
  is_active: boolean;
  category: string;
  specifications: any;
  created_at: string;
  updated_at: string;
}

interface ProductState {
  products: Product[];
  isLoading: boolean;
  error: string | null;
  selectedProduct: Product | null;
}

interface ProductActions {
  fetchProducts: () => Promise<void>;
  getProductById: (id: string) => Product | undefined;
  setSelectedProduct: (product: Product | null) => void;
  clearError: () => void;
  refreshProducts: () => Promise<void>;
}

type ProductStore = ProductState & ProductActions;

export const useProductStore = create<ProductStore>((set, get) => ({
  // Initial state
  products: [],
  isLoading: false,
  error: null,
  selectedProduct: null,

  // Actions
  fetchProducts: async () => {
    set({ isLoading: true, error: null });

    try {
      // Check authentication first
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.warn('fetchProducts: User not authenticated');
        set({ 
          products: [], 
          isLoading: false, 
          error: 'User not authenticated' 
        });
        return;
      }

      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      logApiCall('fetchProducts', data, error);

      if (error) {
        console.error('Supabase error in fetchProducts:', error);
        
        // Handle specific error types
        let errorMessage = 'Failed to load products';
        if (error.message.includes('Failed to fetch') || error.message.includes('TypeError: Failed to fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.message.includes('CORS')) {
          errorMessage = 'Connection blocked. Please disable antivirus web protection temporarily.';
        } else {
          errorMessage = `Database error: ${error.message}`;
        }
        
        set({ error: errorMessage, isLoading: false, products: [] });
        toast.error(errorMessage);
        return;
      }

      // Defensive data validation
      const validProducts = Array.isArray(data) ? data : [];
      
      set({ 
        products: validProducts, 
        isLoading: false, 
        error: null 
      });

      console.log(`✅ Loaded ${validProducts.length} products`);
    } catch (err: any) {
      console.error('Unexpected error fetching products:', err);
      
      // Handle network and CORS errors specifically
      let errorMessage = 'Failed to load products';
      if (err.message?.includes('Failed to fetch') || err.name === 'TypeError') {
        errorMessage = 'Network connection failed. Please check your internet connection or disable antivirus web protection.';
      } else if (err.message?.includes('CORS')) {
        errorMessage = 'Connection blocked by security software. Please disable antivirus web protection temporarily.';
      } else {
        errorMessage = err.message || 'An unexpected error occurred';
      }
      
      set({ error: errorMessage, isLoading: false, products: [] });
      toast.error(errorMessage);
      
      // Don't re-throw to prevent app crashes
      console.warn('fetchProducts completed with error, continuing gracefully');
    }
  },

  getProductById: (id: string) => {
    return get().products.find(product => product.id === id);
  },

  setSelectedProduct: (product: Product | null) => {
    set({ selectedProduct: product });
  },

  clearError: () => {
    set({ error: null });
  },

  refreshProducts: async () => {
    await get().fetchProducts();
  }
}));

// Auto-fetch products when store is first used
let hasInitialized = false;
if (!hasInitialized) {
  hasInitialized = true;
  // Delay initial fetch to avoid calling during store creation
  setTimeout(() => {
    useProductStore.getState().fetchProducts();
  }, 100);
}