import { create } from 'zustand';
import { 
  healthAssessmentOperations, 
  healthAssessmentInitialState, 
  HealthAssessment,
  HealthAssessmentState 
} from './slices/aiHealthAssessmentSlice';
import { 
  trainingPlanOperations, 
  trainingPlanInitialState, 
  TrainingPlan,
  TrainingPlanState 
} from './slices/aiTrainingPlanSlice';
import { 
  readinessScoreOperations, 
  readinessScoreInitialState, 
  ReadinessScore,
  ReadinessScoreState 
} from './slices/aiReadinessScoreSlice';
import { 
  coachingTipOperations, 
  coachingTipInitialState, 
  CoachingTip,
  CoachingTipState 
} from './slices/aiCoachingTipSlice';
import { 
  aiAnalysisOperations, 
  aiAnalysisInitialState, 
  AIAnalysisState 
} from './slices/aiAnalysisSlice';

// Combined AI Store interface
interface AIStore extends 
  HealthAssessmentState,
  TrainingPlanState,
  ReadinessScoreState,
  CoachingTipState,
  AIAnalysisState {
  // Actions
  requestAIAnalysis: (animalId: string) => Promise<void>;
  requestRealtimeAnalysis: (animalId: string) => Promise<void>;
  requestDehydrationAnalysis: (animalId: string, days?: number) => Promise<void>;
  fetchHealthAssessments: (animalId?: string) => Promise<void>;
  fetchTrainingPlans: (animalId?: string) => Promise<void>;
  fetchReadinessScores: (animalId?: string) => Promise<void>;
  fetchCoachingTips: (animalId?: string) => Promise<void>;
  markTipAsRead: (tipId: string) => Promise<void>;
  
  // Getters
  getLatestHealthAssessment: (animalId: string) => HealthAssessment | null;
  getLatestTrainingPlan: (animalId: string) => TrainingPlan | null;
  getLatestReadinessScore: (animalId: string) => ReadinessScore | null;
  getLatestRealtimeAnalysis: (animalId: string) => any | null;
  getLatestDehydrationAnalysis: (animalId: string) => any | null;
  getUnreadTips: (animalId?: string) => CoachingTip[];
}

export const useAIStoreRefactored = create<AIStore>((set, get) => ({
  // Combined initial state
  ...healthAssessmentInitialState,
  ...trainingPlanInitialState,
  ...readinessScoreInitialState,
  ...coachingTipInitialState,
  ...aiAnalysisInitialState,
  
  // Actions that delegate to slice operations
  requestAIAnalysis: (animalId) => aiAnalysisOperations.requestAIAnalysis(set, get, animalId),
  requestRealtimeAnalysis: (animalId) => aiAnalysisOperations.requestRealtimeAnalysis(set, get, animalId),
  requestDehydrationAnalysis: (animalId, days) => aiAnalysisOperations.requestDehydrationAnalysis(set, get, animalId, days),
  
  fetchHealthAssessments: (animalId) => healthAssessmentOperations.fetchHealthAssessments(set, get, animalId),
  fetchTrainingPlans: (animalId) => trainingPlanOperations.fetchTrainingPlans(set, get, animalId),
  fetchReadinessScores: (animalId) => readinessScoreOperations.fetchReadinessScores(set, get, animalId),
  fetchCoachingTips: (animalId) => coachingTipOperations.fetchCoachingTips(set, get, animalId),
  markTipAsRead: (tipId) => coachingTipOperations.markTipAsRead(set, get, tipId),
  
  // Getters
  getLatestHealthAssessment: (animalId) => healthAssessmentOperations.getLatestHealthAssessment(get, animalId),
  getLatestTrainingPlan: (animalId) => trainingPlanOperations.getLatestTrainingPlan(get, animalId),
  getLatestReadinessScore: (animalId) => readinessScoreOperations.getLatestReadinessScore(get, animalId),
  getLatestRealtimeAnalysis: (animalId) => aiAnalysisOperations.getLatestRealtimeAnalysis(get, animalId),
  getLatestDehydrationAnalysis: (animalId) => aiAnalysisOperations.getLatestDehydrationAnalysis(get, animalId),
  getUnreadTips: (animalId) => coachingTipOperations.getUnreadTips(get, animalId),
}));

// Export types for backward compatibility
export type { HealthAssessment, TrainingPlan, ReadinessScore, CoachingTip };