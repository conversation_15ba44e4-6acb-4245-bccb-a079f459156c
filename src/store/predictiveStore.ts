import { create } from 'zustand';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

export interface PredictiveHealthInsight {
  id: string;
  animal_id: string;
  prediction_date: string;
  prediction_horizon_days: number;
  predicted_health_score?: number;
  predicted_weight_kg?: number;
  predicted_activity_level?: string;
  predicted_stress_level?: string;
  predicted_sleep_quality?: number;
  disease_risk_probability?: number;
  injury_risk_probability?: number;
  behavioral_issue_risk?: number;
  environmental_stress_risk?: number;
  vital_signs_forecast?: any;
  weight_trend_forecast?: any;
  activity_pattern_forecast?: any;
  prediction_confidence?: number;
  model_accuracy_score?: number;
  contributing_factors?: any;
  preventive_actions?: any;
  environmental_adjustments?: any;
  care_schedule_recommendations?: any;
  prediction_model_version?: string;
  training_data_period_days?: number;
  created_at: string;
  updated_at: string;
}

export interface PredictiveAlert {
  id: string;
  animal_id: string;
  alert_type: string;
  severity_level: string;
  predicted_event: string;
  probability_percentage: number;
  predicted_timeframe_days?: number;
  confidence_level?: number;
  alert_title: string;
  alert_message: string;
  recommended_actions?: any;
  environmental_factors?: any;
  health_indicators?: any;
  behavioral_patterns?: any;
  status: string;
  acknowledged_at?: string;
  resolved_at?: string;
  follow_up_required: boolean;
  follow_up_date?: string;
  outcome_recorded: boolean;
  actual_outcome?: string;
  created_at: string;
  updated_at: string;
}

interface PredictiveStore {
  // Predictive Insights
  predictions: PredictiveHealthInsight[];
  isLoadingPredictions: boolean;
  
  // Predictive Alerts
  alerts: PredictiveAlert[];
  isLoadingAlerts: boolean;
  
  // Actions
  generatePredictiveInsights: (animalId: string, horizons?: number[]) => Promise<PredictiveHealthInsight[]>;
  fetchPredictions: (animalId: string) => Promise<void>;
  fetchAlerts: (animalId: string) => Promise<void>;
  acknowledgeAlert: (alertId: string) => Promise<void>;
  resolveAlert: (alertId: string, outcome?: string) => Promise<void>;
  dismissAlert: (alertId: string) => Promise<void>;
  clearPredictiveData: () => void;
}

export const usePredictiveStore = create<PredictiveStore>((set, get) => ({
  // Initial state
  predictions: [],
  isLoadingPredictions: false,
  alerts: [],
  isLoadingAlerts: false,

  // Generate predictive insights
  generatePredictiveInsights: async (animalId: string, horizons: number[] = [7, 30, 90]) => {
    set({ isLoadingPredictions: true });
    
    try {
      const { data, error } = await supabase.functions.invoke('generate-predictive-insights', {
        body: {
          animalId,
          predictionHorizons: horizons,
          includeRiskAssessment: true,
          includeEnvironmentalFactors: true
        }
      });

      if (error) {
        console.error('Error generating predictive insights:', error);
        toast.error('Failed to generate predictive insights');
        return [];
      }

      if (!data.success) {
        console.error('Predictive insights generation failed:', data.error);
        toast.error(data.error || 'Predictive insights generation failed');
        return [];
      }

      // Update predictions
      const currentPredictions = get().predictions;
      const updatedPredictions = [...data.predictions, ...currentPredictions.filter(
        pred => !data.predictions.some((newPred: any) => 
          newPred.animal_id === pred.animal_id && 
          newPred.prediction_horizon_days === pred.prediction_horizon_days
        )
      )];
      set({ predictions: updatedPredictions });
      
      // Update alerts if provided
      if (data.predictiveAlerts && data.predictiveAlerts.length > 0) {
        const currentAlerts = get().alerts;
        set({ alerts: [...data.predictiveAlerts, ...currentAlerts] });
      }
      
      logApiCall('predictive_insights', 'generate', {
        animalId,
        horizons,
        predictionsGenerated: data.predictions.length,
        alertsGenerated: data.predictiveAlerts?.length || 0
      });
      
      toast.success('Predictive insights generated successfully');
      return data.predictions;
      
    } catch (error) {
      console.error('Error in generatePredictiveInsights:', error);
      toast.error('Failed to generate predictive insights');
      return [];
    } finally {
      set({ isLoadingPredictions: false });
    }
  },

  // Fetch existing predictions
  fetchPredictions: async (animalId: string) => {
    set({ isLoadingPredictions: true });
    
    try {
      const { data, error } = await supabase
        .from('predictive_health_insights')
        .select('*')
        .eq('animal_id', animalId)
        .order('prediction_date', { ascending: false })
        .limit(20);

      if (error) {
        console.error('Error fetching predictions:', error);
        toast.error('Failed to load predictive insights');
        return;
      }

      set({ predictions: data || [] });
      
      logApiCall('predictive_health_insights', 'fetch', {
        animalId,
        predictionCount: data?.length || 0
      });
      
    } catch (error) {
      console.error('Error in fetchPredictions:', error);
      toast.error('Failed to load predictive insights');
    } finally {
      set({ isLoadingPredictions: false });
    }
  },

  // Fetch predictive alerts
  fetchAlerts: async (animalId: string) => {
    set({ isLoadingAlerts: true });
    
    try {
      const { data, error } = await supabase
        .from('predictive_alerts')
        .select('*')
        .eq('animal_id', animalId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        console.error('Error fetching alerts:', error);
        toast.error('Failed to load predictive alerts');
        return;
      }

      set({ alerts: data || [] });
      
      logApiCall('predictive_alerts', 'fetch', {
        animalId,
        alertCount: data?.length || 0
      });
      
    } catch (error) {
      console.error('Error in fetchAlerts:', error);
      toast.error('Failed to load predictive alerts');
    } finally {
      set({ isLoadingAlerts: false });
    }
  },

  // Acknowledge alert
  acknowledgeAlert: async (alertId: string) => {
    try {
      const { data, error } = await supabase
        .from('predictive_alerts')
        .update({
          status: 'acknowledged',
          acknowledged_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', alertId)
        .select()
        .single();

      if (error) {
        console.error('Error acknowledging alert:', error);
        toast.error('Failed to acknowledge alert');
        return;
      }

      // Update local state
      const currentAlerts = get().alerts;
      const updatedAlerts = currentAlerts.map(alert => 
        alert.id === alertId ? data : alert
      );
      set({ alerts: updatedAlerts });
      
      logApiCall('predictive_alerts', 'acknowledge', { alertId });
      toast.success('Alert acknowledged');
      
    } catch (error) {
      console.error('Error in acknowledgeAlert:', error);
      toast.error('Failed to acknowledge alert');
    }
  },

  // Resolve alert
  resolveAlert: async (alertId: string, outcome?: string) => {
    try {
      const updateData: any = {
        status: 'resolved',
        resolved_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      if (outcome) {
        updateData.actual_outcome = outcome;
        updateData.outcome_recorded = true;
      }
      
      const { data, error } = await supabase
        .from('predictive_alerts')
        .update(updateData)
        .eq('id', alertId)
        .select()
        .single();

      if (error) {
        console.error('Error resolving alert:', error);
        toast.error('Failed to resolve alert');
        return;
      }

      // Update local state
      const currentAlerts = get().alerts;
      const updatedAlerts = currentAlerts.map(alert => 
        alert.id === alertId ? data : alert
      );
      set({ alerts: updatedAlerts });
      
      logApiCall('predictive_alerts', 'resolve', { alertId, outcome });
      toast.success('Alert resolved');
      
    } catch (error) {
      console.error('Error in resolveAlert:', error);
      toast.error('Failed to resolve alert');
    }
  },

  // Dismiss alert
  dismissAlert: async (alertId: string) => {
    try {
      const { data, error } = await supabase
        .from('predictive_alerts')
        .update({
          status: 'dismissed',
          updated_at: new Date().toISOString()
        })
        .eq('id', alertId)
        .select()
        .single();

      if (error) {
        console.error('Error dismissing alert:', error);
        toast.error('Failed to dismiss alert');
        return;
      }

      // Update local state
      const currentAlerts = get().alerts;
      const updatedAlerts = currentAlerts.map(alert => 
        alert.id === alertId ? data : alert
      );
      set({ alerts: updatedAlerts });
      
      logApiCall('predictive_alerts', 'dismiss', { alertId });
      toast.success('Alert dismissed');
      
    } catch (error) {
      console.error('Error in dismissAlert:', error);
      toast.error('Failed to dismiss alert');
    }
  },

  // Clear all predictive data
  clearPredictiveData: () => {
    set({
      predictions: [],
      alerts: [],
      isLoadingPredictions: false,
      isLoadingAlerts: false
    });
  }
}));

// Helper functions for predictive insights
export const getPredictionConfidenceColor = (confidence: number | undefined): string => {
  if (!confidence) return '#6B7280';
  
  if (confidence >= 0.8) return '#10B981';
  if (confidence >= 0.6) return '#F59E0B';
  return '#EF4444';
};

export const getRiskLevelColor = (risk: number | undefined): string => {
  if (!risk) return '#6B7280';
  
  if (risk < 0.1) return '#10B981';
  if (risk < 0.3) return '#F59E0B';
  if (risk < 0.6) return '#EF4444';
  return '#DC2626';
};

export const getAlertSeverityColor = (severity: string): string => {
  switch (severity) {
    case 'info': return '#3B82F6';
    case 'warning': return '#F59E0B';
    case 'critical': return '#EF4444';
    case 'emergency': return '#DC2626';
    default: return '#6B7280';
  }
};

export const formatPredictionHorizon = (days: number): string => {
  if (days === 1) return '1 day';
  if (days === 7) return '1 week';
  if (days === 30) return '1 month';
  if (days === 90) return '3 months';
  if (days === 365) return '1 year';
  return `${days} days`;
};

export const formatRiskPercentage = (risk: number | undefined): string => {
  if (!risk) return 'N/A';
  return `${(risk * 100).toFixed(1)}%`;
};

export const formatConfidenceLevel = (confidence: number | undefined): string => {
  if (!confidence) return 'Unknown';
  
  if (confidence >= 0.9) return 'Very High';
  if (confidence >= 0.8) return 'High';
  if (confidence >= 0.6) return 'Medium';
  if (confidence >= 0.4) return 'Low';
  return 'Very Low';
};

export const getHealthScoreTrend = (currentScore: number | undefined, predictedScore: number | undefined): { trend: string; color: string } => {
  if (!currentScore || !predictedScore) {
    return { trend: 'stable', color: '#6B7280' };
  }
  
  const difference = predictedScore - currentScore;
  
  if (difference > 5) return { trend: 'improving', color: '#10B981' };
  if (difference < -5) return { trend: 'declining', color: '#EF4444' };
  return { trend: 'stable', color: '#F59E0B' };
};

export const getActivityLevelColor = (level: string | undefined): string => {
  switch (level) {
    case 'very_low': return '#DC2626';
    case 'low': return '#EF4444';
    case 'moderate': return '#F59E0B';
    case 'high': return '#10B981';
    case 'very_high': return '#059669';
    default: return '#6B7280';
  }
};

export const getStressLevelColor = (level: string | undefined): string => {
  switch (level) {
    case 'very_low': return '#10B981';
    case 'low': return '#059669';
    case 'moderate': return '#F59E0B';
    case 'high': return '#EF4444';
    case 'very_high': return '#DC2626';
    default: return '#6B7280';
  }
};

export const groupPredictionsByHorizon = (predictions: PredictiveHealthInsight[]) => {
  return predictions.reduce((groups, prediction) => {
    const horizon = prediction.prediction_horizon_days;
    if (!groups[horizon]) {
      groups[horizon] = [];
    }
    groups[horizon].push(prediction);
    return groups;
  }, {} as { [key: number]: PredictiveHealthInsight[] });
};

export const getActiveAlerts = (alerts: PredictiveAlert[]) => {
  return alerts.filter(alert => alert.status === 'active');
};

export const getHighPriorityAlerts = (alerts: PredictiveAlert[]) => {
  return alerts.filter(alert => 
    alert.status === 'active' && 
    (alert.severity_level === 'critical' || alert.severity_level === 'emergency')
  );
};