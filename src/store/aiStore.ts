import { create } from 'zustand';
import { supabase } from '../supabase/client';
import { toast } from 'sonner-native';

export interface HealthAssessment {
  assessment_id: string;
  animal_id: string;
  user_id: string;
  assessment_text: string;
  severity_level: string;
  generated_at: string;
}

export interface TrainingPlan {
  plan_id: string;
  animal_id: string;
  user_id: string;
  plan_text: string;
  plan_type: string;
  plan_data: any;
  generated_at: string;
}

export interface ReadinessScore {
  score_id: string;
  animal_id: string;
  user_id: string;
  score_value: number;
  score_category: string;
  generated_at: string;
}

export interface CoachingTip {
  tip_id: string;
  animal_id: string;
  user_id: string;
  tip_text: string;
  category: string;
  priority: string;
  is_read: boolean;
  generated_at: string;
}

interface RealtimeAnalysis {
  analysis: string;
  timestamp: string;
  dataPoints: {
    speed: number | null;
    location: string | null;
    heartRate: number | null;
    temperature: number | null;
    speedHistory: Array<{ timestamp: string; speed: number }>;
  };
}

interface DehydrationAnalysis {
  id?: string;
  animal_id: string;
  overall_status: 'excellent' | 'good' | 'concerning' | 'critical';
  trend: 'improving' | 'stable' | 'declining';
  average_hydration: number;
  hydration_variance: number;
  risk_factors: string[];
  recommendations: string[];
  alerts: {
    type: 'dehydration_risk' | 'trend_warning' | 'data_quality';
    severity: 'low' | 'medium' | 'high';
    message: string;
  }[];
  insights: {
    category: 'hydration_patterns' | 'environmental_factors' | 'health_indicators';
    finding: string;
    confidence: number;
  }[];
  next_actions: string[];
  generated_at: string;
}

interface AIState {
  // Data
  healthAssessments: HealthAssessment[];
  trainingPlans: TrainingPlan[];
  readinessScores: ReadinessScore[];
  coachingTips: CoachingTip[];
  realtimeAnalyses: RealtimeAnalysis[];
  dehydrationAnalyses: DehydrationAnalysis[];
  
  // Loading states
  isLoadingAnalysis: boolean;
  isLoadingHealthAssessments: boolean;
  isLoadingTrainingPlans: boolean;
  isLoadingReadinessScores: boolean;
  isLoadingCoachingTips: boolean;
  isLoadingRealtimeAnalysis: boolean;
  isLoadingDehydrationAnalysis: boolean;
  
  // Actions
  requestAIAnalysis: (animalId: string) => Promise<void>;
  requestRealtimeAnalysis: (animalId: string) => Promise<void>;
  requestDehydrationAnalysis: (animalId: string, days?: number) => Promise<void>;
  fetchHealthAssessments: (animalId?: string) => Promise<void>;
  fetchTrainingPlans: (animalId?: string) => Promise<void>;
  fetchReadinessScores: (animalId?: string) => Promise<void>;
  fetchCoachingTips: (animalId?: string) => Promise<void>;
  markTipAsRead: (tipId: string) => Promise<void>;
  
  // Getters
  getLatestHealthAssessment: (animalId: string) => HealthAssessment | null;
  getLatestTrainingPlan: (animalId: string) => TrainingPlan | null;
  getLatestReadinessScore: (animalId: string) => ReadinessScore | null;
  getLatestRealtimeAnalysis: (animalId: string) => RealtimeAnalysis | null;
  getLatestDehydrationAnalysis: (animalId: string) => DehydrationAnalysis | null;
  getUnreadTips: (animalId?: string) => CoachingTip[];
}

export const useAIStore = create<AIState>((set, get) => ({
  // Initial state
  healthAssessments: [],
  trainingPlans: [],
  readinessScores: [],
  coachingTips: [],
  realtimeAnalyses: [],
  dehydrationAnalyses: [],
  isLoadingAnalysis: false,
  isLoadingHealthAssessments: false,
  isLoadingTrainingPlans: false,
  isLoadingReadinessScores: false,
  isLoadingCoachingTips: false,
  isLoadingRealtimeAnalysis: false,
  isLoadingDehydrationAnalysis: false,

  // Request AI Analysis
  requestAIAnalysis: async (animalId: string) => {
    set({ isLoadingAnalysis: true });
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      const response = await fetch(
        `${supabase.supabaseUrl}/functions/v1/get-assistant-trainer-analysis`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ animalId }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get AI analysis');
      }

      const result = await response.json();
      console.log('AI Analysis completed:', result);
      
      toast.success('AI analysis completed successfully!');
      
      // Refresh all AI data for this animal
      await Promise.all([
        get().fetchHealthAssessments(animalId),
        get().fetchTrainingPlans(animalId),
        get().fetchReadinessScores(animalId),
        get().fetchCoachingTips(animalId),
        get().requestDehydrationAnalysis(animalId),
      ]);
      
    } catch (error) {
      console.error('AI Analysis error:', error);
      toast.error(`Failed to get AI analysis: ${error.message}`);
      throw error;
    } finally {
      set({ isLoadingAnalysis: false });
    }
  },

  // Request Real-time Analysis
  requestRealtimeAnalysis: async (animalId: string) => {
    set({ isLoadingRealtimeAnalysis: true });
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      const response = await fetch(
        `${supabase.supabaseUrl}/functions/v1/get-realtime-animal-analysis`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ animalId }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get real-time analysis');
      }

      const result = await response.json();
      console.log('Real-time Analysis completed:', result);
      
      if (result.success && result.data) {
        // Add the new analysis to the array
        set(state => ({
          realtimeAnalyses: [result.data, ...state.realtimeAnalyses.slice(0, 9)] // Keep last 10
        }));
        
        toast.success('Live check-in completed!');
        
        // Refresh health assessments to include the new analysis
        await get().fetchHealthAssessments(animalId);
      }
      
    } catch (error) {
      console.error('Real-time Analysis error:', error);
      toast.error(`Failed to get live check-in: ${error.message}`);
      throw error;
    } finally {
      set({ isLoadingRealtimeAnalysis: false });
    }
  },

  // Request Dehydration Analysis
  requestDehydrationAnalysis: async (animalId: string, days: number = 7) => {
    set({ isLoadingDehydrationAnalysis: true });
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }

      const response = await fetch(
        `${supabase.supabaseUrl}/functions/v1/get-dehydration-analysis`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ animal_id: animalId, days }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get dehydration analysis');
      }

      const analysis = await response.json();
      console.log('Dehydration Analysis completed:', analysis);
      
      // Add the new analysis to the array
      const newAnalysis: DehydrationAnalysis = {
        ...analysis,
        animal_id: animalId,
        generated_at: new Date().toISOString()
      };
      
      set(state => ({
        dehydrationAnalyses: [newAnalysis, ...state.dehydrationAnalyses.filter(a => a.animal_id !== animalId).slice(0, 9)]
      }));
      
      toast.success('Dehydration analysis completed!');
      
    } catch (error) {
      console.error('Dehydration Analysis error:', error);
      toast.error(`Failed to get dehydration analysis: ${error.message}`);
      throw error;
    } finally {
      set({ isLoadingDehydrationAnalysis: false });
    }
  },

  // Fetch Health Assessments
  fetchHealthAssessments: async (animalId?: string) => {
    set({ isLoadingHealthAssessments: true });
    
    try {
      let query = supabase
        .from('ai_health_assessments')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      set({ healthAssessments: data || [] });
      console.log(`Fetched ${data?.length || 0} health assessments`);
      
    } catch (error) {
      console.error('Error fetching health assessments:', error);
      toast.error('Failed to fetch health assessments');
    } finally {
      set({ isLoadingHealthAssessments: false });
    }
  },

  // Fetch Training Plans
  fetchTrainingPlans: async (animalId?: string) => {
    set({ isLoadingTrainingPlans: true });
    
    try {
      let query = supabase
        .from('ai_training_plans')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      set({ trainingPlans: data || [] });
      console.log(`Fetched ${data?.length || 0} training plans`);
      
    } catch (error) {
      console.error('Error fetching training plans:', error);
      toast.error('Failed to fetch training plans');
    } finally {
      set({ isLoadingTrainingPlans: false });
    }
  },

  // Fetch Readiness Scores
  fetchReadinessScores: async (animalId?: string) => {
    set({ isLoadingReadinessScores: true });
    
    try {
      let query = supabase
        .from('ai_readiness_scores')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      set({ readinessScores: data || [] });
      console.log(`Fetched ${data?.length || 0} readiness scores`);
      
    } catch (error) {
      console.error('Error fetching readiness scores:', error);
      toast.error('Failed to fetch readiness scores');
    } finally {
      set({ isLoadingReadinessScores: false });
    }
  },

  // Fetch Coaching Tips
  fetchCoachingTips: async (animalId?: string) => {
    set({ isLoadingCoachingTips: true });
    
    try {
      let query = supabase
        .from('ai_coaching_tips')
        .select('*')
        .order('generated_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      set({ coachingTips: data || [] });
      console.log(`Fetched ${data?.length || 0} coaching tips`);
      
    } catch (error) {
      console.error('Error fetching coaching tips:', error);
      toast.error('Failed to fetch coaching tips');
    } finally {
      set({ isLoadingCoachingTips: false });
    }
  },

  // Mark Tip as Read
  markTipAsRead: async (tipId: string) => {
    try {
      const { error } = await supabase
        .from('ai_coaching_tips')
        .update({ is_read: true })
        .eq('tip_id', tipId);
      
      if (error) throw error;
      
      // Update local state
      set(state => ({
        coachingTips: state.coachingTips.map(tip => 
          tip.tip_id === tipId ? { ...tip, is_read: true } : tip
        )
      }));
      
    } catch (error) {
      console.error('Error marking tip as read:', error);
      toast.error('Failed to mark tip as read');
    }
  },

  // Getters
  getLatestHealthAssessment: (animalId: string) => {
    const assessments = get().healthAssessments.filter(a => a.animal_id === animalId);
    return assessments.length > 0 ? assessments[0] : null;
  },

  getLatestTrainingPlan: (animalId: string) => {
    const plans = get().trainingPlans.filter(p => p.animal_id === animalId);
    return plans.length > 0 ? plans[0] : null;
  },

  getLatestReadinessScore: (animalId: string) => {
    const scores = get().readinessScores.filter(s => s.animal_id === animalId);
    return scores.length > 0 ? scores[0] : null;
  },

  getLatestRealtimeAnalysis: (animalId: string) => {
    const analyses = get().realtimeAnalyses;
    return analyses.length > 0 ? analyses[0] : null;
  },

  getLatestDehydrationAnalysis: (animalId: string) => {
    const analyses = get().dehydrationAnalyses.filter(a => a.animal_id === animalId);
    return analyses.length > 0 ? analyses[0] : null;
  },

  getUnreadTips: (animalId?: string) => {
    let tips = get().coachingTips.filter(tip => !tip.is_read);
    if (animalId) {
      tips = tips.filter(tip => tip.animal_id === animalId);
    }
    return tips;
  },
}));