
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Device } from '../mocks/devices';
import { useAnimalStore } from './animalStore';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

interface DeviceState {
  devices: Device[];
  pairedDevices: Device[];
  availableDevices: Device[];
  isScanning: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions (updated for Supabase integration)
  fetchDevices: () => Promise<void>;
  getDevices: () => Device[];
  getDeviceById: (id: string) => Device | undefined;
  addDevice: (device: Omit<Device, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => Promise<Device | null>;
  updateDevice: (id: string, updates: Partial<Omit<Device, 'id' | 'user_id' | 'created_at' | 'updated_at'>>) => Promise<Device | null>;
  deleteDevice: (id: string) => Promise<boolean>;
  pairDevice: (deviceId: string, animalId: string) => Promise<void>;
  unpairDevice: (deviceId: string) => Promise<void>;
  startScan: () => void;
  stopScan: () => void;
  updateDeviceStatus: (id: string, status: Device['status']) => Promise<void>;
  updateBatteryLevel: (id: string, batteryLevel: number) => Promise<void>;
}

export const useDeviceStore = create<DeviceState>()(
  persist(
    (set, get) => ({
      devices: [], // Initialize empty, will be populated from Supabase
      pairedDevices: [],
      availableDevices: [],
      isScanning: false,
      isLoading: false,
      error: null,
      
      // Fetch devices from Supabase
      fetchDevices: async () => {
        try {
          set({ isLoading: true, error: null });
          
          // Get current user for defense-in-depth filtering
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            console.warn('fetchDevices: User not authenticated');
            set({ 
              devices: [], 
              pairedDevices: [], 
              isLoading: false, 
              error: 'User not authenticated' 
            });
            return;
          }
          
          const { data, error } = await supabase
            .from('devices')
            .select('*')
            .eq('user_id', user.id) // Defense-in-depth: explicit user filtering
            .order('created_at', { ascending: false });
          
          logApiCall('fetchDevices', data, error);
          
          if (error) {
            console.error('Supabase error in fetchDevices:', error);
            // Ensure we always set valid arrays even on error
            set({ 
              devices: [], 
              pairedDevices: [], 
              isLoading: false, 
              error: `Database error: ${error.message}` 
            });
            // Don't show toast for silent background operations
            return;
          }
          
          // Defensive data validation - ensure data is always an array
          const validDevices = Array.isArray(data) ? data : [];
          
          // Safely derive paired devices with comprehensive validation
          const pairedDevices = validDevices.filter(device => {
            return device && 
                   typeof device === 'object' && 
                   device.id && 
                   device.name && 
                   device.status && 
                   (device.status === 'connected' || 
                    device.status === 'paired' || 
                    device.status === 'low_battery');
          });
          
          set({ 
            devices: validDevices, 
            pairedDevices,
            isLoading: false, 
            error: null 
          });
          
          console.log(`fetchDevices success: ${validDevices.length} devices, ${pairedDevices.length} paired`);
          
        } catch (error: any) {
          console.error('Unexpected error in fetchDevices:', error);
          // Always ensure valid state even on unexpected errors
          set({ 
            devices: [], 
            pairedDevices: [], 
            isLoading: false, 
            error: error.message || 'Failed to fetch devices' 
          });
          // Don't re-throw to prevent app crashes
        }
      },
      
      getDevices: () => get().devices,
      
      getDeviceById: (id) => {
        return get().devices.find(device => device.id === id);
      },
      
      // Add device to Supabase
      addDevice: async (deviceData) => {
        try {
          set({ isLoading: true, error: null });
          
          // Get current user
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            toast.error('User not found. Please log in.');
            set({ isLoading: false, error: 'User not found' });
            return null;
          }
          
          // Store the original scan ID for removing from availableDevices
          const originalScanId = deviceData.id;
          
          // Remove the temporary scan ID before sending to Supabase
          const { id, ...deviceDataWithoutId } = deviceData;
          const newDevice = {
            ...deviceDataWithoutId,
            user_id: user.id,
          };
          
          logApiCall('addDevice', 'devices', 'INSERT', newDevice);
          const { data, error } = await supabase
            .from('devices')
            .insert(newDevice)
            .select()
            .single();
          
          if (error) {
            console.error('Error adding device:', error);
            set({ isLoading: false, error: error.message });
            toast.error(`Failed to add device: ${error.message}`);
            throw error;
          }
          
          // Update local state and remove from availableDevices using original scan ID
          set(state => ({
            devices: [data, ...state.devices],
            availableDevices: state.availableDevices.filter(d => d.id !== originalScanId),
            pairedDevices: data.status === 'connected' || data.status === 'paired' || data.status === 'low_battery'
              ? [data, ...state.pairedDevices]
              : state.pairedDevices,
            isLoading: false,
            error: null
          }));
          
          toast.success(`Device "${data.name}" added successfully!`);
          return data;
          
        } catch (error: any) {
          console.error('Error in addDevice:', error);
          set({ isLoading: false, error: error.message || 'Failed to add device' });
          return null;
        }
      },
      
      // Update device in Supabase
      updateDevice: async (id, updates) => {
        try {
          set({ isLoading: true, error: null });
          
          // Get current user for defense-in-depth filtering
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ isLoading: false, error: 'User not authenticated' });
            return null;
          }
          
          logApiCall('updateDevice', 'devices', 'UPDATE', { id, ...updates });
          const { data, error } = await supabase
            .from('devices')
            .update(updates)
            .eq('id', id)
            .eq('user_id', user.id) // Defense-in-depth: ensure user owns device
            .select()
            .single();
          
          if (error) {
            console.error('Error updating device:', error);
            set({ isLoading: false, error: error.message });
            toast.error(`Failed to update device: ${error.message}`);
            throw error;
          }
          
          // Update local state
          set(state => ({
            devices: state.devices.map(device => 
              device.id === id ? data : device
            ),
            pairedDevices: state.pairedDevices.map(device => 
              device.id === id ? data : device
            ),
            isLoading: false,
            error: null
          }));
          
          toast.success(`Device "${data.name}" updated successfully!`);
          return data;
          
        } catch (error: any) {
          console.error('Error in updateDevice:', error);
          set({ isLoading: false, error: error.message || 'Failed to update device' });
          return null;
        }
      },
      
      // Delete device from Supabase
      deleteDevice: async (id) => {
        try {
          set({ isLoading: true, error: null });
          
          // Get current user for defense-in-depth filtering
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            set({ isLoading: false, error: 'User not authenticated' });
            return false;
          }
          
          const device = get().devices.find(d => d.id === id);
          if (!device) {
            toast.error('Device not found');
            set({ isLoading: false, error: 'Device not found' });
            return false;
          }
          
          logApiCall('deleteDevice', 'devices', 'DELETE', { id });
          const { error } = await supabase
            .from('devices')
            .delete()
            .eq('id', id)
            .eq('user_id', user.id); // Defense-in-depth: ensure user owns device
          
          if (error) {
            console.error('Error deleting device:', error);
            set({ isLoading: false, error: error.message });
            toast.error(`Failed to delete device: ${error.message}`);
            throw error;
          }
          
          // Update local state
          set(state => ({
            devices: state.devices.filter(device => device.id !== id),
            pairedDevices: state.pairedDevices.filter(device => device.id !== id),
            isLoading: false,
            error: null
          }));
          
          toast.success(`Device "${device.name}" deleted successfully!`);
          return true;
          
        } catch (error: any) {
          console.error('Error in deleteDevice:', error);
          set({ isLoading: false, error: error.message || 'Failed to delete device' });
          return false;
        }
      },
      
      pairDevice: async (deviceId, animalId) => {
        set({ isLoading: true, error: null });
        
        try {
          const device = get().devices.find(d => d.id === deviceId);
          if (!device) {
            throw new Error('Device not found');
          }

          // Optimistic UI update
          const updatedDevice = { ...device, status: 'paired' as const, last_sync_time: new Date().toISOString() };
          set(state => ({
            devices: state.devices.map(d => 
              d.id === deviceId ? updatedDevice : d
            ),
            pairedDevices: [
              ...state.pairedDevices.filter(d => d.id !== deviceId),
              updatedDevice
            ],
          }));
          
          // Update device status in Supabase
          const deviceUpdateResult = await get().updateDevice(deviceId, {
            status: 'paired',
            last_sync_time: new Date().toISOString()
          });
          
          if (!deviceUpdateResult) {
            throw new Error('Failed to update device status');
          }
          
          // Update animal's device info via animalStore
          await useAnimalStore.getState().updateAnimal(animalId, {
            deviceId: deviceId,
            deviceName: device.name,
            deviceStatus: 'paired',
            lastSyncTime: Date.now(),
          });

          set({ isLoading: false });
          toast.success(`${device.name} paired successfully with animal!`);
        } catch (error: any) {
          console.error('Error pairing device:', error);
          const errorMessage = error.message || 'Failed to pair device';
          
          // Revert optimistic update on error
          const device = get().devices.find(d => d.id === deviceId);
          if (device) {
            set(state => ({
              devices: state.devices.map(d => 
                d.id === deviceId 
                  ? { ...d, status: 'disconnected' } 
                  : d
              ),
              pairedDevices: state.pairedDevices.filter(d => d.id !== deviceId),
              error: errorMessage,
              isLoading: false
            }));
          } else {
            set({ error: errorMessage, isLoading: false });
          }
          
          toast.error(`Pairing failed: ${errorMessage}`);
          throw error;
        }
      },
      
      unpairDevice: async (deviceId) => {
        set({ isLoading: true, error: null });
        
        try {
          const device = get().devices.find(d => d.id === deviceId);
          if (!device) {
            throw new Error('Device not found');
          }

          // Find the animal this device is paired with
          const animalStore = useAnimalStore.getState();
          const pairedAnimal = animalStore.animals.find(animal => animal.deviceId === deviceId);
          
          // Optimistic UI update
          const updatedDevice = { ...device, status: 'disconnected' as const, last_sync_time: new Date().toISOString() };
          set(state => ({
            devices: state.devices.map(d => 
              d.id === deviceId ? updatedDevice : d
            ),
            pairedDevices: state.pairedDevices.filter(d => d.id !== deviceId),
          }));

          // Update device status in Supabase
          const deviceUpdateResult = await get().updateDevice(deviceId, {
            status: 'disconnected',
            last_sync_time: new Date().toISOString()
          });
          
          if (!deviceUpdateResult) {
            throw new Error('Failed to update device status');
          }

          // Update the animal to remove device association
          if (pairedAnimal) {
            await useAnimalStore.getState().updateAnimal(pairedAnimal.id, {
              deviceId: null,
              deviceName: null,
              deviceStatus: 'disconnected',
              lastSyncTime: null,
            });
          }

          set({ isLoading: false });
          toast.success(`${device.name} unpaired successfully!`);
        } catch (error: any) {
          console.error('Error unpairing device:', error);
          const errorMessage = error.message || 'Failed to unpair device';
          
          // Revert optimistic update on error
          const device = get().devices.find(d => d.id === deviceId);
          if (device) {
            set(state => ({
              devices: state.devices.map(d => 
                d.id === deviceId 
                  ? { ...d, status: 'paired' } 
                  : d
              ),
              pairedDevices: [
                ...state.pairedDevices.filter(d => d.id !== deviceId),
                { ...device, status: 'paired' }
              ],
              error: errorMessage,
              isLoading: false
            }));
          } else {
            set({ error: errorMessage, isLoading: false });
          }
          
          toast.error(`Unpairing failed: ${errorMessage}`);
          throw error;
        }
      },
      
      startScan: () => {
        set({ isScanning: true, isLoading: true, error: null });
        
        // Simulate finding devices (in real implementation, this would use Bluetooth scanning)
        setTimeout(() => {
          try {
            // Simulate discovering new devices that aren't in the user's registered devices list
            const currentDeviceIds = get().devices.map(d => d.id);
            const simulatedDevices = [
              {
                id: 'scan_device_1',
                name: 'HoofMonitor Ultra',
                type: 'Multi-sensor',
                battery_level: 100,
                status: 'disconnected' as const,
              },
              {
                id: 'scan_device_2',
                name: 'EquiTrack Pro',
                type: 'GPS Tracker',
                battery_level: 95,
                status: 'disconnected' as const,
              },
              {
                id: 'scan_device_3',
                name: 'SmartCollar V2',
                type: 'Collar',
                battery_level: 78,
                status: 'disconnected' as const,
              },
            ];
            
            // Only show devices that aren't already registered
            const availableDevices = simulatedDevices.filter(d => !currentDeviceIds.includes(d.id));
            
            set({ 
              isScanning: false,
              isLoading: false,
              availableDevices
            });
            
            toast.success(`Scan complete. Found ${availableDevices.length} available devices.`);
          } catch (error: any) {
            console.error('Error during device scan:', error);
            const errorMessage = error.message || 'Device scan failed';
            set({ 
              isScanning: false,
              isLoading: false,
              error: errorMessage,
              availableDevices: []
            });
            toast.error(`Scan failed: ${errorMessage}`);
          }
        }, 3000);
      },
      
      stopScan: () => {
        set({ isScanning: false });
      },
      
      // Update device status in Supabase
      updateDeviceStatus: async (id, status) => {
        try {
          const device = get().devices.find(d => d.id === id);
          if (!device) {
            console.error('Device not found for status update:', id);
            toast.error('Device not found');
            return;
          }
          
          // Use the existing updateDevice method to persist to Supabase
          const result = await get().updateDevice(id, {
            status,
            last_sync_time: new Date().toISOString()
          });
          
          if (!result) {
            throw new Error('Failed to update device status');
          }
          
          // Update pairedDevices list based on new status
          set(state => ({
            pairedDevices: status === 'connected' || status === 'paired' || status === 'low_battery'
              ? [...state.pairedDevices.filter(d => d.id !== id), result]
              : state.pairedDevices.filter(d => d.id !== id),
          }));
          
        } catch (error: any) {
          console.error('Error updating device status:', error);
          // Error toast is already handled by updateDevice
        }
      },
      
      // Update device battery level in Supabase
      updateBatteryLevel: async (id, batteryLevel) => {
        try {
          const device = get().devices.find(d => d.id === id);
          if (!device) {
            console.error('Device not found for battery update:', id);
            return;
          }
          
          // Use the existing updateDevice method to persist to Supabase
          const result = await get().updateDevice(id, {
            battery_level: batteryLevel,
            last_sync_time: new Date().toISOString()
          });
          
          if (!result) {
            throw new Error('Failed to update battery level');
          }
          
          // Update pairedDevices if this device is in the list
          set(state => ({
            pairedDevices: state.pairedDevices.map(d => 
              d.id === id ? result : d
            ),
          }));
          
        } catch (error: any) {
          console.error('Error updating battery level:', error);
          // Error toast is already handled by updateDevice
        }
      },
    }),
    {
      name: 'hoofbeat-device-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
