import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

export interface AIImageAnalysis {
  id: string;
  animal_id: string;
  user_id: string;
  image_url: string;
  image_type: string;
  analysis_status: string;
  confidence_score?: number;
  body_condition_score?: number;
  health_indicators?: any;
  behavioral_markers?: any;
  symptom_detection?: any;
  facial_features?: any;
  emotion_analysis?: any;
  identity_confidence?: number;
  image_quality_score?: number;
  model_version?: string;
  processing_time_ms?: number;
  lighting_conditions?: string;
  image_clarity?: string;
  created_at: string;
  updated_at: string;
}

export interface AIChatConversation {
  id: string;
  user_id: string;
  animal_id?: string;
  conversation_title?: string;
  conversation_type: string;
  conversation_status: string;
  ai_model_version?: string;
  conversation_context?: any;
  total_messages: number;
  last_message_at?: string;
  conversation_summary?: string;
  health_concerns?: any;
  mentioned_symptoms?: any;
  recommended_actions?: any;
  urgency_level?: string;
  created_at: string;
  updated_at: string;
}

export interface AIChatMessage {
  id: string;
  conversation_id: string;
  message_type: string;
  message_content: string;
  message_metadata?: any;
  intent_analysis?: any;
  sentiment_score?: number;
  confidence_score?: number;
  extracted_symptoms?: any;
  extracted_concerns?: any;
  medical_entities?: any;
  response_time_ms?: number;
  model_version?: string;
  created_at: string;
}

export interface MLModel {
  id: string;
  model_name: string;
  model_type: string;
  model_version: string;
  model_description?: string;
  model_url?: string;
  model_config?: any;
  model_metadata?: any;
  accuracy_score?: number;
  precision_score?: number;
  recall_score?: number;
  f1_score?: number;
  training_data_size?: number;
  training_duration_minutes?: number;
  training_completed_at?: string;
  deployment_status: string;
  deployed_at?: string;
  prediction_count: number;
  last_used_at?: string;
  created_at: string;
  updated_at: string;
}

export interface MLPrediction {
  id: string;
  model_id: string;
  animal_id?: string;
  user_id?: string;
  prediction_type: string;
  input_features: any;
  prediction_result: any;
  confidence_score?: number;
  processing_time_ms?: number;
  model_version?: string;
  actual_outcome?: any;
  prediction_accuracy?: number;
  feedback_provided: boolean;
  prediction_context?: any;
  business_impact?: string;
  created_at: string;
}

export interface AutomatedHealthAssessment {
  id: string;
  animal_id: string;
  assessment_type: string;
  assessment_trigger?: string;
  overall_health_score?: number;
  health_status?: string;
  risk_level?: string;
  vital_signs_assessment?: any;
  behavioral_assessment?: any;
  physical_condition_assessment?: any;
  environmental_assessment?: any;
  immediate_actions?: any;
  care_recommendations?: any;
  veterinary_consultation_needed: boolean;
  urgency_level?: string;
  assessment_models?: any;
  confidence_scores?: any;
  follow_up_required: boolean;
  follow_up_date?: string;
  follow_up_notes?: string;
  created_at: string;
  updated_at: string;
}

export interface AIInsight {
  id: string;
  animal_id: string;
  user_id: string;
  insight_type: string;
  insight_category?: string;
  insight_title: string;
  insight_description: string;
  confidence_level?: number;
  importance_score?: number;
  evidence_strength?: string;
  supporting_data?: any;
  data_sources?: any;
  analysis_period_start?: string;
  analysis_period_end?: string;
  recommended_actions?: any;
  expected_outcomes?: any;
  implementation_difficulty?: string;
  viewed_by_user: boolean;
  user_feedback?: string;
  implementation_status: string;
  insight_accuracy?: number;
  outcome_validation?: any;
  created_at: string;
  updated_at: string;
}

export interface GeneticAnalysis {
  id: string;
  animal_id: string;
  genetic_profile?: any;
  breed_composition?: any;
  genetic_markers?: any;
  disease_predispositions?: any;
  trait_predictions?: any;
  behavioral_tendencies?: any;
  genetic_health_risks?: any;
  carrier_status?: any;
  breeding_recommendations?: any;
  analysis_provider?: string;
  analysis_date?: string;
  analysis_version?: string;
  confidence_scores?: any;
  created_at: string;
  updated_at: string;
}

interface AIMLStore {
  // Image Analysis
  imageAnalyses: AIImageAnalysis[];
  isLoadingImageAnalysis: boolean;
  
  // AI Chat
  conversations: AIChatConversation[];
  currentConversation: AIChatConversation | null;
  messages: AIChatMessage[];
  isLoadingChat: boolean;
  
  // ML Models
  mlModels: MLModel[];
  predictions: MLPrediction[];
  isLoadingModels: boolean;
  
  // Health Assessments
  healthAssessments: AutomatedHealthAssessment[];
  isLoadingAssessments: boolean;
  
  // AI Insights
  insights: AIInsight[];
  isLoadingInsights: boolean;
  
  // Genetic Analysis
  geneticAnalyses: GeneticAnalysis[];
  isLoadingGenetics: boolean;
  
  // Actions - Image Analysis
  analyzeImage: (animalId: string, imageUrl: string, imageType: string, analysisTypes: string[]) => Promise<AIImageAnalysis | null>;
  fetchImageAnalyses: (animalId?: string) => Promise<void>;
  
  // Actions - AI Chat
  createConversation: (animalId?: string, conversationType?: string) => Promise<AIChatConversation | null>;
  sendMessage: (conversationId: string, message: string, animalId?: string) => Promise<AIChatMessage | null>;
  fetchConversations: () => Promise<void>;
  fetchMessages: (conversationId: string) => Promise<void>;
  setCurrentConversation: (conversation: AIChatConversation | null) => void;
  
  // Actions - ML Models
  fetchMLModels: (modelType?: string) => Promise<void>;
  makePrediction: (modelId: string, animalId: string, inputFeatures: any, predictionType: string) => Promise<MLPrediction | null>;
  fetchPredictions: (animalId?: string) => Promise<void>;
  providePredictionFeedback: (predictionId: string, actualOutcome: any, accuracy: number) => Promise<void>;
  
  // Actions - Health Assessments
  requestHealthAssessment: (animalId: string, assessmentType: string, trigger?: string) => Promise<AutomatedHealthAssessment | null>;
  fetchHealthAssessments: (animalId?: string) => Promise<void>;
  updateAssessmentFollowUp: (assessmentId: string, followUpDate: string, notes: string) => Promise<void>;
  
  // Actions - AI Insights
  fetchInsights: (animalId?: string) => Promise<void>;
  markInsightAsViewed: (insightId: string) => Promise<void>;
  provideInsightFeedback: (insightId: string, feedback: string) => Promise<void>;
  updateInsightImplementation: (insightId: string, status: string) => Promise<void>;
  
  // Actions - Genetic Analysis
  fetchGeneticAnalyses: (animalId?: string) => Promise<void>;
  uploadGeneticData: (animalId: string, geneticData: any, provider: string) => Promise<GeneticAnalysis | null>;
  
  // Utility Actions
  clearAIMLData: () => void;
}

export const useAIMLStore = create<AIMLStore>()(persist(
  (set, get) => ({
    // Initial state
    imageAnalyses: [],
    isLoadingImageAnalysis: false,
    conversations: [],
    currentConversation: null,
    messages: [],
    isLoadingChat: false,
    mlModels: [],
    predictions: [],
    isLoadingModels: false,
    healthAssessments: [],
    isLoadingAssessments: false,
    insights: [],
    isLoadingInsights: false,
    geneticAnalyses: [],
    isLoadingGenetics: false,

    // Image Analysis Actions
    analyzeImage: async (animalId: string, imageUrl: string, imageType: string, analysisTypes: string[]) => {
      set({ isLoadingImageAnalysis: true });
      
      try {
        const { data, error } = await supabase.functions.invoke('analyze-image-health', {
          body: {
            animalId,
            imageUrl,
            imageType,
            analysisTypes
          }
        });

        if (error) {
          console.error('Error analyzing image:', error);
          toast.error('Failed to analyze image');
          return null;
        }

        if (!data.success) {
          console.error('Image analysis failed:', data.error);
          toast.error(data.error || 'Image analysis failed');
          return null;
        }

        // Refresh image analyses
        await get().fetchImageAnalyses(animalId);
        
        logApiCall('ai_image_analysis', 'create', {
          animalId,
          imageType,
          analysisTypes
        });
        
        toast.success('Image analysis completed successfully');
        return data.results;
        
      } catch (error) {
        console.error('Error in analyzeImage:', error);
        toast.error('Failed to analyze image');
        return null;
      } finally {
        set({ isLoadingImageAnalysis: false });
      }
    },

    fetchImageAnalyses: async (animalId?: string) => {
      set({ isLoadingImageAnalysis: true });
      
      try {
        let query = supabase
          .from('ai_image_analysis')
          .select('*')
          .order('created_at', { ascending: false });
        
        if (animalId) {
          query = query.eq('animal_id', animalId);
        }
        
        const { data, error } = await query;

        if (error) {
          console.error('Error fetching image analyses:', error);
          toast.error('Failed to load image analyses');
          return;
        }

        set({ imageAnalyses: data || [] });
        
        logApiCall('ai_image_analysis', 'fetch', {
          animalId,
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchImageAnalyses:', error);
        toast.error('Failed to load image analyses');
      } finally {
        set({ isLoadingImageAnalysis: false });
      }
    },

    // AI Chat Actions
    createConversation: async (animalId?: string, conversationType?: string) => {
      try {
        const { data, error } = await supabase
          .from('ai_chat_conversations')
          .insert({
            animal_id: animalId,
            conversation_type: conversationType || 'general',
            conversation_status: 'active'
          })
          .select()
          .single();

        if (error) {
          console.error('Error creating conversation:', error);
          toast.error('Failed to create conversation');
          return null;
        }

        const currentConversations = get().conversations;
        set({ 
          conversations: [data, ...currentConversations],
          currentConversation: data,
          messages: []
        });
        
        logApiCall('ai_chat_conversations', 'create', {
          animalId,
          conversationType
        });
        
        return data;
        
      } catch (error) {
        console.error('Error in createConversation:', error);
        toast.error('Failed to create conversation');
        return null;
      }
    },

    sendMessage: async (conversationId: string, message: string, animalId?: string) => {
      set({ isLoadingChat: true });
      
      try {
        const { data, error } = await supabase.functions.invoke('advanced-ai-chat', {
          body: {
            conversationId,
            message,
            animalId
          }
        });

        if (error) {
          console.error('Error sending message:', error);
          toast.error('Failed to send message');
          return null;
        }

        if (!data.success) {
          console.error('Chat message failed:', data.error);
          toast.error(data.error || 'Failed to send message');
          return null;
        }

        // Refresh messages for this conversation
        await get().fetchMessages(conversationId);
        
        logApiCall('ai_chat_messages', 'create', {
          conversationId,
          messageLength: message.length
        });
        
        return data;
        
      } catch (error) {
        console.error('Error in sendMessage:', error);
        toast.error('Failed to send message');
        return null;
      } finally {
        set({ isLoadingChat: false });
      }
    },

    fetchConversations: async () => {
      set({ isLoadingChat: true });
      
      try {
        const { data, error } = await supabase
          .from('ai_chat_conversations')
          .select('*')
          .order('last_message_at', { ascending: false, nullsFirst: false })
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching conversations:', error);
          toast.error('Failed to load conversations');
          return;
        }

        set({ conversations: data || [] });
        
        logApiCall('ai_chat_conversations', 'fetch', {
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchConversations:', error);
        toast.error('Failed to load conversations');
      } finally {
        set({ isLoadingChat: false });
      }
    },

    fetchMessages: async (conversationId: string) => {
      try {
        const { data, error } = await supabase
          .from('ai_chat_messages')
          .select('*')
          .eq('conversation_id', conversationId)
          .order('created_at', { ascending: true });

        if (error) {
          console.error('Error fetching messages:', error);
          return;
        }

        set({ messages: data || [] });
        
      } catch (error) {
        console.error('Error in fetchMessages:', error);
      }
    },

    setCurrentConversation: (conversation: AIChatConversation | null) => {
      set({ currentConversation: conversation });
      if (conversation) {
        get().fetchMessages(conversation.id);
      } else {
        set({ messages: [] });
      }
    },

    // ML Models Actions
    fetchMLModels: async (modelType?: string) => {
      set({ isLoadingModels: true });
      
      try {
        let query = supabase
          .from('ml_model_registry')
          .select('*')
          .order('created_at', { ascending: false });
        
        if (modelType) {
          query = query.eq('model_type', modelType);
        }
        
        const { data, error } = await query;

        if (error) {
          console.error('Error fetching ML models:', error);
          toast.error('Failed to load ML models');
          return;
        }

        set({ mlModels: data || [] });
        
        logApiCall('ml_model_registry', 'fetch', {
          modelType,
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchMLModels:', error);
        toast.error('Failed to load ML models');
      } finally {
        set({ isLoadingModels: false });
      }
    },

    makePrediction: async (modelId: string, animalId: string, inputFeatures: any, predictionType: string) => {
      try {
        const { data, error } = await supabase
          .from('ml_predictions')
          .insert({
            model_id: modelId,
            animal_id: animalId,
            prediction_type: predictionType,
            input_features: inputFeatures,
            prediction_result: {}, // This would be populated by the ML service
            confidence_score: Math.random() * 0.3 + 0.7, // Mock confidence
            processing_time_ms: Math.floor(Math.random() * 1000) + 100
          })
          .select()
          .single();

        if (error) {
          console.error('Error making prediction:', error);
          toast.error('Failed to make prediction');
          return null;
        }

        const currentPredictions = get().predictions;
        set({ predictions: [data, ...currentPredictions] });
        
        logApiCall('ml_predictions', 'create', {
          modelId,
          animalId,
          predictionType
        });
        
        toast.success('Prediction completed successfully');
        return data;
        
      } catch (error) {
        console.error('Error in makePrediction:', error);
        toast.error('Failed to make prediction');
        return null;
      }
    },

    fetchPredictions: async (animalId?: string) => {
      try {
        let query = supabase
          .from('ml_predictions')
          .select('*')
          .order('created_at', { ascending: false });
        
        if (animalId) {
          query = query.eq('animal_id', animalId);
        }
        
        const { data, error } = await query;

        if (error) {
          console.error('Error fetching predictions:', error);
          return;
        }

        set({ predictions: data || [] });
        
      } catch (error) {
        console.error('Error in fetchPredictions:', error);
      }
    },

    providePredictionFeedback: async (predictionId: string, actualOutcome: any, accuracy: number) => {
      try {
        const { data, error } = await supabase
          .from('ml_predictions')
          .update({
            actual_outcome: actualOutcome,
            prediction_accuracy: accuracy,
            feedback_provided: true
          })
          .eq('id', predictionId)
          .select()
          .single();

        if (error) {
          console.error('Error providing feedback:', error);
          toast.error('Failed to provide feedback');
          return;
        }

        const currentPredictions = get().predictions;
        const updatedPredictions = currentPredictions.map(pred => 
          pred.id === predictionId ? data : pred
        );
        set({ predictions: updatedPredictions });
        
        toast.success('Feedback provided successfully');
        
      } catch (error) {
        console.error('Error in providePredictionFeedback:', error);
        toast.error('Failed to provide feedback');
      }
    },

    // Health Assessments Actions
    requestHealthAssessment: async (animalId: string, assessmentType: string, trigger?: string) => {
      set({ isLoadingAssessments: true });
      
      try {
        // This would call an AI service to perform the assessment
        // For now, we'll create a mock assessment
        const { data, error } = await supabase
          .from('automated_health_assessments')
          .insert({
            animal_id: animalId,
            assessment_type: assessmentType,
            assessment_trigger: trigger,
            overall_health_score: Math.random() * 30 + 70, // 70-100
            health_status: ['excellent', 'good', 'fair'][Math.floor(Math.random() * 3)],
            risk_level: ['low', 'moderate', 'high'][Math.floor(Math.random() * 3)],
            veterinary_consultation_needed: Math.random() < 0.2,
            follow_up_required: Math.random() < 0.3
          })
          .select()
          .single();

        if (error) {
          console.error('Error requesting health assessment:', error);
          toast.error('Failed to request health assessment');
          return null;
        }

        const currentAssessments = get().healthAssessments;
        set({ healthAssessments: [data, ...currentAssessments] });
        
        logApiCall('automated_health_assessments', 'create', {
          animalId,
          assessmentType,
          trigger
        });
        
        toast.success('Health assessment completed');
        return data;
        
      } catch (error) {
        console.error('Error in requestHealthAssessment:', error);
        toast.error('Failed to request health assessment');
        return null;
      } finally {
        set({ isLoadingAssessments: false });
      }
    },

    fetchHealthAssessments: async (animalId?: string) => {
      set({ isLoadingAssessments: true });
      
      try {
        let query = supabase
          .from('automated_health_assessments')
          .select('*')
          .order('created_at', { ascending: false });
        
        if (animalId) {
          query = query.eq('animal_id', animalId);
        }
        
        const { data, error } = await query;

        if (error) {
          console.error('Error fetching health assessments:', error);
          toast.error('Failed to load health assessments');
          return;
        }

        set({ healthAssessments: data || [] });
        
        logApiCall('automated_health_assessments', 'fetch', {
          animalId,
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchHealthAssessments:', error);
        toast.error('Failed to load health assessments');
      } finally {
        set({ isLoadingAssessments: false });
      }
    },

    updateAssessmentFollowUp: async (assessmentId: string, followUpDate: string, notes: string) => {
      try {
        const { data, error } = await supabase
          .from('automated_health_assessments')
          .update({
            follow_up_date: followUpDate,
            follow_up_notes: notes,
            updated_at: new Date().toISOString()
          })
          .eq('id', assessmentId)
          .select()
          .single();

        if (error) {
          console.error('Error updating assessment follow-up:', error);
          toast.error('Failed to update follow-up');
          return;
        }

        const currentAssessments = get().healthAssessments;
        const updatedAssessments = currentAssessments.map(assessment => 
          assessment.id === assessmentId ? data : assessment
        );
        set({ healthAssessments: updatedAssessments });
        
        toast.success('Follow-up updated successfully');
        
      } catch (error) {
        console.error('Error in updateAssessmentFollowUp:', error);
        toast.error('Failed to update follow-up');
      }
    },

    // AI Insights Actions
    fetchInsights: async (animalId?: string) => {
      set({ isLoadingInsights: true });
      
      try {
        let query = supabase
          .from('ai_insights')
          .select('*')
          .order('created_at', { ascending: false });
        
        if (animalId) {
          query = query.eq('animal_id', animalId);
        }
        
        const { data, error } = await query;

        if (error) {
          console.error('Error fetching insights:', error);
          toast.error('Failed to load insights');
          return;
        }

        set({ insights: data || [] });
        
        logApiCall('ai_insights', 'fetch', {
          animalId,
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchInsights:', error);
        toast.error('Failed to load insights');
      } finally {
        set({ isLoadingInsights: false });
      }
    },

    markInsightAsViewed: async (insightId: string) => {
      try {
        const { data, error } = await supabase
          .from('ai_insights')
          .update({ viewed_by_user: true })
          .eq('id', insightId)
          .select()
          .single();

        if (error) {
          console.error('Error marking insight as viewed:', error);
          return;
        }

        const currentInsights = get().insights;
        const updatedInsights = currentInsights.map(insight => 
          insight.id === insightId ? data : insight
        );
        set({ insights: updatedInsights });
        
      } catch (error) {
        console.error('Error in markInsightAsViewed:', error);
      }
    },

    provideInsightFeedback: async (insightId: string, feedback: string) => {
      try {
        const { data, error } = await supabase
          .from('ai_insights')
          .update({ user_feedback: feedback })
          .eq('id', insightId)
          .select()
          .single();

        if (error) {
          console.error('Error providing insight feedback:', error);
          toast.error('Failed to provide feedback');
          return;
        }

        const currentInsights = get().insights;
        const updatedInsights = currentInsights.map(insight => 
          insight.id === insightId ? data : insight
        );
        set({ insights: updatedInsights });
        
        toast.success('Feedback provided successfully');
        
      } catch (error) {
        console.error('Error in provideInsightFeedback:', error);
        toast.error('Failed to provide feedback');
      }
    },

    updateInsightImplementation: async (insightId: string, status: string) => {
      try {
        const { data, error } = await supabase
          .from('ai_insights')
          .update({ implementation_status: status })
          .eq('id', insightId)
          .select()
          .single();

        if (error) {
          console.error('Error updating insight implementation:', error);
          toast.error('Failed to update implementation status');
          return;
        }

        const currentInsights = get().insights;
        const updatedInsights = currentInsights.map(insight => 
          insight.id === insightId ? data : insight
        );
        set({ insights: updatedInsights });
        
        toast.success('Implementation status updated');
        
      } catch (error) {
        console.error('Error in updateInsightImplementation:', error);
        toast.error('Failed to update implementation status');
      }
    },

    // Genetic Analysis Actions
    fetchGeneticAnalyses: async (animalId?: string) => {
      set({ isLoadingGenetics: true });
      
      try {
        let query = supabase
          .from('genetic_analysis')
          .select('*')
          .order('created_at', { ascending: false });
        
        if (animalId) {
          query = query.eq('animal_id', animalId);
        }
        
        const { data, error } = await query;

        if (error) {
          console.error('Error fetching genetic analyses:', error);
          toast.error('Failed to load genetic analyses');
          return;
        }

        set({ geneticAnalyses: data || [] });
        
        logApiCall('genetic_analysis', 'fetch', {
          animalId,
          count: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchGeneticAnalyses:', error);
        toast.error('Failed to load genetic analyses');
      } finally {
        set({ isLoadingGenetics: false });
      }
    },

    uploadGeneticData: async (animalId: string, geneticData: any, provider: string) => {
      try {
        const { data, error } = await supabase
          .from('genetic_analysis')
          .insert({
            animal_id: animalId,
            genetic_profile: geneticData.profile,
            breed_composition: geneticData.breed_composition,
            genetic_markers: geneticData.markers,
            disease_predispositions: geneticData.disease_risks,
            trait_predictions: geneticData.traits,
            behavioral_tendencies: geneticData.behavior,
            genetic_health_risks: geneticData.health_risks,
            carrier_status: geneticData.carrier_status,
            breeding_recommendations: geneticData.breeding_advice,
            analysis_provider: provider,
            analysis_date: new Date().toISOString(),
            analysis_version: '1.0'
          })
          .select()
          .single();

        if (error) {
          console.error('Error uploading genetic data:', error);
          toast.error('Failed to upload genetic data');
          return null;
        }

        const currentAnalyses = get().geneticAnalyses;
        set({ geneticAnalyses: [data, ...currentAnalyses] });
        
        logApiCall('genetic_analysis', 'create', {
          animalId,
          provider
        });
        
        toast.success('Genetic data uploaded successfully');
        return data;
        
      } catch (error) {
        console.error('Error in uploadGeneticData:', error);
        toast.error('Failed to upload genetic data');
        return null;
      }
    },

    // Utility Actions
    clearAIMLData: () => {
      set({
        imageAnalyses: [],
        conversations: [],
        currentConversation: null,
        messages: [],
        mlModels: [],
        predictions: [],
        healthAssessments: [],
        insights: [],
        geneticAnalyses: [],
        isLoadingImageAnalysis: false,
        isLoadingChat: false,
        isLoadingModels: false,
        isLoadingAssessments: false,
        isLoadingInsights: false,
        isLoadingGenetics: false
      });
    }
  }),
  {
    name: `ai-ml-store-2b237ff5-0e25-49db-b0c2-56f261d0a573`,
    storage: createJSONStorage(() => AsyncStorage),
    partialize: (state) => ({
      // Only persist non-sensitive data
      conversations: state.conversations,
      currentConversation: state.currentConversation,
      insights: state.insights.filter(insight => insight.viewed_by_user),
      geneticAnalyses: state.geneticAnalyses
    })
  }
));

// Helper functions for AI/ML features
export const getConfidenceColor = (confidence: number | undefined): string => {
  if (!confidence) return '#6B7280';
  
  if (confidence >= 0.9) return '#10B981';
  if (confidence >= 0.7) return '#F59E0B';
  if (confidence >= 0.5) return '#EF4444';
  return '#6B7280';
};

export const getHealthStatusColor = (status: string | undefined): string => {
  switch (status) {
    case 'excellent': return '#10B981';
    case 'good': return '#84CC16';
    case 'fair': return '#F59E0B';
    case 'poor': return '#EF4444';
    case 'critical': return '#DC2626';
    default: return '#6B7280';
  }
};

export const getRiskLevelColor = (risk: string | undefined): string => {
  switch (risk) {
    case 'low': return '#10B981';
    case 'moderate': return '#F59E0B';
    case 'high': return '#EF4444';
    case 'critical': return '#DC2626';
    default: return '#6B7280';
  }
};

export const getUrgencyLevelColor = (urgency: string | undefined): string => {
  switch (urgency) {
    case 'low': return '#10B981';
    case 'medium': return '#F59E0B';
    case 'high': return '#EF4444';
    case 'emergency': return '#DC2626';
    default: return '#6B7280';
  }
};

export const formatConfidence = (confidence: number | undefined): string => {
  if (!confidence) return 'N/A';
  return `${(confidence * 100).toFixed(0)}%`;
};

export const formatHealthScore = (score: number | undefined): string => {
  if (!score) return 'N/A';
  return `${score.toFixed(1)}/100`;
};

export const getInsightPriorityColor = (importance: number | undefined): string => {
  if (!importance) return '#6B7280';
  
  if (importance >= 0.8) return '#DC2626';
  if (importance >= 0.6) return '#F59E0B';
  if (importance >= 0.4) return '#84CC16';
  return '#10B981';
};