import { create } from 'zustand';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

export interface EnvironmentalData {
  id: string;
  animal_id: string;
  temperature_celsius?: number;
  humidity_percentage?: number;
  barometric_pressure_hpa?: number;
  wind_speed_kmh?: number;
  wind_direction_degrees?: number;
  precipitation_mm?: number;
  uv_index?: number;
  air_quality_index?: number;
  indoor_temperature_celsius?: number;
  indoor_humidity_percentage?: number;
  indoor_air_quality_ppm?: number;
  noise_level_db?: number;
  light_intensity_lux?: number;
  co2_level_ppm?: number;
  location_name?: string;
  altitude_meters?: number;
  latitude?: number;
  longitude?: number;
  timezone?: string;
  data_source: string;
  confidence_score: number;
  recorded_at: string;
  created_at: string;
  updated_at: string;
}

export interface EnvironmentalImpactAnalysis {
  id: string;
  animal_id: string;
  analysis_period_start: string;
  analysis_period_end: string;
  temperature_impact_score?: number;
  humidity_impact_score?: number;
  pressure_impact_score?: number;
  air_quality_impact_score?: number;
  seasonal_pattern_detected: boolean;
  seasonal_health_trend?: string;
  seasonal_risk_factors?: any;
  location_health_correlation?: number;
  optimal_environment_suggestions?: any;
  environmental_stress_indicators?: any;
  environmental_health_score: number;
  environmental_risk_level: string;
  analysis_model_version?: string;
  confidence_level?: number;
  data_points_analyzed?: number;
  created_at: string;
  updated_at: string;
}

export interface EnvironmentalOptimization {
  id: string;
  animal_id: string;
  optimization_type: string;
  priority_level: string;
  current_value?: number;
  optimal_value_min?: number;
  optimal_value_max?: number;
  improvement_potential_score?: number;
  recommendation_title: string;
  recommendation_description?: string;
  implementation_difficulty?: string;
  estimated_cost_category?: string;
  expected_health_improvement?: number;
  implementation_steps?: any;
  required_equipment?: any;
  monitoring_requirements?: any;
  status: string;
  implemented_at?: string;
  effectiveness_score?: number;
  created_at: string;
  updated_at: string;
}

interface EnvironmentalStore {
  // Environmental Data
  environmentalData: EnvironmentalData[];
  isLoadingEnvironmentalData: boolean;
  
  // Environmental Impact Analysis
  impactAnalyses: EnvironmentalImpactAnalysis[];
  isLoadingImpactAnalysis: boolean;
  
  // Environmental Optimization
  optimizations: EnvironmentalOptimization[];
  isLoadingOptimizations: boolean;
  
  // Actions
  fetchEnvironmentalData: (animalId: string, days?: number) => Promise<void>;
  addEnvironmentalData: (data: Partial<EnvironmentalData>) => Promise<void>;
  requestEnvironmentalAnalysis: (animalId: string, analysisPeriodDays?: number) => Promise<EnvironmentalImpactAnalysis | null>;
  fetchImpactAnalyses: (animalId: string) => Promise<void>;
  fetchOptimizations: (animalId: string) => Promise<void>;
  updateOptimizationStatus: (optimizationId: string, status: string, effectivenessScore?: number) => Promise<void>;
  clearEnvironmentalData: () => void;
}

export const useEnvironmentalStore = create<EnvironmentalStore>((set, get) => ({
  // Initial state
  environmentalData: [],
  isLoadingEnvironmentalData: false,
  impactAnalyses: [],
  isLoadingImpactAnalysis: false,
  optimizations: [],
  isLoadingOptimizations: false,

  // Fetch environmental data for an animal
  fetchEnvironmentalData: async (animalId: string, days: number = 30) => {
    set({ isLoadingEnvironmentalData: true });
    
    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000));
      
      const { data, error } = await supabase
        .from('environmental_data')
        .select('*')
        .eq('animal_id', animalId)
        .gte('recorded_at', startDate.toISOString())
        .order('recorded_at', { ascending: false });

      if (error) {
        console.error('Error fetching environmental data:', error);
        toast.error('Failed to load environmental data');
        return;
      }

      set({ environmentalData: data || [] });
      
      logApiCall('environmental_data', 'fetch', {
        animalId,
        days,
        recordCount: data?.length || 0
      });
      
    } catch (error) {
      console.error('Error in fetchEnvironmentalData:', error);
      toast.error('Failed to load environmental data');
    } finally {
      set({ isLoadingEnvironmentalData: false });
    }
  },

  // Add new environmental data
  addEnvironmentalData: async (data: Partial<EnvironmentalData>) => {
    try {
      const { data: newData, error } = await supabase
        .from('environmental_data')
        .insert({
          ...data,
          recorded_at: data.recorded_at || new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error adding environmental data:', error);
        toast.error('Failed to add environmental data');
        return;
      }

      // Add to current data
      const currentData = get().environmentalData;
      set({ environmentalData: [newData, ...currentData] });
      
      logApiCall('environmental_data', 'create', {
        animalId: data.animal_id,
        dataSource: data.data_source
      });
      
      toast.success('Environmental data added successfully');
      
    } catch (error) {
      console.error('Error in addEnvironmentalData:', error);
      toast.error('Failed to add environmental data');
    }
  },

  // Request environmental impact analysis
  requestEnvironmentalAnalysis: async (animalId: string, analysisPeriodDays: number = 30) => {
    set({ isLoadingImpactAnalysis: true });
    
    try {
      const { data, error } = await supabase.functions.invoke('analyze-environmental-impact', {
        body: {
          animalId,
          analysisPeriodDays,
          includeWeatherData: true,
          includeIndoorEnvironment: true
        }
      });

      if (error) {
        console.error('Error requesting environmental analysis:', error);
        toast.error('Failed to analyze environmental impact');
        return null;
      }

      if (!data.success) {
        console.error('Environmental analysis failed:', data.error);
        toast.error(data.error || 'Environmental analysis failed');
        return null;
      }

      // Update impact analyses
      const currentAnalyses = get().impactAnalyses;
      set({ impactAnalyses: [data.analysis, ...currentAnalyses] });
      
      // Update optimizations if provided
      if (data.optimizations && data.optimizations.length > 0) {
        const currentOptimizations = get().optimizations;
        set({ optimizations: [...data.optimizations, ...currentOptimizations] });
      }
      
      logApiCall('environmental_analysis', 'request', {
        animalId,
        analysisPeriodDays,
        dataPointsAnalyzed: data.dataPointsAnalyzed
      });
      
      toast.success('Environmental analysis completed successfully');
      return data.analysis;
      
    } catch (error) {
      console.error('Error in requestEnvironmentalAnalysis:', error);
      toast.error('Failed to analyze environmental impact');
      return null;
    } finally {
      set({ isLoadingImpactAnalysis: false });
    }
  },

  // Fetch environmental impact analyses
  fetchImpactAnalyses: async (animalId: string) => {
    set({ isLoadingImpactAnalysis: true });
    
    try {
      const { data, error } = await supabase
        .from('environmental_impact_analysis')
        .select('*')
        .eq('animal_id', animalId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Error fetching impact analyses:', error);
        toast.error('Failed to load environmental analyses');
        return;
      }

      set({ impactAnalyses: data || [] });
      
      logApiCall('environmental_impact_analysis', 'fetch', {
        animalId,
        analysisCount: data?.length || 0
      });
      
    } catch (error) {
      console.error('Error in fetchImpactAnalyses:', error);
      toast.error('Failed to load environmental analyses');
    } finally {
      set({ isLoadingImpactAnalysis: false });
    }
  },

  // Fetch environmental optimizations
  fetchOptimizations: async (animalId: string) => {
    set({ isLoadingOptimizations: true });
    
    try {
      const { data, error } = await supabase
        .from('environmental_optimization')
        .select('*')
        .eq('animal_id', animalId)
        .order('priority_level', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching optimizations:', error);
        toast.error('Failed to load optimization recommendations');
        return;
      }

      set({ optimizations: data || [] });
      
      logApiCall('environmental_optimization', 'fetch', {
        animalId,
        optimizationCount: data?.length || 0
      });
      
    } catch (error) {
      console.error('Error in fetchOptimizations:', error);
      toast.error('Failed to load optimization recommendations');
    } finally {
      set({ isLoadingOptimizations: false });
    }
  },

  // Update optimization status
  updateOptimizationStatus: async (optimizationId: string, status: string, effectivenessScore?: number) => {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      };
      
      if (status === 'completed') {
        updateData.implemented_at = new Date().toISOString();
        if (effectivenessScore !== undefined) {
          updateData.effectiveness_score = effectivenessScore;
        }
      }
      
      const { data, error } = await supabase
        .from('environmental_optimization')
        .update(updateData)
        .eq('id', optimizationId)
        .select()
        .single();

      if (error) {
        console.error('Error updating optimization status:', error);
        toast.error('Failed to update optimization status');
        return;
      }

      // Update local state
      const currentOptimizations = get().optimizations;
      const updatedOptimizations = currentOptimizations.map(opt => 
        opt.id === optimizationId ? data : opt
      );
      set({ optimizations: updatedOptimizations });
      
      logApiCall('environmental_optimization', 'update', {
        optimizationId,
        status,
        effectivenessScore
      });
      
      toast.success('Optimization status updated successfully');
      
    } catch (error) {
      console.error('Error in updateOptimizationStatus:', error);
      toast.error('Failed to update optimization status');
    }
  },

  // Clear all environmental data
  clearEnvironmentalData: () => {
    set({
      environmentalData: [],
      impactAnalyses: [],
      optimizations: [],
      isLoadingEnvironmentalData: false,
      isLoadingImpactAnalysis: false,
      isLoadingOptimizations: false
    });
  }
}));

// Helper functions for environmental data analysis
export const getEnvironmentalRiskLevel = (score: number): string => {
  if (score >= 80) return 'low';
  if (score >= 60) return 'moderate';
  if (score >= 40) return 'high';
  return 'critical';
};

export const getEnvironmentalRiskColor = (riskLevel: string): string => {
  switch (riskLevel) {
    case 'low': return '#10B981';
    case 'moderate': return '#F59E0B';
    case 'high': return '#EF4444';
    case 'critical': return '#DC2626';
    default: return '#6B7280';
  }
};

export const getOptimizationPriorityColor = (priority: string): string => {
  switch (priority) {
    case 'critical': return '#DC2626';
    case 'high': return '#EF4444';
    case 'medium': return '#F59E0B';
    case 'low': return '#10B981';
    default: return '#6B7280';
  }
};

export const formatEnvironmentalValue = (value: number | undefined, unit: string): string => {
  if (value === undefined || value === null) return 'N/A';
  return `${value.toFixed(1)}${unit}`;
};

export const getTemperatureStatus = (temp: number | undefined): { status: string; color: string } => {
  if (!temp) return { status: 'Unknown', color: '#6B7280' };
  
  if (temp < 10) return { status: 'Very Cold', color: '#3B82F6' };
  if (temp < 18) return { status: 'Cold', color: '#06B6D4' };
  if (temp <= 24) return { status: 'Optimal', color: '#10B981' };
  if (temp <= 30) return { status: 'Warm', color: '#F59E0B' };
  return { status: 'Hot', color: '#EF4444' };
};

export const getHumidityStatus = (humidity: number | undefined): { status: string; color: string } => {
  if (!humidity) return { status: 'Unknown', color: '#6B7280' };
  
  if (humidity < 30) return { status: 'Too Dry', color: '#EF4444' };
  if (humidity <= 60) return { status: 'Optimal', color: '#10B981' };
  if (humidity <= 70) return { status: 'High', color: '#F59E0B' };
  return { status: 'Too Humid', color: '#EF4444' };
};

export const getAirQualityStatus = (aqi: number | undefined): { status: string; color: string } => {
  if (!aqi) return { status: 'Unknown', color: '#6B7280' };
  
  if (aqi <= 50) return { status: 'Good', color: '#10B981' };
  if (aqi <= 100) return { status: 'Moderate', color: '#F59E0B' };
  if (aqi <= 150) return { status: 'Unhealthy for Sensitive', color: '#EF4444' };
  if (aqi <= 200) return { status: 'Unhealthy', color: '#DC2626' };
  return { status: 'Hazardous', color: '#7C2D12' };
};