import { create } from 'zustand';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';
import { Product } from './productStore';

export interface Order {
  id: string;
  user_id: string;
  total_amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  shipping_address_line1: string;
  shipping_address_line2?: string;
  shipping_city: string;
  shipping_state_province: string;
  shipping_postal_code: string;
  shipping_country: string;
  payment_intent_id?: string;
  payment_method?: string;
  payment_status: string;
  tracking_number?: string;
  estimated_delivery_date?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  price_at_purchase: number;
  created_at: string;
  product?: Product;
}

export interface OrderWithItems extends Order {
  order_items: OrderItem[];
}

export interface ShippingAddress {
  line1: string;
  line2?: string;
  city: string;
  state_province: string;
  postal_code: string;
  country: string;
}

export interface CreateOrderRequest {
  user_id?: string;
  product_id: string;
  quantity: number;
  total_amount?: number;
  currency?: string;
  status?: 'pending' | 'paid' | 'failed' | 'cancelled' | 'refunded';
  checkout_url?: string;
  plan_type?: 'monthly' | 'yearly';
  payment_method?: 'lemonsqueezy' | 'stripe' | 'paypal';
  lemonsqueezy_order_id?: number;
  lemonsqueezy_order_number?: number;
  shipping_address?: ShippingAddress;
  notes?: string;
}

export interface CreateOrderResponse {
  success: boolean;
  order_id?: string;
  total_amount?: number;
  payment_intent_id?: string;
  error?: string;
}

interface OrderState {
  orders: OrderWithItems[];
  isLoading: boolean;
  isCreatingOrder: boolean;
  error: string | null;
  currentOrderDetails: CreateOrderRequest | null;
}

interface OrderActions {
  fetchOrderHistory: () => Promise<void>;
  createOrder: (orderRequest: CreateOrderRequest) => Promise<CreateOrderResponse>;
  updateOrderStatus: (orderId: string, updates: Partial<CreateOrderRequest>) => Promise<void>;
  getOrderById: (id: string) => OrderWithItems | undefined;
  setCurrentOrderDetails: (details: CreateOrderRequest | null) => void;
  clearError: () => void;
  refreshOrders: () => Promise<void>;
}

type OrderStore = OrderState & OrderActions;

export const useOrderStore = create<OrderStore>((set, get) => ({
  // Initial state
  orders: [],
  isLoading: false,
  isCreatingOrder: false,
  error: null,
  currentOrderDetails: null,

  // Actions
  fetchOrderHistory: async () => {
    const startTime = Date.now();
    set({ isLoading: true, error: null });

    try {
      // Fetch orders with order items and product details
      const { data: ordersData, error: ordersError } = await supabase
        .from('orders')
        .select(`
          *,
          order_items (
            *,
            product:products (*)
          )
        `)
        .order('created_at', { ascending: false });

      logApiCall('orders', ordersData, ordersError);

      if (ordersError) {
        console.error('Error fetching orders:', ordersError);
        set({ error: ordersError.message, isLoading: false });
        toast.error('Failed to load order history');
        return;
      }

      set({ 
        orders: ordersData || [], 
        isLoading: false, 
        error: null 
      });

      console.log(`✅ Loaded ${ordersData?.length || 0} orders`);
    } catch (err: any) {
      console.error('Unexpected error fetching orders:', err);
      const errorMessage = err.message || 'Failed to load order history';
      set({ error: errorMessage, isLoading: false });
      toast.error(errorMessage);
    }
  },

  createOrder: async (orderRequest: CreateOrderRequest): Promise<CreateOrderResponse | string> => {
    set({ isCreatingOrder: true, error: null });

    try {
      // Get current user session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError || !session) {
        throw new Error('Authentication required');
      }

      // For Lemon Squeezy orders, create directly in database
      if (orderRequest.payment_method === 'lemonsqueezy') {
        const orderData = {
          user_id: orderRequest.user_id || session.user.id,
          product_id: orderRequest.product_id,
          quantity: orderRequest.quantity,
          total_amount: orderRequest.total_amount || 0,
          currency: orderRequest.currency || 'USD',
          status: orderRequest.status || 'pending',
          payment_method: 'lemonsqueezy',
          plan_type: orderRequest.plan_type,
          checkout_url: orderRequest.checkout_url,
          lemonsqueezy_order_id: orderRequest.lemonsqueezy_order_id,
          lemonsqueezy_order_number: orderRequest.lemonsqueezy_order_number,
          notes: orderRequest.notes,
        };

        const { data, error } = await supabase
          .from('orders')
          .insert(orderData)
          .select()
          .single();

        logApiCall('createOrder', data, error);

        if (error) {
          console.error('Error creating order:', error);
          const errorMessage = error.message || 'Failed to create order';
          set({ error: errorMessage, isCreatingOrder: false });
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }

        set({ isCreatingOrder: false, error: null });
        console.log('Order created successfully:', data.id);
        
        // Refresh orders to include the new order
        await get().fetchOrderHistory();

        return data.id; // Return order ID for Lemon Squeezy orders
      }

      // For device orders, use the existing Edge Function
      const { data, error } = await supabase.functions.invoke('create-device-order', {
        body: orderRequest,
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      logApiCall('create-device-order', data, error);

      if (error) {
        console.error('Error creating order:', error);
        const errorMessage = error.message || 'Failed to create order';
        set({ error: errorMessage, isCreatingOrder: false });
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }

      if (!data.success) {
        console.error('Order creation failed:', data.error);
        set({ error: data.error, isCreatingOrder: false });
        toast.error(data.error || 'Failed to create order');
        return { success: false, error: data.error };
      }

      set({ isCreatingOrder: false, error: null });
      toast.success('Order created successfully!');
      
      // Refresh orders to include the new order
      await get().fetchOrderHistory();

      return {
        success: true,
        order_id: data.order_id,
        total_amount: data.total_amount,
        payment_intent_id: data.payment_intent_id
      };

    } catch (err: any) {
      console.error('Unexpected error creating order:', err);
      const errorMessage = err.message || 'Failed to create order';
      set({ error: errorMessage, isCreatingOrder: false });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  },

  updateOrderStatus: async (orderId: string, updates: Partial<CreateOrderRequest>): Promise<void> => {
    set({ isLoading: true, error: null });

    try {
      const { error } = await supabase
        .from('orders')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', orderId);

      logApiCall('updateOrderStatus', { orderId, updates }, error);

      if (error) {
        console.error('Error updating order status:', error);
        const errorMessage = error.message || 'Failed to update order status';
        set({ error: errorMessage, isLoading: false });
        toast.error(errorMessage);
        return;
      }

      set({ isLoading: false, error: null });
      console.log(`Order ${orderId} updated successfully`);
      
      // Refresh orders to reflect the update
      await get().fetchOrderHistory();

    } catch (err: any) {
      console.error('Unexpected error updating order:', err);
      const errorMessage = err.message || 'Failed to update order status';
      set({ error: errorMessage, isLoading: false });
      toast.error(errorMessage);
    }
  },

  getOrderById: (id: string) => {
    return get().orders.find(order => order.id === id);
  },

  setCurrentOrderDetails: (details: CreateOrderRequest | null) => {
    set({ currentOrderDetails: details });
  },

  clearError: () => {
    set({ error: null });
  },

  refreshOrders: async () => {
    await get().fetchOrderHistory();
  }
}));

// Auto-fetch orders when store is first used (only if user is authenticated)
let hasInitialized = false;
if (!hasInitialized) {
  hasInitialized = true;
  // Delay initial fetch to avoid calling during store creation
  setTimeout(async () => {
    const { data: { session } } = await supabase.auth.getSession();
    if (session) {
      useOrderStore.getState().fetchOrderHistory();
    }
  }, 100);
}