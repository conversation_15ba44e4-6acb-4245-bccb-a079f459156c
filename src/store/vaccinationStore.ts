
import { create } from 'zustand';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

export interface VaccinationRecord {
  id: string;
  animalId: string;
  vaccineName: string;
  date: string;
  nextDue?: string;
  vetName?: string;
  notes?: string;
}

interface VaccinationStore {
  vaccinations: VaccinationRecord[];
  isLoading: boolean;
  error: string | null;
  fetchVaccinations: () => Promise<void>;
  addVaccination: (vaccination: Omit<VaccinationRecord, 'id'>) => Promise<void>;
  updateVaccination: (id: string, vaccination: Partial<VaccinationRecord>) => Promise<void>;
  deleteVaccination: (id: string) => Promise<void>;
  getVaccinationsByAnimalId: (animalId: string) => VaccinationRecord[];
}

export const useVaccinationStore = create<VaccinationStore>((set, get) => ({
  vaccinations: [],
  isLoading: false,
  error: null,
  
  fetchVaccinations: async () => {
    set({ isLoading: true, error: null });
    
    try {
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return;
      }
      
      const { data, error } = await supabase
        .from('vaccinations')
        .select('*')
        .eq('user_id', user.id) // Defense-in-depth: explicit user filtering
        .order('date', { ascending: false });
      
      logApiCall('fetchVaccinations', data, error);
      
      if (error) {
        console.error('Error fetching vaccinations:', error);
        throw new Error(`Failed to fetch vaccination records: ${error.message}`);
      }
      
      console.log(`API Success (fetchVaccinations): Retrieved ${data?.length || 0} records`);
      
      // Convert database fields to match our interface
      const formattedData = data ? data.map((item: any) => ({
        id: item.id,
        animalId: item.animal_id,
        vaccineName: item.vaccine_name,
        date: item.date,
        nextDue: item.next_due,
        vetName: item.vet_name,
        notes: item.notes
      })) : [];
      
      set({ vaccinations: formattedData, isLoading: false });
    } catch (error: any) {
      console.error('Error fetching vaccinations:', error);
      const errorMessage = error.message || 'Failed to fetch vaccination records';
      set({ error: errorMessage, isLoading: false });
      // Note: No toast for fetch errors as they're usually silent background operations
      throw error;
    }
  },
  
  addVaccination: async (vaccination) => {
    set({ isLoading: true, error: null });
    
    try {
      // Convert to database field names
      const dbVaccination = {
        animal_id: vaccination.animalId,
        vaccine_name: vaccination.vaccineName,
        date: vaccination.date,
        next_due: vaccination.nextDue,
        vet_name: vaccination.vetName,
        notes: vaccination.notes
      };
      
      const { data, error } = await supabase
        .from('vaccinations')
        .insert(dbVaccination)
        .select();
      
      logApiCall('addVaccination', data, error);
      
      if (error) {
        console.error('Error adding vaccination:', error);
        throw new Error(`Failed to add vaccination record: ${error.message}`);
      }
      
      if (!data || data.length === 0) {
        throw new Error('No data returned from vaccination insert');
      }
      
      // Add to local state
      const newVaccination = {
        ...vaccination,
        id: data[0].id
      };
      
      set(state => ({
        vaccinations: [...state.vaccinations, newVaccination],
        isLoading: false
      }));
      
      toast.success(`${vaccination.vaccineName} vaccination record added successfully!`);
    } catch (error: any) {
      console.error('Error adding vaccination:', error);
      const errorMessage = error.message || 'Failed to add vaccination record';
      set({ error: errorMessage, isLoading: false });
      toast.error(errorMessage);
      throw error;
    }
  },
  
  updateVaccination: async (id, vaccination) => {
    set({ isLoading: true, error: null });
    
    try {
      // Convert to database field names
      const dbVaccination: any = {};
      
      if (vaccination.animalId) dbVaccination.animal_id = vaccination.animalId;
      if (vaccination.vaccineName) dbVaccination.vaccine_name = vaccination.vaccineName;
      if (vaccination.date) dbVaccination.date = vaccination.date;
      if (vaccination.nextDue !== undefined) dbVaccination.next_due = vaccination.nextDue;
      if (vaccination.vetName !== undefined) dbVaccination.vet_name = vaccination.vetName;
      if (vaccination.notes !== undefined) dbVaccination.notes = vaccination.notes;
      
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return;
      }
      
      const { error } = await supabase
        .from('vaccinations')
        .update(dbVaccination)
        .eq('id', id)
        .eq('user_id', user.id); // Defense-in-depth: ensure user owns record
      
      logApiCall('updateVaccination', { id, ...dbVaccination }, error);
      
      if (error) {
        console.error('Error updating vaccination:', error);
        throw new Error(`Failed to update vaccination record: ${error.message}`);
      }
      
      set(state => ({
        vaccinations: state.vaccinations.map(v => 
          v.id === id ? { ...v, ...vaccination } : v
        ),
        isLoading: false
      }));
      
      toast.success('Vaccination record updated successfully!');
    } catch (error: any) {
      console.error('Error updating vaccination:', error);
      const errorMessage = error.message || 'Failed to update vaccination record';
      set({ error: errorMessage, isLoading: false });
      toast.error(errorMessage);
      throw error;
    }
  },
  
  deleteVaccination: async (id) => {
    set({ isLoading: true, error: null });
    
    try {
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return;
      }
      
      const { error } = await supabase
        .from('vaccinations')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id); // Defense-in-depth: ensure user owns record
      
      logApiCall('deleteVaccination', { id }, error);
      
      if (error) {
        console.error('Error deleting vaccination:', error);
        throw new Error(`Failed to delete vaccination record: ${error.message}`);
      }
      
      set(state => ({
        vaccinations: state.vaccinations.filter(v => v.id !== id),
        isLoading: false
      }));
      
      toast.success('Vaccination record deleted successfully!');
    } catch (error: any) {
      console.error('Error deleting vaccination:', error);
      const errorMessage = error.message || 'Failed to delete vaccination record';
      set({ error: errorMessage, isLoading: false });
      toast.error(errorMessage);
      throw error;
    }
  },
  
  getVaccinationsByAnimalId: (animalId) => {
    const { vaccinations } = get();
    return vaccinations.filter(vaccination => vaccination.animalId === animalId);
  }
}));
