import { create } from 'zustand';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

// Types
export interface HealthScore {
  id: string;
  animal_id: string;
  user_id: string;
  score_date: string;
  overall_score: number;
  vitals_score: number;
  activity_score: number;
  feeding_score: number;
  medication_score: number;
  score_explanation: string;
  data_quality_score: number;
  recommendations?: string[];
  created_at: string;
  updated_at: string;
}

export interface DiseaseRiskAssessment {
  id: string;
  animal_id: string;
  user_id: string;
  assessment_date: string;
  disease_category: string;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  risk_score: number;
  contributing_factors: Record<string, any>;
  recommendations: string;
  confidence_level: number;
  created_at: string;
}

export interface HealthTrend {
  id: string;
  animal_id: string;
  user_id: string;
  trend_type: string;
  trend_direction: 'improving' | 'stable' | 'declining' | 'concerning';
  trend_strength: number;
  start_date: string;
  end_date?: string;
  trend_data: Record<string, any>;
  analysis_summary: string;
  created_at: string;
  updated_at: string;
}

export interface SmartHealthAlert {
  id: string;
  animal_id: string;
  user_id: string;
  alert_type: string;
  priority_level: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  recommended_actions: string[];
  is_resolved: boolean;
  resolved_at?: string;
  resolution_notes?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

interface AIHealthState {
  // Health Scores
  healthScores: HealthScore[];
  currentHealthScore: HealthScore | null;
  isLoadingHealthScores: boolean;
  
  // Disease Risk Assessments
  diseaseRiskAssessments: DiseaseRiskAssessment[];
  isLoadingRiskAssessments: boolean;
  
  // Health Trends
  healthTrends: HealthTrend[];
  isLoadingTrends: boolean;
  
  // Smart Alerts
  smartAlerts: SmartHealthAlert[];
  unreadAlertsCount: number;
  isLoadingAlerts: boolean;
  
  // Actions
  fetchHealthScores: (animalId: string) => Promise<void>;
  fetchLatestHealthScore: (animalId: string) => Promise<void>;
  calculateHealthScore: (animalId: string) => Promise<void>;
  
  fetchDiseaseRiskAssessments: (animalId: string) => Promise<void>;
  runDiseaseRiskAnalysis: (animalId: string) => Promise<void>;
  
  fetchHealthTrends: (animalId: string) => Promise<void>;
  processHealthTrends: (animalId: string) => Promise<void>;
  
  fetchSmartAlerts: (animalId?: string) => Promise<void>;
  generateSmartAlerts: (animalId?: string) => Promise<void>;
  markAlertAsResolved: (alertId: string, notes?: string) => Promise<void>;
  
  // Utility
  clearData: () => void;
}

export const useAIHealthStore = create<AIHealthState>((set, get) => ({
  // Initial state
  healthScores: [],
  currentHealthScore: null,
  isLoadingHealthScores: false,
  
  diseaseRiskAssessments: [],
  isLoadingRiskAssessments: false,
  
  healthTrends: [],
  isLoadingTrends: false,
  
  smartAlerts: [],
  unreadAlertsCount: 0,
  isLoadingAlerts: false,
  
  // Health Scores Actions
  fetchHealthScores: async (animalId: string) => {
    set({ isLoadingHealthScores: true });
    try {
      const { data, error } = await supabase
        .from('ai_health_scores')
        .select('*')
        .eq('animal_id', animalId)
        .order('score_date', { ascending: false });
      
      if (error) throw error;
      
      set({ 
        healthScores: data || [],
        currentHealthScore: data?.[0] || null
      });
      
      logApiCall('fetchHealthScores', 'success', { animalId, count: data?.length });
    } catch (error) {
      console.error('Error fetching health scores:', error);
      logApiCall('fetchHealthScores', 'error', { animalId, error: error.message });
      toast.error('Failed to load health scores');
    } finally {
      set({ isLoadingHealthScores: false });
    }
  },
  
  fetchLatestHealthScore: async (animalId: string) => {
    try {
      const { data, error } = await supabase
        .from('ai_health_scores')
        .select('*')
        .eq('animal_id', animalId)
        .order('score_date', { ascending: false })
        .limit(1)
        .single();
      
      if (error && error.code !== 'PGRST116') throw error;
      
      set({ currentHealthScore: data || null });
    } catch (error) {
      console.error('Error fetching latest health score:', error);
    }
  },
  
  calculateHealthScore: async (animalId: string) => {
    set({ isLoadingHealthScores: true });
    try {
      const { data, error } = await supabase.functions.invoke('calculate-daily-health-score', {
        body: { animal_id: animalId }
      });
      
      if (error) throw error;
      
      toast.success('Health score calculated successfully');
      
      // Refresh health scores
      await get().fetchLatestHealthScore(animalId);
      
      logApiCall('calculateHealthScore', 'success', { animalId });
    } catch (error) {
      console.error('Error calculating health score:', error);
      logApiCall('calculateHealthScore', 'error', { animalId, error: error.message });
      toast.error('Failed to calculate health score');
    } finally {
      set({ isLoadingHealthScores: false });
    }
  },
  
  // Disease Risk Assessments Actions
  fetchDiseaseRiskAssessments: async (animalId: string) => {
    set({ isLoadingRiskAssessments: true });
    try {
      const { data, error } = await supabase
        .from('disease_risk_assessments')
        .select('*')
        .eq('animal_id', animalId)
        .order('assessment_date', { ascending: false });
      
      if (error) throw error;
      
      set({ diseaseRiskAssessments: data || [] });
      
      logApiCall('fetchDiseaseRiskAssessments', 'success', { animalId, count: data?.length });
    } catch (error) {
      console.error('Error fetching disease risk assessments:', error);
      logApiCall('fetchDiseaseRiskAssessments', 'error', { animalId, error: error.message });
      toast.error('Failed to load risk assessments');
    } finally {
      set({ isLoadingRiskAssessments: false });
    }
  },
  
  runDiseaseRiskAnalysis: async (animalId: string) => {
    set({ isLoadingRiskAssessments: true });
    try {
      const { data, error } = await supabase.functions.invoke('analyze-disease-risk', {
        body: { animal_id: animalId }
      });
      
      if (error) throw error;
      
      toast.success('Disease risk analysis completed');
      
      // Refresh risk assessments
      await get().fetchDiseaseRiskAssessments(animalId);
      
      logApiCall('runDiseaseRiskAnalysis', 'success', { animalId });
    } catch (error) {
      console.error('Error running disease risk analysis:', error);
      logApiCall('runDiseaseRiskAnalysis', 'error', { animalId, error: error.message });
      toast.error('Failed to analyze disease risk');
    } finally {
      set({ isLoadingRiskAssessments: false });
    }
  },
  
  // Health Trends Actions
  fetchHealthTrends: async (animalId: string) => {
    set({ isLoadingTrends: true });
    try {
      const { data, error } = await supabase
        .from('health_trends')
        .select('*')
        .eq('animal_id', animalId)
        .order('updated_at', { ascending: false });
      
      if (error) throw error;
      
      set({ healthTrends: data || [] });
      
      logApiCall('fetchHealthTrends', 'success', { animalId, count: data?.length });
    } catch (error) {
      console.error('Error fetching health trends:', error);
      logApiCall('fetchHealthTrends', 'error', { animalId, error: error.message });
      toast.error('Failed to load health trends');
    } finally {
      set({ isLoadingTrends: false });
    }
  },
  
  processHealthTrends: async (animalId: string) => {
    set({ isLoadingTrends: true });
    try {
      const { data, error } = await supabase.functions.invoke('process-health-trends', {
        body: { animal_id: animalId }
      });
      
      if (error) throw error;
      
      toast.success('Health trends processed successfully');
      
      // Refresh health trends
      await get().fetchHealthTrends(animalId);
      
      logApiCall('processHealthTrends', 'success', { animalId });
    } catch (error) {
      console.error('Error processing health trends:', error);
      logApiCall('processHealthTrends', 'error', { animalId, error: error.message });
      toast.error('Failed to process health trends');
    } finally {
      set({ isLoadingTrends: false });
    }
  },
  
  // Smart Alerts Actions
  fetchSmartAlerts: async (animalId?: string) => {
    set({ isLoadingAlerts: true });
    try {
      let query = supabase
        .from('smart_health_alerts')
        .select('*')
        .eq('is_resolved', false)
        .order('created_at', { ascending: false });
      
      if (animalId) {
        query = query.eq('animal_id', animalId);
      }
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      set({ 
        smartAlerts: data || [],
        unreadAlertsCount: data?.length || 0
      });
      
      logApiCall('fetchSmartAlerts', 'success', { animalId, count: data?.length });
    } catch (error) {
      console.error('Error fetching smart alerts:', error);
      logApiCall('fetchSmartAlerts', 'error', { animalId, error: error.message });
      toast.error('Failed to load alerts');
    } finally {
      set({ isLoadingAlerts: false });
    }
  },
  
  generateSmartAlerts: async (animalId?: string) => {
    set({ isLoadingAlerts: true });
    try {
      const { data, error } = await supabase.functions.invoke('generate-smart-alerts', {
        body: { animal_id: animalId }
      });
      
      if (error) throw error;
      
      toast.success('Smart alerts generated successfully');
      
      // Refresh alerts
      await get().fetchSmartAlerts(animalId);
      
      logApiCall('generateSmartAlerts', 'success', { animalId });
    } catch (error) {
      console.error('Error generating smart alerts:', error);
      logApiCall('generateSmartAlerts', 'error', { animalId, error: error.message });
      toast.error('Failed to generate alerts');
    } finally {
      set({ isLoadingAlerts: false });
    }
  },
  
  markAlertAsResolved: async (alertId: string, notes?: string) => {
    try {
      const { error } = await supabase
        .from('smart_health_alerts')
        .update({
          is_resolved: true,
          resolved_at: new Date().toISOString(),
          resolution_notes: notes
        })
        .eq('id', alertId);
      
      if (error) throw error;
      
      // Update local state
      set(state => ({
        smartAlerts: state.smartAlerts.filter(alert => alert.id !== alertId),
        unreadAlertsCount: Math.max(0, state.unreadAlertsCount - 1)
      }));
      
      toast.success('Alert marked as resolved');
      
      logApiCall('markAlertAsResolved', 'success', { alertId });
    } catch (error) {
      console.error('Error marking alert as resolved:', error);
      logApiCall('markAlertAsResolved', 'error', { alertId, error: error.message });
      toast.error('Failed to resolve alert');
    }
  },
  
  // Utility
  clearData: () => {
    set({
      healthScores: [],
      currentHealthScore: null,
      diseaseRiskAssessments: [],
      healthTrends: [],
      smartAlerts: [],
      unreadAlertsCount: 0
    });
  }
}));