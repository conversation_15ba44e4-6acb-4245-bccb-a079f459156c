import { create } from 'zustand';
import { 
  UserProfile,
  UserProfileState, 
  userProfileInitialState, 
  userProfileOperations 
} from './slices/userProfileSlice';
import { 
  UserSubscriptionState, 
  userSubscriptionInitialState, 
  userSubscriptionOperations 
} from './slices/userSubscriptionSlice';
import { 
  UserImageState, 
  userImageInitialState, 
  userImageOperations 
} from './slices/userImageSlice';
import { 
  UserMfaState, 
  userMfaInitialState, 
  userMfaOperations 
} from './slices/userMfaSlice';

// Combined state interface
interface UserStore extends 
  UserProfileState, 
  UserSubscriptionState, 
  UserImageState, 
  UserMfaState {
  // Actions
  fetchProfile: () => Promise<void>;
  updateProfile: (profile: Partial<UserProfile>) => Promise<void>;
  updateSubscription: (isPremium: boolean, subscriptionType: 'monthly' | 'yearly') => Promise<void>;
  uploadProfileImage: (imageUri: string) => Promise<string>;
  checkMfaStatus: () => Promise<boolean>;
  updateMfaStatus: (enabled: boolean) => void;
}

export const useUserStoreRefactored = create<UserStore>((set, get) => ({
  // Combined initial state
  ...userProfileInitialState,
  ...userSubscriptionInitialState,
  ...userImageInitialState,
  ...userMfaInitialState,
  
  // Actions that delegate to slice operations
  fetchProfile: () => userProfileOperations.fetchProfile(set, get),
  updateProfile: (profile) => userProfileOperations.updateProfile(set, get, profile),
  updateSubscription: (isPremium, subscriptionType) => 
    userSubscriptionOperations.updateSubscription(set, get, isPremium, subscriptionType),
  uploadProfileImage: (imageUri) => userImageOperations.uploadProfileImage(set, get, imageUri),
  checkMfaStatus: () => userMfaOperations.checkMfaStatus(set, get),
  updateMfaStatus: (enabled) => userMfaOperations.updateMfaStatus(set, enabled),
}));

// Export the original interface for backward compatibility
export type { UserProfile };