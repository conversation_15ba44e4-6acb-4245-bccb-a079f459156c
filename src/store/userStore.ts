
import { create } from 'zustand';
import { supabase } from '../supabase/client';
import { toast } from 'sonner-native';

// Add logging utility
const logApiCall = (operation: string, data?: any, error?: any) => {
  if (error) {
    console.error(`❌ API Error (${operation}):`, error);
  } else {
    console.log(`✅ API Success (${operation}):`, data);
  }
};

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  farmName?: string;
  address?: string;
  phone?: string;
  isPremium: boolean;
  subscriptionType?: string;
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;
  imageUrl?: string;
  createdAt?: string;
  updatedAt?: string;
  mfaEnabled?: boolean;
}

interface UserStore {
  user: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  fetchProfile: () => Promise<void>;
  updateProfile: (profile: Partial<UserProfile>) => Promise<void>;
  updateSubscription: (isPremium: boolean, subscriptionType: 'monthly' | 'yearly') => Promise<void>;
  uploadProfileImage: (imageUri: string) => Promise<string>;
  checkMfaStatus: () => Promise<boolean>;
  updateMfaStatus: (enabled: boolean) => void;
}

export const useUserStore = create<UserStore>((set, get) => ({
  user: null,
  isLoading: false,
  error: null,
  
  fetchProfile: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        console.log('No authenticated user found');
        set({ user: null, isLoading: false });
        return;
      }
      
      console.log('Fetching profile for user:', user.id);
      
      // Try to get existing user profile
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (error && error.code === 'PGRST116') {
        // User profile doesn't exist, create one
        console.log('User profile not found, creating new profile for:', user.email);
        
        const newUserProfile = {
          id: user.id,
          email: user.email,
          name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
          farm_name: null,
          address: null,
          phone: null,
          is_premium: false,
          subscription_type: null,
          subscription_start_date: null,
          subscription_end_date: null,
          image_url: null
        };
        
        const { data: newData, error: insertError } = await supabase
          .from('users')
          .insert([newUserProfile])
          .select()
          .single();
        
        if (insertError) {
          console.error('Error creating user profile:', insertError);
          throw new Error(`Failed to create user profile: ${insertError.message}`);
        }
        
        console.log('User profile created successfully:', newData);
        
        // Use the newly created profile
        const formattedData: UserProfile = {
          id: newData.id,
          email: newData.email,
          name: newData.name,
          farmName: newData.farm_name,
          address: newData.address,
          phone: newData.phone,
          isPremium: newData.is_premium || false,
          subscriptionType: newData.subscription_type,
          subscriptionStartDate: newData.subscription_start_date,
          subscriptionEndDate: newData.subscription_end_date,
          imageUrl: newData.image_url,
          createdAt: newData.created_at,
          updatedAt: newData.updated_at
        };
        
        set({ user: formattedData, isLoading: false });
        return;
      }
      
      if (error) {
        console.error('Database error fetching profile:', error);
        throw new Error(`Database error: ${error.message}`);
      }
      
      if (!data) {
        console.error('No user data returned from database');
        throw new Error('User profile not found in database');
      }
      
      console.log('API Success (fetchProfile):', data);
      
      // Convert database fields to match our interface
      const formattedData: UserProfile = {
        id: data.id,
        email: data.email,
        name: data.name,
        farmName: data.farm_name,
        address: data.address,
        phone: data.phone,
        isPremium: data.is_premium || false,
        subscriptionType: data.subscription_type,
        subscriptionStartDate: data.subscription_start_date,
        subscriptionEndDate: data.subscription_end_date,
        imageUrl: data.image_url,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
      
      set({ user: formattedData, isLoading: false });
    } catch (error: any) {
      console.error('Error fetching profile:', error);
      set({ error: error.message, isLoading: false });
      toast.error('Failed to load user profile');
    }
  },
  
  updateProfile: async (profile) => {
    set({ isLoading: true, error: null });
    
    try {
      const { user } = get();
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Convert to database field names
      const dbProfile: any = {};
      
      if (profile.name !== undefined) dbProfile.name = profile.name;
      if (profile.farmName !== undefined) dbProfile.farm_name = profile.farmName;
      if (profile.address !== undefined) dbProfile.address = profile.address;
      if (profile.phone !== undefined) dbProfile.phone = profile.phone;
      if (profile.imageUrl !== undefined) dbProfile.image_url = profile.imageUrl;
      
      const { error } = await supabase
        .from('users')
        .update(dbProfile)
        .eq('id', user.id);
      
      if (error) {
        throw error;
      }
      
      set(state => ({
        user: state.user ? { ...state.user, ...profile } : null,
        isLoading: false
      }));
      
      toast.success('Profile updated successfully');
    } catch (error: any) {
      console.error('Error updating profile:', error);
      set({ error: error.message, isLoading: false });
      toast.error('Failed to update profile');
      throw error;
    }
  },
  
  updateSubscription: async (isPremium: boolean, subscriptionType: 'monthly' | 'yearly') => {
    set({ isLoading: true, error: null });
    
    try {
      const { user } = get();
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Calculate subscription dates
      const startDate = new Date();
      const endDate = new Date();
      
      if (subscriptionType === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else {
        endDate.setFullYear(endDate.getFullYear() + 1);
      }
      
      // Update subscription in Supabase
      const { error } = await supabase
        .from('users')
        .update({
          is_premium: isPremium,
          subscription_type: subscriptionType,
          subscription_start_date: startDate.toISOString(),
          subscription_end_date: endDate.toISOString()
        })
        .eq('id', user.id);
      
      if (error) {
        throw error;
      }
      
      // Update local state
      set(state => ({
        user: state.user ? {
          ...state.user,
          isPremium,
          subscriptionType,
          subscriptionStartDate: startDate.toISOString(),
          subscriptionEndDate: endDate.toISOString()
        } : null,
        isLoading: false
      }));
      
      console.log('Subscription updated successfully:', { isPremium, subscriptionType });
      
    } catch (error: any) {
      console.error('Error updating subscription:', error);
      set({ error: error.message, isLoading: false });
      toast.error('Failed to update subscription');
      throw error;
    }
  },
  
  uploadProfileImage: async (imageUri) => {
    const { user } = get();
    
    if (!user) {
      toast.error('User not found. Please log in.');
      throw new Error('User not found');
    }

    set({ isLoading: true, error: null });
    
    let uploadedImageUrl = '';
    const originalImageUrl = user.imageUrl;
    
    try {
      console.log('Starting profile image upload for user:', user.id);
      
      // Fetch the image blob from the local URI
      const response = await fetch(imageUri);
      if (!response.ok) {
        throw new Error('Failed to fetch image data');
      }
      
      const blob = await response.blob();
      
      // Determine file extension and create unique filename
      // Use user.id folder structure to match RLS policies
      const fileExt = imageUri.split('.').pop() || 'jpg';
      const fileName = `${user.id}/profile.${fileExt}`;
      
      console.log('Uploading image to Supabase Storage:', fileName);
      
      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('profile-images')
        .upload(fileName, blob, {
          contentType: blob.type,
          upsert: true // Overwrite existing profile image
        });

      if (uploadError) {
        console.error('Error uploading image to Supabase Storage:', uploadError);
        
        // Check if bucket doesn't exist and provide helpful error
        if (uploadError.message.includes('Bucket not found')) {
          toast.error('Storage not configured. Please contact support.');
          throw new Error('Storage bucket not found. Please contact support.');
        }
        
        toast.error(`Image upload failed: ${uploadError.message}`);
        throw uploadError;
      }

      console.log('Image uploaded successfully:', uploadData);

      // Get public URL for the uploaded image
      const { data: urlData } = supabase.storage
        .from('profile-images')
        .getPublicUrl(fileName);
      
      if (!urlData || !urlData.publicUrl) {
        console.error('Error getting public URL for image');
        toast.error('Failed to get image URL. Please try again.');
        throw new Error('Failed to get image URL');
      }

      uploadedImageUrl = urlData.publicUrl;
      console.log('Got public URL:', uploadedImageUrl);

      // Optimistically update local state
      set(state => ({ 
        user: state.user ? { ...state.user, imageUrl: uploadedImageUrl } : null
      }));

      // Update user profile in the database with the new public URL
      await get().updateProfile({ imageUrl: uploadedImageUrl });
      
      set({ isLoading: false });
      toast.success('Profile image updated successfully!');
      
      return uploadedImageUrl;

    } catch (error: any) {
      console.error('Error in uploadProfileImage:', error);
      
      // Revert optimistic update if we had set the new URL
      if (uploadedImageUrl && user.imageUrl === uploadedImageUrl) {
        set(state => ({ 
          user: state.user ? { ...state.user, imageUrl: originalImageUrl } : null
        }));
      }
      
      set({ isLoading: false, error: error.message || 'Failed to upload profile image' });
      
      // Only show toast if we haven't already shown a specific error
      if (!error.message?.includes('Image upload failed') && 
          !error.message?.includes('Failed to get image URL') &&
          !error.message?.includes('Storage not configured')) {
        toast.error(error.message || 'Failed to update profile image');
      }
      
      throw error;
    }
  },
  
  checkMfaStatus: async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return false;
      }
      
      // Check if user has MFA enabled
      const { data: mfaData, error } = await supabase
        .from('user_mfa_settings')
        .select('is_enabled')
        .eq('user_id', user.id)
        .eq('is_enabled', true)
        .single();
      
      const mfaEnabled = !error && mfaData?.is_enabled === true;
      
      // Update local state
      set(state => ({
        user: state.user ? { ...state.user, mfaEnabled } : null
      }));
      
      return mfaEnabled;
    } catch (error) {
      console.error('Error checking MFA status:', error);
      return false;
    }
  },
  
  updateMfaStatus: (enabled: boolean) => {
    set(state => ({
      user: state.user ? { ...state.user, mfaEnabled: enabled } : null
    }));
  }
}));
