import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

export interface IoTDevice {
  id: string;
  user_id: string;
  animal_id?: string;
  device_name: string;
  device_type: string;
  manufacturer?: string;
  model?: string;
  firmware_version?: string;
  device_identifier: string;
  connection_type: string;
  api_endpoint?: string;
  status: string;
  last_seen?: string;
  battery_level?: number;
  signal_strength?: number;
  data_collection_interval: number;
  enabled_sensors?: any;
  device_settings?: any;
  created_at: string;
  updated_at: string;
}

export interface DataExportJob {
  id: string;
  user_id: string;
  export_type: string;
  data_types: string[];
  animal_ids?: string[];
  date_range_start?: string;
  date_range_end?: string;
  export_format: string;
  status: string;
  progress_percentage: number;
  total_records?: number;
  processed_records?: number;
  file_url?: string;
  file_size_bytes?: number;
  download_expires_at?: string;
  download_count: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface ExternalIntegration {
  id: string;
  user_id: string;
  service_type: string;
  service_name: string;
  integration_status: string;
  integration_config?: any;
  sync_frequency: number;
  last_sync_at?: string;
  next_sync_at?: string;
  sync_success_count: number;
  sync_error_count: number;
  last_error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface PushSubscription {
  id: string;
  user_id: string;
  device_type: string;
  device_token: string;
  device_identifier?: string;
  subscription_data?: any;
  is_active: boolean;
  notification_types: string[];
  quiet_hours_start?: string;
  quiet_hours_end?: string;
  timezone?: string;
  created_at: string;
  updated_at: string;
}

export interface NotificationHistory {
  id: string;
  user_id: string;
  animal_id?: string;
  notification_type: string;
  title: string;
  message: string;
  priority: string;
  delivery_method: string;
  delivery_status: string;
  delivered_at?: string;
  opened_at?: string;
  clicked_at?: string;
  action_taken?: string;
  notification_data?: any;
  created_at: string;
}

export interface UserCollaboration {
  id: string;
  owner_user_id: string;
  collaborator_user_id: string;
  animal_id: string;
  role: string;
  permissions: string[];
  status: string;
  invited_at: string;
  accepted_at?: string;
  expires_at?: string;
  invitation_token?: string;
  invitation_message?: string;
  created_at: string;
  updated_at: string;
}

interface PlatformStore {
  // IoT Devices
  iotDevices: IoTDevice[];
  isLoadingDevices: boolean;
  
  // Data Export
  exportJobs: DataExportJob[];
  isLoadingExports: boolean;
  
  // External Integrations
  integrations: ExternalIntegration[];
  isLoadingIntegrations: boolean;
  
  // Push Notifications
  pushSubscriptions: PushSubscription[];
  notificationHistory: NotificationHistory[];
  isLoadingNotifications: boolean;
  
  // User Collaborations
  collaborations: UserCollaboration[];
  isLoadingCollaborations: boolean;
  
  // Performance Metrics
  performanceMetrics: any[];
  isLoadingMetrics: boolean;
  
  // Actions - IoT Devices
  fetchIoTDevices: () => Promise<void>;
  addIoTDevice: (device: Partial<IoTDevice>) => Promise<void>;
  updateIoTDevice: (deviceId: string, updates: Partial<IoTDevice>) => Promise<void>;
  removeIoTDevice: (deviceId: string) => Promise<void>;
  syncDeviceData: (deviceId: string) => Promise<void>;
  
  // Actions - Data Export
  fetchExportJobs: () => Promise<void>;
  createExportJob: (exportConfig: Partial<DataExportJob>) => Promise<DataExportJob | null>;
  downloadExport: (jobId: string) => Promise<string | null>;
  
  // Actions - External Integrations
  fetchIntegrations: () => Promise<void>;
  addIntegration: (integration: Partial<ExternalIntegration>) => Promise<void>;
  updateIntegration: (integrationId: string, updates: Partial<ExternalIntegration>) => Promise<void>;
  removeIntegration: (integrationId: string) => Promise<void>;
  syncIntegration: (integrationId: string) => Promise<void>;
  
  // Actions - Push Notifications
  fetchPushSubscriptions: () => Promise<void>;
  registerPushSubscription: (subscription: Partial<PushSubscription>) => Promise<void>;
  updateNotificationPreferences: (subscriptionId: string, preferences: Partial<PushSubscription>) => Promise<void>;
  fetchNotificationHistory: () => Promise<void>;
  
  // Actions - User Collaborations
  fetchCollaborations: () => Promise<void>;
  inviteCollaborator: (animalId: string, email: string, role: string, permissions: string[]) => Promise<void>;
  acceptCollaboration: (invitationToken: string) => Promise<void>;
  updateCollaborationRole: (collaborationId: string, role: string, permissions: string[]) => Promise<void>;
  removeCollaboration: (collaborationId: string) => Promise<void>;
  
  // Actions - Performance
  fetchPerformanceMetrics: (metricType?: string) => Promise<void>;
  recordPerformanceMetric: (metric: any) => Promise<void>;
  
  // Utility Actions
  clearPlatformData: () => void;
}

export const usePlatformStore = create<PlatformStore>()(persist(
  (set, get) => ({
    // Initial state
    iotDevices: [],
    isLoadingDevices: false,
    exportJobs: [],
    isLoadingExports: false,
    integrations: [],
    isLoadingIntegrations: false,
    pushSubscriptions: [],
    notificationHistory: [],
    isLoadingNotifications: false,
    collaborations: [],
    isLoadingCollaborations: false,
    performanceMetrics: [],
    isLoadingMetrics: false,

    // IoT Devices Actions
    fetchIoTDevices: async () => {
      set({ isLoadingDevices: true });
      
      try {
        const { data, error } = await supabase
          .from('iot_devices')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching IoT devices:', error);
          toast.error('Failed to load IoT devices');
          return;
        }

        set({ iotDevices: data || [] });
        
        logApiCall('iot_devices', 'fetch', {
          deviceCount: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchIoTDevices:', error);
        toast.error('Failed to load IoT devices');
      } finally {
        set({ isLoadingDevices: false });
      }
    },

    addIoTDevice: async (device: Partial<IoTDevice>) => {
      try {
        const { data, error } = await supabase
          .from('iot_devices')
          .insert(device)
          .select()
          .single();

        if (error) {
          console.error('Error adding IoT device:', error);
          toast.error('Failed to add IoT device');
          return;
        }

        const currentDevices = get().iotDevices;
        set({ iotDevices: [data, ...currentDevices] });
        
        logApiCall('iot_devices', 'create', {
          deviceType: device.device_type,
          connectionType: device.connection_type
        });
        
        toast.success('IoT device added successfully');
        
      } catch (error) {
        console.error('Error in addIoTDevice:', error);
        toast.error('Failed to add IoT device');
      }
    },

    updateIoTDevice: async (deviceId: string, updates: Partial<IoTDevice>) => {
      try {
        const { data, error } = await supabase
          .from('iot_devices')
          .update({
            ...updates,
            updated_at: new Date().toISOString()
          })
          .eq('id', deviceId)
          .select()
          .single();

        if (error) {
          console.error('Error updating IoT device:', error);
          toast.error('Failed to update IoT device');
          return;
        }

        const currentDevices = get().iotDevices;
        const updatedDevices = currentDevices.map(device => 
          device.id === deviceId ? data : device
        );
        set({ iotDevices: updatedDevices });
        
        logApiCall('iot_devices', 'update', { deviceId });
        toast.success('IoT device updated successfully');
        
      } catch (error) {
        console.error('Error in updateIoTDevice:', error);
        toast.error('Failed to update IoT device');
      }
    },

    removeIoTDevice: async (deviceId: string) => {
      try {
        const { error } = await supabase
          .from('iot_devices')
          .delete()
          .eq('id', deviceId);

        if (error) {
          console.error('Error removing IoT device:', error);
          toast.error('Failed to remove IoT device');
          return;
        }

        const currentDevices = get().iotDevices;
        const filteredDevices = currentDevices.filter(device => device.id !== deviceId);
        set({ iotDevices: filteredDevices });
        
        logApiCall('iot_devices', 'delete', { deviceId });
        toast.success('IoT device removed successfully');
        
      } catch (error) {
        console.error('Error in removeIoTDevice:', error);
        toast.error('Failed to remove IoT device');
      }
    },

    syncDeviceData: async (deviceId: string) => {
      try {
        // This would trigger a sync with the actual device
        // For now, we'll just update the last_seen timestamp
        await get().updateIoTDevice(deviceId, {
          last_seen: new Date().toISOString(),
          status: 'active'
        });
        
        toast.success('Device data synced successfully');
        
      } catch (error) {
        console.error('Error in syncDeviceData:', error);
        toast.error('Failed to sync device data');
      }
    },

    // Data Export Actions
    fetchExportJobs: async () => {
      set({ isLoadingExports: true });
      
      try {
        const { data, error } = await supabase
          .from('data_export_jobs')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(20);

        if (error) {
          console.error('Error fetching export jobs:', error);
          toast.error('Failed to load export jobs');
          return;
        }

        set({ exportJobs: data || [] });
        
        logApiCall('data_export_jobs', 'fetch', {
          jobCount: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchExportJobs:', error);
        toast.error('Failed to load export jobs');
      } finally {
        set({ isLoadingExports: false });
      }
    },

    createExportJob: async (exportConfig: Partial<DataExportJob>) => {
      try {
        const { data, error } = await supabase.functions.invoke('export-data', {
          body: exportConfig
        });

        if (error) {
          console.error('Error creating export job:', error);
          toast.error('Failed to create export job');
          return null;
        }

        if (!data.success) {
          console.error('Export job creation failed:', data.error);
          toast.error(data.error || 'Export job creation failed');
          return null;
        }

        // Refresh export jobs
        await get().fetchExportJobs();
        
        logApiCall('data_export_jobs', 'create', {
          exportType: exportConfig.export_type,
          exportFormat: exportConfig.export_format
        });
        
        toast.success('Export job created successfully');
        return data.exportJob;
        
      } catch (error) {
        console.error('Error in createExportJob:', error);
        toast.error('Failed to create export job');
        return null;
      }
    },

    downloadExport: async (jobId: string) => {
      try {
        const job = get().exportJobs.find(j => j.id === jobId);
        if (!job || !job.file_url) {
          toast.error('Export file not available');
          return null;
        }

        // Update download count
        await supabase
          .from('data_export_jobs')
          .update({ download_count: job.download_count + 1 })
          .eq('id', jobId);

        // Refresh jobs to update download count
        await get().fetchExportJobs();
        
        logApiCall('data_export_jobs', 'download', { jobId });
        return job.file_url;
        
      } catch (error) {
        console.error('Error in downloadExport:', error);
        toast.error('Failed to download export');
        return null;
      }
    },

    // External Integrations Actions
    fetchIntegrations: async () => {
      set({ isLoadingIntegrations: true });
      
      try {
        const { data, error } = await supabase
          .from('external_integrations')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching integrations:', error);
          toast.error('Failed to load integrations');
          return;
        }

        set({ integrations: data || [] });
        
        logApiCall('external_integrations', 'fetch', {
          integrationCount: data?.length || 0
        });
        
      } catch (error) {
        console.error('Error in fetchIntegrations:', error);
        toast.error('Failed to load integrations');
      } finally {
        set({ isLoadingIntegrations: false });
      }
    },

    addIntegration: async (integration: Partial<ExternalIntegration>) => {
      try {
        const { data, error } = await supabase
          .from('external_integrations')
          .insert(integration)
          .select()
          .single();

        if (error) {
          console.error('Error adding integration:', error);
          toast.error('Failed to add integration');
          return;
        }

        const currentIntegrations = get().integrations;
        set({ integrations: [data, ...currentIntegrations] });
        
        logApiCall('external_integrations', 'create', {
          serviceType: integration.service_type
        });
        
        toast.success('Integration added successfully');
        
      } catch (error) {
        console.error('Error in addIntegration:', error);
        toast.error('Failed to add integration');
      }
    },

    updateIntegration: async (integrationId: string, updates: Partial<ExternalIntegration>) => {
      try {
        const { data, error } = await supabase
          .from('external_integrations')
          .update({
            ...updates,
            updated_at: new Date().toISOString()
          })
          .eq('id', integrationId)
          .select()
          .single();

        if (error) {
          console.error('Error updating integration:', error);
          toast.error('Failed to update integration');
          return;
        }

        const currentIntegrations = get().integrations;
        const updatedIntegrations = currentIntegrations.map(integration => 
          integration.id === integrationId ? data : integration
        );
        set({ integrations: updatedIntegrations });
        
        logApiCall('external_integrations', 'update', { integrationId });
        toast.success('Integration updated successfully');
        
      } catch (error) {
        console.error('Error in updateIntegration:', error);
        toast.error('Failed to update integration');
      }
    },

    removeIntegration: async (integrationId: string) => {
      try {
        const { error } = await supabase
          .from('external_integrations')
          .delete()
          .eq('id', integrationId);

        if (error) {
          console.error('Error removing integration:', error);
          toast.error('Failed to remove integration');
          return;
        }

        const currentIntegrations = get().integrations;
        const filteredIntegrations = currentIntegrations.filter(integration => integration.id !== integrationId);
        set({ integrations: filteredIntegrations });
        
        logApiCall('external_integrations', 'delete', { integrationId });
        toast.success('Integration removed successfully');
        
      } catch (error) {
        console.error('Error in removeIntegration:', error);
        toast.error('Failed to remove integration');
      }
    },

    syncIntegration: async (integrationId: string) => {
      try {
        // This would trigger a sync with the external service
        await get().updateIntegration(integrationId, {
          last_sync_at: new Date().toISOString(),
          sync_success_count: get().integrations.find(i => i.id === integrationId)?.sync_success_count + 1 || 1
        });
        
        toast.success('Integration synced successfully');
        
      } catch (error) {
        console.error('Error in syncIntegration:', error);
        toast.error('Failed to sync integration');
      }
    },

    // Push Notifications Actions
    fetchPushSubscriptions: async () => {
      set({ isLoadingNotifications: true });
      
      try {
        const { data, error } = await supabase
          .from('push_subscriptions')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching push subscriptions:', error);
          toast.error('Failed to load notification settings');
          return;
        }

        set({ pushSubscriptions: data || [] });
        
      } catch (error) {
        console.error('Error in fetchPushSubscriptions:', error);
        toast.error('Failed to load notification settings');
      } finally {
        set({ isLoadingNotifications: false });
      }
    },

    registerPushSubscription: async (subscription: Partial<PushSubscription>) => {
      try {
        const { data, error } = await supabase
          .from('push_subscriptions')
          .insert(subscription)
          .select()
          .single();

        if (error) {
          console.error('Error registering push subscription:', error);
          toast.error('Failed to register for notifications');
          return;
        }

        const currentSubscriptions = get().pushSubscriptions;
        set({ pushSubscriptions: [data, ...currentSubscriptions] });
        
        toast.success('Notifications enabled successfully');
        
      } catch (error) {
        console.error('Error in registerPushSubscription:', error);
        toast.error('Failed to register for notifications');
      }
    },

    updateNotificationPreferences: async (subscriptionId: string, preferences: Partial<PushSubscription>) => {
      try {
        const { data, error } = await supabase
          .from('push_subscriptions')
          .update({
            ...preferences,
            updated_at: new Date().toISOString()
          })
          .eq('id', subscriptionId)
          .select()
          .single();

        if (error) {
          console.error('Error updating notification preferences:', error);
          toast.error('Failed to update notification preferences');
          return;
        }

        const currentSubscriptions = get().pushSubscriptions;
        const updatedSubscriptions = currentSubscriptions.map(sub => 
          sub.id === subscriptionId ? data : sub
        );
        set({ pushSubscriptions: updatedSubscriptions });
        
        toast.success('Notification preferences updated');
        
      } catch (error) {
        console.error('Error in updateNotificationPreferences:', error);
        toast.error('Failed to update notification preferences');
      }
    },

    fetchNotificationHistory: async () => {
      try {
        const { data, error } = await supabase
          .from('notification_history')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(50);

        if (error) {
          console.error('Error fetching notification history:', error);
          return;
        }

        set({ notificationHistory: data || [] });
        
      } catch (error) {
        console.error('Error in fetchNotificationHistory:', error);
      }
    },

    // User Collaborations Actions
    fetchCollaborations: async () => {
      set({ isLoadingCollaborations: true });
      
      try {
        const { data, error } = await supabase
          .from('user_collaborations')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching collaborations:', error);
          toast.error('Failed to load collaborations');
          return;
        }

        set({ collaborations: data || [] });
        
      } catch (error) {
        console.error('Error in fetchCollaborations:', error);
        toast.error('Failed to load collaborations');
      } finally {
        set({ isLoadingCollaborations: false });
      }
    },

    inviteCollaborator: async (animalId: string, email: string, role: string, permissions: string[]) => {
      try {
        // This would send an invitation email and create a collaboration record
        // For now, we'll create a mock collaboration
        const invitationToken = Math.random().toString(36).substring(2, 15);
        
        const { data, error } = await supabase
          .from('user_collaborations')
          .insert({
            animal_id: animalId,
            role: role,
            permissions: permissions,
            invitation_token: invitationToken,
            status: 'pending'
          })
          .select()
          .single();

        if (error) {
          console.error('Error inviting collaborator:', error);
          toast.error('Failed to send invitation');
          return;
        }

        await get().fetchCollaborations();
        toast.success('Invitation sent successfully');
        
      } catch (error) {
        console.error('Error in inviteCollaborator:', error);
        toast.error('Failed to send invitation');
      }
    },

    acceptCollaboration: async (invitationToken: string) => {
      try {
        const { data, error } = await supabase
          .from('user_collaborations')
          .update({
            status: 'active',
            accepted_at: new Date().toISOString()
          })
          .eq('invitation_token', invitationToken)
          .select()
          .single();

        if (error) {
          console.error('Error accepting collaboration:', error);
          toast.error('Failed to accept invitation');
          return;
        }

        await get().fetchCollaborations();
        toast.success('Invitation accepted successfully');
        
      } catch (error) {
        console.error('Error in acceptCollaboration:', error);
        toast.error('Failed to accept invitation');
      }
    },

    updateCollaborationRole: async (collaborationId: string, role: string, permissions: string[]) => {
      try {
        const { data, error } = await supabase
          .from('user_collaborations')
          .update({
            role: role,
            permissions: permissions,
            updated_at: new Date().toISOString()
          })
          .eq('id', collaborationId)
          .select()
          .single();

        if (error) {
          console.error('Error updating collaboration role:', error);
          toast.error('Failed to update collaboration');
          return;
        }

        const currentCollaborations = get().collaborations;
        const updatedCollaborations = currentCollaborations.map(collab => 
          collab.id === collaborationId ? data : collab
        );
        set({ collaborations: updatedCollaborations });
        
        toast.success('Collaboration updated successfully');
        
      } catch (error) {
        console.error('Error in updateCollaborationRole:', error);
        toast.error('Failed to update collaboration');
      }
    },

    removeCollaboration: async (collaborationId: string) => {
      try {
        const { error } = await supabase
          .from('user_collaborations')
          .delete()
          .eq('id', collaborationId);

        if (error) {
          console.error('Error removing collaboration:', error);
          toast.error('Failed to remove collaboration');
          return;
        }

        const currentCollaborations = get().collaborations;
        const filteredCollaborations = currentCollaborations.filter(collab => collab.id !== collaborationId);
        set({ collaborations: filteredCollaborations });
        
        toast.success('Collaboration removed successfully');
        
      } catch (error) {
        console.error('Error in removeCollaboration:', error);
        toast.error('Failed to remove collaboration');
      }
    },

    // Performance Actions
    fetchPerformanceMetrics: async (metricType?: string) => {
      set({ isLoadingMetrics: true });
      
      try {
        let query = supabase
          .from('performance_metrics')
          .select('*')
          .order('recorded_at', { ascending: false })
          .limit(100);
        
        if (metricType) {
          query = query.eq('metric_type', metricType);
        }
        
        const { data, error } = await query;

        if (error) {
          console.error('Error fetching performance metrics:', error);
          return;
        }

        set({ performanceMetrics: data || [] });
        
      } catch (error) {
        console.error('Error in fetchPerformanceMetrics:', error);
      } finally {
        set({ isLoadingMetrics: false });
      }
    },

    recordPerformanceMetric: async (metric: any) => {
      try {
        await supabase
          .from('performance_metrics')
          .insert(metric);
        
      } catch (error) {
        console.error('Error recording performance metric:', error);
      }
    },

    // Utility Actions
    clearPlatformData: () => {
      set({
        iotDevices: [],
        exportJobs: [],
        integrations: [],
        pushSubscriptions: [],
        notificationHistory: [],
        collaborations: [],
        performanceMetrics: [],
        isLoadingDevices: false,
        isLoadingExports: false,
        isLoadingIntegrations: false,
        isLoadingNotifications: false,
        isLoadingCollaborations: false,
        isLoadingMetrics: false
      });
    }
  }),
  {
    name: `platform-store-2b237ff5-0e25-49db-b0c2-56f261d0a573`,
    storage: createJSONStorage(() => AsyncStorage),
    partialize: (state) => ({
      // Only persist non-sensitive data
      iotDevices: state.iotDevices,
      exportJobs: state.exportJobs,
      integrations: state.integrations.map(integration => ({
        ...integration,
        // Remove sensitive data from persistence
        api_key_encrypted: undefined,
        access_token_encrypted: undefined,
        refresh_token_encrypted: undefined
      })),
      pushSubscriptions: state.pushSubscriptions,
      collaborations: state.collaborations
    })
  }
));

// Helper functions for platform optimization
export const getDeviceStatusColor = (status: string): string => {
  switch (status) {
    case 'active': return '#10B981';
    case 'inactive': return '#6B7280';
    case 'error': return '#EF4444';
    case 'maintenance': return '#F59E0B';
    default: return '#6B7280';
  }
};

export const getExportStatusColor = (status: string): string => {
  switch (status) {
    case 'completed': return '#10B981';
    case 'processing': return '#F59E0B';
    case 'pending': return '#6B7280';
    case 'failed': return '#EF4444';
    default: return '#6B7280';
  }
};

export const getIntegrationStatusColor = (status: string): string => {
  switch (status) {
    case 'active': return '#10B981';
    case 'inactive': return '#6B7280';
    case 'error': return '#EF4444';
    case 'expired': return '#F59E0B';
    default: return '#6B7280';
  }
};

export const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return 'N/A';
  
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

export const formatBatteryLevel = (level: number | undefined): string => {
  if (level === undefined || level === null) return 'N/A';
  return `${level}%`;
};

export const formatSignalStrength = (strength: number | undefined): string => {
  if (strength === undefined || strength === null) return 'N/A';
  
  if (strength >= 80) return 'Excellent';
  if (strength >= 60) return 'Good';
  if (strength >= 40) return 'Fair';
  return 'Poor';
};