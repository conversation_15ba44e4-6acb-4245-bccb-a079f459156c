
import { create } from 'zustand';
import { supabase, logApiCall } from '../supabase/client';
import { toast } from 'sonner-native';

export interface VitalRecord {
  id: string;
  animalId: string;
  temperature: number;
  heartRate?: number;
  respirationRate?: number;
  weight?: number;
  notes?: string;
  timestamp: string;
  isFromDevice: boolean;
  isLive: boolean;
}

interface VitalsStore {
  vitals: VitalRecord[];
  isLoading: boolean;
  error: string | null;
  fetchVitals: () => Promise<void>;
  addVital: (vital: Omit<VitalRecord, 'id' | 'timestamp'>) => Promise<void>;
  updateVital: (id: string, vital: Partial<VitalRecord>) => Promise<void>;
  deleteVital: (id: string) => Promise<void>;
  getVitalsByAnimalId: (animalId: string) => VitalRecord[];
}

export const useVitalsStore = create<VitalsStore>((set, get) => ({
  vitals: [],
  isLoading: false,
  error: null,
  
  fetchVitals: async () => {
    set({ isLoading: true, error: null });
    
    try {
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return;
      }
      
      const { data, error } = await supabase
        .from('vitals')
        .select('*')
        .eq('user_id', user.id) // Defense-in-depth: explicit user filtering
        .order('timestamp', { ascending: false });
      
      logApiCall('fetchVitals', data, error);
      
      if (error) {
        console.error('Error fetching vitals:', error);
        throw new Error(`Failed to fetch vital records: ${error.message}`);
      }
      
      console.log(`API Success (fetchVitals): Retrieved ${data?.length || 0} records`);
      
      // Convert database fields to match our interface
      const formattedData = data ? data.map((item: any) => ({
        id: item.id,
        animalId: item.animal_id,
        temperature: item.temperature,
        heartRate: item.heart_rate,
        respirationRate: item.respiration_rate,
        weight: item.weight,
        notes: item.notes,
        timestamp: item.timestamp,
        isFromDevice: item.is_from_device,
        isLive: item.is_live
      })) : [];
      
      set({ vitals: formattedData, isLoading: false });
    } catch (error: any) {
      console.error('Error fetching vitals:', error);
      const errorMessage = error.message || 'Failed to fetch vital records';
      set({ error: errorMessage, isLoading: false });
      // Note: No toast for fetch errors as they're usually silent background operations
      throw error;
    }
  },
  
  addVital: async (vital) => {
    set({ isLoading: true, error: null });
    
    try {
      // Check if this is a device-recorded vital
      if (!vital.isFromDevice) {
        const deviceError = 'Vitals can only be recorded by connected devices';
        toast.error(deviceError);
        throw new Error(deviceError);
      }
      
      // Convert to database field names
      const dbVital = {
        animal_id: vital.animalId,
        temperature: vital.temperature,
        heart_rate: vital.heartRate,
        respiration_rate: vital.respirationRate,
        weight: vital.weight,
        notes: vital.notes,
        is_from_device: vital.isFromDevice,
        is_live: vital.isLive || false
      };
      
      const { data, error } = await supabase
        .from('vitals')
        .insert(dbVital)
        .select();
      
      logApiCall('addVital', data, error);
      
      if (error) {
        console.error('Error adding vital:', error);
        throw new Error(`Failed to add vital record: ${error.message}`);
      }
      
      if (!data || data.length === 0) {
        throw new Error('No data returned from vital insert');
      }
      
      // Add to local state
      const newVital = {
        ...vital,
        id: data[0].id,
        timestamp: data[0].timestamp || new Date().toISOString()
      };
      
      set(state => ({
        vitals: [...state.vitals, newVital],
        isLoading: false
      }));
      
      toast.success('Vital record added successfully!');
    } catch (error: any) {
      console.error('Error adding vital:', error);
      const errorMessage = error.message || 'Failed to add vital record';
      set({ error: errorMessage, isLoading: false });
      if (!error.message.includes('connected devices')) {
        toast.error(errorMessage);
      }
      throw error;
    }
  },
  
  updateVital: async (id, vital) => {
    set({ isLoading: true, error: null });
    
    try {
      // Convert to database field names
      const dbVital: any = {};
      
      if (vital.animalId) dbVital.animal_id = vital.animalId;
      if (vital.temperature !== undefined) dbVital.temperature = vital.temperature;
      if (vital.heartRate !== undefined) dbVital.heart_rate = vital.heartRate;
      if (vital.respirationRate !== undefined) dbVital.respiration_rate = vital.respirationRate;
      if (vital.weight !== undefined) dbVital.weight = vital.weight;
      if (vital.notes !== undefined) dbVital.notes = vital.notes;
      if (vital.isFromDevice !== undefined) dbVital.is_from_device = vital.isFromDevice;
      if (vital.isLive !== undefined) dbVital.is_live = vital.isLive;
      
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return;
      }
      
      const { error } = await supabase
        .from('vitals')
        .update(dbVital)
        .eq('id', id)
        .eq('user_id', user.id); // Defense-in-depth: ensure user owns record
      
      logApiCall('updateVital', { id, ...dbVital }, error);
      
      if (error) {
        console.error('Error updating vital:', error);
        throw new Error(`Failed to update vital record: ${error.message}`);
      }
      
      set(state => ({
        vitals: state.vitals.map(v => 
          v.id === id ? { ...v, ...vital } : v
        ),
        isLoading: false
      }));
      
      toast.success('Vital record updated successfully!');
    } catch (error: any) {
      console.error('Error updating vital:', error);
      const errorMessage = error.message || 'Failed to update vital record';
      set({ error: errorMessage, isLoading: false });
      toast.error(errorMessage);
      throw error;
    }
  },
  
  deleteVital: async (id) => {
    set({ isLoading: true, error: null });
    
    try {
      // Get current user for defense-in-depth filtering
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        set({ isLoading: false, error: 'User not authenticated' });
        return;
      }
      
      const { error } = await supabase
        .from('vitals')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id); // Defense-in-depth: ensure user owns record
      
      logApiCall('deleteVital', { id }, error);
      
      if (error) {
        console.error('Error deleting vital:', error);
        throw new Error(`Failed to delete vital record: ${error.message}`);
      }
      
      set(state => ({
        vitals: state.vitals.filter(v => v.id !== id),
        isLoading: false
      }));
      
      toast.success('Vital record deleted successfully!');
    } catch (error: any) {
      console.error('Error deleting vital:', error);
      const errorMessage = error.message || 'Failed to delete vital record';
      set({ error: errorMessage, isLoading: false });
      toast.error(errorMessage);
      throw error;
    }
  },
  
  getVitalsByAnimalId: (animalId) => {
    const { vitals } = get();
    return vitals.filter(vital => vital.animalId === animalId);
  }
}));
