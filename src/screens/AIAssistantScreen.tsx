import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { 
  Brain, 
  Heart, 
  Target, 
  Lightbulb, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  TrendingUp
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalStore } from '../store/animalStore';
import { useAIStore } from '../store/aiStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import { toast } from 'sonner-native';
import { format } from 'date-fns';
import AIGuidanceComponent from '../components/ai/AIGuidanceComponent';
import { useAIDataDetection } from '../hooks/useAIDataDetection';

type AIAssistantScreenRouteProp = RouteProp<HomeStackParamList, 'AIAssistant'>;
type AIAssistantScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'AIAssistant'>;

const AIAssistantScreen = () => {
  const navigation = useNavigation<AIAssistantScreenNavigationProp>();
  const route = useRoute<AIAssistantScreenRouteProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  // Safe parameter extraction with fallback
  const animalId = route.params?.animalId;
  
  // Early return if no animalId provided
  if (!animalId) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('aiAssistant')} showBackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {t('animalNotSelected') || 'Please select an animal first'}
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  
  const animals = useAnimalStore(state => state.animals);
  const animal = animals.find(a => a.id === animalId);
  
  const {
    healthAssessments,
    trainingPlans,
    readinessScores,
    coachingTips,
    dehydrationAnalyses,
    isLoadingAnalysis,
    isLoadingDehydrationAnalysis,
    requestAIAnalysis,
    requestDehydrationAnalysis,
    fetchHealthAssessments,
    fetchTrainingPlans,
    fetchReadinessScores,
    fetchCoachingTips,
    markTipAsRead,
    getLatestHealthAssessment,
    getLatestTrainingPlan,
    getLatestReadinessScore,
    getLatestDehydrationAnalysis,
    getUnreadTips
  } = useAIStore();
  
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const latestHealthAssessment = getLatestHealthAssessment(animalId);
  const latestTrainingPlan = getLatestTrainingPlan(animalId);
  const latestReadinessScore = getLatestReadinessScore(animalId);
  const latestDehydrationAnalysis = getLatestDehydrationAnalysis(animalId);
  const unreadTips = getUnreadTips(animalId);
  
  // AI Data Detection
  const {
    hasVitalsData,
    hasFeedingData,
    hasMedicationData,
    hasAIData,
    isReadyForAI,
    hasMinimalData,
    dataQualityScore,
    recentDataCounts,
    totalDataCounts,
    recommendations
  } = useAIDataDetection();
  
  // Calculate guidance state
  const shouldShowGuidance = !isReadyForAI || dataQualityScore < 50;
  const missingDataTypes = [];
  if (!hasVitalsData) missingDataTypes.push('vitals');
  if (!hasFeedingData) missingDataTypes.push('feeding');
  const dataCompleteness = dataQualityScore;
  const hasAnyAIInsights = hasAIData;
  
  useEffect(() => {
    loadAIData();
  }, [animalId]);
  
  const loadAIData = async () => {
    try {
      await Promise.all([
        fetchHealthAssessments(animalId),
        fetchTrainingPlans(animalId),
        fetchReadinessScores(animalId),
        fetchCoachingTips(animalId),
        requestDehydrationAnalysis(animalId, 7),
      ]);
    } catch (error) {
      console.error('Error loading AI data:', error);
    }
  };
  
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadAIData();
    setIsRefreshing(false);
  };
  
  const handleRequestAnalysis = async () => {
    try {
      await requestAIAnalysis(animalId);
    } catch (error) {
      // Error is already handled in the store
    }
  };
  
  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'high':
      case 'critical':
        return '#EF4444';
      case 'medium':
      case 'moderate':
        return '#F59E0B';
      case 'low':
      case 'normal':
        return '#10B981';
      default:
        return colors.textLight;
    }
  };
  
  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return '#EF4444';
      case 'medium':
        return '#F59E0B';
      case 'low':
      case 'normal':
        return '#10B981';
      default:
        return colors.textLight;
    }
  };
  
  if (!animal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('aiAssistant')} showBackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>{t('animalNotFound')}</Text>
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header title="AI Assistant" showBackButton={true} />
      
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* AI Guidance or Animal Info */}
        {shouldShowGuidance ? (
          <AIGuidanceComponent 
            animal={animal} 
            missingDataTypes={missingDataTypes}
          />
        ) : (
          <Card style={styles.animalCard}>
            <View style={styles.animalInfo}>
              <Text style={[styles.animalName, { color: colors.text }]}>{animal.name}</Text>
              <Text style={[styles.animalDetails, { color: colors.textLight }]}>
                {animal.breed} • {animal.age} years old
              </Text>
              {dataCompleteness < 100 && (
                <View style={styles.completenessContainer}>
                  <Text style={[styles.completenessText, { color: colors.textLight }]}>
                    {t('dataCompleteness')}: {dataCompleteness}%
                  </Text>
                  <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                    <View 
                      style={[
                        styles.progressFill, 
                        { 
                          backgroundColor: colors.primary,
                          width: `${dataCompleteness}%`
                        }
                      ]} 
                    />
                  </View>
                </View>
              )}
            </View>
            
            <TouchableOpacity
              style={[
                styles.analysisButton,
                { backgroundColor: colors.primary },
                isLoadingAnalysis && styles.analysisButtonDisabled
              ]}
              onPress={handleRequestAnalysis}
              disabled={isLoadingAnalysis}
            >
              {isLoadingAnalysis ? (
                <ActivityIndicator color="#FFFFFF" size="small" />
              ) : (
                <Brain size={20} color="#FFFFFF" />
              )}
              <Text style={styles.analysisButtonText}>
                {isLoadingAnalysis ? t('analyzing') : t('getAIAnalysis')}
              </Text>
            </TouchableOpacity>
          </Card>
        )}
        
        {/* Readiness Score */}
        {latestReadinessScore && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <Target size={24} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('readinessScore')}</Text>
            </View>
            
            <View style={styles.readinessContainer}>
              <View style={styles.scoreCircle}>
                <Text style={[styles.scoreValue, { color: colors.primary }]}>
                  {Math.round(latestReadinessScore.score_value * 100)}
                </Text>
                <Text style={[styles.scoreLabel, { color: colors.textLight }]}>%</Text>
              </View>
              
              <View style={styles.scoreDetails}>
                <Text style={[styles.scoreCategory, { color: colors.text }]}>
                  {latestReadinessScore.score_category}
                </Text>
                <Text style={[styles.scoreDate, { color: colors.textLight }]}>
                  {format(new Date(latestReadinessScore.generated_at), 'MMM d, yyyy')}
                </Text>
              </View>
            </View>
          </Card>
        )}
        
        {/* Health Assessment */}
        {latestHealthAssessment && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <Heart size={24} color={getSeverityColor(latestHealthAssessment.severity_level)} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('healthAssessment')}</Text>
              <View style={[
                styles.severityBadge,
                { backgroundColor: getSeverityColor(latestHealthAssessment.severity_level) }
              ]}>
                <Text style={styles.severityText}>
                  {latestHealthAssessment.severity_level}
                </Text>
              </View>
            </View>
            
            <Text style={[styles.assessmentText, { color: colors.text }]}>
              {latestHealthAssessment.assessment_text}
            </Text>
            
            <Text style={[styles.timestamp, { color: colors.textLight }]}>
              {format(new Date(latestHealthAssessment.generated_at), 'MMM d, yyyy • h:mm a')}
            </Text>
          </Card>
        )}
        
        {/* Dehydration Analysis */}
        {latestDehydrationAnalysis && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <View style={styles.dehydrationIcon}>
                <Text style={styles.dehydrationEmoji}>💧</Text>
              </View>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('hydrationAnalysis')}</Text>
              <View style={[
                styles.statusBadge,
                { backgroundColor: getSeverityColor(latestDehydrationAnalysis.overall_status) }
              ]}>
                <Text style={styles.statusText}>
                  {latestDehydrationAnalysis.overall_status}
                </Text>
              </View>
            </View>
            
            <View style={styles.dehydrationMetrics}>
              <View style={styles.metricItem}>
                <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('avgHydration')}</Text>
                <Text style={[styles.metricValue, { color: colors.text }]}>
                  {latestDehydrationAnalysis.average_hydration.toFixed(1)}%
                </Text>
              </View>
              <View style={styles.metricItem}>
                <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('trend')}</Text>
                <Text style={[styles.metricValue, { color: colors.text }]}>
                  {latestDehydrationAnalysis.trend === 'improving' ? '📈' : 
                   latestDehydrationAnalysis.trend === 'declining' ? '📉' : '➡️'} 
                  {latestDehydrationAnalysis.trend}
                </Text>
              </View>
              <View style={styles.metricItem}>
                <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('variability')}</Text>
                <Text style={[styles.metricValue, { color: colors.text }]}>
                  {latestDehydrationAnalysis.hydration_variance.toFixed(1)}
                </Text>
              </View>
            </View>
            
            {/* AI Alerts */}
            {latestDehydrationAnalysis.alerts.length > 0 && (
              <View style={styles.alertsSection}>
                <Text style={[styles.alertsTitle, { color: colors.text }]}>⚠️ Alerts</Text>
                {latestDehydrationAnalysis.alerts.slice(0, 2).map((alert, index) => (
                  <View key={index} style={[
                    styles.alertItem,
                    { backgroundColor: getSeverityColor(alert.severity) + '20' }
                  ]}>
                    <Text style={[styles.alertText, { color: getSeverityColor(alert.severity) }]}>
                      {alert.message}
                    </Text>
                  </View>
                ))}
              </View>
            )}
            
            {/* Top Recommendations */}
            {latestDehydrationAnalysis.recommendations.length > 0 && (
              <View style={styles.recommendationsSection}>
                <Text style={[styles.recommendationsTitle, { color: colors.text }]}>💡 Recommendations</Text>
                {latestDehydrationAnalysis.recommendations.slice(0, 3).map((rec, index) => (
                  <Text key={index} style={[styles.recommendationText, { color: colors.textLight }]}>
                    • {rec}
                  </Text>
                ))}
              </View>
            )}
            
            <Text style={[styles.timestamp, { color: colors.textLight }]}>
              {format(new Date(latestDehydrationAnalysis.generated_at), 'MMM d, yyyy • h:mm a')}
            </Text>
          </Card>
        )}
        
        {/* Training Plan */}
        {latestTrainingPlan && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <TrendingUp size={24} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('trainingPlan')}</Text>
              {latestTrainingPlan.plan_type && (
                <View style={[styles.typeBadge, { backgroundColor: colors.secondary }]}>
                  <Text style={[styles.typeText, { color: colors.card }]}>
                    {latestTrainingPlan.plan_type}
                  </Text>
                </View>
              )}
            </View>
            
            <Text style={[styles.planText, { color: colors.text }]}>
              {latestTrainingPlan.plan_text}
            </Text>
            
            <Text style={[styles.timestamp, { color: colors.textLight }]}>
              {format(new Date(latestTrainingPlan.generated_at), 'MMM d, yyyy • h:mm a')}
            </Text>
          </Card>
        )}
        
        {/* Coaching Tips */}
        {unreadTips.length > 0 && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <Lightbulb size={24} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('coachingTips')}</Text>
              <View style={[styles.countBadge, { backgroundColor: colors.primary }]}>
                <Text style={styles.countText}>{unreadTips.length}</Text>
              </View>
            </View>
            
            {unreadTips.slice(0, 3).map((tip) => (
              <TouchableOpacity
                key={tip.tip_id}
                style={[styles.tipItem, { borderBottomColor: colors.border }]}
                onPress={() => markTipAsRead(tip.tip_id)}
              >
                <View style={styles.tipHeader}>
                  <View style={[
                    styles.priorityDot,
                    { backgroundColor: getPriorityColor(tip.priority) }
                  ]} />
                  <Text style={[styles.tipCategory, { color: colors.textLight }]}>
                    {tip.category}
                  </Text>
                  <Clock size={12} color={colors.textLight} />
                  <Text style={[styles.tipDate, { color: colors.textLight }]}>
                    {format(new Date(tip.generated_at), 'MMM d')}
                  </Text>
                </View>
                
                <Text style={[styles.tipText, { color: colors.text }]}>
                  {tip.tip_text}
                </Text>
              </TouchableOpacity>
            ))}
            
            {unreadTips.length > 3 && (
              <Text style={[styles.moreTips, { color: colors.textLight }]}>
                +{unreadTips.length - 3} more tips
              </Text>
            )}
          </Card>
        )}
        
        {/* Empty State - Only show if we have data but no insights */}
        {!shouldShowGuidance && !hasAnyAIInsights && (
          <Card style={styles.emptyState}>
            <Brain size={48} color={colors.textLight} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>{t('readyForAIAnalysis')}</Text>
            <Text style={[styles.emptyDescription, { color: colors.textLight }]}>
              Great! You have data for {animal.name}. Request an AI analysis to get personalized insights.
            </Text>
            
            <TouchableOpacity
              style={[styles.logSessionButton, { backgroundColor: colors.primary }]}
              onPress={handleRequestAnalysis}
              disabled={isLoadingAnalysis}
            >
              {isLoadingAnalysis ? (
                <ActivityIndicator color="#FFFFFF" size="small" />
              ) : (
                <Brain size={20} color="#FFFFFF" />
              )}
              <Text style={[styles.logSessionText, { color: '#FFFFFF' }]}>
                {isLoadingAnalysis ? 'Analyzing...' : 'Get AI Analysis'}
              </Text>
            </TouchableOpacity>
          </Card>
        )}
        
        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 20,
    textAlign: 'center',
  },
  backButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  animalCard: {
    marginBottom: 16,
  },
  animalInfo: {
    marginBottom: 16,
  },
  animalName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  animalDetails: {
    fontSize: 14,
  },
  analysisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  analysisButtonDisabled: {
    opacity: 0.6,
  },
  analysisButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
    flex: 1,
  },
  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  severityText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  countBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  countText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  readinessContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#E5E7EB',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  scoreValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 12,
  },
  scoreDetails: {
    flex: 1,
  },
  scoreCategory: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  scoreDate: {
    fontSize: 14,
  },
  assessmentText: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 8,
  },
  planText: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 8,
  },
  timestamp: {
    fontSize: 12,
  },
  tipItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  tipHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  tipCategory: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
    marginRight: 8,
  },
  tipDate: {
    fontSize: 12,
    marginLeft: 4,
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
  },
  moreTips: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  logSessionButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  logSessionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  bottomPadding: {
    height: 20,
  },
  
  // Dehydration Analysis Styles
  dehydrationIcon: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dehydrationEmoji: {
    fontSize: 20,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  dehydrationMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
  },
  metricItem: {
    alignItems: 'center',
    flex: 1,
  },
  metricLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  alertsSection: {
    marginBottom: 16,
  },
  alertsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  alertItem: {
    padding: 8,
    borderRadius: 6,
    marginBottom: 4,
  },
  alertText: {
    fontSize: 12,
    fontWeight: '500',
  },
  recommendationsSection: {
    marginBottom: 16,
  },
  recommendationsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  recommendationText: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 4,
  },
  completenessContainer: {
    marginTop: 8,
  },
  completenessText: {
    fontSize: 12,
    marginBottom: 4,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
});

export default AIAssistantScreen;