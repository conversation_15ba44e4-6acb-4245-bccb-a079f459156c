
import React, { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { toast } from 'sonner-native';
import { useUserStore } from '../store/userStore';
import PlanSelectionView from '../components/subscription/PlanSelectionView';
import PaymentProcessingView from '../components/subscription/PaymentProcessingView';

const SubscriptionScreen = () => {
  const navigation = useNavigation();
  const [selectedPlan, setSelectedPlan] = useState<'monthly' | 'yearly' | null>(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  
  // Get user from store
  const { user, updateSubscription } = useUserStore();
  
  const handleSubscribe = () => {
    if (!selectedPlan) {
      toast.error('Please select a plan first');
      return;
    }
    setShowPaymentForm(true);
  };
  
  const handlePaymentSuccess = () => {
    // Payment processing hook already handles subscription update
    // This callback just handles UI updates
    setShowPaymentForm(false);
    navigation.goBack();
  };
  
  const handlePaymentCancel = () => {
    setShowPaymentForm(false);
  };
  
  if (showPaymentForm) {
    return (
      <PaymentProcessingView
        selectedPlan={selectedPlan}
        onCancel={handlePaymentCancel}
        onPaymentSuccess={handlePaymentSuccess}
      />
    );
  }
  
  return (
    <PlanSelectionView
      selectedPlan={selectedPlan}
      onPlanSelect={setSelectedPlan}
      onSubscribe={handleSubscribe}
    />
  );
};



export default SubscriptionScreen;
