
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Settings,
  Shield,
  LogOut,
  HelpCircle,
  Mail,
  Crown
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';
import { useUserStore } from '../store/userStore';
import { ProfileStackParamList } from '../navigation';
import { toast } from 'sonner-native';
import ProfileHeader from '../components/profile/ProfileHeader';
import SubscriptionInfoCard from '../components/profile/SubscriptionInfoCard';
import ProfileSection from '../components/profile/ProfileSection';
import ProfileMenuItem from '../components/profile/ProfileMenuItem';

type ProfileScreenNavigationProp = NativeStackNavigationProp<ProfileStackParamList, 'Profile'>;

const ProfileScreen = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const { colors, isDarkMode, toggleTheme } = useTheme();
  const { t } = useLanguage();
  const { signOut } = useAuth();
  const { user, isLoading, fetchProfile } = useUserStore();
  
  const [isSigningOut, setIsSigningOut] = useState(false);
  
  // Refresh profile data when screen is focused
  useEffect(() => {
    fetchProfile();
  }, []);
  
  const handleEditProfile = () => {
    navigation.navigate('EditProfile');
  };
  
  const handleAdvancedSettings = () => {
    navigation.navigate('AdvancedSettings');
  };
  
  const handlePrivacySettings = () => {
    navigation.navigate('PrivacySettings');
  };
  
  const handleFAQ = () => {
    navigation.navigate('FAQ');
  };
  
  const handleContact = () => {
    navigation.navigate('Contact');
  };
  
  const handleSubscription = () => {
    navigation.navigate('Subscription');
  };
  
  const handleMfaSettings = () => {
    navigation.navigate('MfaScreen');
  };
  
  const handleSignOut = async () => {
    Alert.alert(
      t('profileSignOut'),
      t('profileSignOutConfirm'),
      [
        { text: t('profileCancel'), style: 'cancel' },
        { 
          text: t('profileSignOut'), 
          style: 'destructive',
          onPress: async () => {
            setIsSigningOut(true);
            try {
              await signOut();
              // Navigation will be handled by the auth state change in App.tsx
            } catch (error) {
              console.error('Error signing out:', error);
              toast.error(t('profileSignOutFailed'));
              setIsSigningOut(false);
            }
          }
        }
      ]
    );
  };
  
  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <ProfileHeader 
          user={user}
          onEditProfilePress={handleEditProfile}
        />
        
        {user?.isPremium && (
          <SubscriptionInfoCard 
            subscriptionType={user.subscriptionType}
            subscriptionEndDate={user.subscriptionEndDate}
            onManageSubscriptionPress={handleSubscription}
          />
        )}
        
        <ProfileSection title={t('profileSettings')}>
          <ProfileMenuItem 
            icon={Settings}
            text={t('profileAdvancedSettings')}
            onPress={handleAdvancedSettings}
          />
          
          <ProfileMenuItem 
            icon={Shield}
            text={t('profilePrivacySettings')}
            onPress={handlePrivacySettings}
          />
          
          <ProfileMenuItem 
            icon={Shield}
            text={t('profileMfaSettings', 'Two-Factor Authentication')}
            onPress={handleMfaSettings}
            iconColor={colors.accent1}
          />
          
          {!user?.isPremium && (
            <ProfileMenuItem 
              icon={Crown}
              text={t('profileUpgradeToPremium')}
              onPress={handleSubscription}
              iconColor={colors.accent1}
            />
          )}
        </ProfileSection>
        
        <ProfileSection title={t('profileHelpSupport')}>
          <ProfileMenuItem 
            icon={HelpCircle}
            text={t('profileFAQ')}
            onPress={handleFAQ}
            iconColor={colors.secondary}
          />
          
          <ProfileMenuItem 
            icon={Mail}
            text={t('profileContactSupport')}
            onPress={handleContact}
            iconColor={colors.secondary}
          />
        </ProfileSection>
        
        <TouchableOpacity
          style={[styles.signOutButton, { backgroundColor: colors.error + '20' }]}
          onPress={handleSignOut}
          disabled={isSigningOut}
        >
          {isSigningOut ? (
            <ActivityIndicator size="small" color={colors.error} />
          ) : (
            <>
              <LogOut size={20} color={colors.error} />
              <Text style={[styles.signOutText, { color: colors.error }]}>{t('profileSignOut')}</Text>
            </>
          )}
        </TouchableOpacity>
        
        <Text style={[styles.versionText, { color: colors.textLight }]}>
          {t('profileVersion')} 1.0.0
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 24,
    gap: 8,
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '500',
  },
  versionText: {
    textAlign: 'center',
    fontSize: 12,
  },
});

export default ProfileScreen;
