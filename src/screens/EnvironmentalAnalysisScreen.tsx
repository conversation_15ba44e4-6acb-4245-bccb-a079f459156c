import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Thermometer,
  Droplets,
  Wind,
  Sun,
  Cloud,
  Gauge,
  RefreshCw,
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalStore } from '../store/animalStore';
import { useEnvironmentalStore } from '../store/environmentalStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import { toast } from 'sonner-native';
import { format } from 'date-fns';

type EnvironmentalAnalysisScreenRouteProp = RouteProp<HomeStackParamList, 'EnvironmentalAnalysisScreen'>;
type EnvironmentalAnalysisScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'EnvironmentalAnalysisScreen'>;

const EnvironmentalAnalysisScreen = () => {
  const route = useRoute<EnvironmentalAnalysisScreenRouteProp>();
  const navigation = useNavigation<EnvironmentalAnalysisScreenNavigationProp>();
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  // Safe destructuring with fallback for missing params
  const animalId = route.params?.animalId;
  
  // Early return if no animalId provided
  if (!animalId) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
        <Header title={t('environmentalAnalysis')} showBackButton />
        <View style={styles.centerContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>{t('missingAnimalId')}</Text>
        </View>
      </View>
    );
  }
  
  const { animals } = useAnimalStore();
  const {
    environmentalData,
    impactAnalyses,
    optimizations,
    isLoadingEnvironmentalData,
    isLoadingImpactAnalysis,
    isLoadingOptimizations,
    fetchEnvironmentalData,
    requestEnvironmentalAnalysis,
    fetchImpactAnalyses,
    fetchOptimizations,
    updateOptimizationStatus
  } = useEnvironmentalStore();
  
  const [refreshing, setRefreshing] = useState(false);
  
  const animal = animals.find(a => a.id === animalId);
  const latestAnalysis = impactAnalyses.length > 0 ? impactAnalyses[0] : null;
  const latestEnvironmentalData = environmentalData.length > 0 ? environmentalData[0] : null;
  const pendingOptimizations = optimizations.filter(opt => opt.status === 'pending');
  const highPriorityOptimizations = optimizations.filter(opt => opt.priority_level === 'high' || opt.priority_level === 'critical');
  
  useEffect(() => {
    loadData();
  }, [animalId]);
  
  const loadData = async () => {
    await Promise.all([
      fetchEnvironmentalData(animalId),
      fetchImpactAnalyses(animalId),
      fetchOptimizations(animalId)
    ]);
  };
  
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };
  
  const handleAnalyzeEnvironment = async () => {
    const analysis = await requestEnvironmentalAnalysis(animalId);
    if (analysis) {
      // Refresh optimizations after analysis
      await fetchOptimizations(animalId);
    }
  };
  
  const handleImplementOptimization = async (optimizationId: string) => {
    await updateOptimizationStatus(optimizationId, 'in_progress');
  };
  
  const handleCompleteOptimization = async (optimizationId: string) => {
    await updateOptimizationStatus(optimizationId, 'completed', 85); // Default effectiveness score
  };
  
  const getEnvironmentalRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return colors.success;
      case 'moderate': return colors.warning;
      case 'high': return colors.error;
      case 'critical': return colors.error;
      default: return colors.textLight;
    }
  };
  
  const getTemperatureStatus = (temp: number | undefined) => {
    if (!temp) return { status: 'Unknown', color: colors.textLight };
    
    if (temp < 10) return { status: 'Very Cold', color: colors.info };
    if (temp < 18) return { status: 'Cold', color: colors.info };
    if (temp <= 24) return { status: t('optimal'), color: colors.success };
    if (temp <= 30) return { status: 'Warm', color: colors.warning };
    return { status: 'Hot', color: colors.error };
  };
  
  const getHumidityStatus = (humidity: number | undefined) => {
    if (!humidity) return { status: 'Unknown', color: colors.textLight };
    
    if (humidity < 30) return { status: 'Too Dry', color: colors.error };
    if (humidity <= 60) return { status: t('optimal'), color: colors.success };
    if (humidity <= 70) return { status: 'High', color: colors.warning };
    return { status: 'Too Humid', color: colors.error };
  };
  
  const getAirQualityStatus = (aqi: number | undefined) => {
    if (!aqi) return { status: 'Unknown', color: colors.textLight };
    
    if (aqi <= 50) return { status: t('good'), color: colors.success };
    if (aqi <= 100) return { status: t('moderate'), color: colors.warning };
    if (aqi <= 150) return { status: 'Unhealthy for Sensitive', color: colors.error };
    return { status: t('hazardous'), color: colors.error };
  };
  
  if (!animal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
        <Header title={t('environmentalAnalysis')} showBackButton />
        <View style={styles.centerContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>Animal not found</Text>
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      <Header title={t('environmentalAnalysis')} showBackButton />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Animal Info Header */}
        <Card style={styles.animalHeader}>
          <View style={styles.animalInfo}>
            <Text style={[styles.animalName, { color: colors.text }]}>{animal.name}</Text>
            <Text style={[styles.animalBreed, { color: colors.textLight }]}>
              {animal.breed} • {animal.species}
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.analyzeButton, { backgroundColor: colors.primary }]}
            onPress={handleAnalyzeEnvironment}
            disabled={isLoadingImpactAnalysis}
          >
            {isLoadingImpactAnalysis ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <RefreshCw size={16} color="#FFFFFF" />
            )}
            <Text style={styles.analyzeButtonText}>
              {isLoadingImpactAnalysis ? t('analyzingEnvironment') : t('analyzeEnvironment')}
            </Text>
          </TouchableOpacity>
        </Card>
        
        {/* Current Environmental Conditions */}
        {latestEnvironmentalData && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <Gauge size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Current Conditions</Text>
            </View>
            
            <View style={styles.conditionsGrid}>
              {/* Temperature */}
              <View style={styles.conditionItem}>
                <Thermometer size={24} color={getTemperatureStatus(latestEnvironmentalData.temperature_celsius).color} />
                <Text style={[styles.conditionLabel, { color: colors.textLight }]}>{t('temperature')}</Text>
                <Text style={[styles.conditionValue, { color: colors.text }]}>
                  {latestEnvironmentalData.temperature_celsius?.toFixed(1) || 'N/A'}°C
                </Text>
                <Text style={[styles.conditionStatus, { color: getTemperatureStatus(latestEnvironmentalData.temperature_celsius).color }]}>
                  {getTemperatureStatus(latestEnvironmentalData.temperature_celsius).status}
                </Text>
              </View>
              
              {/* Humidity */}
              <View style={styles.conditionItem}>
                <Droplets size={24} color={getHumidityStatus(latestEnvironmentalData.humidity_percentage).color} />
                <Text style={[styles.conditionLabel, { color: colors.textLight }]}>{t('humidity')}</Text>
                <Text style={[styles.conditionValue, { color: colors.text }]}>
                  {latestEnvironmentalData.humidity_percentage?.toFixed(1) || 'N/A'}%
                </Text>
                <Text style={[styles.conditionStatus, { color: getHumidityStatus(latestEnvironmentalData.humidity_percentage).color }]}>
                  {getHumidityStatus(latestEnvironmentalData.humidity_percentage).status}
                </Text>
              </View>
              
              {/* Air Quality */}
              <View style={styles.conditionItem}>
                <Wind size={24} color={getAirQualityStatus(latestEnvironmentalData.air_quality_index).color} />
                <Text style={[styles.conditionLabel, { color: colors.textLight }]}>{t('airQuality')}</Text>
                <Text style={[styles.conditionValue, { color: colors.text }]}>
                  {latestEnvironmentalData.air_quality_index || 'N/A'}
                </Text>
                <Text style={[styles.conditionStatus, { color: getAirQualityStatus(latestEnvironmentalData.air_quality_index).color }]}>
                  {getAirQualityStatus(latestEnvironmentalData.air_quality_index).status}
                </Text>
              </View>
              
              {/* UV Index */}
              <View style={styles.conditionItem}>
                <Sun size={24} color={colors.warning} />
                <Text style={[styles.conditionLabel, { color: colors.textLight }]}>{t('uvIndex')}</Text>
                <Text style={[styles.conditionValue, { color: colors.text }]}>
                  {latestEnvironmentalData.uv_index?.toFixed(1) || 'N/A'}
                </Text>
                <Text style={[styles.conditionStatus, { color: colors.textLight }]}>Index</Text>
              </View>
            </View>
            
            <Text style={[styles.lastUpdated, { color: colors.textLight }]}>
              Last updated: {format(new Date(latestEnvironmentalData.recorded_at), 'MMM dd, HH:mm')}
            </Text>
          </Card>
        )}
        
        {/* Environmental Impact Analysis */}
        {latestAnalysis && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <TrendingUp size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('environmentalImpact')}</Text>
            </View>
            
            <View style={styles.impactScoreContainer}>
              <View style={styles.impactScore}>
                <Text style={[styles.impactScoreValue, { color: getEnvironmentalRiskColor(latestAnalysis.environmental_risk_level) }]}>
                  {latestAnalysis.environmental_health_score}
                </Text>
                <Text style={[styles.impactScoreLabel, { color: colors.textLight }]}>Health Score</Text>
              </View>
              <View style={styles.riskLevel}>
                <Text style={[styles.riskLevelText, { color: getEnvironmentalRiskColor(latestAnalysis.environmental_risk_level) }]}>
                  {latestAnalysis.environmental_risk_level.toUpperCase()} RISK
                </Text>
              </View>
            </View>
            
            <View style={styles.impactFactors}>
              <View style={styles.impactFactor}>
                <Text style={[styles.factorLabel, { color: colors.textLight }]}>Temperature Impact</Text>
                <Text style={[styles.factorValue, { color: colors.text }]}>
                  {latestAnalysis.temperature_impact_score || 'N/A'}/100
                </Text>
              </View>
              <View style={styles.impactFactor}>
                <Text style={[styles.factorLabel, { color: colors.textLight }]}>Humidity Impact</Text>
                <Text style={[styles.factorValue, { color: colors.text }]}>
                  {latestAnalysis.humidity_impact_score || 'N/A'}/100
                </Text>
              </View>
              <View style={styles.impactFactor}>
                <Text style={[styles.factorLabel, { color: colors.textLight }]}>Air Quality Impact</Text>
                <Text style={[styles.factorValue, { color: colors.text }]}>
                  {latestAnalysis.air_quality_impact_score || 'N/A'}/100
                </Text>
              </View>
            </View>
            
            <Text style={[styles.analysisDate, { color: colors.textLight }]}>
              Analysis from {format(new Date(latestAnalysis.created_at), 'MMM dd, yyyy')}
            </Text>
          </Card>
        )}
        
        {/* High Priority Optimizations */}
        {highPriorityOptimizations.length > 0 && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <AlertTriangle size={20} color={colors.error} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Priority Optimizations</Text>
            </View>
            
            {highPriorityOptimizations.slice(0, 3).map((optimization) => (
              <View key={optimization.id} style={[styles.optimizationItem, { borderColor: colors.border }]}>
                <View style={styles.optimizationHeader}>
                  <Text style={[styles.optimizationTitle, { color: colors.text }]}>
                    {optimization.recommendation_title}
                  </Text>
                  <View style={[styles.priorityBadge, { backgroundColor: colors.error }]}>
                    <Text style={styles.priorityText}>{optimization.priority_level.toUpperCase()}</Text>
                  </View>
                </View>
                
                <Text style={[styles.optimizationDescription, { color: colors.textLight }]}>
                  {optimization.recommendation_description}
                </Text>
                
                <View style={styles.optimizationActions}>
                  {optimization.status === 'pending' && (
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: colors.primary }]}
                      onPress={() => handleImplementOptimization(optimization.id)}
                    >
                      <Text style={styles.actionButtonText}>Implement</Text>
                    </TouchableOpacity>
                  )}
                  
                  {optimization.status === 'in_progress' && (
                    <TouchableOpacity
                      style={[styles.actionButton, { backgroundColor: colors.success }]}
                      onPress={() => handleCompleteOptimization(optimization.id)}
                    >
                      <CheckCircle size={16} color="#FFFFFF" />
                      <Text style={styles.actionButtonText}>Complete</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            ))}
          </Card>
        )}
        
        {/* Quick Actions */}
        <Card style={styles.section}>
          <View style={styles.sectionHeader}>
            <Gauge size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Quick Actions</Text>
          </View>
          
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={[styles.quickAction, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={() => navigation.navigate('PredictiveInsights', { animalId })}
            >
              <TrendingUp size={24} color={colors.primary} />
              <Text style={[styles.quickActionText, { color: colors.text }]}>{t('predictiveInsights')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.quickAction, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={handleAnalyzeEnvironment}
              disabled={isLoadingImpactAnalysis}
            >
              <RefreshCw size={24} color={colors.primary} />
              <Text style={[styles.quickActionText, { color: colors.text }]}>Refresh Analysis</Text>
            </TouchableOpacity>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 40,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  animalHeader: {
    marginHorizontal: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  animalInfo: {
    flex: 1,
  },
  animalName: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  animalBreed: {
    fontSize: 14,
    fontWeight: '500',
  },
  analyzeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  analyzeButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  conditionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  conditionItem: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  conditionLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 8,
    marginBottom: 4,
  },
  conditionValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  conditionStatus: {
    fontSize: 12,
    fontWeight: '600',
  },
  lastUpdated: {
    fontSize: 12,
    textAlign: 'center',
  },
  impactScoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  impactScore: {
    alignItems: 'center',
  },
  impactScoreValue: {
    fontSize: 36,
    fontWeight: '800',
  },
  impactScoreLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 4,
  },
  riskLevel: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  riskLevelText: {
    fontSize: 14,
    fontWeight: '700',
  },
  impactFactors: {
    marginBottom: 16,
  },
  impactFactor: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  factorLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  factorValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  analysisDate: {
    fontSize: 12,
    textAlign: 'center',
  },
  optimizationItem: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  optimizationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  optimizationTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 12,
  },
  priorityBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 6,
  },
  priorityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '700',
  },
  optimizationDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  optimizationActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickAction: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginHorizontal: 4,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
});

export default EnvironmentalAnalysisScreen;