import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  StatusBar,
  Platform,
  KeyboardAvoidingView,
  Animated
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Shield, Key, RefreshCw, AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';
import { RootStackParamList } from '../navigation';
import { toast } from 'sonner-native';
import { supabase } from '../supabase/client';

type VerifyMfaScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'VerifyMfaScreen'>;
type VerifyMfaScreenRouteProp = RouteProp<RootStackParamList, 'VerifyMfaScreen'>;

interface VerifyMfaScreenProps {
  challengeId: string;
  email: string;
}

const VerifyMfaScreen = () => {
  const navigation = useNavigation<VerifyMfaScreenNavigationProp>();
  const route = useRoute<VerifyMfaScreenRouteProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  const { signIn } = useAuth();
  
  const { challengeId, email } = route.params as VerifyMfaScreenProps;
  
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isUsingBackupCode, setIsUsingBackupCode] = useState(false);
  const [attemptsRemaining, setAttemptsRemaining] = useState(5);
  const [timeRemaining, setTimeRemaining] = useState(300); // 5 minutes
  
  // Animation for shake effect on error
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  
  // Timer for challenge expiration
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Start countdown timer
    timerRef.current = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          // Challenge expired
          toast.error('Verification session expired. Please sign in again.');
          navigation.replace('LoginScreen');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [navigation]);

  const startShakeAnimation = () => {
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();
  };

  const verifyCode = async () => {
    if (!verificationCode || (isUsingBackupCode ? verificationCode.length !== 8 : verificationCode.length !== 6)) {
      toast.error(isUsingBackupCode ? 'Please enter a valid 8-digit backup code' : 'Please enter a valid 6-digit code');
      startShakeAnimation();
      return;
    }

    try {
      setIsVerifying(true);
      
      const { data, error } = await supabase.functions.invoke('mfa-verify', {
        body: {
          challenge_id: challengeId,
          verification_code: verificationCode,
          is_backup_code: isUsingBackupCode
        }
      });

      if (error) {
        console.error('Error verifying MFA code:', error);
        toast.error('Failed to verify code. Please try again.');
        startShakeAnimation();
        return;
      }

      if (data.success && data.verified) {
        // MFA verification successful
        toast.success('Verification successful! Signing you in...');
        
        // Complete the login process using the session token
        // In a real implementation, you would use the session token to complete authentication
        // For now, we'll simulate successful login
        
        // Clear the timer
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
        
        // Navigate to the main app
        // The AuthContext will handle the navigation automatically when session is established
        navigation.reset({
          index: 0,
          routes: [{ name: 'MainTabs' }],
        });
        
      } else {
        // Verification failed
        const remainingAttempts = data.attempts_remaining || 0;
        setAttemptsRemaining(remainingAttempts);
        
        if (remainingAttempts === 0) {
          toast.error('Too many failed attempts. Please sign in again.');
          navigation.replace('LoginScreen');
        } else {
          toast.error(data.error || 'Invalid verification code');
          startShakeAnimation();
          setVerificationCode('');
        }
      }
    } catch (error) {
      console.error('Error verifying MFA code:', error);
      toast.error('Failed to verify code. Please try again.');
      startShakeAnimation();
    } finally {
      setIsVerifying(false);
    }
  };

  const requestNewChallenge = async () => {
    try {
      const { data, error } = await supabase.functions.invoke('mfa-challenge', {
        body: { email }
      });

      if (error || !data.success) {
        toast.error('Failed to request new verification. Please sign in again.');
        navigation.replace('LoginScreen');
        return;
      }

      if (data.requires_mfa && data.challenge_id) {
        // Replace current screen with new challenge
        navigation.replace('VerifyMfaScreen', {
          challengeId: data.challenge_id,
          email
        });
        toast.success('New verification code requested');
      } else {
        navigation.replace('LoginScreen');
      }
    } catch (error) {
      console.error('Error requesting new challenge:', error);
      toast.error('Failed to request new verification');
      navigation.replace('LoginScreen');
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleCodeChange = (text: string) => {
    // Only allow numeric input
    const numericText = text.replace(/[^0-9]/g, '');
    
    // Limit length based on code type
    const maxLength = isUsingBackupCode ? 8 : 6;
    if (numericText.length <= maxLength) {
      setVerificationCode(numericText);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar
        barStyle={colors.text === '#FFFFFF' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />
      
      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
      >
        <View style={styles.header}>
          <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
            <Shield size={32} color={colors.primary} />
          </View>
          
          <Text style={[styles.title, { color: colors.text }]}>
            Two-Factor Authentication
          </Text>
          
          <Text style={[styles.subtitle, { color: colors.textLight }]}>
            Enter the {isUsingBackupCode ? '8-digit backup code' : '6-digit code'} from your authenticator app
          </Text>
          
          <Text style={[styles.email, { color: colors.textLight }]}>
            {email}
          </Text>
        </View>

        <Animated.View 
          style={[
            styles.formContainer,
            { transform: [{ translateX: shakeAnimation }] }
          ]}
        >
          <View style={styles.inputContainer}>
            <Key size={20} color={colors.primary} style={styles.inputIcon} />
            <TextInput
              style={[styles.codeInput, { 
                backgroundColor: colors.card, 
                color: colors.text,
                borderColor: colors.border
              }]}
              placeholder={isUsingBackupCode ? '12345678' : '123456'}
              placeholderTextColor={colors.textLight}
              value={verificationCode}
              onChangeText={handleCodeChange}
              keyboardType="numeric"
              maxLength={isUsingBackupCode ? 8 : 6}
              autoFocus
              editable={!isVerifying}
              returnKeyType="done"
              onSubmitEditing={verifyCode}
            />
          </View>

          <TouchableOpacity
            style={[
              styles.verifyButton,
              { backgroundColor: colors.primary },
              (isVerifying || verificationCode.length < (isUsingBackupCode ? 8 : 6)) && styles.buttonDisabled
            ]}
            onPress={verifyCode}
            disabled={isVerifying || verificationCode.length < (isUsingBackupCode ? 8 : 6)}
          >
            {isVerifying ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <Text style={styles.verifyButtonText}>
                Verify & Sign In
              </Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.switchModeButton}
            onPress={() => {
              setIsUsingBackupCode(!isUsingBackupCode);
              setVerificationCode('');
            }}
            disabled={isVerifying}
          >
            <Text style={[styles.switchModeText, { color: colors.primary }]}>
              {isUsingBackupCode ? 'Use authenticator code instead' : 'Use backup code instead'}
            </Text>
          </TouchableOpacity>
        </Animated.View>

        <View style={styles.footer}>
          <View style={styles.statusContainer}>
            <View style={styles.statusItem}>
              <Text style={[styles.statusLabel, { color: colors.textLight }]}>Time remaining:</Text>
              <Text style={[styles.statusValue, { color: timeRemaining < 60 ? colors.error : colors.text }]}>
                {formatTime(timeRemaining)}
              </Text>
            </View>
            
            <View style={styles.statusItem}>
              <Text style={[styles.statusLabel, { color: colors.textLight }]}>Attempts left:</Text>
              <Text style={[styles.statusValue, { color: attemptsRemaining <= 2 ? colors.error : colors.text }]}>
                {attemptsRemaining}
              </Text>
            </View>
          </View>

          {attemptsRemaining <= 2 && (
            <View style={[styles.warningContainer, { backgroundColor: colors.error + '20' }]}>
              <AlertTriangle size={16} color={colors.error} />
              <Text style={[styles.warningText, { color: colors.error }]}>
                {attemptsRemaining === 1 
                  ? 'Last attempt remaining. Account will be locked after one more failed attempt.'
                  : 'Only 2 attempts remaining. Please double-check your code.'
                }
              </Text>
            </View>
          )}

          <View style={styles.actionLinks}>
            <TouchableOpacity
              style={styles.linkButton}
              onPress={requestNewChallenge}
              disabled={isVerifying}
            >
              <RefreshCw size={16} color={colors.primary} />
              <Text style={[styles.linkText, { color: colors.primary }]}>
                Request new code
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.linkButton}
              onPress={() => navigation.replace('LoginScreen')}
              disabled={isVerifying}
            >
              <Text style={[styles.linkText, { color: colors.textLight }]}>
                Back to sign in
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  header: {
    alignItems: 'center',
    marginTop: 40,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 8,
  },
  email: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
  formContainer: {
    flex: 1,
    justifyContent: 'center',
    marginVertical: 40,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    position: 'relative',
  },
  inputIcon: {
    position: 'absolute',
    left: 16,
    zIndex: 1,
  },
  codeInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    paddingVertical: 16,
    paddingHorizontal: 50,
    borderRadius: 12,
    borderWidth: 2,
    letterSpacing: 4,
  },
  verifyButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  verifyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  switchModeButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchModeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  footer: {
    marginBottom: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statusItem: {
    alignItems: 'center',
  },
  statusLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  statusValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  warningText: {
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
    lineHeight: 16,
  },
  actionLinks: {
    alignItems: 'center',
    gap: 12,
  },
  linkButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  linkText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
});

export default VerifyMfaScreen;