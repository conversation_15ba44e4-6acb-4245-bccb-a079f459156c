import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  StatusBar
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Package, Clock, CheckCircle, Truck, XCircle, Calendar } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useOrderStore, OrderWithItems } from '../store/orderStore';
import { DevicesStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import { format } from 'date-fns';

type OrderHistoryScreenNavigationProp = NativeStackNavigationProp<DevicesStackParamList, 'OrderHistory'>;

const OrderHistoryScreen = () => {
  const navigation = useNavigation<OrderHistoryScreenNavigationProp>();
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  const { orders, isLoading, error, fetchOrderHistory, clearError } = useOrderStore();

  const handleRefresh = async () => {
    clearError();
    await fetchOrderHistory();
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(price);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} color={theme.colors.warning} />;
      case 'paid':
      case 'processing':
        return <Package size={16} color={theme.colors.primary} />;
      case 'shipped':
        return <Truck size={16} color={theme.colors.info} />;
      case 'delivered':
        return <CheckCircle size={16} color={theme.colors.success} />;
      case 'cancelled':
      case 'refunded':
        return <XCircle size={16} color={theme.colors.error} />;
      default:
        return <Clock size={16} color={theme.colors.textSecondary} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning;
      case 'paid':
      case 'processing':
        return theme.colors.primary;
      case 'shipped':
        return theme.colors.info;
      case 'delivered':
        return theme.colors.success;
      case 'cancelled':
      case 'refunded':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const formatStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const renderOrderCard = ({ item: order }: { item: OrderWithItems }) => {
    const orderDate = new Date(order.created_at);
    const statusColor = getStatusColor(order.status);
    const statusIcon = getStatusIcon(order.status);

    return (
      <Card style={styles.orderCard}>
        <TouchableOpacity
          style={styles.orderContent}
          onPress={() => {
            // Navigate to order details if needed
            // navigation.navigate('OrderDetails', { orderId: order.id });
          }}
        >
          {/* Order Header */}
          <View style={styles.orderHeader}>
            <View style={styles.orderInfo}>
              <Text style={[styles.orderId, { color: theme.colors.text }]}>
                Order #{order.id.substring(0, 8).toUpperCase()}
              </Text>
              <View style={styles.orderDate}>
                <Calendar size={14} color={theme.colors.textSecondary} />
                <Text style={[styles.orderDateText, { color: theme.colors.textSecondary }]}>
                  {format(orderDate, 'MMM dd, yyyy')}
                </Text>
              </View>
            </View>
            
            <View style={[styles.statusBadge, { backgroundColor: statusColor + '20' }]}>
              {statusIcon}
              <Text style={[styles.statusText, { color: statusColor }]}>
                {formatStatus(order.status)}
              </Text>
            </View>
          </View>

          {/* Order Items */}
          <View style={styles.orderItems}>
            {order.order_items.map((item, index) => (
              <View key={item.id} style={styles.orderItem}>
                {item.product && (
                  <>
                    <Image
                      source={{ uri: item.product.image_url }}
                      style={styles.itemImage}
                      resizeMode="cover"
                    />
                    <View style={styles.itemDetails}>
                      <Text style={[styles.itemName, { color: theme.colors.text }]}>
                        {item.product.name}
                      </Text>
                      <Text style={[styles.itemQuantity, { color: theme.colors.textSecondary }]}>
                        Qty: {item.quantity} • {formatPrice(item.price_at_purchase, order.currency)} each
                      </Text>
                    </View>
                  </>
                )}
              </View>
            ))}
          </View>

          {/* Order Total */}
          <View style={styles.orderFooter}>
            <View style={styles.shippingInfo}>
              <Text style={[styles.shippingLabel, { color: theme.colors.textSecondary }]}>
                Shipping to:
              </Text>
              <Text style={[styles.shippingAddress, { color: theme.colors.text }]}>
                {order.shipping_city}, {order.shipping_state_province}
              </Text>
            </View>
            
            <View style={styles.totalSection}>
              <Text style={[styles.totalLabel, { color: theme.colors.textSecondary }]}>
                Total:
              </Text>
              <Text style={[styles.totalAmount, { color: theme.colors.primary }]}>
                {formatPrice(order.total_amount, order.currency)}
              </Text>
            </View>
          </View>

          {/* Tracking Info */}
          {order.tracking_number && (
            <View style={[styles.trackingInfo, { backgroundColor: theme.colors.primary + '10' }]}>
              <Truck size={16} color={theme.colors.primary} />
              <Text style={[styles.trackingText, { color: theme.colors.primary }]}>
                Tracking: {order.tracking_number}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Package size={64} color={theme.colors.textSecondary} />
      <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
        No Orders Yet
      </Text>
      <Text style={[styles.emptyStateDescription, { color: theme.colors.textSecondary }]}>
        Your order history will appear here once you place your first order
      </Text>
      <TouchableOpacity
        style={[styles.shopButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => navigation.navigate('OrderDevice')}
      >
        <Text style={[styles.shopButtonText, { color: theme.colors.white }]}>
          Start Shopping
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.errorState}>
      <Text style={[styles.errorText, { color: theme.colors.error }]}>
        {error}
      </Text>
      <TouchableOpacity
        style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
        onPress={handleRefresh}
      >
        <Text style={[styles.retryButtonText, { color: theme.colors.white }]}>
          Try Again
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar
        barStyle={theme.dark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.background}
      />
      
      <Header title="Order History" showBack />

      {isLoading && orders.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            Loading orders...
          </Text>
        </View>
      ) : error ? (
        renderErrorState()
      ) : orders.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={orders}
          renderItem={renderOrderCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  orderCard: {
    marginBottom: 16,
  },
  orderContent: {
    padding: 16,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  orderDate: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  orderDateText: {
    fontSize: 14,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  orderItems: {
    marginBottom: 16,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 12,
  },
  itemImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  itemQuantity: {
    fontSize: 12,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  shippingInfo: {
    flex: 1,
  },
  shippingLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  shippingAddress: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalSection: {
    alignItems: 'flex-end',
  },
  totalLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  totalAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  trackingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    padding: 8,
    borderRadius: 6,
    gap: 6,
  },
  trackingText: {
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    gap: 16,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  emptyStateDescription: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 8,
  },
  shopButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  shopButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    gap: 16,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  retryButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default OrderHistoryScreen;