
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Plus } from 'lucide-react-native';
import { COLORS } from '../constants/colors';
import { useAnimalStore } from '../store/animalStore';
import AnimalCard from '../components/AnimalCard';
import Header from '../components/Header';
import { AnimalsStackParamList } from '../navigation';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

type AnimalsScreenNavigationProp = NativeStackNavigationProp<AnimalsStackParamList, 'Animals'>;

const AnimalsScreen = () => {
  const navigation = useNavigation<AnimalsScreenNavigationProp>();
  const { animals, isLoading, fetchAnimals } = useAnimalStore();
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  
  useEffect(() => {
    fetchAnimals();
  }, []);
  
  const handleAddAnimal = () => {
    navigation.navigate('AddAnimal');
  };
  
  const renderItem = ({ item }) => (
    <AnimalCard animal={item} />
  );
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <Header 
        title={t('myAnimals')} 
        rightAction={{
          icon: <Plus size={24} color={colors.primary} />,
          onPress: handleAddAnimal
        }}
      />
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : animals.length > 0 ? (
        <FlatList
          data={animals}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            {t('noAnimalsYet')}
          </Text>
          <TouchableOpacity 
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={handleAddAnimal}
          >
            <Text style={[styles.addButtonText, { color: colors.card }]}>
              {t('addAnimal')}
            </Text>
          </TouchableOpacity>
        </View>
      )}
      
      <TouchableOpacity 
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={handleAddAnimal}
      >
        <Plus size={24} color="#FFF" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  addButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default AnimalsScreen;
