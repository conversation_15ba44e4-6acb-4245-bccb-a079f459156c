import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../contexts/ThemeContext';
import { useChatStore } from '../store/chatStore';
import { useAnimalStore } from '../store/animalStore';
import { useUserStore } from '../store/userStore';
import Header from '../components/Header';
import MessageList from '../components/chat/MessageList';
import ChatInput from '../components/chat/ChatInput';
import { HomeStackParamList } from '../navigation';
import { toast } from 'sonner-native';

type AIChatScreenRouteProp = RouteProp<HomeStackParamList, 'AIChat'>;
type AIChatScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'AIChat'>;

interface AIChatScreenProps {
  route: AIChatScreenRouteProp;
  navigation: AIChatScreenNavigationProp;
}

const AIChatScreen: React.FC = () => {
  const { colors } = useTheme();
  const navigation = useNavigation<AIChatScreenNavigationProp>();
  const route = useRoute<AIChatScreenRouteProp>();
  const { animalId } = route.params || {};
  
  const {
    messages,
    currentConversationId,
    isLoading,
    isSending,
    sendMessage,
    loadConversations,
    createNewConversation,
    setCurrentConversation,
    getCurrentConversation
  } = useChatStore();
  
  const { animals } = useAnimalStore();
  const { user } = useUserStore();
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Get animal info if animalId is provided
  const currentAnimal = animalId ? animals.find(a => a.id === animalId) : null;
  
  useEffect(() => {
    initializeChat();
  }, [animalId]);
  
  const initializeChat = async () => {
    try {
      // Load existing conversations
      await loadConversations();
      
      // If no current conversation, create a new one
      if (!currentConversationId) {
        const conversationId = await createNewConversation(
          animalId,
          currentAnimal?.name
        );
        
        if (conversationId) {
          setCurrentConversation(conversationId);
        }
      }
      
      setIsInitialized(true);
    } catch (error) {
      console.error('Failed to initialize chat:', error);
      toast.error('Failed to initialize chat');
    }
  };
  
  const handleSendMessage = async (content: string, attachments?: File[]) => {
    if (!content.trim() && (!attachments || attachments.length === 0)) {
      return;
    }
    
    try {
      await sendMessage(content, attachments, animalId);
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
    }
  };
  
  const handleImagePress = (url: string) => {
    // Could implement image viewer modal here
    Alert.alert(
      'Image Viewer',
      'Image viewer functionality can be implemented here',
      [{ text: 'OK' }]
    );
  };
  
  const handleStartNewChat = () => {
    // This could trigger a new conversation or show suggestions
    toast.success('Start typing to begin your conversation!');
  };
  
  const getHeaderTitle = () => {
    const conversation = getCurrentConversation();
    if (conversation?.title) {
      return conversation.title;
    }
    if (currentAnimal) {
      return `Chat about ${currentAnimal.name}`;
    }
    return 'AI Assistant';
  };
  
  const getPlaceholder = () => {
    if (currentAnimal) {
      return `Ask me anything about ${currentAnimal.name}...`;
    }
    return 'Ask me anything about your pets...';
  };
  
  if (!isInitialized) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar
          barStyle={colors.statusBarStyle}
          backgroundColor={colors.statusBarBackground}
        />
        <Header
          title="AI Assistant"
          showBack
          onBackPress={() => navigation.goBack()}
        />
        <View style={styles.loadingContainer}>
          {/* Loading state could be added here */}
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar
        barStyle={colors.statusBarStyle}
        backgroundColor={colors.statusBarBackground}
      />
      
      <Header
        title={getHeaderTitle()}
        showBackButton={true}
      />
      
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <MessageList
          messages={messages}
          isLoading={isLoading}
          onImagePress={handleImagePress}
          onStartNewChat={handleStartNewChat}
          userProfileImage={user?.imageUrl}
        />
        
        <ChatInput
          onSendMessage={handleSendMessage}
          isLoading={isSending}
          placeholder={getPlaceholder()}
        />
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AIChatScreen;