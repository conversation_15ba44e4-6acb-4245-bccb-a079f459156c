
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  StatusBar 
} from 'react-native';
import { ChevronDown, ChevronUp } from 'lucide-react-native';
import { COLORS } from '../constants/colors';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

interface FAQItem {
  question: string;
  answer: string;
}

const FAQScreen = () => {
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);
  
  const toggleItem = (index: number) => {
    setExpandedIndex(expandedIndex === index ? null : index);
  };
  
  const faqItems: FAQItem[] = [
    {
      question: t('whatIsHoofBeat'),
      answer: t('whatIsHoofBeatAnswer')
    },
    {
      question: t('howToAddNewAnimal'),
      answer: t('howToAddNewAnimalAnswer')
    },
    {
      question: t('canRecordVitalsManually'),
      answer: t('canRecordVitalsManuallyAnswer')
    },
    {
      question: t('freePremiumDifference'),
      answer: t('freePremiumDifferenceAnswer')
    },
    {
      question: t('howToConnectDevice'),
      answer: t('howToConnectDeviceAnswer')
    },
    {
      question: t('howToSetupFeeding'),
      answer: t('howToSetupFeedingAnswer')
    },
    {
      question: t('howToTrackMedications'),
      answer: t('howToTrackMedicationsAnswer')
    },
    {
      question: t('canExportPrintRecords'),
      answer: t('canExportPrintRecordsAnswer')
    },
    {
      question: t('howToGetSupport'),
      answer: t('howToGetSupportAnswer')
    },
    {
      question: t('howToCancelSubscription'),
      answer: t('howToCancelSubscriptionAnswer')
    }
  ];
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <Header title={t('frequentlyAskedQuestions')} showBackButton />
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {faqItems.map((item, index) => (
            <View 
              key={index}
              style={[
                styles.faqItem, 
                { backgroundColor: colors.card, borderColor: colors.border }
              ]}
            >
              <TouchableOpacity 
                style={styles.questionContainer}
                onPress={() => toggleItem(index)}
              >
                <Text style={[styles.question, { color: colors.text }]}>
                  {item.question}
                </Text>
                {expandedIndex === index ? (
                  <ChevronUp size={20} color={colors.primary} />
                ) : (
                  <ChevronDown size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
              
              {expandedIndex === index && (
                <View style={[styles.answerContainer, { borderTopColor: colors.border }]}>
                  <Text style={[styles.answer, { color: colors.textLight }]}>
                    {item.answer}
                  </Text>
                </View>
              )}
            </View>
          ))}
          
          <View style={styles.contactSection}>
            <Text style={[styles.contactText, { color: colors.textLight }]}>
              {t('stillHaveQuestions')}
            </Text>
            <TouchableOpacity 
              style={[styles.contactButton, { backgroundColor: colors.primary }]}
              onPress={() => {
                // In a real app, this would open the Crisp chat
                // For now, we'll just show a toast
                toast.info(t('openingChatSupport'));
              }}
            >
              <Text style={[styles.contactButtonText, { color: colors.card }]}>
                {t('chatWithSupport')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  faqItem: {
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  questionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  question: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  answerContainer: {
    padding: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  answer: {
    fontSize: 14,
    lineHeight: 20,
  },
  contactSection: {
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 32,
  },
  contactText: {
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
  },
  contactButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  contactButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default FAQScreen;
