import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Moon,
  Clock,
  Activity,
  TrendingUp,
  RefreshCw,
  Thermometer,
  Eye,
  BarChart3
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalStore } from '../store/animalStore';
import { useBehavioralStore } from '../store/behavioralStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import { toast } from 'sonner-native';
import { format } from 'date-fns';
import { useEffect, useState } from 'react';
import SleepAnalysisHeader from '../components/behavioral/SleepAnalysisHeader';
import SleepQualityChart from '../components/charts/SleepQualityChart';

type SleepMonitoringScreenRouteProp = RouteProp<HomeStackParamList, 'SleepMonitoring'>;
type SleepMonitoringScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'SleepMonitoring'>;

const SleepMonitoringScreen = () => {
  const navigation = useNavigation<SleepMonitoringScreenNavigationProp>();
  const route = useRoute<SleepMonitoringScreenRouteProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  // Safe destructuring with fallback for missing params
  const animalId = route.params?.animalId;
  
  // Early return if no animalId provided
  if (!animalId) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('sleepMonitoring')} showBackButtonButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {t('missingAnimalId')}
          </Text>
        </View>
      </View>
    );
  }
  
  const animals = useAnimalStore(state => state.animals);
  const animal = animals.find(a => a.id === animalId);
  
  const {
    sleepAnalyses,
    isLoadingSleepAnalysis,
    sleepAnalysisError,
    fetchSleepAnalyses,
    requestSleepAnalysis,
    getLatestSleepAnalysis
  } = useBehavioralStore();
  
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  );
  
  const latestSleepAnalysis = getLatestSleepAnalysis(animalId);
  
  useEffect(() => {
    loadSleepData();
  }, [animalId]);
  
  const loadSleepData = async () => {
    try {
      await fetchSleepAnalyses(animalId);
    } catch (error) {
      console.error('Error loading sleep data:', error);
    }
  };
  
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadSleepData();
    setIsRefreshing(false);
  };
  
  const handleRequestAnalysis = async () => {
    try {
      await requestSleepAnalysis(animalId, selectedDate);
    } catch (error) {
      // Error is already handled in the store
    }
  };
  
  const getSleepQualityColor = (score: number) => {
    if (score >= 80) return '#10B981';
    if (score >= 60) return '#F59E0B';
    if (score >= 40) return '#EF4444';
    return '#DC2626';
  };
  
  const getSleepQualityText = (score: number) => {
    if (score >= 80) return t('excellent');
    if (score >= 60) return t('good');
    if (score >= 40) return t('fair');
    return t('poor');
  };
  
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };
  
  const getSleepStageColor = (stage: string) => {
    switch (stage) {
      case 'deep':
        return '#1E40AF';
      case 'light':
        return '#3B82F6';
      case 'rem':
        return '#8B5CF6';
      case 'awake':
        return '#EF4444';
      default:
        return colors.textLight;
    }
  };
  
  if (!animal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('sleepMonitoring')} />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>{t('animalNotFound')}</Text>
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background} />
      <Header title={t('sleepMonitoring')} />
      
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Animal Info */}
        <SleepAnalysisHeader
          animal={animal}
          isLoading={isLoadingSleepAnalysis}
          onAnalyze={handleRequestAnalysis}
        />
        
        {/* Latest Sleep Analysis */}
        {latestSleepAnalysis && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <Moon size={24} color={getSleepQualityColor(latestSleepAnalysis.sleep_quality_score)} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('lastNightSleep')}</Text>
              <View style={[
                styles.qualityBadge,
                { backgroundColor: getSleepQualityColor(latestSleepAnalysis.sleep_quality_score) }
              ]}>
                <Text style={styles.qualityText}>
                  {getSleepQualityText(latestSleepAnalysis.sleep_quality_score)}
                </Text>
              </View>
            </View>
            
            <View style={styles.sleepScoreContainer}>
              <View style={styles.scoreCircle}>
                <Text style={[styles.scoreValue, { color: getSleepQualityColor(latestSleepAnalysis.sleep_quality_score) }]}>
                  {latestSleepAnalysis.sleep_quality_score}
                </Text>
                <Text style={[styles.scoreLabel, { color: colors.textLight }]}>/100</Text>
              </View>
              
              <View style={styles.scoreDetails}>
                <Text style={[styles.sleepDate, { color: colors.text }]}>
                  {format(new Date(latestSleepAnalysis.sleep_date), 'EEEE, MMM d')}
                </Text>
                <Text style={[styles.sleepDuration, { color: colors.textLight }]}>
                  {formatDuration(latestSleepAnalysis.total_sleep_duration_minutes)} {t('totalSleep')}
                </Text>
                <Text style={[styles.sleepEfficiency, { color: colors.textLight }]}>
                  {latestSleepAnalysis.sleep_efficiency_percentage.toFixed(1)}% {t('efficiency')}
                </Text>
              </View>
            </View>
            
            {/* Sleep Stages Breakdown */}
            <View style={styles.stagesSection}>
              <Text style={[styles.stagesTitle, { color: colors.text }]}>{t('sleepStages')}</Text>
              <View style={styles.stagesGrid}>
                <View style={styles.stageItem}>
                  <View style={[styles.stageIndicator, { backgroundColor: getSleepStageColor('deep') }]} />
                  <Text style={[styles.stageLabel, { color: colors.textLight }]}>{t('deepSleep')}</Text>
                  <Text style={[styles.stageValue, { color: colors.text }]}>
                    {formatDuration(latestSleepAnalysis.deep_sleep_minutes)}
                  </Text>
                </View>
                
                <View style={styles.stageItem}>
                  <View style={[styles.stageIndicator, { backgroundColor: getSleepStageColor('light') }]} />
                  <Text style={[styles.stageLabel, { color: colors.textLight }]}>{t('lightSleep')}</Text>
                  <Text style={[styles.stageValue, { color: colors.text }]}>
                    {formatDuration(latestSleepAnalysis.light_sleep_minutes)}
                  </Text>
                </View>
                
                <View style={styles.stageItem}>
                  <View style={[styles.stageIndicator, { backgroundColor: getSleepStageColor('rem') }]} />
                  <Text style={[styles.stageLabel, { color: colors.textLight }]}>{t('remSleep')}</Text>
                  <Text style={[styles.stageValue, { color: colors.text }]}>
                    {formatDuration(latestSleepAnalysis.rem_sleep_minutes)}
                  </Text>
                </View>
              </View>
            </View>
            
            {/* Sleep Metrics */}
            <View style={styles.metricsSection}>
              <Text style={[styles.metricsTitle, { color: colors.text }]}>{t('sleepMetrics')}</Text>
              <View style={styles.metricsGrid}>
                <View style={styles.metricItem}>
                  <Clock size={16} color={colors.textLight} />
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('timeToSleep')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {latestSleepAnalysis.time_to_sleep_minutes} {t('minutes')}
                  </Text>
                </View>
                
                <View style={styles.metricItem}>
                  <Activity size={16} color={colors.textLight} />
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('wakeEpisodes')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {latestSleepAnalysis.wake_episodes}
                  </Text>
                </View>
                
                <View style={styles.metricItem}>
                  <BarChart3 size={16} color={colors.textLight} />
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('circadianAlignment')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {Math.round(latestSleepAnalysis.circadian_rhythm_alignment * 100)}%
                  </Text>
                </View>
              </View>
            </View>
            
            {/* Environmental Conditions */}
            <View style={styles.environmentSection}>
              <Text style={[styles.environmentTitle, { color: colors.text }]}>{t('sleepEnvironment')}</Text>
              <View style={styles.environmentGrid}>
                <View style={styles.environmentItem}>
                  <Thermometer size={16} color={colors.textLight} />
                  <Text style={[styles.environmentLabel, { color: colors.textLight }]}>{t('avgTemperature')}</Text>
                  <Text style={[styles.environmentValue, { color: colors.text }]}>
                    {latestSleepAnalysis.environmental_conditions.averageTemperature.toFixed(1)}°C
                  </Text>
                </View>
                
                <View style={styles.environmentItem}>
                  <Text style={[styles.environmentLabel, { color: colors.textLight }]}>{t('tempVariation')}</Text>
                  <Text style={[styles.environmentValue, { color: colors.text }]}>
                    ±{latestSleepAnalysis.environmental_conditions.temperatureVariation.toFixed(1)}°C
                  </Text>
                </View>
                
                <View style={styles.environmentItem}>
                  <Text style={[styles.environmentLabel, { color: colors.textLight }]}>{t('humidity')}</Text>
                  <Text style={[styles.environmentValue, { color: colors.text }]}>
                    {latestSleepAnalysis.environmental_conditions.humidity}%
                  </Text>
                </View>
              </View>
            </View>
            
            {/* Sleep Disturbances */}
            {latestSleepAnalysis.sleep_disturbances.length > 0 && (
              <View style={styles.disturbancesSection}>
                <Text style={[styles.disturbancesTitle, { color: colors.text }]}>{t('sleepDisturbances')}</Text>
                {latestSleepAnalysis.sleep_disturbances.slice(0, 3).map((disturbance, index) => (
                  <View key={index} style={[
                    styles.disturbanceItem,
                    { backgroundColor: colors.card, borderColor: colors.border }
                  ]}>
                    <View style={styles.disturbanceHeader}>
                      <Text style={[styles.disturbanceType, { color: colors.text }]}>{disturbance.type}</Text>
                      <Text style={[styles.disturbanceSeverity, { color: getSleepQualityColor(50) }]}>
                        {disturbance.severity}
                      </Text>
                    </View>
                    <Text style={[styles.disturbanceTime, { color: colors.textLight }]}>
                      {format(new Date(disturbance.timestamp), 'h:mm a')} • {disturbance.durationMinutes} {t('minutes')}
                    </Text>
                    {disturbance.cause && (
                      <Text style={[styles.disturbanceCause, { color: colors.textLight }]}>
                        {disturbance.cause}
                      </Text>
                    )}
                  </View>
                ))}
              </View>
            )}
            
            {/* Sleep Recommendations */}
            {latestSleepAnalysis.sleep_recommendations && (
              <View style={styles.recommendationsSection}>
                <Text style={[styles.recommendationsTitle, { color: colors.text }]}>{t('sleepRecommendations')}</Text>
                <Text style={[styles.recommendationsText, { color: colors.textLight }]}>
                  {latestSleepAnalysis.sleep_recommendations}
                </Text>
              </View>
            )}
          </Card>
        )}
        
        {/* Sleep History Charts */}
        {sleepAnalyses.length > 0 && (
          <>
            <SleepQualityChart
              data={sleepAnalyses}
              chartType="quality"
              onDataPointPress={(analysis) => {
                toast.success(`Sleep Quality: ${analysis.sleep_quality_score}/100 on ${format(new Date(analysis.sleep_date), 'MMM d')}`);
              }}
            />
            <SleepQualityChart
              data={sleepAnalyses}
              chartType="duration"
              onDataPointPress={(analysis) => {
                toast.success(`Sleep Duration: ${formatDuration(analysis.total_sleep_duration_minutes)} on ${format(new Date(analysis.sleep_date), 'MMM d')}`);
              }}
            />
          </>
        )}
        
        {/* Empty State */}
        {!latestSleepAnalysis && !isLoadingSleepAnalysis && (
          <Card style={styles.emptyState}>
            <Moon size={48} color={colors.textLight} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>{t('noSleepAnalysis')}</Text>
            <Text style={[styles.emptyDescription, { color: colors.textLight }]}>
              {t('runFirstSleepAnalysis')}
            </Text>
            
            <TouchableOpacity
              style={[styles.emptyButton, { backgroundColor: colors.primary }]}
              onPress={handleRequestAnalysis}
            >
              <Moon size={20} color="#FFFFFF" />
              <Text style={styles.emptyButtonText}>{t('analyzeSleep')}</Text>
            </TouchableOpacity>
          </Card>
        )}
        
        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  animalCard: {
    marginBottom: 16,
  },
  animalInfo: {
    marginBottom: 16,
  },
  animalName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  animalDetails: {
    fontSize: 16,
  },
  analysisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  analysisButtonDisabled: {
    opacity: 0.6,
  },
  analysisButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  qualityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  qualityText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  sleepScoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    gap: 16,
  },
  scoreCircle: {
    alignItems: 'center',
  },
  scoreValue: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 14,
  },
  scoreDetails: {
    flex: 1,
  },
  sleepDate: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  sleepDuration: {
    fontSize: 14,
    marginBottom: 2,
  },
  sleepEfficiency: {
    fontSize: 14,
  },
  stagesSection: {
    marginBottom: 20,
  },
  stagesTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  stagesGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stageItem: {
    flex: 1,
    alignItems: 'center',
    gap: 4,
  },
  stageIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  stageLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  stageValue: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  metricsSection: {
    marginBottom: 20,
  },
  metricsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  metricsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metricItem: {
    flex: 1,
    alignItems: 'center',
    gap: 4,
  },
  metricLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  environmentSection: {
    marginBottom: 20,
  },
  environmentTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  environmentGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  environmentItem: {
    flex: 1,
    alignItems: 'center',
    gap: 4,
  },
  environmentLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  environmentValue: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  disturbancesSection: {
    marginBottom: 20,
  },
  disturbancesTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  disturbanceItem: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  disturbanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  disturbanceType: {
    fontSize: 14,
    fontWeight: '600',
  },
  disturbanceSeverity: {
    fontSize: 12,
    fontWeight: '600',
  },
  disturbanceTime: {
    fontSize: 12,
    marginBottom: 4,
  },
  disturbanceCause: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  recommendationsSection: {
    marginTop: 16,
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  recommendationsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  historyItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  historyDate: {
    fontSize: 16,
    fontWeight: '600',
  },
  historyQuality: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  historyQualityText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  historyDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  historyDuration: {
    fontSize: 12,
  },
  historyWakes: {
    fontSize: 12,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  emptyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  bottomPadding: {
    height: 20,
  },
});

export default SleepMonitoringScreen;