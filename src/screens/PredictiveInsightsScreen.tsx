import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Target,
  Calendar,
  RefreshCw,
  CheckCircle,
  Clock,
  Activity,
  Heart,
  Scale
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalStore } from '../store/animalStore';
import { usePredictiveStore } from '../store/predictiveStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import { toast } from 'sonner-native';
import { format } from 'date-fns';

type PredictiveInsightsScreenRouteProp = RouteProp<HomeStackParamList, 'PredictiveInsightsScreen'>;
type PredictiveInsightsScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'PredictiveInsightsScreen'>;

const PredictiveInsightsScreen = () => {
  const route = useRoute<PredictiveInsightsScreenRouteProp>();
  const navigation = useNavigation<PredictiveInsightsScreenNavigationProp>();
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  // Safe destructuring with fallback for missing params
  const animalId = route.params?.animalId;
  
  // Early return if no animalId provided
  if (!animalId) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
        <Header title={t('predictiveInsights')} showBackButton />
        <View style={styles.centerContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>{t('missingAnimalId')}</Text>
        </View>
      </View>
    );
  }
  
  const { animals } = useAnimalStore();
  const {
    predictions,
    alerts,
    isLoadingPredictions,
    isLoadingAlerts,
    generatePredictiveInsights,
    fetchPredictions,
    fetchAlerts,
    acknowledgeAlert,
    resolveAlert
  } = usePredictiveStore();
  
  const [refreshing, setRefreshing] = useState(false);
  const [selectedHorizon, setSelectedHorizon] = useState(30);
  
  const animal = animals.find(a => a.id === animalId);
  const activeAlerts = alerts.filter(alert => alert.status === 'active');
  const currentPredictions = predictions.filter(pred => pred.prediction_horizon_days === selectedHorizon);
  const latestPrediction = currentPredictions.length > 0 ? currentPredictions[0] : null;
  
  const horizonOptions = [
    { days: 7, label: t('oneWeek') },
    { days: 30, label: t('oneMonth') },
    { days: 90, label: t('threeMonths') }
  ];
  
  useEffect(() => {
    loadData();
  }, [animalId]);
  
  const loadData = async () => {
    await Promise.all([
      fetchPredictions(animalId),
      fetchAlerts(animalId)
    ]);
  };
  
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };
  
  const handleGeneratePredictions = async () => {
    await generatePredictiveInsights(animalId, [7, 30, 90]);
  };
  
  const handleAcknowledgeAlert = async (alertId: string) => {
    await acknowledgeAlert(alertId);
  };
  
  const handleResolveAlert = async (alertId: string) => {
    await resolveAlert(alertId, 'Resolved by user');
  };
  
  const getPredictionConfidenceColor = (confidence: number | undefined) => {
    if (!confidence) return colors.textLight;
    
    if (confidence >= 0.8) return colors.success;
    if (confidence >= 0.6) return colors.warning;
    return colors.error;
  };
  
  const getRiskLevelColor = (risk: number | undefined) => {
    if (!risk) return colors.textLight;
    
    if (risk < 0.1) return colors.success;
    if (risk < 0.3) return colors.warning;
    if (risk < 0.6) return colors.error;
    return colors.error;
  };
  
  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case 'info': return colors.info;
      case 'warning': return colors.warning;
      case 'critical': return colors.error;
      case 'emergency': return colors.error;
      default: return colors.textLight;
    }
  };
  
  const getHealthTrendIcon = (currentScore: number | undefined, predictedScore: number | undefined) => {
    if (!currentScore || !predictedScore) return TrendingUp;
    
    const difference = predictedScore - currentScore;
    if (difference > 5) return TrendingUp;
    if (difference < -5) return TrendingDown;
    return TrendingUp;
  };
  
  const getHealthTrendColor = (currentScore: number | undefined, predictedScore: number | undefined) => {
    if (!currentScore || !predictedScore) return colors.textLight;
    
    const difference = predictedScore - currentScore;
    if (difference > 5) return colors.success;
    if (difference < -5) return colors.error;
    return colors.warning;
  };
  
  if (!animal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
        <Header title={t('predictiveInsights')} showBackButton />
        <View style={styles.centerContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>Animal not found</Text>
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      <Header title={t('predictiveInsights')} showBackButton />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Animal Info Header */}
        <Card style={styles.animalHeader}>
          <View style={styles.animalInfo}>
            <Text style={[styles.animalName, { color: colors.text }]}>{animal.name}</Text>
            <Text style={[styles.animalBreed, { color: colors.textLight }]}>
              {animal.breed} • {animal.species}
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.generateButton, { backgroundColor: colors.primary }]}
            onPress={handleGeneratePredictions}
            disabled={isLoadingPredictions}
          >
            {isLoadingPredictions ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Brain size={16} color="#FFFFFF" />
            )}
            <Text style={styles.generateButtonText}>
              {isLoadingPredictions ? t('generatingPredictions') : t('generatePredictions')}
            </Text>
          </TouchableOpacity>
        </Card>
        
        {/* Active Alerts */}
        {activeAlerts.length > 0 && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <AlertTriangle size={20} color={colors.error} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('predictiveAlerts')}</Text>
            </View>
            
            {activeAlerts.slice(0, 3).map((alert) => (
              <View key={alert.id} style={[styles.alertItem, { borderColor: getAlertSeverityColor(alert.severity_level) }]}>
                <View style={styles.alertHeader}>
                  <View style={[styles.severityBadge, { backgroundColor: getAlertSeverityColor(alert.severity_level) }]}>
                    <Text style={styles.severityText}>{alert.severity_level.toUpperCase()}</Text>
                  </View>
                  <Text style={[styles.alertProbability, { color: colors.textLight }]}>
                    {alert.probability_percentage.toFixed(1)}% probability
                  </Text>
                </View>
                
                <Text style={[styles.alertTitle, { color: colors.text }]}>{alert.alert_title}</Text>
                <Text style={[styles.alertMessage, { color: colors.textLight }]}>{alert.alert_message}</Text>
                
                <View style={styles.alertActions}>
                  <TouchableOpacity
                    style={[styles.alertActionButton, { backgroundColor: colors.warning }]}
                    onPress={() => handleAcknowledgeAlert(alert.id)}
                  >
                    <Text style={styles.alertActionText}>{t('acknowledgeAlert')}</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.alertActionButton, { backgroundColor: colors.success }]}
                    onPress={() => handleResolveAlert(alert.id)}
                  >
                    <CheckCircle size={14} color="#FFFFFF" />
                    <Text style={styles.alertActionText}>{t('resolveAlert')}</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </Card>
        )}
        
        {/* Prediction Horizon Selector */}
        <Card style={styles.section}>
          <View style={styles.sectionHeader}>
            <Calendar size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('predictionHorizon')}</Text>
          </View>
          
          <View style={styles.horizonSelector}>
            {horizonOptions.map((option) => (
              <TouchableOpacity
                key={option.days}
                style={[
                  styles.horizonOption,
                  {
                    backgroundColor: selectedHorizon === option.days ? colors.primary : colors.card,
                    borderColor: colors.border
                  }
                ]}
                onPress={() => setSelectedHorizon(option.days)}
              >
                <Text
                  style={[
                    styles.horizonOptionText,
                    {
                      color: selectedHorizon === option.days ? '#FFFFFF' : colors.text
                    }
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Card>
        
        {/* Health Predictions */}
        {latestPrediction && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <Target size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('healthPredictions')}</Text>
            </View>
            
            <View style={styles.predictionGrid}>
              {/* Health Score */}
              <View style={styles.predictionItem}>
                <Heart size={24} color={colors.primary} />
                <Text style={[styles.predictionLabel, { color: colors.textLight }]}>{t('predictedHealthScore')}</Text>
                <Text style={[styles.predictionValue, { color: colors.text }]}>
                  {latestPrediction.predicted_health_score || 'N/A'}
                </Text>
                <View style={styles.predictionTrend}>
                  {React.createElement(
                    getHealthTrendIcon(75, latestPrediction.predicted_health_score),
                    {
                      size: 16,
                      color: getHealthTrendColor(75, latestPrediction.predicted_health_score)
                    }
                  )}
                </View>
              </View>
              
              {/* Weight */}
              <View style={styles.predictionItem}>
                <Scale size={24} color={colors.primary} />
                <Text style={[styles.predictionLabel, { color: colors.textLight }]}>{t('predictedWeight')}</Text>
                <Text style={[styles.predictionValue, { color: colors.text }]}>
                  {latestPrediction.predicted_weight_kg?.toFixed(1) || 'N/A'} kg
                </Text>
              </View>
              
              {/* Activity Level */}
              <View style={styles.predictionItem}>
                <Activity size={24} color={colors.primary} />
                <Text style={[styles.predictionLabel, { color: colors.textLight }]}>{t('predictedActivity')}</Text>
                <Text style={[styles.predictionValue, { color: colors.text }]}>
                  {latestPrediction.predicted_activity_level?.replace('_', ' ') || 'N/A'}
                </Text>
              </View>
              
              {/* Sleep Quality */}
              <View style={styles.predictionItem}>
                <Clock size={24} color={colors.primary} />
                <Text style={[styles.predictionLabel, { color: colors.textLight }]}>{t('predictedSleep')}</Text>
                <Text style={[styles.predictionValue, { color: colors.text }]}>
                  {latestPrediction.predicted_sleep_quality || 'N/A'}%
                </Text>
              </View>
            </View>
            
            <View style={styles.confidenceContainer}>
              <Text style={[styles.confidenceLabel, { color: colors.textLight }]}>
                {t('predictionConfidence')}: 
              </Text>
              <Text style={[styles.confidenceValue, { color: getPredictionConfidenceColor(latestPrediction.prediction_confidence) }]}>
                {latestPrediction.prediction_confidence ? `${(latestPrediction.prediction_confidence * 100).toFixed(0)}%` : 'N/A'}
              </Text>
            </View>
          </Card>
        )}
        
        {/* Risk Assessment */}
        {latestPrediction && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <AlertTriangle size={20} color={colors.warning} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('riskAssessment')}</Text>
            </View>
            
            <View style={styles.riskGrid}>
              <View style={styles.riskItem}>
                <Text style={[styles.riskLabel, { color: colors.textLight }]}>{t('diseaseRisk')}</Text>
                <Text style={[styles.riskValue, { color: getRiskLevelColor(latestPrediction.disease_risk_probability) }]}>
                  {latestPrediction.disease_risk_probability ? `${(latestPrediction.disease_risk_probability * 100).toFixed(1)}%` : 'N/A'}
                </Text>
              </View>
              
              <View style={styles.riskItem}>
                <Text style={[styles.riskLabel, { color: colors.textLight }]}>{t('injuryRisk')}</Text>
                <Text style={[styles.riskValue, { color: getRiskLevelColor(latestPrediction.injury_risk_probability) }]}>
                  {latestPrediction.injury_risk_probability ? `${(latestPrediction.injury_risk_probability * 100).toFixed(1)}%` : 'N/A'}
                </Text>
              </View>
              
              <View style={styles.riskItem}>
                <Text style={[styles.riskLabel, { color: colors.textLight }]}>{t('behavioralRisk')}</Text>
                <Text style={[styles.riskValue, { color: getRiskLevelColor(latestPrediction.behavioral_issue_risk) }]}>
                  {latestPrediction.behavioral_issue_risk ? `${(latestPrediction.behavioral_issue_risk * 100).toFixed(1)}%` : 'N/A'}
                </Text>
              </View>
              
              <View style={styles.riskItem}>
                <Text style={[styles.riskLabel, { color: colors.textLight }]}>{t('environmentalStressRisk')}</Text>
                <Text style={[styles.riskValue, { color: getRiskLevelColor(latestPrediction.environmental_stress_risk) }]}>
                  {latestPrediction.environmental_stress_risk ? `${(latestPrediction.environmental_stress_risk * 100).toFixed(1)}%` : 'N/A'}
                </Text>
              </View>
            </View>
          </Card>
        )}
        
        {/* Quick Actions */}
        <Card style={styles.section}>
          <View style={styles.sectionHeader}>
            <RefreshCw size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Quick Actions</Text>
          </View>
          
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={[styles.quickAction, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={() => navigation.navigate('EnvironmentalAnalysis', { animalId })}
            >
              <Target size={24} color={colors.primary} />
              <Text style={[styles.quickActionText, { color: colors.text }]}>{t('environmentalAnalysis')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.quickAction, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={handleGeneratePredictions}
              disabled={isLoadingPredictions}
            >
              <Brain size={24} color={colors.primary} />
              <Text style={[styles.quickActionText, { color: colors.text }]}>Update Predictions</Text>
            </TouchableOpacity>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 40,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  animalHeader: {
    marginHorizontal: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  animalInfo: {
    flex: 1,
  },
  animalName: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  animalBreed: {
    fontSize: 14,
    fontWeight: '500',
  },
  generateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  generateButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  alertItem: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 12,
  },
  alertHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  severityBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 6,
  },
  severityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '700',
  },
  alertProbability: {
    fontSize: 12,
    fontWeight: '600',
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  alertMessage: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  alertActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  alertActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginLeft: 8,
  },
  alertActionText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  horizonSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  horizonOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    borderWidth: 1,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  horizonOptionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  predictionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  predictionItem: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  predictionLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 8,
    marginBottom: 4,
    textAlign: 'center',
  },
  predictionValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  predictionTrend: {
    marginTop: 4,
  },
  confidenceContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  confidenceLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  confidenceValue: {
    fontSize: 14,
    fontWeight: '700',
    marginLeft: 4,
  },
  riskGrid: {
    marginBottom: 16,
  },
  riskItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  riskLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  riskValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickAction: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginHorizontal: 4,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
});

export default PredictiveInsightsScreen;