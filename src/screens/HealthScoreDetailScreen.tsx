import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  Dimensions
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Heart, TrendingUp, RefreshCw, Calendar, Target } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalStore } from '../store/animalStore';
import { useAIHealthStore } from '../store/aiHealthStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import { toast } from 'sonner-native';
import { format } from 'date-fns';

type HealthScoreDetailScreenRouteProp = RouteProp<HomeStackParamList, 'HealthScoreDetail'>;
type HealthScoreDetailScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'HealthScoreDetail'>;

const { width } = Dimensions.get('window');

const HealthScoreDetailScreen: React.FC = () => {
  const navigation = useNavigation<HealthScoreDetailScreenNavigationProp>();
  const route = useRoute<HealthScoreDetailScreenRouteProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  // Safe destructuring with fallback for missing params
  const animalId = route.params?.animalId;
  
  // Early return if no animalId provided
  if (!animalId) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('healthScoreDetails')} showBack />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {t('missingAnimalId')}
          </Text>
        </View>
      </View>
    );
  }
  
  const { animals } = useAnimalStore();
  const {
    healthScores,
    currentHealthScore,
    isLoadingHealthScores,
    fetchHealthScores,
    calculateHealthScore
  } = useAIHealthStore();
  
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d'>('30d');
  
  const currentAnimal = animals.find(a => a.id === animalId);

  useEffect(() => {
    if (animalId) {
      loadHealthScores();
    }
  }, [animalId]);

  const loadHealthScores = async () => {
    try {
      await fetchHealthScores(animalId);
    } catch (error) {
      console.error('Error loading health scores:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadHealthScores();
    setRefreshing(false);
  };

  const handleCalculateScore = async () => {
    await calculateHealthScore(animalId);
    await loadHealthScores();
  };

  const getScoreColor = (score: number) => {
    if (score >= 85) return '#10B981';
    if (score >= 70) return '#F59E0B';
    if (score >= 50) return '#EF4444';
    return '#6B7280';
  };

  const getScoreStatus = (score: number) => {
    if (score >= 85) return t('excellent');
    if (score >= 70) return t('good');
    if (score >= 50) return t('concerning');
    return t('critical');
  };

  const getFilteredScores = () => {
    const days = selectedPeriod === '7d' ? 7 : selectedPeriod === '30d' ? 30 : 90;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    return healthScores.filter(score => 
      new Date(score.score_date) >= cutoffDate
    );
  };

  const filteredScores = getFilteredScores();

  if (!currentAnimal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('healthScoreDetails')} showBack />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {t('animalNotFound')}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={colors.statusBarStyle} backgroundColor={colors.background} />
      <Header title={`${currentAnimal.name} - ${t('healthScore')}`} showBack />
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Current Score Card */}
        {currentHealthScore && (
          <Card style={styles.currentScoreCard}>
            <View style={styles.currentScoreHeader}>
              <Text style={[styles.currentScoreTitle, { color: colors.text }]}>
                {t('currentHealthScore')}
              </Text>
              <TouchableOpacity
                style={styles.refreshButton}
                onPress={handleCalculateScore}
              >
                <RefreshCw size={16} color={colors.primary} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.scoreDisplay}>
              <View style={[
                styles.scoreCircle,
                { borderColor: getScoreColor(currentHealthScore.overall_score) }
              ]}>
                <Text style={[
                  styles.scoreNumber,
                  { color: getScoreColor(currentHealthScore.overall_score) }
                ]}>
                  {currentHealthScore.overall_score}
                </Text>
                <Text style={[styles.scoreOutOf, { color: colors.textLight }]}>
                  /100
                </Text>
              </View>
              
              <View style={styles.scoreInfo}>
                <Text style={[
                  styles.scoreStatus,
                  { color: getScoreColor(currentHealthScore.overall_score) }
                ]}>
                  {getScoreStatus(currentHealthScore.overall_score)}
                </Text>
                <Text style={[styles.scoreDate, { color: colors.textLight }]}>
                  {format(new Date(currentHealthScore.score_date), 'MMMM dd, yyyy')}
                </Text>
                <Text style={[styles.dataQuality, { color: colors.textLight }]}>
                  {t('dataQuality')}: {currentHealthScore.data_quality_score}%
                </Text>
              </View>
            </View>
            
            {/* Component Breakdown */}
            <View style={styles.componentBreakdown}>
              <Text style={[styles.breakdownTitle, { color: colors.text }]}>
                {t('scoreBreakdown')}
              </Text>
              
              <View style={styles.componentGrid}>
                <ScoreComponent
                  label={t('vitals')}
                  score={currentHealthScore.vitals_score}
                  colors={colors}
                  getScoreColor={getScoreColor}
                />
                <ScoreComponent
                  label={t('activity')}
                  score={currentHealthScore.activity_score}
                  colors={colors}
                  getScoreColor={getScoreColor}
                />
                <ScoreComponent
                  label={t('feeding')}
                  score={currentHealthScore.feeding_score}
                  colors={colors}
                  getScoreColor={getScoreColor}
                />
                <ScoreComponent
                  label={t('medication')}
                  score={currentHealthScore.medication_score}
                  colors={colors}
                  getScoreColor={getScoreColor}
                />
              </View>
            </View>
            
            {/* Explanation */}
            {currentHealthScore.score_explanation && (
              <View style={styles.explanationContainer}>
                <Text style={[styles.explanationTitle, { color: colors.text }]}>
                  {t('analysis')}
                </Text>
                <Text style={[styles.explanationText, { color: colors.textLight }]}>
                  {currentHealthScore.score_explanation}
                </Text>
              </View>
            )}
            
            {/* Recommendations */}
            {currentHealthScore.recommendations && currentHealthScore.recommendations.length > 0 && (
              <View style={styles.recommendationsContainer}>
                <Text style={[styles.recommendationsTitle, { color: colors.text }]}>
                  {t('recommendations')}
                </Text>
                {currentHealthScore.recommendations.map((recommendation, index) => (
                  <View key={index} style={styles.recommendationItem}>
                    <Text style={[styles.recommendationBullet, { color: colors.primary }]}>
                      •
                    </Text>
                    <Text style={[styles.recommendationText, { color: colors.textLight }]}>
                      {recommendation}
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </Card>
        )}

        {/* Period Selector */}
        <Card style={styles.periodSelectorCard}>
          <Text style={[styles.periodTitle, { color: colors.text }]}>
            {t('healthScoreHistory')}
          </Text>
          
          <View style={styles.periodSelector}>
            {(['7d', '30d', '90d'] as const).map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.periodButton,
                  {
                    backgroundColor: selectedPeriod === period ? colors.primary : colors.card,
                    borderColor: colors.border
                  }
                ]}
                onPress={() => setSelectedPeriod(period)}
              >
                <Text style={[
                  styles.periodButtonText,
                  {
                    color: selectedPeriod === period ? '#FFFFFF' : colors.text
                  }
                ]}>
                  {period === '7d' ? t('sevenDays') : period === '30d' ? t('thirtyDays') : t('ninetyDays')}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        {/* Score History */}
        <Card style={styles.historyCard}>
          <View style={styles.historyHeader}>
            <TrendingUp size={20} color={colors.primary} />
            <Text style={[styles.historyTitle, { color: colors.text }]}>
              {t('scoreHistory')} ({filteredScores.length} {t('entries')})
            </Text>
          </View>
          
          {filteredScores.length > 0 ? (
            <View style={styles.historyList}>
              {filteredScores.map((score, index) => (
                <View key={score.id} style={[
                  styles.historyItem,
                  { borderColor: colors.border },
                  index === filteredScores.length - 1 && { borderBottomWidth: 0 }
                ]}>
                  <View style={styles.historyItemLeft}>
                    <Text style={[styles.historyDate, { color: colors.text }]}>
                      {format(new Date(score.score_date), 'MMM dd')}
                    </Text>
                    <Text style={[styles.historyTime, { color: colors.textLight }]}>
                      {format(new Date(score.created_at), 'HH:mm')}
                    </Text>
                  </View>
                  
                  <View style={styles.historyItemCenter}>
                    <View style={styles.historyScores}>
                      <Text style={[styles.historyMainScore, { color: colors.text }]}>
                        {score.overall_score}
                      </Text>
                      <View style={styles.historyComponentScores}>
                        <Text style={[styles.historyComponentScore, { color: colors.textLight }]}>
                          V:{score.vitals_score} A:{score.activity_score} F:{score.feeding_score} M:{score.medication_score}
                        </Text>
                      </View>
                    </View>
                  </View>
                  
                  <View style={styles.historyItemRight}>
                    <View style={[
                      styles.historyStatusBadge,
                      { backgroundColor: getScoreColor(score.overall_score) }
                    ]}>
                      <Text style={styles.historyStatusText}>
                        {getScoreStatus(score.overall_score).toUpperCase()}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.emptyHistory}>
              <Calendar size={32} color={colors.textLight} />
              <Text style={[styles.emptyHistoryText, { color: colors.textLight }]}>
                {t('noScoreHistory')}
              </Text>
              <TouchableOpacity
                style={[styles.calculateButton, { backgroundColor: colors.primary }]}
                onPress={handleCalculateScore}
              >
                <Target size={16} color="#FFFFFF" />
                <Text style={styles.calculateButtonText}>
                  {t('calculateFirstScore')}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </Card>
      </ScrollView>
    </View>
  );
};

// Score Component Display
interface ScoreComponentProps {
  label: string;
  score: number;
  colors: any;
  getScoreColor: (score: number) => string;
}

const ScoreComponent: React.FC<ScoreComponentProps> = ({ label, score, colors, getScoreColor }) => {
  return (
    <View style={styles.componentItem}>
      <Text style={[styles.componentLabel, { color: colors.textLight }]}>
        {label}
      </Text>
      <Text style={[
        styles.componentScore,
        { color: getScoreColor(score) }
      ]}>
        {score}
      </Text>
      <View style={[
        styles.componentBar,
        { backgroundColor: colors.border }
      ]}>
        <View style={[
          styles.componentBarFill,
          {
            width: `${score}%`,
            backgroundColor: getScoreColor(score)
          }
        ]} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500'
  },
  currentScoreCard: {
    padding: 20,
    marginVertical: 8
  },
  currentScoreHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  currentScoreTitle: {
    fontSize: 18,
    fontWeight: '600'
  },
  refreshButton: {
    padding: 4
  },
  scoreDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24
  },
  scoreCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 20
  },
  scoreNumber: {
    fontSize: 32,
    fontWeight: 'bold'
  },
  scoreOutOf: {
    fontSize: 14,
    marginTop: -4
  },
  scoreInfo: {
    flex: 1
  },
  scoreStatus: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4
  },
  scoreDate: {
    fontSize: 14,
    marginBottom: 4
  },
  dataQuality: {
    fontSize: 12
  },
  componentBreakdown: {
    marginBottom: 24
  },
  breakdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16
  },
  componentGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between'
  },
  componentItem: {
    width: '48%',
    marginBottom: 16
  },
  componentLabel: {
    fontSize: 12,
    marginBottom: 4
  },
  componentScore: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8
  },
  componentBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden'
  },
  componentBarFill: {
    height: '100%',
    borderRadius: 2
  },
  explanationContainer: {
    marginBottom: 24
  },
  explanationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8
  },
  explanationText: {
    fontSize: 14,
    lineHeight: 20
  },
  recommendationsContainer: {
    marginBottom: 8
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8
  },
  recommendationBullet: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
    marginTop: 2
  },
  recommendationText: {
    fontSize: 14,
    flex: 1,
    lineHeight: 20
  },
  periodSelectorCard: {
    padding: 16,
    marginVertical: 8
  },
  periodTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16
  },
  periodSelector: {
    flexDirection: 'row',
    gap: 8
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    alignItems: 'center'
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500'
  },
  historyCard: {
    padding: 16,
    marginVertical: 8
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600'
  },
  historyList: {
    gap: 0
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1
  },
  historyItemLeft: {
    width: 60
  },
  historyDate: {
    fontSize: 14,
    fontWeight: '500'
  },
  historyTime: {
    fontSize: 11
  },
  historyItemCenter: {
    flex: 1,
    paddingHorizontal: 12
  },
  historyScores: {
    alignItems: 'flex-start'
  },
  historyMainScore: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2
  },
  historyComponentScores: {
    flexDirection: 'row'
  },
  historyComponentScore: {
    fontSize: 10
  },
  historyItemRight: {
    width: 80,
    alignItems: 'flex-end'
  },
  historyStatusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4
  },
  historyStatusText: {
    color: '#FFFFFF',
    fontSize: 9,
    fontWeight: 'bold'
  },
  emptyHistory: {
    alignItems: 'center',
    paddingVertical: 32
  },
  emptyHistoryText: {
    fontSize: 14,
    marginTop: 12,
    marginBottom: 20
  },
  calculateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 6,
    gap: 8
  },
  calculateButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500'
  }
});

export default HealthScoreDetailScreen;