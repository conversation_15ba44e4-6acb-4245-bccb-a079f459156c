
import React, { useState, useEffect } from 'react';
import { 
  View, 
  StyleSheet, 
  ScrollView,
  StatusBar,
  Alert,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { toast } from 'sonner-native';
import CrispService from '../services/CrispService';
import { useUserStore } from '../store/userStore';
import { supabase } from '../supabase/client';
import { LinearGradient } from 'expo-linear-gradient';
import ContactHeader from '../components/contact/ContactHeader';
import ContactForm from '../components/contact/ContactForm';
import ContactOptions from '../components/contact/ContactOptions';

const ContactScreen = () => {
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  const { user } = useUserStore();
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  
  const handleSendMessage = async (name: string, email: string, message: string): Promise<boolean> => {
    setIsLoading(true);
    setEmailSent(false);
    
    try {
      // Call our Supabase Edge Function to send the email
      const { data, error } = await supabase.functions.invoke('send-contact-email', {
        body: {
          name,
          email,
          message
        }
      });
      
      if (error) {
        throw new Error(error.message);
      }
      
      // Log the email that would be sent
      console.log(`
        Email would be sent to: <EMAIL>
        From: ${name} (${email})
        Message: ${message}
      `);
      
      // Show success toast and update UI state
      toast.success(t('messageSent'));
      setEmailSent(true);
      
      // Show a success alert for extra confirmation
      Alert.alert(
        t('messageSentTitle'),
        t('messageSentDescription'),
        [{ text: t('ok') }]
      );
      
      return true;
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error(t('failedToSendMessage'));
      
      // Show error alert with more details
      Alert.alert(
        t('errorSendingMessageTitle'),
        t('errorSendingMessageDescription'),
        [{ text: t('ok') }]
      );
      
      return false;
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleOpenChat = () => {
    // Set user info for Crisp
    if (user) {
      CrispService.setUserInfo(user.email, user.name || 'User');
    }
    
    // Open Crisp chat
    const success = CrispService.openChat();
    
    if (!success) {
      Alert.alert(
        t('chatError'),
        t('failedToOpenChat'),
        [{ text: t('ok') }]
      );
    } else {
      toast.success(t('openingChatSupport'));
    }
  };
  
  return (
    <KeyboardAvoidingView 
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar 
          barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
          backgroundColor={colors.background} 
        />
        
        <Header title={t('contactUs')} showBackButton />
        
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <LinearGradient
            colors={[
              isDarkMode ? 'rgba(61, 140, 145, 0.2)' : 'rgba(61, 140, 145, 0.1)', 
              'transparent'
            ]}
            style={styles.gradientBackground}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
          
          <View style={styles.content}>
            <ContactHeader emailSent={emailSent} />
            
            <ContactForm
              initialName={user?.name || ''}
              initialEmail={user?.email || ''}
              onSubmit={handleSendMessage}
              isLoading={isLoading}
            />
            
            <ContactOptions onOpenChat={handleOpenChat} />
          </View>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  gradientBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 300,
  },
  content: {
    padding: 20,
  },
});

export default ContactScreen;
