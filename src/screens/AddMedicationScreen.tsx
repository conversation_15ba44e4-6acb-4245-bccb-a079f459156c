
import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AlertCircle } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAnimalStore } from '../store/animalStore';
import { useMedicationStore } from '../store/medicationStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import AddMedicationForm from '../components/medication/AddMedicationForm';

type AddMedicationScreenRouteProp = RouteProp<HomeStackParamList, 'AddMedication'>;
type AddMedicationScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'AddMedication'>;

const AddMedicationScreen = () => {
  const navigation = useNavigation<AddMedicationScreenNavigationProp>();
  const route = useRoute<AddMedicationScreenRouteProp>();
  const { colors } = useTheme();
  const { animalId } = route.params || {};
  
  const { getAnimalById } = useAnimalStore();
  const { addMedication } = useMedicationStore();
  
  const animal = animalId ? getAnimalById(animalId) : null;
  
  const handleSaveSuccess = () => {
    navigation.goBack();
  };
  
  return (
    <KeyboardAvoidingView 
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header 
          title={animal ? `Add Medication for ${animal.name}` : 'Add Medication'} 
          showBackButton 
        />
        
        {!animal && (
          <View style={[styles.warningBanner, { backgroundColor: colors.warning + '20' }]}>
            <AlertCircle size={20} color={colors.warning} />
            <Text style={[styles.warningText, { color: colors.warning }]}>
              No animal selected. Please go back and select an animal.
            </Text>
          </View>
        )}
        
        {animal && (
          <AddMedicationForm
            animalId={animal.id}
            addMedicationFunction={addMedication}
            onSaveSuccess={handleSaveSuccess}
          />
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  warningBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    margin: 16,
  },
  warningText: {
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
});

export default AddMedicationScreen;
