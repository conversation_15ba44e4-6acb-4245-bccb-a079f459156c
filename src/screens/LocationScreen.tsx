
import React, { useEffect } from 'react';
import { 
  View, 
  StyleSheet, 
  StatusBar
} from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { useTheme } from '../contexts/ThemeContext';
import { HomeStackParamList } from '../navigation';
import { useLocationManagement } from '../hooks/useLocationManagement';
import Header from '../components/Header';
import MapViewComponent from '../components/location/MapViewComponent';
import AnimalLocationInfo from '../components/location/AnimalLocationInfo';
import LocationActions from '../components/location/LocationActions';

type LocationScreenRouteProp = RouteProp<HomeStackParamList, 'Location'>;

/**
 * @magic_description Animal location tracking screen
 * Displays animal location on map with tracking and device management
 */
const LocationScreen = () => {
  const route = useRoute<LocationScreenRouteProp>();
  const navigation = useNavigation();
  const { colors, isDarkMode } = useTheme();
  const { animalId } = route.params;
  
  // Get location management data and actions from custom hook
  const {
    animal,
    isLoading,
    isUpdating,
    connectedDevice,
    lastUpdated,
    handleUpdateLocation,
    handleOpenInMaps,
    handleConnectDevice,
  } = useLocationManagement(animalId);
  
  // Navigate back if animal not found
  useEffect(() => {
    if (!animal) {
      navigation.goBack();
    }
  }, [animal, navigation]);
  
  // Don't render if animal not found
  if (!animal) {
    return null;
  }
  

  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <Header title={`${animal.name}'s Location`} showBackButton />
      
      <View style={styles.content}>
        <MapViewComponent
          animalLocation={animal.location}
          animalName={animal.name}
          colors={colors}
          isDarkMode={isDarkMode}
          onLocationUpdate={() => {
            // Location updates are handled by the hook
            // This callback is kept for potential future use
          }}
        />
        
        <AnimalLocationInfo
          lastUpdated={lastUpdated}
          connectedDevice={connectedDevice}
          coordinates={animal.location ? {
            latitude: animal.location.latitude,
            longitude: animal.location.longitude
          } : null}
          colors={colors}
        />
        
        <LocationActions
          onUpdateLocation={handleUpdateLocation}
          onOpenInMaps={handleOpenInMaps}
          onConnectDevice={handleConnectDevice}
          isUpdating={isUpdating}
          isLoading={isLoading}
          hasLocation={!!animal.location}
          connectedDevice={connectedDevice}
          colors={colors}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});

export default LocationScreen;
