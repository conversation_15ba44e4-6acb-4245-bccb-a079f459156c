import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Share
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../contexts/ThemeContext';
import { useAnimalStore } from '../store/animalStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import { toast } from 'sonner-native';
import AnimalProfileHeader from '../components/animal/AnimalProfileHeader';
import AnimalQuickActions from '../components/animal/AnimalQuickActions';
import RecentActivityCard from '../components/animal/RecentActivityCard';
import UpcomingEventsCard from '../components/animal/UpcomingEventsCard';
import { useImagePicker } from '../hooks/useImagePicker';
import { useState, useEffect } from 'react';

type AnimalDetailScreenRouteProp = RouteProp<HomeStackParamList, 'AnimalDetail'>;
type AnimalDetailScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'AnimalDetail'>;

const AnimalDetailScreenRefactored = () => {
  const navigation = useNavigation<AnimalDetailScreenNavigationProp>();
  const route = useRoute<AnimalDetailScreenRouteProp>();
  const { colors } = useTheme();
  const { animals, updateAnimal } = useAnimalStore();
  const { showImagePicker } = useImagePicker();
  
  const { animalId } = route.params;
  const animal = animals.find(a => a.id === animalId);
  
  const [recentActivities, setRecentActivities] = useState<any[]>([]);
  const [upcomingEvents, setUpcomingEvents] = useState<any[]>([]);
  
  useEffect(() => {
    if (!animal) {
      navigation.goBack();
      return;
    }
    
    // Mock recent activities - in real app, fetch from stores
    setRecentActivities([
      {
        id: '1',
        type: 'training',
        title: 'Training Session',
        description: 'Completed 30-minute agility training',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      },
      {
        id: '2',
        type: 'vitals',
        title: 'Vitals Recorded',
        description: 'Heart rate: 72 bpm, Temperature: 101.5°F',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
      },
      {
        id: '3',
        type: 'feeding',
        title: 'Fed',
        description: 'Morning feed - 2.5kg hay',
        timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
      },
    ]);
    
    // Mock upcoming events - in real app, fetch from stores
    setUpcomingEvents([
      {
        id: '1',
        type: 'feeding',
        title: 'Evening Feed',
        description: '3kg hay + supplements',
        scheduledTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
      },
      {
        id: '2',
        type: 'medication',
        title: 'Antibiotic Dose',
        description: 'Penicillin 10ml injection',
        scheduledTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
      },
      {
        id: '3',
        type: 'vaccination',
        title: 'Annual Vaccination',
        description: 'Tetanus and flu vaccination due',
        scheduledTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Next week
      },
    ]);
  }, [animal, navigation]);
  
  if (!animal) {
    return null;
  }
  
  const handleEdit = () => {
    navigation.navigate('AddAnimal', { animalId: animal.id });
  };
  
  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out ${animal.name}, a ${animal.breed} in my livestock management app!`,
        title: `${animal.name} - Livestock Profile`,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };
  
  const handleBack = () => {
    navigation.goBack();
  };
  
  const handleLogTraining = () => {
    navigation.navigate('LogTrainingSession', { animalId: animal.id });
  };
  
  const handleRecordVitals = () => {
    navigation.navigate('RecordVitals', { animalId: animal.id });
  };
  
  const handleViewFeeding = () => {
    navigation.navigate('FeedSchedule', { animalId: animal.id });
  };
  
  const handleAddMedication = () => {
    navigation.navigate('AddMedication', { animalId: animal.id });
  };
  
  const handleViewLocation = () => {
    navigation.navigate('Location', { animalId: animal.id });
  };
  
  const handleTakePhoto = async () => {
    try {
      const imageUri = await showImagePicker();
      if (imageUri) {
        await updateAnimal(animal.id, { imageUrl: imageUri });
        toast.success('Photo updated successfully!');
      }
    } catch (error) {
      console.error('Error updating photo:', error);
      toast.error('Failed to update photo');
    }
  };
  
  const handleAIAnalysis = () => {
    navigation.navigate('AIAssistant', { animalId: animal.id });
  };
  
  const handleHealthDashboard = () => {
    navigation.navigate('AIHealthDashboard', { animalId: animal.id });
  };
  
  const handleEventPress = (event: any) => {
    // Navigate to appropriate screen based on event type
    switch (event.type) {
      case 'feeding':
        handleViewFeeding();
        break;
      case 'medication':
        handleAddMedication();
        break;
      case 'vaccination':
        // Navigate to vaccination screen when available
        toast.info('Vaccination management coming soon!');
        break;
      case 'checkup':
        handleRecordVitals();
        break;
      default:
        break;
    }
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header
        title={animal.name}
        showBack
        onBack={handleBack}
      />
      
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <AnimalProfileHeader
          animal={animal}
          onEdit={handleEdit}
          onShare={handleShare}
          onBack={handleBack}
        />
        
        <AnimalQuickActions
          onLogTraining={handleLogTraining}
          onRecordVitals={handleRecordVitals}
          onViewFeeding={handleViewFeeding}
          onAddMedication={handleAddMedication}
          onViewLocation={handleViewLocation}
          onTakePhoto={handleTakePhoto}
          onAIAnalysis={handleAIAnalysis}
          onHealthDashboard={handleHealthDashboard}
        />
        
        <RecentActivityCard activities={recentActivities} />
        
        <UpcomingEventsCard
          events={upcomingEvents}
          onEventPress={handleEventPress}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
});

export default AnimalDetailScreenRefactored;