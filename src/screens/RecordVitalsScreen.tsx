
import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RefreshCw, AlertCircle } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import VitalCard from '../components/VitalCard';
import PremiumUpsellBanner from '../components/PremiumUpsellBanner';
import { useAnimalVitalsHistory } from '../hooks/useAnimalVitalsHistory';
import { VitalRecord } from '../mocks/vitals';

type RecordVitalsScreenRouteProp = RouteProp<HomeStackParamList, 'RecordVitals'>;
type RecordVitalsScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'RecordVitals'>;

/**
 * @magic_description Vital signs history screen
 * Displays animal's vital history with premium feature limitations
 */
const RecordVitalsScreen = () => {
  const navigation = useNavigation<RecordVitalsScreenNavigationProp>();
  const route = useRoute<RecordVitalsScreenRouteProp>();
  const { colors } = useTheme();
  const { animalId } = route.params || {};
  
  // Get vital history data and actions from custom hook
  const {
    animal,
    vitalsHistory,
    isLoading,
    isRefreshing,
    isPremium,
    isLimitedView,
    error,
    refreshVitalsHistory,
  } = useAnimalVitalsHistory(animalId || '');
  
  // Handle navigation to subscription screen
  const handleUpgradePress = () => {
    navigation.navigate('SubscriptionScreen' as any);
  };
  
  // Loading state
  if (isLoading) {
    return (
      <View style={[styles.container, styles.centerContainer, { backgroundColor: colors.background }]}>
        <Header 
          title="Vital History" 
          showBackButton 
        />
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading vital history...</Text>
      </View>
    );
  }
  
  // Error state
  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header 
          title="Vital History" 
          showBackButton 
        />
        <View style={styles.errorContainer}>
          <AlertCircle size={48} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.error }]}>{error}</Text>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={refreshVitalsHistory}
          >
            <RefreshCw size={16} color="#FFF" />
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  
  // Render vital history list item
  const renderVitalItem = ({ item }: { item: VitalRecord }) => (
    <VitalCard vital={item} />
  );
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header 
        title={animal ? `${animal.name}'s Vital History` : 'Vital History'} 
        showBackButton 
        rightComponent={
          <TouchableOpacity 
            style={styles.refreshButton}
            onPress={refreshVitalsHistory}
            disabled={isRefreshing}
          >
            <RefreshCw 
              size={20} 
              color={colors.primary} 
              style={isRefreshing ? styles.spinning : undefined}
            />
          </TouchableOpacity>
        }
      />
      
      <View style={styles.content}>
        {/* Premium upsell banner for limited view */}
        {isLimitedView && (
          <PremiumUpsellBanner
            message={`You're viewing the last 3 vital records. Upgrade to Premium to see complete history for ${animal?.name || 'this animal'}.`}
            buttonText="Upgrade to Premium"
            onUpgradePress={handleUpgradePress}
          />
        )}
        
        {/* Vital history list */}
        {vitalsHistory.length === 0 ? (
          <View style={styles.emptyContainer}>
            <AlertCircle size={48} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No vital records found for {animal?.name || 'this animal'}
            </Text>
            <Text style={[styles.emptySubtext, { color: colors.textLight }]}>
              Vital signs will appear here once recorded by connected devices
            </Text>
          </View>
        ) : (
          <FlatList
            data={vitalsHistory}
            renderItem={renderVitalItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={refreshVitalsHistory}
                colors={[colors.primary]}
                tintColor={colors.primary}
              />
            }
          />
        )}
        
        {/* Premium status indicator */}
        {!isPremium && vitalsHistory.length > 0 && (
          <View style={[styles.statusBar, { backgroundColor: colors.warning + '20' }]}>
            <Text style={[styles.statusText, { color: colors.warning }]}>
              Free Plan: Showing recent records only
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  refreshButton: {
    padding: 8,
  },
  spinning: {
    transform: [{ rotate: '180deg' }],
  },
  listContainer: {
    paddingBottom: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  retryButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '500',
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  statusBar: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default RecordVitalsScreen;
