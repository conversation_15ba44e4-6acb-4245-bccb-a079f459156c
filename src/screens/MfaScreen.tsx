import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  StatusBar,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { 
  Shield, 
  Smartphone, 
  Key, 
  CheckCircle, 
  AlertTriangle,
  Copy,
  QrCode
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { ProfileStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import { toast } from 'sonner-native';
import { supabase } from '../supabase/client';
import * as Clipboard from 'expo-clipboard';
import QRCodeDisplay from '../components/auth/QRCodeDisplay';

type MfaScreenNavigationProp = NativeStackNavigationProp<ProfileStackParamList, 'MfaScreen'>;

interface MfaSettings {
  isEnabled: boolean;
  secret?: string;
  qrCodeUrl?: string;
  backupCodes?: string[];
}

const MfaScreen = () => {
  const navigation = useNavigation<MfaScreenNavigationProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  const [isLoading, setIsLoading] = useState(false);
  const [mfaSettings, setMfaSettings] = useState<MfaSettings>({ isEnabled: false });
  const [setupStep, setSetupStep] = useState<'overview' | 'setup' | 'verify' | 'complete'>('overview');
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [setupError, setSetupError] = useState<string | null>(null);
  const [verificationAttempts, setVerificationAttempts] = useState(0);

  useEffect(() => {
    checkMfaStatus();
  }, []);

  const checkMfaStatus = async () => {
    try {
      setIsLoading(true);
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error('Please log in to access MFA settings');
        navigation.goBack();
        return;
      }

      // Check if user has MFA enabled
      const { data: mfaData, error } = await supabase
        .from('user_mfa_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error checking MFA status:', error);
        toast.error('Failed to load MFA settings');
        return;
      }

      if (mfaData && mfaData.is_enabled) {
        setMfaSettings({ isEnabled: true });
      } else {
        setMfaSettings({ isEnabled: false });
      }
    } catch (error) {
      console.error('Error checking MFA status:', error);
      toast.error('Failed to load MFA settings');
    } finally {
      setIsLoading(false);
    }
  };

  const startMfaSetup = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase.functions.invoke('mfa-enroll', {
        body: { action: 'generate_secret' }
      });

      if (error) {
        console.error('Error starting MFA setup:', error);
        toast.error('Failed to start MFA setup');
        return;
      }

      if (data.success) {
        setMfaSettings({
          isEnabled: false,
          secret: data.secret,
          qrCodeUrl: data.qr_code_url,
          backupCodes: data.backup_codes
        });
        setSetupStep('setup');
        toast.success('MFA setup started. Scan the QR code with your authenticator app.');
      } else {
        toast.error(data.error || 'Failed to generate MFA secret');
      }
    } catch (error) {
      console.error('Error starting MFA setup:', error);
      toast.error('Failed to start MFA setup');
    } finally {
      setIsLoading(false);
    }
  };

  const verifyMfaSetup = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setSetupError('Please enter a valid 6-digit code');
      return;
    }

    try {
      setIsVerifying(true);
      setSetupError(null);
      
      const { data, error } = await supabase.functions.invoke('mfa-enroll', {
        body: { 
          action: 'verify_setup',
          verification_code: verificationCode
        }
      });

      if (error) {
        console.error('Error verifying MFA setup:', error);
        setSetupError('Failed to verify MFA setup. Please try again.');
        setVerificationAttempts(prev => prev + 1);
        return;
      }

      if (data.success) {
        setMfaSettings(prev => ({ ...prev, isEnabled: true }));
        setSetupStep('complete');
        setVerificationCode('');
        setVerificationAttempts(0);
        toast.success('Two-factor authentication enabled successfully!');
      } else {
        setSetupError(data.error || 'Invalid verification code. Please check your authenticator app.');
        setVerificationAttempts(prev => prev + 1);
        
        // Clear the input after failed attempt
        setVerificationCode('');
        
        // Show helpful message after multiple attempts
        if (verificationAttempts >= 2) {
          setSetupError('Having trouble? Make sure your device time is synchronized and try generating a new code.');
        }
      }
    } catch (error) {
      console.error('Error verifying MFA setup:', error);
      setSetupError('Network error. Please check your connection and try again.');
      setVerificationAttempts(prev => prev + 1);
    } finally {
      setIsVerifying(false);
    }
  };

  const disableMfa = async () => {
    Alert.alert(
      'Disable Two-Factor Authentication',
      'Are you sure you want to disable 2FA? This will make your account less secure.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Disable',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              
              const { data: { user } } = await supabase.auth.getUser();
              if (!user) return;

              const { error } = await supabase
                .from('user_mfa_settings')
                .update({ is_enabled: false })
                .eq('user_id', user.id);

              if (error) {
                console.error('Error disabling MFA:', error);
                toast.error('Failed to disable MFA');
                return;
              }

              setMfaSettings({ isEnabled: false });
              setSetupStep('overview');
              toast.success('Two-factor authentication disabled');
            } catch (error) {
              console.error('Error disabling MFA:', error);
              toast.error('Failed to disable MFA');
            } finally {
              setIsLoading(false);
            }
          }
        }
      ]
    );
  };

  const copyToClipboard = async (text: string, label: string) => {
    await Clipboard.setStringAsync(text);
    toast.success(`${label} copied to clipboard`);
  };

  const renderOverview = () => (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <View style={styles.cardHeader}>
          <Shield size={24} color={mfaSettings.isEnabled ? colors.success : colors.primary} />
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Two-Factor Authentication
          </Text>
        </View>
        
        <Text style={[styles.description, { color: colors.textLight }]}>
          {mfaSettings.isEnabled 
            ? 'Two-factor authentication is currently enabled for your account. This adds an extra layer of security by requiring a verification code from your authenticator app when signing in.'
            : 'Add an extra layer of security to your account by enabling two-factor authentication. You\'ll need an authenticator app like Google Authenticator or Authy.'
          }
        </Text>

        <View style={styles.statusContainer}>
          <View style={styles.statusItem}>
            {mfaSettings.isEnabled ? (
              <CheckCircle size={20} color={colors.success} />
            ) : (
              <AlertTriangle size={20} color={colors.warning} />
            )}
            <Text style={[styles.statusText, { color: colors.text }]}>
              Status: {mfaSettings.isEnabled ? 'Enabled' : 'Disabled'}
            </Text>
          </View>
        </View>

        <View style={styles.actionContainer}>
          {mfaSettings.isEnabled ? (
            <TouchableOpacity
              style={[styles.button, styles.dangerButton, { borderColor: colors.error }]}
              onPress={disableMfa}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={colors.error} />
              ) : (
                <Text style={[styles.buttonText, { color: colors.error }]}>
                  Disable 2FA
                </Text>
              )}
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[styles.button, styles.primaryButton, { backgroundColor: colors.primary }]}
              onPress={startMfaSetup}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <Text style={styles.primaryButtonText}>
                  Enable 2FA
                </Text>
              )}
            </TouchableOpacity>
          )}
        </View>
      </Card>

      <Card style={styles.card}>
        <View style={styles.cardHeader}>
          <Smartphone size={24} color={colors.primary} />
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Supported Apps
          </Text>
        </View>
        
        <Text style={[styles.description, { color: colors.textLight }]}>
          You can use any TOTP-compatible authenticator app:
        </Text>
        
        <View style={styles.appList}>
          <Text style={[styles.appItem, { color: colors.text }]}>• Google Authenticator</Text>
          <Text style={[styles.appItem, { color: colors.text }]}>• Microsoft Authenticator</Text>
          <Text style={[styles.appItem, { color: colors.text }]}>• Authy</Text>
          <Text style={[styles.appItem, { color: colors.text }]}>• 1Password</Text>
          <Text style={[styles.appItem, { color: colors.text }]}>• LastPass Authenticator</Text>
        </View>
      </Card>
    </ScrollView>
  );

  const renderSetup = () => (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView>
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <QrCode size={24} color={colors.primary} />
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              Scan QR Code
            </Text>
          </View>
          
          <Text style={[styles.description, { color: colors.textLight }]}>
            1. Open your authenticator app
            2. Tap "Add account" or "+"
            3. Scan this QR code
          </Text>

          {mfaSettings.qrCodeUrl && mfaSettings.secret && (
            <QRCodeDisplay
              qrCodeUrl={mfaSettings.qrCodeUrl}
              secret={mfaSettings.secret}
              onCopySecret={() => {}}
            />
          )}

          <TouchableOpacity
            style={[styles.button, styles.secondaryButton, { borderColor: colors.border }]}
            onPress={() => setSetupStep('verify')}
          >
            <Text style={[styles.buttonText, { color: colors.text }]}>
              I've Added the Account
            </Text>
          </TouchableOpacity>
        </Card>
      </ScrollView>
    </KeyboardAvoidingView>
  );

  const renderVerify = () => (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView>
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Key size={24} color={colors.primary} />
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              Verify Setup
            </Text>
          </View>
          
          <Text style={[styles.description, { color: colors.textLight }]}>
            Enter the 6-digit code from your authenticator app to complete the setup.
          </Text>

          <View style={styles.inputContainer}>
            <TextInput
              style={[styles.codeInput, { 
                backgroundColor: colors.card, 
                color: colors.text,
                borderColor: setupError ? colors.error : colors.border
              }]}
              placeholder="000000"
              placeholderTextColor={colors.textLight}
              value={verificationCode}
              onChangeText={(text) => {
                setVerificationCode(text);
                if (setupError) setSetupError(null);
              }}
              keyboardType="numeric"
              maxLength={6}
              autoFocus
              editable={!isVerifying}
            />
            
            {setupError && (
              <View style={styles.errorContainer}>
                <AlertTriangle size={16} color={colors.error} />
                <Text style={[styles.errorText, { color: colors.error }]}>
                  {setupError}
                </Text>
              </View>
            )}
            
            {verificationAttempts > 0 && !setupError && (
              <Text style={[styles.attemptsText, { color: colors.textLight }]}>
                Attempts: {verificationAttempts}/5
              </Text>
            )}
          </View>

          <View style={styles.actionContainer}>
            <TouchableOpacity
              style={[styles.button, styles.primaryButton, { backgroundColor: colors.primary }]}
              onPress={verifyMfaSetup}
              disabled={isVerifying || verificationCode.length !== 6}
            >
              {isVerifying ? (
                <ActivityIndicator color="#FFFFFF" />
              ) : (
                <Text style={styles.primaryButtonText}>
                  Verify & Enable
                </Text>
              )}
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, styles.secondaryButton, { borderColor: colors.border }]}
              onPress={() => setSetupStep('setup')}
              disabled={isVerifying}
            >
              <Text style={[styles.buttonText, { color: colors.text }]}>
                Back to QR Code
              </Text>
            </TouchableOpacity>
          </View>
        </Card>
      </ScrollView>
    </KeyboardAvoidingView>
  );

  const renderComplete = () => (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <View style={styles.cardHeader}>
          <CheckCircle size={24} color={colors.success} />
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Setup Complete!
          </Text>
        </View>
        
        <Text style={[styles.description, { color: colors.textLight }]}>
          Two-factor authentication has been successfully enabled for your account. 
          From now on, you'll need to enter a code from your authenticator app when signing in.
        </Text>

        {mfaSettings.backupCodes && (
          <View style={styles.backupCodesContainer}>
            <Text style={[styles.backupCodesTitle, { color: colors.text }]}>
              Backup Codes
            </Text>
            <Text style={[styles.backupCodesDescription, { color: colors.textLight }]}>
              Save these backup codes in a safe place. You can use them to access your account if you lose your authenticator device.
            </Text>
            
            {showBackupCodes ? (
              <View style={styles.codesContainer}>
                {mfaSettings.backupCodes.map((code, index) => (
                  <View key={index} style={[styles.codeItem, { backgroundColor: colors.card }]}>
                    <Text style={[styles.codeText, { color: colors.text }]}>{code}</Text>
                  </View>
                ))}
                
                <TouchableOpacity
                  style={[styles.copyButton, { backgroundColor: colors.card, borderColor: colors.border }]}
                  onPress={() => copyToClipboard(mfaSettings.backupCodes!.join('\n'), 'Backup codes')}
                >
                  <Copy size={16} color={colors.primary} />
                  <Text style={[styles.copyButtonText, { color: colors.primary }]}>
                    Copy All Codes
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <TouchableOpacity
                style={[styles.button, styles.secondaryButton, { borderColor: colors.border }]}
                onPress={() => setShowBackupCodes(true)}
              >
                <Text style={[styles.buttonText, { color: colors.text }]}>
                  Show Backup Codes
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        <TouchableOpacity
          style={[styles.button, styles.primaryButton, { backgroundColor: colors.primary }]}
          onPress={() => {
            setSetupStep('overview');
            setVerificationCode('');
            setShowBackupCodes(false);
          }}
        >
          <Text style={styles.primaryButtonText}>
            Done
          </Text>
        </TouchableOpacity>
      </Card>
    </ScrollView>
  );

  const renderContent = () => {
    switch (setupStep) {
      case 'setup':
        return renderSetup();
      case 'verify':
        return renderVerify();
      case 'complete':
        return renderComplete();
      default:
        return renderOverview();
    }
  };

  return (
    <View style={[styles.screen, { backgroundColor: colors.background }]}>
      <StatusBar
        barStyle={colors.text === '#FFFFFF' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />
      
      <Header
        title={t('profileMfaSettings')}
        showBackButton={true}
        onBackPress={() => {
          if (setupStep !== 'overview') {
            setSetupStep('overview');
            setVerificationCode('');
            setShowBackupCodes(false);
          } else {
            navigation.goBack();
          }
        }}
      />
      
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  screen: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 12,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  statusContainer: {
    marginBottom: 20,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  actionContainer: {
    gap: 12,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  primaryButton: {
    // backgroundColor set dynamically
  },
  secondaryButton: {
    borderWidth: 1,
  },
  dangerButton: {
    borderWidth: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  appList: {
    marginTop: 8,
  },
  appItem: {
    fontSize: 14,
    marginBottom: 4,
  },
  qrContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  qrPlaceholder: {
    width: 200,
    height: 200,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 8,
    textAlign: 'center',
    textAlignVertical: 'center',
    fontSize: 12,
    marginBottom: 12,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
  },
  copyButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  inputContainer: {
    marginVertical: 20,
  },
  codeInput: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    letterSpacing: 8,
  },
  backupCodesContainer: {
    marginTop: 20,
    marginBottom: 20,
  },
  backupCodesTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  backupCodesDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  codesContainer: {
    marginTop: 12,
  },
  codeItem: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  codeText: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 4,
  },
  errorText: {
    fontSize: 14,
    marginLeft: 6,
    flex: 1,
  },
  attemptsText: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
  },
});

export default MfaScreen;