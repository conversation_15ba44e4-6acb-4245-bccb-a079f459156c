
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  StatusBar
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { 
  MapPin, 
  Share2, 
  Database, 
  Save
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import Header from '../components/Header';
import { toast } from 'sonner-native';
import PrivacyInfoCard from '../components/settings/PrivacyInfoCard';
import SettingToggleItem from '../components/settings/SettingToggleItem';
import DeleteAccountSection from '../components/settings/DeleteAccountSection';
import DeleteAccountModal from '../components/settings/DeleteAccountModal';

const PrivacySettingsScreen = () => {
  const navigation = useNavigation();
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  
  // Privacy settings
  const [locationTracking, setLocationTracking] = useState(true);
  const [shareData, setShareData] = useState(false);
  const [analyticsEnabled, setAnalyticsEnabled] = useState(true);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  
  const handleSave = () => {
    // In a real app with Supabase, this would save to the database
    toast.success(t('privacySettingsSaved'));
    navigation.goBack();
  };
  
  const handleDeleteAccount = () => {
    // In a real app with Supabase, this would delete the user's account
    toast.success(t('accountDeleted'));
    navigation.navigate('Profile' as never);
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <Header 
        title={t('privacySettings')} 
        showBackButton
        rightAction={{
          icon: <Save size={24} color={colors.primary} />,
          onPress: handleSave,
        }}
      />
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <PrivacyInfoCard />
          
          <SettingToggleItem
            icon={MapPin}
            title={t('locationTracking')}
            description={t('locationTrackingDescription')}
            value={locationTracking}
            onValueChange={setLocationTracking}
          />
          
          <SettingToggleItem
            icon={Share2}
            title={t('shareDataWithVets')}
            description={t('shareDataWithVetsDescription')}
            value={shareData}
            onValueChange={setShareData}
          />
          
          <SettingToggleItem
            icon={Database}
            title={t('analytics')}
            description={t('analyticsDescription')}
            value={analyticsEnabled}
            onValueChange={setAnalyticsEnabled}
          />
          
          <DeleteAccountSection onShowConfirm={() => setShowDeleteConfirm(true)} />
          
          <TouchableOpacity 
            style={[styles.saveButton, { backgroundColor: colors.primary }]}
            onPress={handleSave}
          >
            <Save size={20} color={colors.card} />
            <Text style={[styles.saveButtonText, { color: colors.card }]}>{t('saveSettings')}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
      
      <DeleteAccountModal
        visible={showDeleteConfirm}
        onCancel={() => setShowDeleteConfirm(false)}
        onConfirmDelete={handleDeleteAccount}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingVertical: 12,
    marginBottom: 32,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default PrivacySettingsScreen;
