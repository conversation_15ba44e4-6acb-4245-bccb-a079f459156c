
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  TextInput,
  Switch,
  StatusBar,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { 
  Plus, 
  Printer
} from 'lucide-react-native';
import { COLORS } from '../constants/colors';
import { useTheme } from '../contexts/ThemeContext';
import Header from '../components/Header';
import { useFeedingStore } from '../store/feedingStore';
import { useUserStore } from '../store/userStore';
import { toast } from 'sonner-native';
import FeedScheduleForm from '../components/feedSchedule/FeedScheduleForm';
import FeedScheduleListItem from '../components/feedSchedule/FeedScheduleListItem';
import PremiumUpsellBanner from '../components/PremiumUpsellBanner';

interface FeedScheduleScreenParams {
  animalId: string;
}

type FeedScheduleScreenRouteProp = RouteProp<{ params: FeedScheduleScreenParams }, 'params'>;

const FeedScheduleScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<FeedScheduleScreenRouteProp>();
  const { colors, isDarkMode } = useTheme();
  const { animalId } = route.params;
  
  const { getFeedingsByAnimalId, addFeedingEntry, deleteFeedingEntry } = useFeedingStore();
  const feedings = getFeedingsByAnimalId(animalId);
  
  // Get actual premium status from user store
  const { user } = useUserStore();
  const isPremium = user?.isPremium || false;
  const MAX_FREE_ENTRIES = 3;
  
  const [showAddForm, setShowAddForm] = useState(false);
  

  
  const handleDeleteFeeding = (id: string) => {
    deleteFeedingEntry(id);
    toast.success('Feeding schedule deleted');
  };
  
  const handlePrint = () => {
    if (!isPremium) {
      toast.error('Printing is a Premium feature. Please upgrade to access this feature.');
      return;
    }
    
    navigation.navigate('PrintScreen' as never, { 
      type: 'feeding', 
      animalId 
    } as never);
  };
  
  const handleUpgradeToPremium = () => {
    navigation.navigate('Subscription' as never);
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <Header 
        title="Feeding Schedule" 
        showBackButton
        rightAction={{
          icon: <Printer size={24} color={isPremium ? colors.primary : colors.textLight} />,
          onPress: handlePrint,
        }}
      />
      
      <KeyboardAvoidingView 
        style={styles.keyboardAvoidingContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 88 : 0}
      >
        <ScrollView style={styles.scrollView}>
          <View style={styles.content}>
            {!isPremium && feedings.length >= MAX_FREE_ENTRIES && (
              <PremiumUpsellBanner
                message="Free plan limited to 3 feeding schedules. Upgrade to Premium for unlimited schedules."
                buttonText="Upgrade"
                onUpgradePress={handleUpgradeToPremium}
              />
            )}
            
            {!showAddForm && (
              <TouchableOpacity 
                style={[
                  styles.addButton, 
                  { backgroundColor: (!isPremium && feedings.length >= MAX_FREE_ENTRIES) ? colors.textLight : colors.primary }
                ]}
                onPress={() => {
                  if (!isPremium && feedings.length >= MAX_FREE_ENTRIES) {
                    toast.error('Free plan limited to 3 feeding schedules. Upgrade to Premium for unlimited schedules.');
                    return;
                  }
                  setShowAddForm(true);
                }}
              >
                <Plus size={20} color={colors.card} />
                <Text style={[styles.addButtonText, { color: colors.card }]}>Add Feeding Schedule</Text>
              </TouchableOpacity>
            )}
            
            {showAddForm && (
              <FeedScheduleForm
                animalId={animalId}
                onFormSubmit={() => setShowAddForm(false)}
                onFormCancel={() => setShowAddForm(false)}
                isPremium={isPremium}
                currentEntryCount={feedings.length}
                maxFreeEntries={MAX_FREE_ENTRIES}
                addFeedingEntry={addFeedingEntry}
              />
            )}
            
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Current Feeding Schedule</Text>
            
            {feedings.length > 0 ? (
              feedings.map(feeding => (
                <FeedScheduleListItem
                  key={feeding.id}
                  feeding={feeding}
                  onDelete={handleDeleteFeeding}
                />
              ))
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={[styles.emptyText, { color: colors.textLight }]}>No feeding schedules added yet</Text>
              </View>
            )}
            
            {isPremium && (
              <View style={styles.premiumFeatures}>
                <Text style={[styles.premiumFeaturesTitle, { color: colors.text }]}>Premium Features</Text>
                <View style={styles.premiumFeaturesList}>
                  <View style={styles.premiumFeatureItem}>
                    <Text style={[styles.premiumFeatureText, { color: colors.text }]}>• Unlimited feeding schedules</Text>
                  </View>
                  <View style={styles.premiumFeatureItem}>
                    <Text style={[styles.premiumFeatureText, { color: colors.text }]}>• Advanced nutrition tracking</Text>
                  </View>
                  <View style={styles.premiumFeatureItem}>
                    <Text style={[styles.premiumFeatureText, { color: colors.text }]}>• Print feeding schedules</Text>
                  </View>
                  <View style={styles.premiumFeatureItem}>
                    <Text style={[styles.premiumFeatureText, { color: colors.text }]}>• Nutrition analysis</Text>
                  </View>
                </View>
              </View>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },

  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingVertical: 12,
    marginBottom: 16,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },

  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },

  emptyContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
  },
  premiumFeatures: {
    marginTop: 24,
    marginBottom: 16,
  },
  premiumFeaturesTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  premiumFeaturesList: {
    marginLeft: 8,
  },
  premiumFeatureItem: {
    marginBottom: 8,
  },
  premiumFeatureText: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default FeedScheduleScreen;
