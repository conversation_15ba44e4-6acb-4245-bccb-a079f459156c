import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Shield, AlertTriangle, RefreshCw, TrendingUp, Info } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalStore } from '../store/animalStore';
import { useAIHealthStore } from '../store/aiHealthStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import RiskAssessmentCard from '../components/diseaseRisk/RiskAssessmentCard';
import DiseaseInsightsCard from '../components/diseaseRisk/DiseaseInsightsCard';
import PreventiveActionsCard from '../components/diseaseRisk/PreventiveActionsCard';
import { toast } from 'sonner-native';
import { format } from 'date-fns';
import { supabase } from '../supabase/client';

type DiseaseRiskScreenRouteProp = RouteProp<HomeStackParamList, 'DiseaseRisk'>;
type DiseaseRiskScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'DiseaseRisk'>;

const DiseaseRiskScreen: React.FC = () => {
  const navigation = useNavigation<DiseaseRiskScreenNavigationProp>();
  const route = useRoute<DiseaseRiskScreenRouteProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  // Safe destructuring with fallback for missing params
  const animalId = route.params?.animalId;
  
  // Early return if no animalId provided
  if (!animalId) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('diseaseRiskAssessment')} showBackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {t('missingAnimalId')}
          </Text>
        </View>
      </View>
    );
  }
  const { animals } = useAnimalStore();
  const {
    diseaseRiskAssessments,
    isLoadingRiskAssessments,
    fetchDiseaseRiskAssessments
  } = useAIHealthStore();
  
  // Enhanced AI data state
  const [aiInsights, setAiInsights] = useState([]);
  const [preventiveActions, setPreventiveActions] = useState([]);
  const [aiExplanation, setAiExplanation] = useState('');
  
  const [refreshing, setRefreshing] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  const currentAnimal = animals.find(a => a.id === animalId);

  useEffect(() => {
    if (animalId) {
      loadRiskAssessments();
    }
  }, [animalId]);

  const loadRiskAssessments = async () => {
    try {
      await fetchDiseaseRiskAssessments(animalId);
    } catch (error) {
      console.error('Error loading risk assessments:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadRiskAssessments();
    setRefreshing(false);
  };

  const handleRunAnalysis = async () => {
    setIsAnalyzing(true);
    try {
      const { data, error } = await supabase.functions.invoke('analyze-disease-risk', {
        body: { 
          animal_id: animalId,
          analysis_period_days: 90,
          include_environmental_factors: true,
          include_behavioral_analysis: true
        }
      });
      
      if (error) throw error;
      
      // 🤖 Store enhanced AI insights
      if (data.ai_insights) {
        setAiInsights(data.ai_insights);
      }
      if (data.preventive_actions) {
        setPreventiveActions(data.preventive_actions);
      }
      if (data.ai_explanation) {
        setAiExplanation(data.ai_explanation);
      }
      
      toast.success(t('riskAnalysisCompleted'));
      await loadRiskAssessments();
    } catch (error) {
      console.error('Error running risk analysis:', error);
      toast.error(t('riskAnalysisFailed'));
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return '#EF4444';
      case 'high': return '#F59E0B';
      case 'medium': return '#3B82F6';
      case 'low': return '#10B981';
      default: return colors.textLight;
    }
  };

  const getFilteredAssessments = () => {
    if (selectedCategory === 'all') {
      return diseaseRiskAssessments;
    }
    return diseaseRiskAssessments.filter(assessment => 
      assessment.disease_category === selectedCategory
    );
  };

  const getRiskSummary = () => {
    const total = diseaseRiskAssessments.length;
    const critical = diseaseRiskAssessments.filter(r => r.risk_level === 'critical').length;
    const high = diseaseRiskAssessments.filter(r => r.risk_level === 'high').length;
    const medium = diseaseRiskAssessments.filter(r => r.risk_level === 'medium').length;
    const low = diseaseRiskAssessments.filter(r => r.risk_level === 'low').length;
    
    return { total, critical, high, medium, low };
  };

  const getUniqueCategories = () => {
    const categories = ['all', ...new Set(diseaseRiskAssessments.map(r => r.disease_category))];
    return categories;
  };

  const filteredAssessments = getFilteredAssessments();
  const riskSummary = getRiskSummary();
  const categories = getUniqueCategories();

  if (!currentAnimal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('diseaseRiskAssessment')} showBackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {t('animalNotFound')}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={colors.statusBarStyle} backgroundColor={colors.background} />
      <Header title={`${currentAnimal.name} - ${t('diseaseRisk')}`} showBackButton />
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Risk Summary Card */}
        <Card style={styles.summaryCard}>
          <View style={styles.summaryHeader}>
            <View style={styles.summaryTitleContainer}>
              <Shield size={20} color={colors.primary} />
              <Text style={[styles.summaryTitle, { color: colors.text }]}>
                {t('riskOverview')}
              </Text>
            </View>
            
            <TouchableOpacity
              style={[
                styles.analyzeButton,
                { backgroundColor: colors.primary },
                isAnalyzing && { opacity: 0.7 }
              ]}
              onPress={handleRunAnalysis}
              disabled={isAnalyzing}
            >
              {isAnalyzing ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <RefreshCw size={16} color="#FFFFFF" />
              )}
              <Text style={styles.analyzeButtonText}>
                {isAnalyzing ? t('analyzing') : t('runAnalysis')}
              </Text>
            </TouchableOpacity>
          </View>
          
          {riskSummary.total > 0 ? (
            <>
              <View style={styles.summaryStats}>
                <View style={styles.statItem}>
                  <Text style={[styles.statNumber, { color: colors.text }]}>
                    {riskSummary.total}
                  </Text>
                  <Text style={[styles.statLabel, { color: colors.textLight }]}>
                    {t('totalAssessments')}
                  </Text>
                </View>
                
                <View style={styles.riskLevelStats}>
                  {riskSummary.critical > 0 && (
                    <View style={styles.riskLevelItem}>
                      <View style={[styles.riskDot, { backgroundColor: '#EF4444' }]} />
                      <Text style={[styles.riskLevelText, { color: colors.textLight }]}>
                        {riskSummary.critical} {t('critical')}
                      </Text>
                    </View>
                  )}
                  {riskSummary.high > 0 && (
                    <View style={styles.riskLevelItem}>
                      <View style={[styles.riskDot, { backgroundColor: '#F59E0B' }]} />
                      <Text style={[styles.riskLevelText, { color: colors.textLight }]}>
                        {riskSummary.high} {t('high')}
                      </Text>
                    </View>
                  )}
                  {riskSummary.medium > 0 && (
                    <View style={styles.riskLevelItem}>
                      <View style={[styles.riskDot, { backgroundColor: '#3B82F6' }]} />
                      <Text style={[styles.riskLevelText, { color: colors.textLight }]}>
                        {riskSummary.medium} {t('medium')}
                      </Text>
                    </View>
                  )}
                  {riskSummary.low > 0 && (
                    <View style={styles.riskLevelItem}>
                      <View style={[styles.riskDot, { backgroundColor: '#10B981' }]} />
                      <Text style={[styles.riskLevelText, { color: colors.textLight }]}>
                        {riskSummary.low} {t('low')}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
              
              <Text style={[styles.lastUpdated, { color: colors.textLight }]}>
                {t('lastUpdated')}: {diseaseRiskAssessments.length > 0 ? 
                  format(new Date(diseaseRiskAssessments[0].assessment_date), 'MMM dd, yyyy HH:mm') : 
                  t('never')
                }
              </Text>
            </>
          ) : (
            <View style={styles.noDataContainer}>
              <AlertTriangle size={32} color={colors.textLight} />
              <Text style={[styles.noDataText, { color: colors.textLight }]}>
                {t('noRiskAssessments')}
              </Text>
              <Text style={[styles.noDataSubtext, { color: colors.textLight }]}>
                {t('runFirstAnalysis')}
              </Text>
            </View>
          )}
        </Card>

        {/* Category Filter */}
        {categories.length > 1 && (
          <Card style={styles.filterCard}>
            <Text style={[styles.filterTitle, { color: colors.text }]}>
              {t('filterByCategory')}
            </Text>
            
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.categoryFilters}>
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category}
                    style={[
                      styles.categoryButton,
                      {
                        backgroundColor: selectedCategory === category ? colors.primary : colors.card,
                        borderColor: colors.border
                      }
                    ]}
                    onPress={() => setSelectedCategory(category)}
                  >
                    <Text style={[
                      styles.categoryButtonText,
                      {
                        color: selectedCategory === category ? '#FFFFFF' : colors.text
                      }
                    ]}>
                      {category === 'all' ? t('allCategories') : 
                       category.charAt(0).toUpperCase() + category.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </Card>
        )}

        {/* 🤖 AI Disease Risk Insights */}
        {aiInsights.length > 0 && (
          <DiseaseInsightsCard
            insights={aiInsights}
            explanation={aiExplanation}
            onViewDetails={() => {
              toast.info('Detailed disease insights view coming soon!');
            }}
          />
        )}
        
        {/* 🛡️ Preventive Actions */}
        {preventiveActions.length > 0 && (
          <PreventiveActionsCard
            actions={preventiveActions}
            onActionPress={(action) => {
              toast.success(`Starting prevention: ${action.action}`);
              // Could implement action tracking
            }}
          />
        )}
        
        {/* Risk Assessments List */}
        {filteredAssessments.length > 0 ? (
          <View style={styles.assessmentsList}>
            {filteredAssessments.map((assessment) => (
              <RiskAssessmentCard
                key={assessment.id}
                assessment={assessment}
                onViewDetails={(assessment) => {
                  // Could navigate to detailed risk view
                  toast.info(t('detailedRiskView'));
                }}
              />
            ))}
          </View>
        ) : (
          selectedCategory !== 'all' && (
            <Card style={styles.noResultsCard}>
              <View style={styles.noResultsContainer}>
                <Info size={32} color={colors.textLight} />
                <Text style={[styles.noResultsText, { color: colors.textLight }]}>
                  {t('noRisksInCategory')}
                </Text>
                <Text style={[styles.noResultsSubtext, { color: colors.textLight }]}>
                  {t('tryDifferentCategory')}
                </Text>
              </View>
            </Card>
          )
        )}

        {/* Risk Information */}
        <Card style={styles.infoCard}>
          <View style={styles.infoHeader}>
            <Info size={20} color={colors.primary} />
            <Text style={[styles.infoTitle, { color: colors.text }]}>
              {t('aboutRiskAssessment')}
            </Text>
          </View>
          
          <Text style={[styles.infoText, { color: colors.textLight }]}>
            {t('riskAssessmentInfo')}
          </Text>
          
          <View style={styles.riskLevelsInfo}>
            <Text style={[styles.riskLevelsTitle, { color: colors.text }]}>
              {t('riskLevels')}:
            </Text>
            
            <View style={styles.riskLevelInfoItem}>
              <View style={[styles.riskDot, { backgroundColor: '#EF4444' }]} />
              <Text style={[styles.riskLevelInfoText, { color: colors.textLight }]}>
                <Text style={{ fontWeight: '500' }}>{t('critical')}:</Text> {t('criticalRiskDescription')}
              </Text>
            </View>
            
            <View style={styles.riskLevelInfoItem}>
              <View style={[styles.riskDot, { backgroundColor: '#F59E0B' }]} />
              <Text style={[styles.riskLevelInfoText, { color: colors.textLight }]}>
                <Text style={{ fontWeight: '500' }}>{t('high')}:</Text> {t('highRiskDescription')}
              </Text>
            </View>
            
            <View style={styles.riskLevelInfoItem}>
              <View style={[styles.riskDot, { backgroundColor: '#3B82F6' }]} />
              <Text style={[styles.riskLevelInfoText, { color: colors.textLight }]}>
                <Text style={{ fontWeight: '500' }}>{t('medium')}:</Text> {t('mediumRiskDescription')}
              </Text>
            </View>
            
            <View style={styles.riskLevelInfoItem}>
              <View style={[styles.riskDot, { backgroundColor: '#10B981' }]} />
              <Text style={[styles.riskLevelInfoText, { color: colors.textLight }]}>
                <Text style={{ fontWeight: '500' }}>{t('low')}:</Text> {t('lowRiskDescription')}
              </Text>
            </View>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500'
  },
  summaryCard: {
    padding: 16,
    marginVertical: 8
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  summaryTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600'
  },
  analyzeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 6
  },
  analyzeButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500'
  },
  summaryStats: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  statItem: {
    alignItems: 'center',
    marginRight: 24
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold'
  },
  statLabel: {
    fontSize: 12,
    marginTop: 2
  },
  riskLevelStats: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12
  },
  riskLevelItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4
  },
  riskDot: {
    width: 8,
    height: 8,
    borderRadius: 4
  },
  riskLevelText: {
    fontSize: 12
  },
  lastUpdated: {
    fontSize: 11,
    textAlign: 'center'
  },
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 24
  },
  noDataText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 4
  },
  noDataSubtext: {
    fontSize: 14,
    textAlign: 'center'
  },
  filterCard: {
    padding: 16,
    marginVertical: 8
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12
  },
  categoryFilters: {
    flexDirection: 'row',
    gap: 8
  },
  categoryButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500'
  },
  assessmentsList: {
    gap: 0
  },
  noResultsCard: {
    padding: 16,
    marginVertical: 8
  },
  noResultsContainer: {
    alignItems: 'center',
    paddingVertical: 24
  },
  noResultsText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 4
  },
  noResultsSubtext: {
    fontSize: 14,
    textAlign: 'center'
  },
  infoCard: {
    padding: 16,
    marginVertical: 8
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600'
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16
  },
  riskLevelsInfo: {
    gap: 8
  },
  riskLevelsTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8
  },
  riskLevelInfoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8
  },
  riskLevelInfoText: {
    fontSize: 13,
    flex: 1,
    lineHeight: 18
  }
});

export default DiseaseRiskScreen;