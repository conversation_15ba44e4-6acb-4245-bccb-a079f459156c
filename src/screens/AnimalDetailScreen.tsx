
import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { HomeStackParamList } from '../navigation';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalDetail } from '../hooks/useAnimalDetail';

// Import new modular components
import AnimalDetailHeader from '../components/animalDetail/AnimalDetailHeader';
import AnimalInfoSection from '../components/animalDetail/AnimalInfoSection';
import AnimalSpeedMonitorSection from '../components/animalDetail/AnimalSpeedMonitorSection';
import AnimalLocationAction from '../components/animalDetail/AnimalLocationAction';
import AnimalVitalsSection from '../components/animalDetail/AnimalVitalsSection';
import AnimalFeedingScheduleSection from '../components/animalDetail/AnimalFeedingScheduleSection';
import AnimalMedicationsSection from '../components/animalDetail/AnimalMedicationsSection';
import AnimalVaccinationsSection from '../components/animalDetail/AnimalVaccinationsSection';
import AnimalNotesSection from '../components/animalDetail/AnimalNotesSection';

import AnimalTrainingSection from '../components/animalDetail/AnimalTrainingSection';
import AnimalDehydrationSection from '../components/animalDetail/AnimalDehydrationSection';
import AIAssistantWidget from '../components/AIAssistantWidget';
import { useAIStore } from '../store/aiStore';
import { Brain, Moon, ChevronRight, Heart, Shield, TrendingUp } from 'lucide-react-native';
import Card from '../components/ui/Card';

type AnimalDetailScreenRouteProp = RouteProp<HomeStackParamList, 'AnimalDetail'>;
type AnimalDetailScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'AnimalDetail'>;

/**
 * @magic_description Main animal detail screen component - refactored into smaller components
 * Shows comprehensive animal information including vitals, feeding, medications, and vaccinations
 */
/**
 * @magic_description Animal detail screen
 * Displays comprehensive animal information with modular sections
 */
const AnimalDetailScreen = () => {
  const route = useRoute<AnimalDetailScreenRouteProp>();
  const navigation = useNavigation<AnimalDetailScreenNavigationProp>();
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  // Safe destructuring with fallback for missing params
  const id = route.params?.id;
  
  // Early return if no id provided
  if (!id) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar 
          barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
          backgroundColor="transparent" 
          translucent 
        />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>{t('missingAnimalId')}</Text>
          <TouchableOpacity 
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.buttonText}>{t('goBack')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  
  // Get all animal detail data and actions from custom hook
  const {
    animal,
    animalVitals,
    todayFeedings,
    medications,
    vaccinations,
    isRefreshingSpeed,
    isLoading,
    handleDelete,
    handleEdit,
    handleAddVitals,
    handleAddMedication,
    handleAddVaccination,
    handleViewLocation,
    handleViewFeedSchedule,
    handlePrint,
    handleRefreshSpeed,
  } = useAnimalDetail(id);
  
  // AI Store for AI-related functionality
  const { requestAIAnalysis } = useAIStore();
  
  // Loading state
  if (isLoading) {
    return (
      <View style={[styles.container, styles.centerContainer, { backgroundColor: colors.background }]}>
        <StatusBar 
          barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
          backgroundColor="transparent" 
          translucent 
        />
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading animal details...</Text>
      </View>
    );
  }
  
  // Animal not found state
  if (!animal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar 
          barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
          backgroundColor="transparent" 
          translucent 
        />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>Animal not found</Text>
          <TouchableOpacity 
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.buttonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  
  const latestVital = animalVitals.length > 0 ? animalVitals[0] : null;
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle="light-content" 
        backgroundColor="transparent" 
        translucent 
      />
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <AnimalDetailHeader
          animal={animal}
          onBack={() => navigation.goBack()}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
        
        <AnimalInfoSection animal={animal} />
        
        <AnimalSpeedMonitorSection
          animal={animal}
          onRefreshSpeed={handleRefreshSpeed}
          isRefreshingSpeed={isRefreshingSpeed}
        />
        
        <AnimalLocationAction onViewLocation={handleViewLocation} />
        
        <AnimalTrainingSection
          animalId={id}
          onLogTrainingSession={() => navigation.navigate('LogTrainingSession', { animalId: id })}
        />
        
        {/* AI Health Dashboard Section */}
        <Card style={styles.aiHealthSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <Brain size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                AI Health Analysis
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.viewButton, { backgroundColor: colors.primary }]}
              onPress={() => navigation.navigate('AIHealthDashboard', { animalId: id })}
            >
              <Text style={styles.viewButtonText}>View Dashboard</Text>
            </TouchableOpacity>
          </View>
          
          <Text style={[styles.sectionDescription, { color: colors.textLight }]}>
            Get AI-powered insights into {animal.name}'s health with comprehensive analysis, risk assessments, and trend monitoring.
          </Text>
          
          <View style={styles.aiFeatures}>
            <View style={styles.aiFeature}>
              <Heart size={16} color={colors.primary} />
              <Text style={[styles.aiFeatureText, { color: colors.textLight }]}>Health Score</Text>
            </View>
            <View style={styles.aiFeature}>
              <Shield size={16} color={colors.primary} />
              <Text style={[styles.aiFeatureText, { color: colors.textLight }]}>Risk Assessment</Text>
            </View>
            <View style={styles.aiFeature}>
              <TrendingUp size={16} color={colors.primary} />
              <Text style={[styles.aiFeatureText, { color: colors.textLight }]}>Health Trends</Text>
            </View>
          </View>
        </Card>
        
        <AnimalVitalsSection latestVital={latestVital} />
        
        <AnimalDehydrationSection animal={animal} />
        
        <AnimalFeedingScheduleSection
          todayFeedings={todayFeedings}
          onViewFeedSchedule={handleViewFeedSchedule}
          onPrintFeedings={() => handlePrint('feeding')}
        />
        
        <AnimalMedicationsSection
          medications={medications}
          onAddMedication={handleAddMedication}
          onPrintMedications={() => handlePrint('medication')}
        />
        
        <AnimalVaccinationsSection
          animal={animal}
          vaccinations={vaccinations}
          onAddVaccination={handleAddVaccination}
          onPrintVaccinations={() => handlePrint('vaccination')}
        />
        
        <AnimalNotesSection animalNotes={animal.notes || ''} />
        
        {/* Behavioral Analysis Section */}
        <Card style={styles.behavioralSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <Brain size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {t('behavioralAnalysis')}
              </Text>
            </View>
          </View>
          
          <Text style={[styles.sectionDescription, { color: colors.textLight }]}>
            Monitor {animal.name}'s stress levels, sleep patterns, and behavioral insights with AI-powered analysis.
          </Text>
          
          <View style={styles.behavioralActions}>
            <TouchableOpacity
              style={[styles.behavioralButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={() => navigation.navigate('StressAnalysis', { animalId: id })}
            >
              <Brain size={20} color={colors.primary} />
              <View style={styles.behavioralButtonContent}>
                <Text style={[styles.behavioralButtonTitle, { color: colors.text }]}>{t('stressAnalysis')}</Text>
                <Text style={[styles.behavioralButtonSubtitle, { color: colors.textLight }]}>Monitor stress levels and triggers</Text>
              </View>
              <ChevronRight size={20} color={colors.textLight} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.behavioralButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={() => navigation.navigate('SleepMonitoring', { animalId: id })}
            >
              <Moon size={20} color={colors.primary} />
              <View style={styles.behavioralButtonContent}>
                <Text style={[styles.behavioralButtonTitle, { color: colors.text }]}>{t('sleepMonitoring')}</Text>
                <Text style={[styles.behavioralButtonSubtitle, { color: colors.textLight }]}>Track sleep quality and patterns</Text>
              </View>
              <ChevronRight size={20} color={colors.textLight} />
            </TouchableOpacity>
          </View>
        </Card>
        
        {/* Environmental Analysis Section */}
        <Card style={styles.environmentalSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <TrendingUp size={20} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {t('environmentalAnalysis')}
              </Text>
            </View>
          </View>
          
          <Text style={[styles.sectionDescription, { color: colors.textLight }]}>
            {t('environmentalAnalysisDescription')}
          </Text>
          
          <View style={styles.environmentalActions}>
            <TouchableOpacity
              style={[styles.environmentalButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={() => navigation.navigate('EnvironmentalAnalysis', { animalId: id })}
            >
              <TrendingUp size={20} color={colors.primary} />
              <View style={styles.environmentalButtonContent}>
                <Text style={[styles.environmentalButtonTitle, { color: colors.text }]}>{t('environmentalImpact')}</Text>
                <Text style={[styles.environmentalButtonSubtitle, { color: colors.textLight }]}>Analyze environmental factors</Text>
              </View>
              <ChevronRight size={20} color={colors.textLight} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.environmentalButton, { backgroundColor: colors.card, borderColor: colors.border }]}
              onPress={() => navigation.navigate('PredictiveInsights', { animalId: id })}
            >
              <Brain size={20} color={colors.primary} />
              <View style={styles.environmentalButtonContent}>
                <Text style={[styles.environmentalButtonTitle, { color: colors.text }]}>{t('predictiveInsights')}</Text>
                <Text style={[styles.environmentalButtonSubtitle, { color: colors.textLight }]}>Future health predictions</Text>
              </View>
              <ChevronRight size={20} color={colors.textLight} />
            </TouchableOpacity>
          </View>
        </Card>
      </ScrollView>
      
      {/* AI Assistant Widget */}
      <AIAssistantWidget
        animalId={id}
        animalName={animal.name}
        position="bottom-right"
        onNavigateToAI={() => navigation.navigate('AIAssistant', { animalId: id })}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 40,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // AI Health Section Styles
  aiHealthSection: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  sectionDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  viewButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  viewButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  aiFeatures: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  aiFeature: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  aiFeatureText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 6,
  },
  // Behavioral Analysis Section Styles
  behavioralSection: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  behavioralActions: {
    marginTop: 16,
  },
  behavioralButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  behavioralButtonContent: {
    flex: 1,
    marginLeft: 12,
  },
  behavioralButtonTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  behavioralButtonSubtitle: {
    fontSize: 14,
    fontWeight: '400',
  },
  // Environmental Analysis Section Styles
  environmentalSection: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  environmentalActions: {
    marginTop: 16,
  },
  environmentalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  environmentalButtonContent: {
    flex: 1,
    marginLeft: 12,
  },
  environmentalButtonTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  environmentalButtonSubtitle: {
    fontSize: 14,
    fontWeight: '400',
  },
});

export default AnimalDetailScreen;
