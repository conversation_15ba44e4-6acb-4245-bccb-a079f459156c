import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { Check, CreditCard, Smartphone, Globe } from 'lucide-react-native';
import { toast } from 'sonner-native';

import { useLemonSqueezyCheckout } from '../hooks/useLemonSqueezyCheckout';
import { useOrderManagement } from '../hooks/useOrderManagement';
import { useApplePayPayment } from '../hooks/useApplePayPayment';
import { useGooglePayPayment } from '../hooks/useGooglePayPayment';
import { useUserStore } from '../store/userStore';

type PlanType = 'monthly' | 'yearly';

interface CheckoutScreenProps {
  route: {
    params: {
      planType: PlanType;
      productId: string;
    };
  };
}

const CheckoutScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { planType = 'monthly', productId = 'default' } = route.params as any || {};
  
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'lemonsqueezy' | 'apple' | 'google'>('lemonsqueezy');
  const [isProcessing, setIsProcessing] = useState(false);
  
  const { user } = useUserStore();
  const {
    isCreatingCheckout,
    checkoutUrl,
    createCheckoutSession,
    openCheckout,
    clearCheckout
  } = useLemonSqueezyCheckout();
  
  const {
    isProcessingOrder,
    createOrder,
    handlePaymentSuccess
  } = useOrderManagement(() => {
    // Success callback
    navigation.goBack();
  });
  
  const {
    isProcessing: isProcessingApplePay,
    isAvailable: isApplePayAvailable,
    checkApplePayAvailability,
    processApplePayPayment
  } = useApplePayPayment(() => {
    navigation.goBack();
  });
  
  const {
    isProcessing: isProcessingGooglePay,
    isAvailable: isGooglePayAvailable,
    checkGooglePayAvailability,
    processGooglePayPayment
  } = useGooglePayPayment(() => {
    navigation.goBack();
  });
  
  const planDetails = {
    monthly: {
      price: '$14.99',
      period: 'per month',
      savings: null,
      description: 'Perfect for getting started'
    },
    yearly: {
      price: '$143.90',
      period: 'per year',
      savings: 'Save $35.98',
      description: 'Best value for committed users'
    }
  };
  
  const features = [
    'Unlimited AI health insights',
    'Advanced training analytics',
    'Personalized recommendations',
    'Priority customer support',
    'Export data capabilities',
    'Advanced goal tracking'
  ];
  
  const handleLemonSqueezyPayment = async () => {
    if (!user) {
      toast.error('Please log in to continue');
      return;
    }
    
    setIsProcessing(true);
    
    try {
      // Step 1: Create checkout session
      const url = await createCheckoutSession(planType, productId);
      
      if (!url) {
        throw new Error('Failed to create checkout session');
      }
      
      // Step 2: Create order in our database
      const orderId = await createOrder({
        user_id: user.id,
        product_id: productId,
        quantity: 1,
        total_amount: planType === 'monthly' ? 14.99 : 143.90,
        currency: 'USD',
        status: 'pending',
        checkout_url: url,
        plan_type: planType,
        payment_method: 'lemonsqueezy'
      });
      
      if (!orderId) {
        throw new Error('Failed to create order');
      }
      
      // Step 3: Open checkout in browser
      await openCheckout(url);
      
      // Note: Payment completion will be handled by webhook
      toast.info('Complete your payment in the browser. You\'ll receive a confirmation email.');
      
    } catch (error: any) {
      console.error('Payment error:', error);
      toast.error(error.message || 'Payment failed');
    } finally {
      setIsProcessing(false);
    }
  };
  
  const handleApplePayPayment = async () => {
    const amount = planType === 'monthly' ? 14.99 : 143.90;
    
    await processApplePayPayment({
      planType,
      productId,
      amount,
      currency: 'USD'
    });
  };
  
  const handleGooglePayPayment = async () => {
    const amount = planType === 'monthly' ? 14.99 : 143.90;
    
    await processGooglePayPayment({
      planType,
      productId,
      amount,
      currency: 'USD'
    });
  };
  
  const handlePayment = async () => {
    switch (selectedPaymentMethod) {
      case 'lemonsqueezy':
        await handleLemonSqueezyPayment();
        break;
      case 'apple':
        await handleApplePayPayment();
        break;
      case 'google':
        await handleGooglePayPayment();
        break;
    }
  };
  
  const isLoading = isCreatingCheckout || isProcessingOrder || isProcessing || isProcessingApplePay || isProcessingGooglePay;
  
  // Check payment method availability on mount
  useEffect(() => {
    checkApplePayAvailability();
    checkGooglePayAvailability();
  }, []);
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>✕</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Complete Purchase</Text>
        </View>
        
        {/* Plan Summary */}
        <View style={styles.planSummary}>
          <LinearGradient
            colors={['#3B82F6', '#1D4ED8']}
            style={styles.planCard}
          >
            <Text style={styles.planTitle}>HoofBeat Premium</Text>
            <Text style={styles.planSubtitle}>{planDetails[planType].description}</Text>
            
            <View style={styles.priceContainer}>
              <Text style={styles.price}>{planDetails[planType].price}</Text>
              <Text style={styles.period}>{planDetails[planType].period}</Text>
            </View>
            
            {planDetails[planType].savings && (
              <View style={styles.savingsContainer}>
                <Text style={styles.savings}>{planDetails[planType].savings}</Text>
              </View>
            )}
          </LinearGradient>
        </View>
        
        {/* Features */}
        <View style={styles.featuresContainer}>
          <Text style={styles.featuresTitle}>What's Included</Text>
          {features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Check size={20} color="#10B981" />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>
        
        {/* Payment Methods */}
        <View style={styles.paymentMethodsContainer}>
          <Text style={styles.paymentMethodsTitle}>Payment Method</Text>
          
          {/* Lemon Squeezy (Web Checkout) */}
          <TouchableOpacity
            style={[
              styles.paymentMethod,
              selectedPaymentMethod === 'lemonsqueezy' && styles.selectedPaymentMethod
            ]}
            onPress={() => setSelectedPaymentMethod('lemonsqueezy')}
          >
            <View style={styles.paymentMethodContent}>
              <Globe size={24} color={selectedPaymentMethod === 'lemonsqueezy' ? '#3B82F6' : '#6B7280'} />
              <View style={styles.paymentMethodText}>
                <Text style={styles.paymentMethodTitle}>Secure Web Checkout</Text>
                <Text style={styles.paymentMethodSubtitle}>Credit card, PayPal, and more</Text>
              </View>
            </View>
            {selectedPaymentMethod === 'lemonsqueezy' && (
              <View style={styles.selectedIndicator}>
                <Check size={20} color="#3B82F6" />
              </View>
            )}
          </TouchableOpacity>
          
          {/* Apple Pay */}
          <TouchableOpacity
            style={[
              styles.paymentMethod,
              selectedPaymentMethod === 'apple' && styles.selectedPaymentMethod,
              !isApplePayAvailable && styles.paymentMethodDisabled
            ]}
            onPress={() => isApplePayAvailable && setSelectedPaymentMethod('apple')}
            disabled={!isApplePayAvailable}
          >
            <View style={styles.paymentMethodContent}>
              <Smartphone size={24} color={selectedPaymentMethod === 'apple' ? '#3B82F6' : isApplePayAvailable ? '#6B7280' : '#D1D5DB'} />
              <View style={styles.paymentMethodText}>
                <Text style={[styles.paymentMethodTitle, !isApplePayAvailable && styles.paymentMethodTitleDisabled]}>Apple Pay</Text>
                <Text style={[styles.paymentMethodSubtitle, !isApplePayAvailable && styles.paymentMethodSubtitleDisabled]}>
                  {isApplePayAvailable ? 'Touch ID or Face ID' : 'Not available on this device'}
                </Text>
              </View>
            </View>
            {selectedPaymentMethod === 'apple' && isApplePayAvailable && (
              <View style={styles.selectedIndicator}>
                <Check size={20} color="#3B82F6" />
              </View>
            )}
          </TouchableOpacity>
          
          {/* Google Pay */}
          <TouchableOpacity
            style={[
              styles.paymentMethod,
              selectedPaymentMethod === 'google' && styles.selectedPaymentMethod,
              !isGooglePayAvailable && styles.paymentMethodDisabled
            ]}
            onPress={() => isGooglePayAvailable && setSelectedPaymentMethod('google')}
            disabled={!isGooglePayAvailable}
          >
            <View style={styles.paymentMethodContent}>
              <CreditCard size={24} color={selectedPaymentMethod === 'google' ? '#3B82F6' : isGooglePayAvailable ? '#6B7280' : '#D1D5DB'} />
              <View style={styles.paymentMethodText}>
                <Text style={[styles.paymentMethodTitle, !isGooglePayAvailable && styles.paymentMethodTitleDisabled]}>Google Pay</Text>
                <Text style={[styles.paymentMethodSubtitle, !isGooglePayAvailable && styles.paymentMethodSubtitleDisabled]}>
                  {isGooglePayAvailable ? 'Fast and secure' : 'Not available on this device'}
                </Text>
              </View>
            </View>
            {selectedPaymentMethod === 'google' && isGooglePayAvailable && (
              <View style={styles.selectedIndicator}>
                <Check size={20} color="#3B82F6" />
              </View>
            )}
          </TouchableOpacity>
        </View>
        
        {/* Terms */}
        <View style={styles.termsContainer}>
          <Text style={styles.termsText}>
            By completing this purchase, you agree to our Terms of Service and Privacy Policy. 
            Your subscription will auto-renew unless cancelled.
          </Text>
        </View>
      </ScrollView>
      
      {/* Purchase Button */}
      <View style={styles.purchaseContainer}>
        <TouchableOpacity
          style={[styles.purchaseButton, isLoading && styles.purchaseButtonDisabled]}
          onPress={handlePayment}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <Text style={styles.purchaseButtonText}>
              Complete Purchase • {planDetails[planType].price}
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 20,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E5E7EB',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    fontSize: 18,
    color: '#374151',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  planSummary: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  planCard: {
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
  },
  planTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  planSubtitle: {
    fontSize: 16,
    color: '#DBEAFE',
    marginBottom: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  price: {
    fontSize: 32,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  period: {
    fontSize: 16,
    color: '#DBEAFE',
    marginLeft: 8,
  },
  savingsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  savings: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  featuresContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#374151',
    marginLeft: 12,
  },
  paymentMethodsContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  paymentMethodsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  paymentMethod: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedPaymentMethod: {
    borderColor: '#3B82F6',
    backgroundColor: '#F0F9FF',
  },
  paymentMethodContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paymentMethodText: {
    marginLeft: 12,
    flex: 1,
  },
  paymentMethodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  paymentMethodSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  selectedIndicator: {
    marginLeft: 12,
  },
  paymentMethodDisabled: {
    opacity: 0.6,
    backgroundColor: '#F9FAFB',
  },
  paymentMethodTitleDisabled: {
    color: '#9CA3AF',
  },
  paymentMethodSubtitleDisabled: {
    color: '#D1D5DB',
  },
  termsContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  termsText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
    textAlign: 'center',
  },
  purchaseContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  purchaseButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  purchaseButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  purchaseButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default CheckoutScreen;