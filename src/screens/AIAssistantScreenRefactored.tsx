import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../contexts/ThemeContext';
import { useAnimalStore } from '../store/animalStore';
import { useAIStore } from '../store/aiStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import { toast } from 'sonner-native';
import AIAssistantHeader from '../components/aiAssistant/AIAssistantHeader';
import AIAnimalInfoCard from '../components/aiAssistant/AIAnimalInfoCard';
import AIReadinessScoreCard from '../components/aiAssistant/AIReadinessScoreCard';
import AIHealthAssessmentCard from '../components/aiAssistant/AIHealthAssessmentCard';
import AICoachingTipsCard from '../components/aiAssistant/AICoachingTipsCard';
import AIDataReadinessCard from '../components/aiAssistant/AIDataReadinessCard';
import AIInsightsList from '../components/aiAssistant/AIInsightsList';
import AIGuidanceComponent from '../components/ai/AIGuidanceComponent';
import { useAIDataDetection } from '../hooks/useAIDataDetection';
import { useState, useEffect } from 'react';

type AIAssistantScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'AIAssistant'>;
type AIAssistantScreenRouteProp = RouteProp<HomeStackParamList, 'AIAssistant'>;

const AIAssistantScreenRefactored = () => {
  const navigation = useNavigation<AIAssistantScreenNavigationProp>();
  const route = useRoute<AIAssistantScreenRouteProp>();
  const { colors } = useTheme();
  
  // Safe parameter extraction with fallback
  const animalId = route.params?.animalId;
  
  if (!animalId) {
    toast.error('Please select an animal first');
    navigation.goBack();
    return null;
  }
  
  const animals = useAnimalStore(state => state.animals);
  const animal = animals.find(a => a.id === animalId);
  
  const {
    healthAssessments,
    trainingPlans,
    readinessScores,
    coachingTips,
    isLoadingAnalysis,
    requestAIAnalysis,
    fetchHealthAssessments,
    fetchTrainingPlans,
    fetchReadinessScores,
    fetchCoachingTips,
    markTipAsRead,
    getLatestHealthAssessment,
    getLatestReadinessScore,
    getUnreadTips
  } = useAIStore();
  
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const latestHealthAssessment = getLatestHealthAssessment(animalId);
  const latestReadinessScore = getLatestReadinessScore(animalId);
  const unreadTips = getUnreadTips(animalId);
  
  // AI Data Detection
  const {
    hasVitalsData,
    hasFeedingData,
    hasMedicationData,
    hasAIData,
    isReadyForAI,
    dataQualityScore,
    recentDataCounts
  } = useAIDataDetection();
  
  // Calculate guidance state
  const shouldShowGuidance = !isReadyForAI || dataQualityScore < 50;
  const missingDataTypes = [];
  if (!hasVitalsData) missingDataTypes.push('vitals');
  if (!hasFeedingData) missingDataTypes.push('feeding');
  
  useEffect(() => {
    loadAIData();
  }, [animalId]);
  
  const loadAIData = async () => {
    try {
      await Promise.all([
        fetchHealthAssessments(animalId),
        fetchReadinessScores(animalId),
        fetchCoachingTips(animalId),
      ]);
    } catch (error) {
      console.error('Error loading AI data:', error);
    }
  };
  
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadAIData();
    setIsRefreshing(false);
  };
  
  const handleRequestAnalysis = async () => {
    try {
      await requestAIAnalysis(animalId);
    } catch (error) {
      // Error is already handled in the store
    }
  };
  
  if (!animal) {
    toast.error('Animal not found');
    navigation.goBack();
    return null;
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header title="AI Assistant" showBackButton={true} />
      
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* AI Guidance or Animal Info */}
        {shouldShowGuidance ? (
          <AIGuidanceComponent 
            animal={animal} 
            missingDataTypes={missingDataTypes}
          />
        ) : (
          <AIAssistantHeader
            animal={animal}
            dataCompleteness={dataQualityScore}
            isLoadingAnalysis={isLoadingAnalysis}
            onRequestAnalysis={handleRequestAnalysis}
          />
        )}
        
        {/* Readiness Score */}
        {latestReadinessScore && (
          <AIReadinessScoreCard readinessScore={latestReadinessScore} />
        )}
        
        {/* Health Assessment */}
        {latestHealthAssessment && (
          <AIHealthAssessmentCard healthAssessment={latestHealthAssessment} />
        )}
        
        {/* Coaching Tips */}
        {unreadTips.length > 0 && (
          <AICoachingTipsCard 
            tips={unreadTips}
            onMarkAsRead={markTipAsRead}
          />
        )}
        
        {/* Data Readiness Card */}
        <AIDataReadinessCard
          dataInfo={{
            hasVitalsData,
            hasFeedingData,
            hasMedicationData,
            dataQualityScore,
            recentDataCounts,
            recommendations: [
              'Add more vital signs data',
              'Log feeding schedules regularly'
            ]
          }}
        />
        
        {/* Mock AI Insights */}
        <AIInsightsList
          insights={[
            {
              id: '1',
              type: 'health',
              title: 'Heart Rate Trend',
              description: 'Heart rate has been elevated during recent training sessions',
              priority: 'medium',
              generated_at: new Date().toISOString(),
              actionable: true
            },
            {
              id: '2', 
              type: 'training',
              title: 'Training Intensity',
              description: 'Consider reducing training intensity for better recovery',
              priority: 'high',
              generated_at: new Date().toISOString(),
              actionable: true
            }
          ]}
        />
        
        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  bottomPadding: {
    height: 20,
  },
});

export default AIAssistantScreenRefactored;