import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  FlatList,
  Image,
  Dimensions
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Brain,
  Heart,
  Activity,
  AlertTriangle,
  TrendingUp,
  ChevronRight,
  Plus
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalStore } from '../store/animalStore';
import { useAIHealthStore } from '../store/aiHealthStore';
import { HealthStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import { toast } from 'sonner-native';
import { format } from 'date-fns';

type HealthDashboardScreenNavigationProp = NativeStackNavigationProp<HealthStackParamList, 'HealthDashboard'>;

interface AnimalHealthSummary {
  id: string;
  name: string;
  species: string;
  breed: string;
  imageUrl?: string;
  healthScore?: number;
  healthStatus: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  alertsCount: number;
  criticalAlertsCount: number;
  lastUpdated: string;
}

const HealthDashboardScreen: React.FC = () => {
  const navigation = useNavigation<HealthDashboardScreenNavigationProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  const { animals } = useAnimalStore();
  const {
    healthScores,
    smartAlerts,
    isLoadingHealthScores,
    isLoadingAlerts,
    fetchLatestHealthScore,
    fetchSmartAlerts
  } = useAIHealthStore();
  
  const [refreshing, setRefreshing] = useState(false);
  const [animalSummaries, setAnimalSummaries] = useState<AnimalHealthSummary[]>([]);
  
  const screenWidth = Dimensions.get('window').width;
  const cardWidth = (screenWidth - 48) / 2; // 2 cards per row with padding

  useEffect(() => {
    loadHealthData();
  }, [animals]);

  const loadHealthData = async () => {
    if (animals.length === 0) return;
    
    try {
      // Load health data for all animals
      const summaries: AnimalHealthSummary[] = await Promise.all(
        animals.map(async (animal) => {
          try {
            // Fetch latest health score and alerts for this animal
            const [healthScore] = await Promise.all([
              fetchLatestHealthScore(animal.id),
              fetchSmartAlerts(animal.id)
            ]);
            
            const animalAlerts = smartAlerts.filter(alert => 
              alert.animal_id === animal.id && !alert.is_resolved
            );
            const criticalAlerts = animalAlerts.filter(alert => 
              alert.priority_level === 'critical'
            );
            
            // Determine health status based on score
            let healthStatus: AnimalHealthSummary['healthStatus'] = 'good';
            const score = healthScore?.overall_score || 0;
            
            if (score >= 90) healthStatus = 'excellent';
            else if (score >= 75) healthStatus = 'good';
            else if (score >= 60) healthStatus = 'fair';
            else if (score >= 40) healthStatus = 'poor';
            else healthStatus = 'critical';
            
            return {
              id: animal.id,
              name: animal.name,
              species: animal.species,
              breed: animal.breed,
              imageUrl: animal.imageUrl,
              healthScore: score,
              healthStatus,
              alertsCount: animalAlerts.length,
              criticalAlertsCount: criticalAlerts.length,
              lastUpdated: healthScore?.created_at || new Date().toISOString()
            };
          } catch (error) {
            console.error(`Error loading health data for ${animal.name}:`, error);
            return {
              id: animal.id,
              name: animal.name,
              species: animal.species,
              breed: animal.breed,
              imageUrl: animal.imageUrl,
              healthScore: 0,
              healthStatus: 'poor' as const,
              alertsCount: 0,
              criticalAlertsCount: 0,
              lastUpdated: new Date().toISOString()
            };
          }
        })
      );
      
      setAnimalSummaries(summaries);
    } catch (error) {
      console.error('Error loading health dashboard data:', error);
      toast.error(t('failedToLoadHealthData'));
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadHealthData();
    setRefreshing(false);
  };

  const handleAnimalPress = (animalId: string) => {
    navigation.navigate('AIHealthDashboard', { animalId });
  };

  const handleAddAnimal = () => {
    // Navigate to add animal screen
    navigation.navigate('Animals' as any);
  };

  const getHealthStatusColor = (status: AnimalHealthSummary['healthStatus']) => {
    switch (status) {
      case 'excellent': return '#10B981'; // Green
      case 'good': return '#3B82F6'; // Blue
      case 'fair': return '#F59E0B'; // Yellow
      case 'poor': return '#EF4444'; // Red
      case 'critical': return '#DC2626'; // Dark Red
      default: return colors.textLight;
    }
  };

  const getHealthStatusText = (status: AnimalHealthSummary['healthStatus']) => {
    switch (status) {
      case 'excellent': return t('excellent');
      case 'good': return t('good');
      case 'fair': return t('fair');
      case 'poor': return t('poor');
      case 'critical': return t('critical');
      default: return t('unknown');
    }
  };

  const renderAnimalCard = ({ item }: { item: AnimalHealthSummary }) => {
    const statusColor = getHealthStatusColor(item.healthStatus);
    
    return (
      <TouchableOpacity
        style={[styles.animalCard, { width: cardWidth }]}
        onPress={() => handleAnimalPress(item.id)}
        activeOpacity={0.7}
      >
        <Card style={[styles.cardContent, { backgroundColor: colors.card }]}>
          {/* Animal Image */}
          <View style={styles.imageContainer}>
            {item.imageUrl ? (
              <Image
                source={{ uri: item.imageUrl }}
                style={styles.animalImage}
                resizeMode="cover"
              />
            ) : (
              <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
                <Heart size={24} color={colors.textLight} />
              </View>
            )}
            
            {/* Health Score Badge */}
            <View style={[styles.scoreBadge, { backgroundColor: statusColor }]}>
              <Text style={styles.scoreText}>
                {item.healthScore || 0}
              </Text>
            </View>
          </View>
          
          {/* Animal Info */}
          <View style={styles.animalInfo}>
            <Text style={[styles.animalName, { color: colors.text }]} numberOfLines={1}>
              {item.name}
            </Text>
            <Text style={[styles.animalBreed, { color: colors.textLight }]} numberOfLines={1}>
              {item.breed}
            </Text>
            
            {/* Health Status */}
            <View style={styles.statusContainer}>
              <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
              <Text style={[styles.statusText, { color: statusColor }]}>
                {getHealthStatusText(item.healthStatus)}
              </Text>
            </View>
            
            {/* Alerts */}
            {item.alertsCount > 0 && (
              <View style={styles.alertsContainer}>
                <AlertTriangle size={12} color={item.criticalAlertsCount > 0 ? '#EF4444' : '#F59E0B'} />
                <Text style={[styles.alertsText, { color: colors.textLight }]}>
                  {item.alertsCount} {item.alertsCount === 1 ? t('alert') : t('alerts')}
                </Text>
              </View>
            )}
            
            {/* Last Updated */}
            <Text style={[styles.lastUpdated, { color: colors.textLight }]}>
              {t('updated')} {format(new Date(item.lastUpdated), 'MMM d')}
            </Text>
          </View>
          
          {/* Navigation Arrow */}
          <View style={styles.arrowContainer}>
            <ChevronRight size={16} color={colors.textLight} />
          </View>
        </Card>
      </TouchableOpacity>
    );
  };

  const renderAddAnimalCard = () => (
    <TouchableOpacity
      style={[styles.animalCard, { width: cardWidth }]}
      onPress={handleAddAnimal}
      activeOpacity={0.7}
    >
      <Card style={[styles.cardContent, styles.addCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <View style={[styles.addIconContainer, { backgroundColor: colors.primary }]}>
          <Plus size={24} color="#FFFFFF" />
        </View>
        <Text style={[styles.addText, { color: colors.text }]}>{t('addAnimal')}</Text>
        <Text style={[styles.addSubtext, { color: colors.textLight }]}>{t('startMonitoring')}</Text>
      </Card>
    </TouchableOpacity>
  );

  const getOverallStats = () => {
    const total = animalSummaries.length;
    const healthy = animalSummaries.filter(a => a.healthStatus === 'excellent' || a.healthStatus === 'good').length;
    const needsAttention = animalSummaries.filter(a => a.healthStatus === 'fair' || a.healthStatus === 'poor' || a.healthStatus === 'critical').length;
    const totalAlerts = animalSummaries.reduce((sum, a) => sum + a.alertsCount, 0);
    
    return { total, healthy, needsAttention, totalAlerts };
  };

  if (animals.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar barStyle={colors.statusBarStyle} backgroundColor={colors.background} />
        <Header title="AI Health Dashboard" />
        
        <View style={styles.emptyContainer}>
          <Brain size={64} color={colors.textLight} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            {t('welcomeToAIHealthDashboard')}
          </Text>
          <Text style={[styles.emptyDescription, { color: colors.textLight }]}>
            {t('addFirstAnimalDescription')}
          </Text>
          
          <TouchableOpacity
            style={[styles.addFirstAnimalButton, { backgroundColor: colors.primary }]}
            onPress={handleAddAnimal}
          >
            <Plus size={20} color="#FFFFFF" />
            <Text style={styles.addFirstAnimalText}>{t('addYourFirstAnimal')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const stats = getOverallStats();

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={colors.statusBarStyle} backgroundColor={colors.background} />
      <Header title="AI Health Dashboard" />
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Overall Stats */}
        <Card style={[styles.statsCard, { backgroundColor: colors.card }]}>
          <View style={styles.statsHeader}>
            <Activity size={20} color={colors.primary} />
            <Text style={[styles.statsTitle, { color: colors.text }]}>{t('healthOverview')}</Text>
          </View>
          
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: colors.text }]}>{stats.total}</Text>
              <Text style={[styles.statLabel, { color: colors.textLight }]}>{t('totalAnimals')}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: '#10B981' }]}>{stats.healthy}</Text>
              <Text style={[styles.statLabel, { color: colors.textLight }]}>{t('healthy')}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: '#F59E0B' }]}>{stats.needsAttention}</Text>
              <Text style={[styles.statLabel, { color: colors.textLight }]}>{t('needsAttention')}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: '#EF4444' }]}>{stats.totalAlerts}</Text>
              <Text style={[styles.statLabel, { color: colors.textLight }]}>{t('activeAlerts')}</Text>
            </View>
          </View>
        </Card>

        {/* Animals Grid */}
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('yourAnimals')}</Text>
          <TouchableOpacity onPress={handleAddAnimal}>
            <Text style={[styles.addMoreText, { color: colors.primary }]}>{t('addMore')}</Text>
          </TouchableOpacity>
        </View>
        
        <FlatList
          data={[...animalSummaries, { id: 'add-new' }] as any}
          renderItem={({ item }) => {
            if (item.id === 'add-new') {
              return renderAddAnimalCard();
            }
            return renderAnimalCard({ item });
          }}
          keyExtractor={(item) => item.id}
          numColumns={2}
          columnWrapperStyle={styles.row}
          scrollEnabled={false}
          showsVerticalScrollIndicator={false}
          ItemSeparatorComponent={() => <View style={{ height: 16 }} />}
        />
        
        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: 24,
    marginBottom: 12,
    textAlign: 'center'
  },
  emptyDescription: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32
  },
  addFirstAnimalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8
  },
  addFirstAnimalText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600'
  },
  statsCard: {
    marginBottom: 24,
    padding: 20
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 8
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  statItem: {
    alignItems: 'center',
    flex: 1
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center'
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700'
  },
  addMoreText: {
    fontSize: 14,
    fontWeight: '600'
  },
  row: {
    justifyContent: 'space-between'
  },
  animalCard: {
    marginBottom: 16
  },
  cardContent: {
    padding: 16,
    borderRadius: 16,
    position: 'relative'
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 12
  },
  animalImage: {
    width: '100%',
    height: 80,
    borderRadius: 12
  },
  placeholderImage: {
    width: '100%',
    height: 80,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center'
  },
  scoreBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center'
  },
  scoreText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '700'
  },
  animalInfo: {
    flex: 1
  },
  animalName: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 2
  },
  animalBreed: {
    fontSize: 12,
    marginBottom: 8
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 6
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600'
  },
  alertsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6
  },
  alertsText: {
    fontSize: 11,
    marginLeft: 4
  },
  lastUpdated: {
    fontSize: 10
  },
  arrowContainer: {
    position: 'absolute',
    top: 16,
    right: 16
  },
  addCard: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    minHeight: 180
  },
  addIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12
  },
  addText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4
  },
  addSubtext: {
    fontSize: 12
  },
  bottomPadding: {
    height: 32
  }
});

export default HealthDashboardScreen;