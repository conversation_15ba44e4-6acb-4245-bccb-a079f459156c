
import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity
} from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Printer, Share2, Download, CheckCircle } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import { usePrintScreenLogic } from '../hooks/usePrintScreenLogic';
import PrintPreviewContent from '../components/print/PrintPreviewContent';
import PrintActionButton from '../components/print/PrintActionButton';

type PrintScreenRouteProp = RouteProp<HomeStackParamList, 'PrintScreen'>;
type PrintScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'PrintScreen'>;

const PrintScreen = () => {
  const route = useRoute<PrintScreenRouteProp>();
  const navigation = useNavigation<PrintScreenNavigationProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  // Safe destructuring with fallback for missing params
  const type = route.params?.type;
  const animalId = route.params?.animalId;
  
  // Early return if required params are missing
  if (!type || !animalId) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('print')} showBackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>{t('missingAnimalId')}</Text>
          <TouchableOpacity
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.buttonText}>{t('goBack')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  
  // Use the print screen logic hook
  const {
    animal,
    data,
    title,
    isLoading,
    isPrinted,
    isShared,
    isDownloaded,
    handlePrint,
    handleShare,
    handleDownload
  } = usePrintScreenLogic({ type, animalId });
  
  if (!animal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('print')} showBackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>{t('animalNotFound')}</Text>
          <TouchableOpacity
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.buttonText}>{t('goBack')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header title={`${t('print')} ${title}`} showBackButton />
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <PrintPreviewContent 
          type={type}
          animal={animal}
          data={data}
          title={title}
        />
        
        <View style={styles.actionsContainer}>
          <PrintActionButton
            onPress={handlePrint}
            isLoading={isLoading}
            isCompleted={isPrinted}
            label={t('print')}
            completedLabel={t('printed')}
            icon={Printer}
            completedIcon={CheckCircle}
            backgroundColor={colors.primary}
          />
          
          <PrintActionButton
            onPress={handleShare}
            isLoading={isLoading}
            isCompleted={isShared}
            label={t('share')}
            completedLabel={t('shared')}
            icon={Share2}
            completedIcon={CheckCircle}
            backgroundColor={colors.secondary}
          />
          
          <PrintActionButton
            onPress={handleDownload}
            isLoading={isLoading}
            isCompleted={isDownloaded}
            label={t('download')}
            completedLabel={t('downloaded')}
            icon={Download}
            completedIcon={CheckCircle}
            backgroundColor={colors.accent3}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  buttonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default PrintScreen;
