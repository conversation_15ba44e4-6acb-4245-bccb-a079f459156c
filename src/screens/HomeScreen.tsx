
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  StatusBar,
  Platform
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { LinearGradient } from 'expo-linear-gradient';
import { useAnimalStore } from '../store/animalStore';
import { useUserStore } from '../store/userStore';
import { HomeStackParamList } from '../navigation';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { toast } from 'sonner-native';

// Import custom hooks
import { useHomeScreenStats } from '../hooks/useHomeScreenStats';
import { useHomeScreenAlerts } from '../hooks/useHomeScreenAlerts';

// Import components
import HealthSnapshot from '../components/home/<USER>';
import AnimalSelector from '../components/home/<USER>';
import AlertsSection from '../components/home/<USER>';
import AnimalsOverview from '../components/home/<USER>';
import UpcomingTasks from '../components/home/<USER>';
import SpeedMonitor from '../components/SpeedMonitor';
import PremiumBanner from '../components/PremiumBanner';
import AIAssistantWidget from '../components/AIAssistantWidget';
import AIOnboardingBanner from '../components/AIOnboardingBanner';
import { useAIStore } from '../store/aiStore';
import { useAIHealthStore } from '../store/aiHealthStore';
import { useBehavioralStore } from '../store/behavioralStore';
import { useDehydrationStore } from '../store/dehydrationStore';
import { Animal } from '../mocks/animals';

type HomeScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'Home'>;

/**
 * @magic_description Main home screen component showing overview of animals, alerts, and tasks
 * Refactored to use custom hooks for better maintainability and separation of concerns
 */
const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  
  // Store hooks
  const { animals, getActiveAnimal, updateAnimalSpeed } = useAnimalStore();
  const { user } = useUserStore();
  const { getLatestHealthAssessment, getLatestReadinessScore } = useAIStore();
  const { fetchLatestHealthScore } = useAIHealthStore();
  const { fetchStressAnalyses, fetchSleepAnalyses } = useBehavioralStore();
  const { fetchReadings } = useDehydrationStore();
  
  // Custom hooks for data processing
  const {
    totalAnimals,
    activeDevices,
    todayFeedings,
    todayMedications,
    abnormalVitals,
    upcomingTasks
  } = useHomeScreenStats();
  
  // Load health data when animal is selected
  React.useEffect(() => {
    if (selectedAnimal) {
      fetchLatestHealthScore(selectedAnimal.id);
      fetchStressAnalyses(selectedAnimal.id, 1);
      fetchSleepAnalyses(selectedAnimal.id, 1);
      fetchReadings(selectedAnimal.id, 1);
    }
  }, [selectedAnimal, fetchLatestHealthScore, fetchStressAnalyses, fetchSleepAnalyses, fetchReadings]);
  
  const { healthAndLocationAlerts, vaccineAlerts } = useHomeScreenAlerts(animals, abnormalVitals);
  
  // Navigation handlers for health snapshot cards
  const handleHealthCardPress = (type: 'health' | 'stress' | 'sleep' | 'dehydration') => {
    if (!selectedAnimal) return;
    
    switch (type) {
      case 'health':
        navigation.navigate('AIHealthDashboard', { animalId: selectedAnimal.id });
        break;
      case 'stress':
        navigation.navigate('StressAnalysis', { animalId: selectedAnimal.id });
        break;
      case 'sleep':
        navigation.navigate('SleepMonitoring', { animalId: selectedAnimal.id });
        break;
      case 'dehydration':
        navigation.navigate('AnimalDetail', { id: selectedAnimal.id });
        break;
    }
  };
  
  // Local state
  const isPremium = user?.isPremium || false;
  const [selectedAnimal, setSelectedAnimal] = useState<Animal | null>(getActiveAnimal() || (animals.length > 0 ? animals[0] : null));
  const [isRefreshingSpeed, setIsRefreshingSpeed] = useState(false);
  
  // Update selected animal when animals change
  React.useEffect(() => {
    if (!selectedAnimal && animals.length > 0) {
      setSelectedAnimal(animals[0]);
    }
  }, [animals, selectedAnimal]);
  
  // Navigation handlers
  const handleStatsPress = (type: 'animals' | 'devices' | 'feedings' | 'medications') => {
    switch (type) {
      case 'animals':
        navigation.navigate('Animals');
        break;
      case 'devices':
        navigation.navigate('Devices');
        break;
      default:
        // For feedings and medications, go to the first animal's detail
        if (animals.length > 0) {
          navigation.navigate('AnimalDetail', { id: animals[0].id });
        }
        break;
    }
  };
  
  const handleAnimalsOverview = {
    onSeeAll: () => navigation.navigate('Animals'),
    onAddAnimal: () => navigation.navigate('AnimalsStack' as never, { screen: 'AddAnimal' } as never),
    onAnimalPress: (id: string) => navigation.navigate('AnimalDetail', { id }),
  };
  
  const handleAlertPress = (animalId: string) => {
    navigation.navigate('AnimalDetail', { id: animalId });
  };
  
  const handleTaskPress = (animalId: string) => {
    navigation.navigate('AnimalDetail', { id: animalId });
  };
  
  const handleSubscription = () => {
    navigation.navigate('Subscription');
  };
  
  const handleRefreshSpeed = async () => {
    if (!activeAnimal) return;
    
    setIsRefreshingSpeed(true);
    
    try {
      const newSpeed = Math.random() * 30;
      await updateAnimalSpeed(activeAnimal.id, newSpeed);
      toast.success(t('speedUpdatedSuccess', { animalName: activeAnimal.name }));
    } catch (error) {
      console.error('Error updating speed:', error);
      toast.error(t('speedUpdateFailed'));
    } finally {
      setIsRefreshingSpeed(false);
    }
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <LinearGradient
        colors={[
          isDarkMode ? 'rgba(61, 140, 145, 0.2)' : 'rgba(61, 140, 145, 0.05)', 
          'transparent'
        ]}
        style={styles.headerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      />
      
      <View style={[styles.header, { backgroundColor: 'transparent' }]}>
        <View>
          <Text style={[styles.title, { color: colors.text }]}>{t('appTitle')}</Text>
          <Text style={[styles.subtitle, { color: colors.textLight }]}>{t('appSubtitle')}</Text>
        </View>
      </View>
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {!isPremium && (
          <PremiumBanner onPress={handleSubscription} />
        )}
        
        {/* AI Onboarding Banner - Show if selected animal has no AI data */}
        {selectedAnimal && 
         !getLatestHealthAssessment(selectedAnimal.id) && 
         !getLatestReadinessScore(selectedAnimal.id) && (
          <AIOnboardingBanner
            onGetStarted={() => navigation.navigate('LogTrainingSession', { animalId: selectedAnimal.id })}
          />
        )}
        
        <AnimalSelector
          animals={animals}
          selectedAnimal={selectedAnimal}
          onAnimalSelect={setSelectedAnimal}
        />
        
        {selectedAnimal && (
          <HealthSnapshot
            animal={selectedAnimal}
            onCardPress={handleHealthCardPress}
          />
        )}
        
        {selectedAnimal && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                {t('animalSpeedTitle', { animalName: selectedAnimal.name })}
              </Text>
            </View>
            
            <SpeedMonitor 
              animal={selectedAnimal} 
              onRefresh={handleRefreshSpeed}
              isRefreshing={isRefreshingSpeed}
            />
          </View>
        )}
        
        <AlertsSection
          alerts={healthAndLocationAlerts}
          vaccineAlerts={vaccineAlerts}
          onAlertPress={handleAlertPress}
        />
        
        <AnimalsOverview
          animals={animals}
          onSeeAll={handleAnimalsOverview.onSeeAll}
          onAddAnimal={handleAnimalsOverview.onAddAnimal}
          onAnimalPress={handleAnimalsOverview.onAnimalPress}
        />
        
        <UpcomingTasks
          tasks={upcomingTasks}
          onTaskPress={handleTaskPress}
        />
      </ScrollView>
      
      {/* AI Assistant Widget for Selected Animal */}
      {selectedAnimal && (
        <AIAssistantWidget
          animalId={selectedAnimal.id}
          animalName={selectedAnimal.name}
          position="bottom-right"
          onNavigateToAI={() => navigation.navigate('AIAssistant', { animalId: selectedAnimal.id })}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 200,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0,
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
  },
  subtitle: {
    fontSize: 14,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
});

export default HomeScreen;
