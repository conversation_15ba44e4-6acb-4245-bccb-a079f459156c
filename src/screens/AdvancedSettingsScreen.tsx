
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  StatusBar
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { 
  Heart, 
  Bell, 
  MapPin, 
  Thermometer,
  Save,
  Moon,
  Sun
} from 'lucide-react-native';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { toast } from 'sonner-native';
import LanguageSelector from '../components/settings/LanguageSelector';
import SettingToggleItem from '../components/settings/SettingToggleItem';
import SettingTextInputItem from '../components/settings/SettingTextInputItem';
import SettingRangeInputItem from '../components/settings/SettingRangeInputItem';
import ReminderSettingsCard from '../components/settings/ReminderSettingsCard';
import { useFormValidation } from '../hooks/useFormValidation';
import { 
  validateNumber, 
  validateHeartRate, 
  validateTemperature 
} from '../utils/validation';

const AdvancedSettingsScreen = () => {
  const navigation = useNavigation();
  const { colors, isDarkMode, toggleTheme } = useTheme();
  const { t, isRTL } = useLanguage();
  const validation = useFormValidation();
  
  // Alert settings
  const [heartRateAlerts, setHeartRateAlerts] = useState(true);
  const [minHeartRate, setMinHeartRate] = useState('30');
  const [maxHeartRate, setMaxHeartRate] = useState('50');
  
  const [temperatureAlerts, setTemperatureAlerts] = useState(true);
  const [minTemperature, setMinTemperature] = useState('37.2');
  const [maxTemperature, setMaxTemperature] = useState('38.5');
  
  const [locationAlerts, setLocationAlerts] = useState(true);
  const [locationUpdateHours, setLocationUpdateHours] = useState('24');
  
  // Reminder settings
  const [medicationReminders, setMedicationReminders] = useState(true);
  const [feedingReminders, setFeedingReminders] = useState(true);
  const [vaccinationReminders, setVaccinationReminders] = useState(true);
  const [reminderTime, setReminderTime] = useState('15'); // minutes before
  
  const handleSave = () => {
    // Comprehensive validation for all numeric settings
    const validations = {
      minHeartRate: heartRateAlerts ? validateHeartRate(minHeartRate) : { isValid: true },
      maxHeartRate: heartRateAlerts ? validateHeartRate(maxHeartRate) : { isValid: true },
      minTemperature: temperatureAlerts ? validateTemperature(minTemperature) : { isValid: true },
      maxTemperature: temperatureAlerts ? validateTemperature(maxTemperature) : { isValid: true },
      locationUpdateHours: locationAlerts ? validateNumber(locationUpdateHours, 'Location update interval', 1, 168, false) : { isValid: true },
      reminderTime: validateNumber(reminderTime, 'Reminder time', 1, 60, false)
    };

    // Additional range validation
    if (heartRateAlerts && parseFloat(minHeartRate) >= parseFloat(maxHeartRate)) {
      validations.maxHeartRate = { isValid: false, error: t('errorMaxHeartRateHigher') };
    }
    
    if (temperatureAlerts && parseFloat(minTemperature) >= parseFloat(maxTemperature)) {
      validations.maxTemperature = { isValid: false, error: t('errorMaxTemperatureHigher') };
    }

    const isFormValid = validation.validateAllFields(validations);
    
    if (!isFormValid) {
      const firstError = Object.values(validation.errors)[0];
      toast.error(firstError || t('errorFixBeforeSaving'));
      return;
    }
    
    // In a real app, this would save to the database
    toast.success(t('settingsSavedSuccess'));
    navigation.goBack();
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <Header 
        title={t('advancedSettings')} 
        showBackButton
        rightAction={{
          icon: <Save size={24} color={colors.primary} />,
          onPress: handleSave,
        }}
      />
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          {/* Language & Region Section */}
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('languageSettings')}</Text>
          <LanguageSelector style={styles.languageSelector} />
          
          {/* Theme Settings Section */}
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('themeSettings')}</Text>
          
          <SettingToggleItem
            icon={isDarkMode ? Moon : Sun}
            title={t('darkMode')}
            value={isDarkMode}
            onValueChange={toggleTheme}
          />
          
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('healthAlerts')}</Text>
          
          <SettingToggleItem
            icon={Heart}
            title={t('heartRateAlerts')}
            value={heartRateAlerts}
            onValueChange={setHeartRateAlerts}
          />
          
          {heartRateAlerts && (
            <SettingRangeInputItem
              icon={Heart}
              title={t('heartRateRange')}
              minLabel={t('minBpm')}
              minValue={minHeartRate}
              onMinChange={setMinHeartRate}
              maxLabel={t('maxBpm')}
              maxValue={maxHeartRate}
              onMaxChange={setMaxHeartRate}
              keyboardType="number-pad"
            />
          )}
          
          <SettingToggleItem
            icon={Thermometer}
            title={t('temperatureAlerts')}
            value={temperatureAlerts}
            onValueChange={setTemperatureAlerts}
          />
          
          {temperatureAlerts && (
            <SettingRangeInputItem
              icon={Thermometer}
              title={t('temperatureRange')}
              minLabel={t('minCelsius')}
              minValue={minTemperature}
              onMinChange={setMinTemperature}
              maxLabel={t('maxCelsius')}
              maxValue={maxTemperature}
              onMaxChange={setMaxTemperature}
              keyboardType="decimal-pad"
            />
          )}
          
          <SettingToggleItem
            icon={MapPin}
            title={t('locationAlerts')}
            value={locationAlerts}
            onValueChange={setLocationAlerts}
          />
          
          {locationAlerts && (
            <SettingTextInputItem
              icon={MapPin}
              title={t('locationUpdateInterval')}
              label={t('locationUpdateLabel')}
              value={locationUpdateHours}
              onValueChange={setLocationUpdateHours}
              keyboardType="number-pad"
            />
          )}
          
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('reminders')}</Text>
          
          <ReminderSettingsCard
            medicationReminders={medicationReminders}
            onMedicationRemindersChange={setMedicationReminders}
            feedingReminders={feedingReminders}
            onFeedingRemindersChange={setFeedingReminders}
            vaccinationReminders={vaccinationReminders}
            onVaccinationRemindersChange={setVaccinationReminders}
            reminderTime={reminderTime}
            onReminderTimeChange={setReminderTime}
          />
          
          <TouchableOpacity 
            style={[
              styles.saveButton, 
              { backgroundColor: !validation.isValid ? colors.textLight : colors.primary }
            ]}
            onPress={handleSave}
            disabled={!validation.isValid}
          >
            <Save size={20} color={colors.card} />
            <Text style={[styles.saveButtonText, { color: colors.card }]}>{t('saveSettings')}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
    marginBottom: 32,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default AdvancedSettingsScreen;
