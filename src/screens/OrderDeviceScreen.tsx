import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  StatusBar
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { ShoppingCart, Star, Truck } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useProductStore, Product } from '../store/productStore';
import { DevicesStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';

type OrderDeviceScreenNavigationProp = NativeStackNavigationProp<DevicesStackParamList, 'OrderDevice'>;

const OrderDeviceScreen = () => {
  const navigation = useNavigation<OrderDeviceScreenNavigationProp>();
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  const { products, isLoading, error, fetchProducts, clearError } = useProductStore();

  const handleProductPress = (product: Product) => {
    navigation.navigate('Checkout', { productId: product.id });
  };

  const handleRefresh = async () => {
    clearError();
    await fetchProducts();
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(price);
  };

  const getStockStatus = (stockQuantity: number) => {
    if (stockQuantity === 0) {
      return { text: 'Out of Stock', color: colors.error };
    } else if (stockQuantity <= 5) {
      return { text: `Only ${stockQuantity} left`, color: colors.warning };
    } else {
      return { text: 'In Stock', color: colors.success };
    }
  };

  const renderProductCard = ({ item: product }: { item: Product }) => {
    const stockStatus = getStockStatus(product.stock_quantity);
    const specifications = product.specifications || {};

    return (
      <Card style={styles.productCard}>
        <TouchableOpacity
          style={styles.productContent}
          onPress={() => handleProductPress(product)}
          disabled={product.stock_quantity === 0}
        >
          {/* Product Image */}
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: product.image_url }}
              style={styles.productImage}
              resizeMode="cover"
            />
            {product.stock_quantity === 0 && (
              <View style={[styles.outOfStockOverlay, { backgroundColor: colors.surface + '90' }]}>
                <Text style={[styles.outOfStockText, { color: colors.error }]}>
                  Out of Stock
                </Text>
              </View>
            )}
          </View>

          {/* Product Info */}
          <View style={styles.productInfo}>
            <Text style={[styles.productName, { color: colors.text }]}>
              {product.name}
            </Text>
            
            <Text style={[styles.productDescription, { color: colors.textSecondary }]} numberOfLines={2}>
              {product.description}
            </Text>

            {/* Key Features */}
            {specifications.features && (
              <View style={styles.featuresContainer}>
                {specifications.features.slice(0, 2).map((feature: string, index: number) => (
                  <View key={index} style={[styles.featureTag, { backgroundColor: colors.primary + '20' }]}>
                    <Text style={[styles.featureText, { color: colors.primary }]}>
                      {feature}
                    </Text>
                  </View>
                ))}
              </View>
            )}

            {/* Price and Stock */}
            <View style={styles.priceRow}>
              <Text style={[styles.price, { color: colors.primary }]}>
                {formatPrice(product.price, product.currency)}
              </Text>
              <Text style={[styles.stockStatus, { color: stockStatus.color }]}>
                {stockStatus.text}
              </Text>
            </View>

            {/* Action Button */}
            <TouchableOpacity
              style={[
                styles.orderButton,
                {
                  backgroundColor: product.stock_quantity > 0 ? colors.primary : colors.disabled
                }
              ]}
              onPress={() => handleProductPress(product)}
              disabled={product.stock_quantity === 0}
            >
              <ShoppingCart size={16} color={colors.white} />
              <Text style={[styles.orderButtonText, { color: colors.white }]}>
                {product.stock_quantity > 0 ? 'Order Now' : 'Out of Stock'}
              </Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <ShoppingCart size={64} color={colors.textSecondary} />
      <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
        No Devices Available
      </Text>
      <Text style={[styles.emptyStateDescription, { color: colors.textSecondary }]}>
        Check back later for new device offerings
      </Text>
    </View>
  );

  const renderErrorState = () => (
    <View style={styles.errorState}>
      <Text style={[styles.errorText, { color: colors.error }]}>
        {error}
      </Text>
      <TouchableOpacity
        style={[styles.retryButton, { backgroundColor: colors.primary }]}
        onPress={handleRefresh}
      >
        <Text style={[styles.retryButtonText, { color: colors.white }]}>
          Try Again
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />
      
      <Header
        title="Order Devices"
        showBack
        rightIcon={Truck}
        onRightPress={() => navigation.navigate('OrderHistory')}
      />

      {isLoading && products.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading devices...
          </Text>
        </View>
      ) : error ? (
        renderErrorState()
      ) : products.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={products}
          renderItem={renderProductCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={handleRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  productCard: {
    marginBottom: 16,
    overflow: 'hidden',
  },
  productContent: {
    padding: 0,
  },
  imageContainer: {
    position: 'relative',
    height: 200,
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  outOfStockOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  outOfStockText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  productInfo: {
    padding: 16,
  },
  productName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  productDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  featureTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  featureText: {
    fontSize: 12,
    fontWeight: '500',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  price: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  stockStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
  orderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    gap: 8,
  },
  orderButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    gap: 16,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  emptyStateDescription: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    gap: 16,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  retryButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default OrderDeviceScreen;