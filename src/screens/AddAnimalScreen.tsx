
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TextInput, 
  TouchableOpacity, 
  Switch,
  Image,
  Alert,
  ActivityIndicator,
  StatusBar,
  Platform
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { COLORS } from '../constants/colors';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import { useAnimalStore } from '../store/animalStore';
import { AnimalsStackParamList } from '../navigation';
import { Animal } from '../mocks/animals';
import { toast } from 'sonner-native';
import ImagePickerWithOptions from '../components/ImagePickerWithOptions';
import AnimalBasicInfoForm from '../components/animalForms/AnimalBasicInfoForm';
import AnimalAdditionalInfoForm from '../components/animalForms/AnimalAdditionalInfoForm';
import { useFormValidation } from '../hooks/useFormValidation';
import { 
  validateRequired, 
  validateAge, 
  validateMicrochipId, 
  validateLength 
} from '../utils/validation';

type AddAnimalScreenRouteProp = RouteProp<AnimalsStackParamList, 'AddAnimal' | 'EditAnimal'>;
type AddAnimalScreenNavigationProp = NativeStackNavigationProp<AnimalsStackParamList, 'AddAnimal' | 'EditAnimal'>;

type AnimalType = 'horse' | 'camel' | 'other';
type Gender = 'male' | 'female';

const AddAnimalScreen = () => {
  const navigation = useNavigation<AddAnimalScreenNavigationProp>();
  const route = useRoute<AddAnimalScreenRouteProp>();
  const { colors, isDarkMode } = useTheme();
  
  const { addAnimal, updateAnimal, getAnimalById } = useAnimalStore();
  
  // Check if we're editing an existing animal
  const isEditing = route.name === 'EditAnimal';
  const animalId = isEditing ? route.params?.id : undefined;
  const existingAnimal = animalId ? getAnimalById(animalId) : undefined;
  
  // Form state
  const [name, setName] = useState('');
  const [type, setType] = useState<AnimalType>('horse');
  const [breed, setBreed] = useState('');
  const [age, setAge] = useState('');
  const [gender, setGender] = useState<Gender>('male');
  const [color, setColor] = useState('');
  const [microchipId, setMicrochipId] = useState('');
  const [dam, setDam] = useState('');
  const [sire, setSire] = useState('');
  const [notes, setNotes] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [passportImageUrl, setPassportImageUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  
  // Load existing animal data if editing
  useEffect(() => {
    if (existingAnimal) {
      setName(existingAnimal.name);
      setType(existingAnimal.type);
      setBreed(existingAnimal.breed);
      setAge(existingAnimal.age?.toString() || '');
      setGender(existingAnimal.gender || 'male');
      setColor(existingAnimal.color || '');
      setMicrochipId(existingAnimal.microchipId || '');
      setDam(existingAnimal.dam || '');
      setSire(existingAnimal.sire || '');
      setNotes(existingAnimal.notes || '');
      setImageUrl(existingAnimal.imageUrl || '');
      setPassportImageUrl(existingAnimal.passportImageUrl || '');
    }
  }, [existingAnimal]);
  
  const validation = useFormValidation();

  const handleSave = async () => {
    // Comprehensive validation
    const validations = {
      name: validateRequired(name, 'Name'),
      breed: validateRequired(breed, 'Breed'),
      age: validateAge(age),
      microchipId: validateMicrochipId(microchipId),
      color: validateLength(color, 'Color', undefined, 50),
      dam: validateLength(dam, 'Dam', undefined, 100),
      sire: validateLength(sire, 'Sire', undefined, 100),
      notes: validateLength(notes, 'Notes', undefined, 500)
    };

    const isFormValid = validation.validateAllFields(validations);
    
    if (!isFormValid) {
      const firstError = Object.values(validation.errors)[0];
      toast.error(firstError || 'Please fix the errors before saving');
      return;
    }
    
    validation.setSubmitting(true);
    
    try {
      const animalData: Omit<Animal, 'id'> = {
        name: name.trim(),
        type,
        breed: breed.trim(),
        age: age ? parseInt(age) : undefined,
        gender,
        color: color.trim() || undefined,
        imageUrl: imageUrl || 'https://magically.life/api/media/image?query=silhouette%20of%20a%20' + type,
        microchipId: microchipId.trim() || undefined,
        dam: dam.trim() || undefined,
        sire: sire.trim() || undefined,
        notes: notes.trim() || undefined,
        passportImageUrl: passportImageUrl || undefined,
      };
      
      if (isEditing && animalId) {
        await updateAnimal(animalId, animalData);
        toast.success(`${name} updated successfully`);
      } else {
        await addAnimal(animalData);
        toast.success(`${name} added successfully`);
      }
      
      navigation.navigate('Animals');
    } catch (error) {
      console.error('Error saving animal:', error);
      toast.error('Failed to save animal. Please try again.');
    } finally {
      validation.setSubmitting(false);
    }
  };
  

  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <Header 
        title={isEditing ? 'Edit Animal' : 'Add Animal'} 
        showBackButton 
      />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <View style={styles.imageSection}>
            <ImagePickerWithOptions
              label="Photo"
              imageUrl={imageUrl}
              onImageChange={setImageUrl}
              placeholder="Add Photo"
            />
          </View>
          
          <AnimalBasicInfoForm
            name={name}
            setName={setName}
            type={type}
            setType={setType}
            breed={breed}
            setBreed={setBreed}
            age={age}
            setAge={setAge}
            color={color}
            setColor={setColor}
            gender={gender}
            setGender={setGender}
          />
          
          <AnimalAdditionalInfoForm
            microchipId={microchipId}
            setMicrochipId={setMicrochipId}
            dam={dam}
            setDam={setDam}
            sire={sire}
            setSire={setSire}
            notes={notes}
            setNotes={setNotes}
          />
          
          <View style={styles.formSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Passport</Text>
            <ImagePickerWithOptions
              label="Passport Image"
              imageUrl={passportImageUrl}
              onImageChange={setPassportImageUrl}
              placeholder="Add Passport Image"
              isPassport={true}
            />
          </View>
          
          <TouchableOpacity 
            style={[
              styles.saveButton, 
              { backgroundColor: isLoading ? colors.textLight : colors.primary }
            ]}
            onPress={handleSave}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#FFF" size="small" />
            ) : (
              <Text style={styles.saveButtonText}>
                {isEditing ? 'Update Animal' : 'Save Animal'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 40,
  },
  imageSection: {
    alignItems: 'center',
    marginBottom: 24,
    position: 'relative',
  },

  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },

  saveButton: {
    paddingVertical: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  saveButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AddAnimalScreen;
