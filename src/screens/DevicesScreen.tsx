
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { AlertTriangle, ShoppingCart } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { DevicesStackParamList } from '../navigation';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useDeviceManagement } from '../hooks/useDeviceManagement';
import { useDeviceStore } from '../store/deviceStore';
import BluetoothUnavailableView from '../components/devices/BluetoothUnavailableView';
import PairedDevicesList from '../components/devices/PairedDevicesList';
import DeviceDiscoveryControls from '../components/devices/DeviceDiscoveryControls';
import AvailableDevicesList from '../components/devices/AvailableDevicesList';

type DevicesScreenNavigationProp = NativeStackNavigationProp<DevicesStackParamList, 'Devices'>;

/**
 * @magic_description Devices management screen
 * Handles device scanning, pairing, and management with tabbed interface
 */
const DevicesScreen = () => {
  const navigation = useNavigation<DevicesScreenNavigationProp>();
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  
  // Tab state management
  const [selectedTab, setSelectedTab] = useState<'paired' | 'available'>('paired');
  
  // Get device store state for loading and error handling - atomic selection for stable references
  const fetchDevices = useDeviceStore(state => state.fetchDevices);
  const deviceStoreLoading = useDeviceStore(state => state.isLoading);
  const deviceStoreError = useDeviceStore(state => state.error);
  
  // Get device management functionality from custom hook with enhanced error handling
  const {
    isScanning,
    isLoading: deviceManagementLoading,
    pairedDevices,
    availableDevices,
    bluetoothAvailable,
    barcodeInput,
    showBarcodeInput,
    isLoadingBluetoothStatus,
    isLoadingDevices,
    bluetoothError,
    devicesError,
    handleStartScan,
    handleStopScan,
    handleAddDevice,
    handlePairDevice,
    handleUnpairDevice,
    handleScanBarcode,
    handleAddDeviceByBarcode,
    setBarcodeInput,
    setShowBarcodeInput,
    retryBluetoothCheck,
    retryDevicesFetch,
  } = useDeviceManagement();
  
  // Fetch devices when component mounts
  useEffect(() => {
    fetchDevices();
  }, [fetchDevices]);
  

  

  

  
  // Enhanced loading state with defensive checks
  const isInitialLoading = (deviceStoreLoading || isLoadingBluetoothStatus || isLoadingDevices) && 
                          (!Array.isArray(pairedDevices) || pairedDevices.length === 0);
  
  if (isInitialLoading) {
    return (
      <View style={[styles.container, styles.centerContent, { backgroundColor: colors.background }]}>
        <StatusBar 
          barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
          backgroundColor={colors.background} 
        />
        <Header title={t('devices')} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            {isLoadingBluetoothStatus ? t('checkingBluetooth') : 
             isLoadingDevices ? t('loadingDevices') : 
             t('initializing')}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <Header title="Devices" />
      
      {/* Order New Device Button */}
      <View style={[styles.orderDeviceContainer, { backgroundColor: colors.background }]}>
        <TouchableOpacity
          style={[styles.orderDeviceButton, { backgroundColor: colors.primary }]}
          onPress={() => navigation.navigate('OrderDevice')}
        >
          <ShoppingCart size={20} color={colors.white} />
          <Text style={[styles.orderDeviceButtonText, { color: colors.white }]}>
            Order New Device
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Show error message if there's a device store error */}
      {deviceStoreError && (
        <View style={[styles.errorContainer, { backgroundColor: colors.error + '20' }]}>
          <AlertTriangle size={20} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.error }]}>
            {deviceStoreError}
          </Text>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={fetchDevices}
          >
            <Text style={styles.retryButtonText}>{t('retry')}</Text>
          </TouchableOpacity>
        </View>
      )}
      
      <View style={styles.tabContainer}>
        <TouchableOpacity 
          style={[
            styles.tabButton,
            selectedTab === 'paired' && [styles.activeTabButton, { borderBottomColor: colors.primary }],
            { borderBottomColor: selectedTab === 'paired' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('paired')}
        >
          <Text 
            style={[
              styles.tabButtonText,
              { color: selectedTab === 'paired' ? colors.primary : colors.textSecondary }
            ]}
          >
            {t('paired')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.tabButton,
            selectedTab === 'available' && [styles.activeTabButton, { borderBottomColor: colors.primary }],
            { borderBottomColor: selectedTab === 'available' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setSelectedTab('available')}
        >
          <Text 
            style={[
              styles.tabButtonText,
              { color: selectedTab === 'available' ? colors.primary : colors.textSecondary }
            ]}
          >
            {t('available')}
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Enhanced error handling for Bluetooth and devices */}
      {bluetoothError ? (
        <View style={[styles.errorContainer, { backgroundColor: colors.error + '20' }]}>
          <AlertTriangle size={20} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.error }]}>
            {t('bluetoothErrorPrefix')} {bluetoothError}
          </Text>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={retryBluetoothCheck}
          >
            <Text style={styles.retryButtonText}>{t('retryBluetoothCheck')}</Text>
          </TouchableOpacity>
        </View>
      ) : !bluetoothAvailable ? (
        <BluetoothUnavailableView colors={colors} />
      ) : (
        <View style={styles.devicesContainer}>
          {selectedTab === 'available' && (
            <DeviceDiscoveryControls
              isScanning={Boolean(isScanning)}
              onStartScan={handleStartScan}
              onStopScan={handleStopScan}
              onScanBarcode={handleScanBarcode}
              showBarcodeInput={Boolean(showBarcodeInput)}
              barcodeInput={String(barcodeInput || '')}
              onBarcodeInputChange={setBarcodeInput}
              onAddDeviceByBarcode={handleAddDeviceByBarcode}
              colors={colors}
            />
          )}
          
          {selectedTab === 'paired' ? (
            <PairedDevicesList
              pairedDevices={Array.isArray(pairedDevices) ? pairedDevices : []}
              onUnpairDevice={handleUnpairDevice}
              onFindDevicesPress={() => setSelectedTab('available')}
              colors={colors}
            />
          ) : (
            <AvailableDevicesList
              availableDevices={Array.isArray(availableDevices) ? availableDevices : []}
              isScanning={Boolean(isScanning || deviceManagementLoading)}
              onPairDevice={handlePairDevice}
              onAddDevice={handleAddDevice}
              colors={colors}
            />
          )}
        </View>
      )}
      
      {!bluetoothAvailable && (
        <View style={[styles.bluetoothWarning, { backgroundColor: colors.error + '20' }]}>
          <AlertTriangle size={20} color={colors.error} />
          <Text style={[styles.bluetoothWarningText, { color: colors.error }]}>
            {t('bluetoothRequiredWarning')}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  orderDeviceContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  orderDeviceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  orderDeviceButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    margin: 16,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    marginLeft: 12,
  },
  retryButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '500',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
  },
  activeTabButton: {
    borderBottomWidth: 2,
  },
  tabButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  devicesContainer: {
    flex: 1,
  },
  bluetoothWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    margin: 16,
    borderRadius: 8,
  },
  bluetoothWarningText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
});

export default DevicesScreen;
