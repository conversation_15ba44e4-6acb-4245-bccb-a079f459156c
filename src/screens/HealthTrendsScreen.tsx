import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { TrendingUp, TrendingDown, Activity, RefreshCw, BarChart3, Info } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalStore } from '../store/animalStore';
import { useAIHealthStore } from '../store/aiHealthStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import { toast } from 'sonner-native';
import { format } from 'date-fns';
import { supabase } from '../supabase/client';

type HealthTrendsScreenRouteProp = RouteProp<HomeStackParamList, 'HealthTrendsScreen'>;
type HealthTrendsScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'HealthTrendsScreen'>;

const HealthTrendsScreen: React.FC = () => {
  const navigation = useNavigation<HealthTrendsScreenNavigationProp>();
  const route = useRoute<HealthTrendsScreenRouteProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  // Safe destructuring with fallback for missing params
  const animalId = route.params?.animalId;
  
  // Early return if no animalId provided
  if (!animalId) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('healthTrends')} showBackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {t('missingAnimalId')}
          </Text>
        </View>
      </View>
    );
  }
  
  const { animals } = useAnimalStore();
  const {
    healthTrends,
    isLoadingTrends,
    fetchHealthTrends
  } = useAIHealthStore();
  
  const [refreshing, setRefreshing] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedTrendType, setSelectedTrendType] = useState<string>('all');
  
  const currentAnimal = animals.find(a => a.id === animalId);

  useEffect(() => {
    if (animalId) {
      loadHealthTrends();
    }
  }, [animalId]);

  const loadHealthTrends = async () => {
    try {
      await fetchHealthTrends(animalId);
    } catch (error) {
      console.error('Error loading health trends:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadHealthTrends();
    setRefreshing(false);
  };

  const handleProcessTrends = async () => {
    setIsProcessing(true);
    try {
      const { data, error } = await supabase.functions.invoke('process-health-trends', {
        body: { animal_id: animalId }
      });
      
      if (error) throw error;
      
      toast.success(t('trendsProcessed'));
      await loadHealthTrends();
    } catch (error) {
      console.error('Error processing trends:', error);
      toast.error(t('trendsProcessingFailed'));
    } finally {
      setIsProcessing(false);
    }
  };

  const getTrendDirectionIcon = (direction: string) => {
    switch (direction) {
      case 'improving':
        return <TrendingUp size={20} color="#10B981" />;
      case 'declining':
      case 'concerning':
        return <TrendingDown size={20} color="#EF4444" />;
      case 'stable':
        return <Activity size={20} color="#6B7280" />;
      default:
        return <Activity size={20} color="#6B7280" />;
    }
  };

  const getTrendDirectionColor = (direction: string) => {
    switch (direction) {
      case 'improving': return '#10B981';
      case 'declining': return '#EF4444';
      case 'concerning': return '#DC2626';
      case 'stable': return '#6B7280';
      default: return '#6B7280';
    }
  };

  const getFilteredTrends = () => {
    if (selectedTrendType === 'all') {
      return healthTrends;
    }
    return healthTrends.filter(trend => 
      trend.trend_type === selectedTrendType
    );
  };

  const getTrendSummary = () => {
    const total = healthTrends.length;
    const improving = healthTrends.filter(t => t.trend_direction === 'improving').length;
    const declining = healthTrends.filter(t => t.trend_direction === 'declining').length;
    const concerning = healthTrends.filter(t => t.trend_direction === 'concerning').length;
    const stable = healthTrends.filter(t => t.trend_direction === 'stable').length;
    
    return { total, improving, declining, concerning, stable };
  };

  const getUniqueTrendTypes = () => {
    const types = ['all', ...new Set(healthTrends.map(t => t.trend_type))];
    return types;
  };

  const formatTrendType = (type: string) => {
    if (type === 'all') return t('allTrends');
    return type.replace(/_/g, ' ').split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const filteredTrends = getFilteredTrends();
  const trendSummary = getTrendSummary();
  const trendTypes = getUniqueTrendTypes();

  if (!currentAnimal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('healthTrends')} showBackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {t('animalNotFound')}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={colors.statusBarStyle} backgroundColor={colors.background} />
      <Header title={`${currentAnimal.name} - ${t('healthTrends')}`} showBackButton />
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Trends Summary Card */}
        <Card style={styles.summaryCard}>
          <View style={styles.summaryHeader}>
            <View style={styles.summaryTitleContainer}>
              <BarChart3 size={20} color={colors.primary} />
              <Text style={[styles.summaryTitle, { color: colors.text }]}>
                {t('trendsOverview')}
              </Text>
            </View>
            
            <TouchableOpacity
              style={[
                styles.processButton,
                { backgroundColor: colors.primary },
                isProcessing && { opacity: 0.7 }
              ]}
              onPress={handleProcessTrends}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <RefreshCw size={16} color="#FFFFFF" />
              )}
              <Text style={styles.processButtonText}>
                {isProcessing ? t('processing') : t('processTrends')}
              </Text>
            </TouchableOpacity>
          </View>
          
          {trendSummary.total > 0 ? (
            <>
              <View style={styles.summaryStats}>
                <View style={styles.statItem}>
                  <Text style={[styles.statNumber, { color: colors.text }]}>
                    {trendSummary.total}
                  </Text>
                  <Text style={[styles.statLabel, { color: colors.textLight }]}>
                    {t('activeTrends')}
                  </Text>
                </View>
                
                <View style={styles.trendDirectionStats}>
                  {trendSummary.improving > 0 && (
                    <View style={styles.trendDirectionItem}>
                      <TrendingUp size={16} color="#10B981" />
                      <Text style={[styles.trendDirectionText, { color: colors.textLight }]}>
                        {trendSummary.improving} {t('improving')}
                      </Text>
                    </View>
                  )}
                  {trendSummary.declining > 0 && (
                    <View style={styles.trendDirectionItem}>
                      <TrendingDown size={16} color="#EF4444" />
                      <Text style={[styles.trendDirectionText, { color: colors.textLight }]}>
                        {trendSummary.declining} {t('declining')}
                      </Text>
                    </View>
                  )}
                  {trendSummary.concerning > 0 && (
                    <View style={styles.trendDirectionItem}>
                      <TrendingDown size={16} color="#DC2626" />
                      <Text style={[styles.trendDirectionText, { color: colors.textLight }]}>
                        {trendSummary.concerning} {t('concerning')}
                      </Text>
                    </View>
                  )}
                  {trendSummary.stable > 0 && (
                    <View style={styles.trendDirectionItem}>
                      <Activity size={16} color="#6B7280" />
                      <Text style={[styles.trendDirectionText, { color: colors.textLight }]}>
                        {trendSummary.stable} {t('stable')}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
              
              <Text style={[styles.lastUpdated, { color: colors.textLight }]}>
                {t('lastUpdated')}: {healthTrends.length > 0 ? 
                  format(new Date(healthTrends[0].updated_at), 'MMM dd, yyyy HH:mm') : 
                  t('never')
                }
              </Text>
            </>
          ) : (
            <View style={styles.noDataContainer}>
              <BarChart3 size={32} color={colors.textLight} />
              <Text style={[styles.noDataText, { color: colors.textLight }]}>
                {t('noHealthTrends')}
              </Text>
              <Text style={[styles.noDataSubtext, { color: colors.textLight }]}>
                {t('processFirstTrends')}
              </Text>
            </View>
          )}
        </Card>

        {/* Trend Type Filter */}
        {trendTypes.length > 1 && (
          <Card style={styles.filterCard}>
            <Text style={[styles.filterTitle, { color: colors.text }]}>
              {t('filterByType')}
            </Text>
            
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.typeFilters}>
                {trendTypes.map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.typeButton,
                      {
                        backgroundColor: selectedTrendType === type ? colors.primary : colors.card,
                        borderColor: colors.border
                      }
                    ]}
                    onPress={() => setSelectedTrendType(type)}
                  >
                    <Text style={[
                      styles.typeButtonText,
                      {
                        color: selectedTrendType === type ? '#FFFFFF' : colors.text
                      }
                    ]}>
                      {formatTrendType(type)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </Card>
        )}

        {/* Trends List */}
        {filteredTrends.length > 0 ? (
          <View style={styles.trendsList}>
            {filteredTrends.map((trend) => (
              <Card key={trend.id} style={styles.trendCard}>
                <View style={styles.trendHeader}>
                  <View style={styles.trendTitleContainer}>
                    {getTrendDirectionIcon(trend.trend_direction)}
                    <View style={styles.trendTitleContent}>
                      <Text style={[styles.trendType, { color: colors.text }]}>
                        {formatTrendType(trend.trend_type)}
                      </Text>
                      <Text style={[
                        styles.trendDirection,
                        { color: getTrendDirectionColor(trend.trend_direction) }
                      ]}>
                        {trend.trend_direction.charAt(0).toUpperCase() + trend.trend_direction.slice(1)} {t('trend')}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.trendStrength}>
                    <Text style={[styles.strengthLabel, { color: colors.textLight }]}>
                      {t('strength')}
                    </Text>
                    <View style={[
                      styles.strengthBar,
                      { backgroundColor: colors.border }
                    ]}>
                      <View style={[
                        styles.strengthBarFill,
                        {
                          width: `${trend.trend_strength * 100}%`,
                          backgroundColor: getTrendDirectionColor(trend.trend_direction)
                        }
                      ]} />
                    </View>
                    <Text style={[styles.strengthValue, { color: colors.textLight }]}>
                      {(trend.trend_strength * 100).toFixed(0)}%
                    </Text>
                  </View>
                </View>
                
                <View style={styles.trendPeriod}>
                  <Text style={[styles.periodLabel, { color: colors.textLight }]}>
                    {t('period')}: {format(new Date(trend.start_date), 'MMM dd')} - 
                    {trend.end_date ? format(new Date(trend.end_date), 'MMM dd') : t('ongoing')}
                  </Text>
                </View>
                
                {trend.analysis_summary && (
                  <View style={styles.trendAnalysis}>
                    <Text style={[styles.analysisTitle, { color: colors.text }]}>
                      {t('analysis')}:
                    </Text>
                    <Text style={[styles.analysisText, { color: colors.textLight }]}>
                      {trend.analysis_summary}
                    </Text>
                  </View>
                )}
                
                {/* Trend Data Visualization */}
                {trend.trend_data && (
                  <View style={styles.trendDataContainer}>
                    <Text style={[styles.dataTitle, { color: colors.text }]}>
                      {t('trendData')}:
                    </Text>
                    
                    {/* Simple data display - could be enhanced with charts */}
                    <View style={styles.dataPoints}>
                      {Object.entries(trend.trend_data).map(([key, value], index) => (
                        <View key={index} style={styles.dataPoint}>
                          <Text style={[styles.dataKey, { color: colors.textLight }]}>
                            {key.replace(/_/g, ' ')}:
                          </Text>
                          <Text style={[styles.dataValue, { color: colors.text }]}>
                            {typeof value === 'number' ? value.toFixed(2) : String(value)}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </View>
                )}
              </Card>
            ))}
          </View>
        ) : (
          selectedTrendType !== 'all' && (
            <Card style={styles.noResultsCard}>
              <View style={styles.noResultsContainer}>
                <Info size={32} color={colors.textLight} />
                <Text style={[styles.noResultsText, { color: colors.textLight }]}>
                  {t('noTrendsOfType')}
                </Text>
                <Text style={[styles.noResultsSubtext, { color: colors.textLight }]}>
                  {t('tryDifferentType')}
                </Text>
              </View>
            </Card>
          )
        )}

        {/* Trends Information */}
        <Card style={styles.infoCard}>
          <View style={styles.infoHeader}>
            <Info size={20} color={colors.primary} />
            <Text style={[styles.infoTitle, { color: colors.text }]}>
              {t('aboutHealthTrends')}
            </Text>
          </View>
          
          <Text style={[styles.infoText, { color: colors.textLight }]}>
            {t('healthTrendsInfo')}
          </Text>
          
          <View style={styles.trendTypesInfo}>
            <Text style={[styles.trendTypesTitle, { color: colors.text }]}>
              {t('trendTypes')}:
            </Text>
            
            <View style={styles.trendTypeInfoItem}>
              <TrendingUp size={16} color="#10B981" />
              <Text style={[styles.trendTypeInfoText, { color: colors.textLight }]}>
                <Text style={{ fontWeight: '500' }}>{t('improving')}:</Text> {t('improvingTrendDescription')}
              </Text>
            </View>
            
            <View style={styles.trendTypeInfoItem}>
              <Activity size={16} color="#6B7280" />
              <Text style={[styles.trendTypeInfoText, { color: colors.textLight }]}>
                <Text style={{ fontWeight: '500' }}>{t('stable')}:</Text> {t('stableTrendDescription')}
              </Text>
            </View>
            
            <View style={styles.trendTypeInfoItem}>
              <TrendingDown size={16} color="#EF4444" />
              <Text style={[styles.trendTypeInfoText, { color: colors.textLight }]}>
                <Text style={{ fontWeight: '500' }}>{t('declining')}:</Text> {t('decliningTrendDescription')}
              </Text>
            </View>
          </View>
        </Card>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500'
  },
  summaryCard: {
    padding: 16,
    marginVertical: 8
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  summaryTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600'
  },
  processButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 6
  },
  processButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500'
  },
  summaryStats: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  statItem: {
    alignItems: 'center',
    marginRight: 24
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold'
  },
  statLabel: {
    fontSize: 12,
    marginTop: 2
  },
  trendDirectionStats: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12
  },
  trendDirectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4
  },
  trendDirectionText: {
    fontSize: 12
  },
  lastUpdated: {
    fontSize: 11,
    textAlign: 'center'
  },
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 24
  },
  noDataText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 4
  },
  noDataSubtext: {
    fontSize: 14,
    textAlign: 'center'
  },
  filterCard: {
    padding: 16,
    marginVertical: 8
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12
  },
  typeFilters: {
    flexDirection: 'row',
    gap: 8
  },
  typeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '500'
  },
  trendsList: {
    gap: 0
  },
  trendCard: {
    padding: 16,
    marginVertical: 8
  },
  trendHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12
  },
  trendTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
    gap: 12
  },
  trendTitleContent: {
    flex: 1
  },
  trendType: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4
  },
  trendDirection: {
    fontSize: 14,
    fontWeight: '500'
  },
  trendStrength: {
    alignItems: 'flex-end',
    width: 80
  },
  strengthLabel: {
    fontSize: 10,
    marginBottom: 4
  },
  strengthBar: {
    width: 60,
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 2
  },
  strengthBarFill: {
    height: '100%',
    borderRadius: 2
  },
  strengthValue: {
    fontSize: 10
  },
  trendPeriod: {
    marginBottom: 12
  },
  periodLabel: {
    fontSize: 12
  },
  trendAnalysis: {
    marginBottom: 12
  },
  analysisTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4
  },
  analysisText: {
    fontSize: 13,
    lineHeight: 18
  },
  trendDataContainer: {
    marginTop: 8
  },
  dataTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8
  },
  dataPoints: {
    gap: 4
  },
  dataPoint: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  dataKey: {
    fontSize: 12,
    flex: 1
  },
  dataValue: {
    fontSize: 12,
    fontWeight: '500'
  },
  noResultsCard: {
    padding: 16,
    marginVertical: 8
  },
  noResultsContainer: {
    alignItems: 'center',
    paddingVertical: 24
  },
  noResultsText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 4
  },
  noResultsSubtext: {
    fontSize: 14,
    textAlign: 'center'
  },
  infoCard: {
    padding: 16,
    marginVertical: 8
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600'
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16
  },
  trendTypesInfo: {
    gap: 8
  },
  trendTypesTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8
  },
  trendTypeInfoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8
  },
  trendTypeInfoText: {
    fontSize: 13,
    flex: 1,
    lineHeight: 18
  }
});

export default HealthTrendsScreen;