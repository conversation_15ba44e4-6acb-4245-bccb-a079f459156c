
import React, { useRef } from 'react';
import {
  View, 
  StyleSheet, 
  Animated, 
  KeyboardAvoidingView, 
  Platform, 
  ScrollView
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

import { useAuthForm } from '../hooks/useAuthForm';

// Import modular components
import LoginHeader from '../components/auth/LoginHeader';
import LoginErrorDisplay from '../components/auth/LoginErrorDisplay';
import LoginFormInputs from '../components/auth/LoginFormInputs';
import LoginSecurityWarnings from '../components/auth/LoginSecurityWarnings';
import LoginActionButtons from '../components/auth/LoginActionButtons';
import EmailConfirmationBanner from '../components/auth/EmailConfirmationBanner';



/**
 * @magic_description Main login screen component - refactored into smaller modular components
 * Handles authentication flow including sign in, sign up, and password reset functionality
 */
const LoginScreen = () => {
  const { colors } = useTheme();
  
  // Animation
  const shakeAnimation = useRef(new Animated.Value(0)).current;
  
  // Start shake animation
  const startShakeAnimation = () => {
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();
  };
  
  // Use the auth form hook with shake animation callback
  const authForm = useAuthForm(startShakeAnimation);
  
  // Handle form data changes from LoginFormInputs
  const handleFormInputChange = (data: { email: string; password: string; confirmPassword: string }) => {
    authForm.setEmail(data.email);
    authForm.setPassword(data.password);
    // Only set confirmPassword if it's relevant (in sign-up mode)
    if (authForm.isSignUp) {
      authForm.setConfirmPassword(data.confirmPassword);
    }
  };
  
  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <LoginHeader 
          isSignUp={authForm.isSignUp}
          isResetPassword={authForm.isResetPassword}
          colors={colors}
        />
        
        <LoginErrorDisplay 
          errorMessage={authForm.errorMessage}
          colors={colors}
        />
        
        <EmailConfirmationBanner 
          needsEmailConfirmation={authForm.needsEmailConfirmation}
          onResendConfirmation={authForm.handleResendConfirmation}
          isLoading={authForm.isLoading}
          colors={colors}
        />
        
        <Animated.View 
          style={[
            styles.formContainer,
            { transform: [{ translateX: shakeAnimation }] }
          ]}
        >
          <LoginFormInputs 
            isSignUp={authForm.isSignUp}
            isResetPassword={authForm.isResetPassword}
            isLoading={authForm.isLoading}
            onFormDataChange={handleFormInputChange}
            initialEmail={authForm.email}
            initialPassword={authForm.password}
            initialConfirmPassword={authForm.confirmPassword}
          />
          
          <LoginSecurityWarnings 
            isAccountLocked={authForm.isAccountLocked}
            lockUntil={authForm.lockUntil}
            loginAttempts={authForm.loginAttempts}
            colors={colors}
          />
          
          <LoginActionButtons 
            isSignUp={authForm.isSignUp}
            isResetPassword={authForm.isResetPassword}
            isLoading={authForm.isLoading}
            isGoogleLoading={authForm.isGoogleLoading}
            isGoogleModuleLoading={authForm.isGoogleModuleLoading}
            googleModuleReady={authForm.googleModuleReady}
            googleModuleLoadFailed={authForm.googleModuleLoadFailed}
            isAccountLocked={authForm.isAccountLocked}
            onSubmit={() => {
              if (authForm.isSignUp) {
                authForm.handleSignUp();
              } else if (authForm.isResetPassword) {
                authForm.handleForgotPassword();
              } else {
                authForm.handleSignIn();
              }
            }}
            onToggleMode={() => {
              if (authForm.isSignUp) {
                authForm.switchToSignIn();
              } else {
                authForm.switchToSignUp();
              }
            }}
            onForgotPassword={authForm.switchToResetPassword}
            onBackToSignIn={authForm.backToSignIn}
            onGoogleSignIn={authForm.handleGoogleSignIn}
          />
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  infoContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
  },
  infoText: {
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 8,
  },
  resendButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  resendButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  formContainer: {
    width: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    position: 'relative',
  },
  inputIcon: {
    position: 'absolute',
    left: 16,
    zIndex: 1,
  },
  input: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 48,
    fontSize: 16,
  },
  eyeIcon: {
    position: 'absolute',
    right: 16,
    zIndex: 1,
  },
  button: {
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  linkButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  linkText: {
    fontSize: 14,
    fontWeight: '500',
  },
  forgotButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  forgotText: {
    fontSize: 14,
  },
  lockedMessage: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  lockedText: {
    fontSize: 14,
    textAlign: 'center',
  },
  warningMessage: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  warningText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default LoginScreen;
