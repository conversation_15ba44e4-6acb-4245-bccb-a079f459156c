
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../contexts/ThemeContext';
import { useUserStore } from '../store/userStore';
import { ProfileStackParamList } from '../navigation';
import Header from '../components/Header';
import { toast } from 'sonner-native';
import ProfileImageEditor from '../components/profile/ProfileImageEditor';
import ValidatedInput from '../components/ui/ValidatedInput';
import { useFormValidation } from '../hooks/useFormValidation';
import { 
  validateRequired, 
  validatePhone, 
  validateLength 
} from '../utils/validation';

type EditProfileScreenNavigationProp = NativeStackNavigationProp<ProfileStackParamList, 'EditProfile'>;

const EditProfileScreen = () => {
  const navigation = useNavigation<EditProfileScreenNavigationProp>();
  const { colors } = useTheme();
  const { user, updateProfile, isLoading } = useUserStore();
  const validation = useFormValidation();
  
  const [name, setName] = useState(user?.name || '');
  const [farmName, setFarmName] = useState(user?.farmName || '');
  const [phone, setPhone] = useState(user?.phone || '');
  const [address, setAddress] = useState(user?.address || '');
  const [imageUrl, setImageUrl] = useState(user?.imageUrl || '');
  
  const handleSave = async () => {
    // Comprehensive validation
    const validations = {
      name: validateRequired(name, 'Name'),
      farmName: validateLength(farmName, 'Farm Name', undefined, 100),
      phone: phone.trim() ? validatePhone(phone) : { isValid: true }, // Optional field
      address: validateLength(address, 'Address', undefined, 200)
    };

    const isFormValid = validation.validateAllFields(validations);
    
    if (!isFormValid) {
      const firstError = Object.values(validation.errors)[0];
      toast.error(firstError || 'Please fix the errors before saving');
      return;
    }

    validation.setSubmitting(true);
    
    try {
      // Create an object with only the fields that have changed
      const updatedProfile: any = {};
      
      if (name.trim() !== user?.name) updatedProfile.name = name.trim();
      if (farmName.trim() !== user?.farmName) updatedProfile.farmName = farmName.trim() || null;
      if (phone.trim() !== user?.phone) updatedProfile.phone = phone.trim() || null;
      if (address.trim() !== user?.address) updatedProfile.address = address.trim() || null;
      if (imageUrl !== user?.imageUrl) updatedProfile.imageUrl = imageUrl;
      
      // Only update if there are changes
      if (Object.keys(updatedProfile).length > 0) {
        await updateProfile(updatedProfile);
        toast.success('Profile updated successfully');
      } else {
        toast.success('No changes to save');
      }
      
      navigation.goBack();
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      validation.setSubmitting(false);
    }
  };
  

  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header title="Edit Profile" showBackButton />
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <ProfileImageEditor
          imageUrl={imageUrl}
          onImageChange={setImageUrl}
          userEmail={user?.email}
        />
        
        <ValidatedInput
          label="Name"
          value={name}
          onChangeText={setName}
          placeholder="Enter your name"
          required
          maxLength={100}
          error={validation.errors.name}
        />
        
        <ValidatedInput
          label="Farm Name"
          value={farmName}
          onChangeText={setFarmName}
          placeholder="Enter your farm name (optional)"
          maxLength={100}
          error={validation.errors.farmName}
        />
        
        <ValidatedInput
          label="Phone"
          value={phone}
          onChangeText={setPhone}
          placeholder="Enter your phone number (optional)"
          keyboardType="phone-pad"
          maxLength={20}
          error={validation.errors.phone}
        />
        
        <ValidatedInput
          label="Address"
          value={address}
          onChangeText={setAddress}
          placeholder="Enter your address (optional)"
          multiline
          numberOfLines={4}
          maxLength={200}
          error={validation.errors.address}
        />
        
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: colors.primary },
            (isLoading || validation.isSubmitting || !validation.isValid) && { opacity: 0.7 }
          ]}
          onPress={handleSave}
          disabled={isLoading || validation.isSubmitting}
        >
          {(isLoading || validation.isSubmitting) ? (
            <ActivityIndicator color="#FFF" size="small" />
          ) : (
            <Text style={styles.saveButtonText}>Save Changes</Text>
          )}
        </TouchableOpacity>
        
        <Text style={[styles.note, { color: colors.textLight }]}>
          Email address cannot be changed. Please contact support if you need to update your email.
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  saveButton: {
    height: 48,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  saveButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
  },
  note: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 16,
  },
});

export default EditProfileScreen;
