import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  ActivityIndicator,
  Dimensions,
  FlatList
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Brain
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalStore } from '../store/animalStore';
import { useAIHealthStore } from '../store/aiHealthStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import HealthScoreCard from '../components/healthScore/HealthScoreCard';
import OverallHealthSummary from '../components/aiHealth/AIHealthDashboardHeader';
import AnimalHealthCard from '../components/aiHealth/AnimalHealthCard';
import PriorityAlertsCard from '../components/aiHealth/PriorityAlertsCard';
import RecentActivityCard from '../components/aiHealth/RecentActivityCard';
import HealthInsightsCard from '../components/aiHealth/HealthInsightsCard';
import QuickActionsGrid from '../components/aiHealth/QuickActionsGrid';
import { toast } from 'sonner-native';
import { format } from 'date-fns';

type AIHealthDashboardScreenRouteProp = RouteProp<HomeStackParamList, 'AIHealthDashboard'>;
type AIHealthDashboardScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'AIHealthDashboard'>;

const AIHealthDashboardScreen: React.FC = () => {
  const navigation = useNavigation<AIHealthDashboardScreenNavigationProp>();
  const route = useRoute<AIHealthDashboardScreenRouteProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  const { animals } = useAnimalStore();
  const {
    healthScores,
    smartAlerts,
    unreadAlertsCount,
    diseaseRiskAssessments,
    healthTrends,
    isLoadingHealthScores,
    isLoadingAlerts,
    isLoadingRiskAssessments,
    isLoadingTrends,
    fetchLatestHealthScore,
    fetchSmartAlerts,
    fetchDiseaseRiskAssessments,
    fetchHealthTrends,
    calculateHealthScore,
    markAlertAsResolved
  } = useAIHealthStore();
  
  const [refreshing, setRefreshing] = useState(false);
  const [allAnimalsData, setAllAnimalsData] = useState<Map<string, any>>(new Map());
  
  const screenWidth = Dimensions.get('window').width;

  useEffect(() => {
    loadAllAnimalsData();
  }, [animals]);

  const loadAllAnimalsData = async () => {
    if (animals.length === 0) return;
    
    try {
      const dataMap = new Map();
      
      // Load data for all animals in parallel
      await Promise.all(
        animals.map(async (animal) => {
          try {
            const [healthScore, alerts, risks, trends] = await Promise.all([
              fetchLatestHealthScore(animal.id),
              fetchSmartAlerts(animal.id),
              fetchDiseaseRiskAssessments(animal.id),
              fetchHealthTrends(animal.id)
            ]);
            
            dataMap.set(animal.id, {
              healthScore,
              alerts: smartAlerts.filter(alert => alert.animal_id === animal.id),
              risks: diseaseRiskAssessments.filter(risk => risk.animal_id === animal.id),
              trends: healthTrends.filter(trend => trend.animal_id === animal.id)
            });
          } catch (error) {
            console.error(`Error loading data for ${animal.name}:`, error);
          }
        })
      );
      
      setAllAnimalsData(dataMap);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAllAnimalsData();
    setRefreshing(false);
  };

  // Helper functions for visual components
  const getHealthScoreForAnimal = (animalId: string) => {
    return healthScores.find(score => score.animal_id === animalId);
  };
  
  const getAlertsForAnimal = (animalId: string) => {
    return smartAlerts.filter(alert => alert.animal_id === animalId && !alert.is_resolved);
  };
  
  const getRisksForAnimal = (animalId: string) => {
    return diseaseRiskAssessments.filter(risk => risk.animal_id === animalId);
  };
  
  const getTrendsForAnimal = (animalId: string) => {
    return healthTrends.filter(trend => trend.animal_id === animalId);
  };
  
  const getOverallStableHealth = () => {
    const totalAnimals = animals.length;
    const healthyAnimals = animals.filter(animal => {
      const score = getHealthScoreForAnimal(animal.id);
      const alerts = getAlertsForAnimal(animal.id);
      return score && score.overall_score >= 70 && alerts.length === 0;
    }).length;
    
    return { healthy: healthyAnimals, total: totalAnimals };
  };
  
  const handleViewAnimalDetails = (animalId: string) => {
    navigation.navigate('AnimalDetail', { animalId });
  };
  
  const handleViewAIInsights = (animalId: string) => {
    navigation.navigate('AIAssistant', { animalId });
  };




  
  // Render animal health card for overview
  const renderAnimalHealthCard = ({ item: animal }: { item: any }) => {
    const healthScore = getHealthScoreForAnimal(animal.id);
    const alerts = getAlertsForAnimal(animal.id);
    const risks = getRisksForAnimal(animal.id);
    const trends = getTrendsForAnimal(animal.id);
    
    const criticalAlerts = alerts.filter(alert => alert.priority_level === 'critical').length;
    const highRisks = risks.filter(risk => risk.risk_level === 'high' || risk.risk_level === 'critical').length;
    
    return (
      <AnimalHealthCard
        animal={animal}
        healthScore={healthScore}
        alertsCount={alerts.length}
        criticalAlertsCount={criticalAlerts}
        highRisksCount={highRisks}
        trendsCount={trends.length}
        onViewDetails={handleViewAnimalDetails}
        onViewAIInsights={handleViewAIInsights}
      />
    );
  };

  if (animals.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('ai.healthDashboard')} showBackButton />
        <View style={styles.noAnimalContainer}>
          <Brain size={48} color={colors.textLight} />
          <Text style={[styles.noAnimalText, { color: colors.text }]}>
            {t('noAnimalsFound')}
          </Text>
          <Text style={[styles.noAnimalSubtext, { color: colors.textLight }]}>
            {t('addAnimalToStart')}
          </Text>
        </View>
      </View>
    );
  }
  
  const stableHealth = getOverallStableHealth();
  const totalAlerts = smartAlerts.filter(alert => !alert.is_resolved).length;
  const criticalAlerts = smartAlerts.filter(alert => !alert.is_resolved && alert.priority_level === 'critical').length;

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={colors.statusBarStyle} backgroundColor={colors.background} />
      <Header title={t('ai.healthDashboard')} showBackButton />
      
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Overall Health Summary */}
        <OverallHealthSummary
          stableHealth={stableHealth}
          totalAlerts={totalAlerts}
          criticalAlerts={criticalAlerts}
          totalAnimals={animals.length}
          isLoading={isLoadingHealthScores || isLoadingAlerts}
        />

        {/* Animals Overview */}
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('yourAnimals')}
          </Text>
          <TouchableOpacity onPress={() => navigation.navigate('Animals')}>
            <Text style={[styles.viewAllLink, { color: colors.primary }]}>
              {t('viewAll')}
            </Text>
          </TouchableOpacity>
        </View>
        
        <FlatList
          data={animals}
          renderItem={renderAnimalHealthCard}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.animalsList}
          ItemSeparatorComponent={() => <View style={{ width: 12 }} />}
        />

        {/* Priority Alerts Section */}
        <PriorityAlertsCard
          criticalAlertsCount={criticalAlerts}
          onReviewAlerts={() => navigation.navigate('AIHealthDashboard')}
        />

        {/* Recent Activity & Trends */}
        <RecentActivityCard
          healthScoresCount={healthScores.length}
          assessmentsCount={diseaseRiskAssessments.length}
          trendsCount={healthTrends.length}
          isLoading={isLoadingTrends}
        />

        {/* Health Insights */}
        <HealthInsightsCard
          averageHealthScore={
            healthScores.length > 0 
              ? Math.round(healthScores.reduce((sum, score) => sum + score.overall_score, 0) / healthScores.length)
              : 0
          }
          highRiskFactorsCount={
            diseaseRiskAssessments.filter(r => r.risk_level === 'high' || r.risk_level === 'critical').length
          }
          onViewDetails={() => navigation.navigate('AIHealthDashboard')}
        />

        {/* Quick Actions */}
        <QuickActionsGrid
          onRefreshData={handleRefresh}
          isRefreshing={refreshing}
        />


      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16
  },
  noAnimalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32
  },
  noAnimalText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8
  },
  noAnimalSubtext: {
    fontSize: 14,
    textAlign: 'center'
  },

  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  viewAllLink: {
    fontSize: 14,
    fontWeight: '500'
  },

  
  // New styles for multi-animal dashboard
  summaryCard: {
    marginBottom: 20,
    padding: 20,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 8,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryStatItem: {
    alignItems: 'center',
    flex: 1,
  },
  summaryStatCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryStatNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  summaryStatLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  animalsList: {
    paddingHorizontal: 4,
    marginBottom: 20,
  },
  animalCard: {
    padding: 16,
    marginBottom: 8,
  },
  animalCardContent: {
    flex: 1,
  },
  animalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  animalInfo: {
    flex: 1,
  },
  animalName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  animalBreed: {
    fontSize: 12,
  },
  gaugeContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  gaugeBackground: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  gaugeScore: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 4,
  },
  statLabel: {
    fontSize: 10,
    marginTop: 2,
  },
  aiInsightsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#F0F9FF',
  },
  aiInsightsText: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  statusIndicators: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginTop: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  priorityAlertsCard: {
    marginBottom: 16,
    padding: 16,
    borderLeftWidth: 4,
  },
  priorityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  priorityTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  priorityDescription: {
    fontSize: 12,
    marginBottom: 12,
  },
  priorityAction: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  priorityActionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  activitySummary: {
    gap: 12,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  activitySubtitle: {
    fontSize: 12,
  },
  insightsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  insightCard: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  insightTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 6,
  },
  insightValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  insightSubtitle: {
    fontSize: 10,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickActionButton: {
    flex: 1,
    minWidth: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 8,
    gap: 6,
  },
  quickActionText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default AIHealthDashboardScreen;