import {
  View,
  StyleSheet,
  ScrollView,
  Platform,
  KeyboardAvoidingView,
  Alert
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../contexts/ThemeContext';
import { useAnimalStore } from '../store/animalStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import { toast } from 'sonner-native';
import { supabase } from '../supabase/client';
import TrainingSessionForm from '../components/training/TrainingSessionForm';
import IntensitySelector from '../components/training/IntensitySelector';
import TrainingSessionHeader from '../components/training/TrainingSessionHeader';
import TrainingNotesSection from '../components/training/TrainingNotesSection';
import AutomatedTrainingLogger from '../components/training/AutomatedTrainingLogger';
import { useAutomatedTrainingLogger } from '../hooks/useAutomatedTrainingLogger';

type LogTrainingSessionScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'LogTrainingSession'>;
type LogTrainingSessionScreenRouteProp = RouteProp<HomeStackParamList, 'LogTrainingSession'>;

const LogTrainingSessionScreenRefactored = () => {
  const navigation = useNavigation<LogTrainingSessionScreenNavigationProp>();
  const route = useRoute<LogTrainingSessionScreenRouteProp>();
  const { colors } = useTheme();
  
  // Safe parameter extraction with fallback
  const animalId = route.params?.animalId;
  
  // Early return if no animalId provided
  if (!animalId) {
    Alert.alert('Error', 'Please select an animal first', [
      { text: 'OK', onPress: () => navigation.goBack() }
    ]);
    return null;
  }
  
  const animals = useAnimalStore(state => state.animals);
  const animal = animals.find(a => a.id === animalId);
  
  if (!animal) {
    Alert.alert('Error', 'Animal not found', [
      { text: 'OK', onPress: () => navigation.goBack() }
    ]);
    return null;
  }
  
  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Header title="Log Training Session" />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <TrainingSessionHeader animal={animal} />
        
        {/* This would contain the actual form implementation */}
        {/* For now, showing a placeholder */}
        <View style={[styles.placeholder, { backgroundColor: colors.card }]}>
          {/* Training form components would go here */}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  placeholder: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
});

export default LogTrainingSessionScreenRefactored;