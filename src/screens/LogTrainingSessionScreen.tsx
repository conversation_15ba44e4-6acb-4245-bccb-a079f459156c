import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Platform,
  KeyboardAvoidingView
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Clock, MapPin, Zap, Save, Play, Square, Pause, Activity, RotateCcw } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../contexts/ThemeContext';
import { useAnimalStore } from '../store/animalStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import { toast } from 'sonner-native';
import { supabase } from '../supabase/client';
import { useAutomatedTrainingLogger } from '../hooks/useAutomatedTrainingLogger';

type LogTrainingSessionScreenRouteProp = RouteProp<HomeStackParamList, 'LogTrainingSession'>;
type LogTrainingSessionScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'LogTrainingSession'>;

const LogTrainingSessionScreen = () => {
  const navigation = useNavigation<LogTrainingSessionScreenNavigationProp>();
  const route = useRoute<LogTrainingSessionScreenRouteProp>();
  const { colors } = useTheme();
  
  // Safe parameter extraction with fallback
  const animalId = route.params?.animalId;
  
  // Early return if no animalId provided
  if (!animalId) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title="Log Training Session" />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            Please select an animal first
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: colors.primary }]}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  
  const animals = useAnimalStore(state => state.animals);
  const animal = animals.find(a => a.id === animalId);
  
  const [isLoading, setIsLoading] = useState(false);
  const [sessionDate, setSessionDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [distance, setDistance] = useState('');
  const [speed, setSpeed] = useState('');
  const [duration, setDuration] = useState('');
  const [restTime, setRestTime] = useState('');
  const [intensityLabel, setIntensityLabel] = useState('Moderate');
  const [notes, setNotes] = useState('');
  
  // Automated training mode
  const [isAutomatedMode, setIsAutomatedMode] = useState(false);
  const [currentDuration, setCurrentDuration] = useState(0);
  
  // Automated training logger
  const {
    isRecording,
    isPaused,
    startTime,
    currentData,
    error: automatedError,
    startRecording,
    pauseRecording,
    resumeRecording,
    stopRecording,
    getCurrentDuration,
    canStart,
    canPause,
    canResume,
    canStop
  } = useAutomatedTrainingLogger(animalId);
  
  const intensityOptions = ['Light', 'Moderate', 'Vigorous', 'High Intensity', 'Endurance'];
  
  // Update duration display for automated mode
  React.useEffect(() => {
    if (isRecording && !isPaused) {
      const interval = setInterval(() => {
        setCurrentDuration(getCurrentDuration());
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isRecording, isPaused, getCurrentDuration]);
  
  // Auto-populate fields when automated session completes
  React.useEffect(() => {
    if (isAutomatedMode && currentData && Object.keys(currentData).length > 0) {
      if (currentData.distance) setDistance(currentData.distance.toFixed(2));
      if (currentData.avg_speed) setSpeed(currentData.avg_speed.toFixed(1));
      if (currentData.avg_intensity) {
        const intensity = currentData.avg_intensity;
        if (intensity >= 80) setIntensityLabel('High Intensity');
        else if (intensity >= 60) setIntensityLabel('Vigorous');
        else if (intensity >= 40) setIntensityLabel('Moderate');
        else setIntensityLabel('Light');
      }
    }
  }, [isAutomatedMode, currentData]);
  
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };
  
  const handleStopAutomatedSession = async () => {
    const session = await stopRecording();
    if (session) {
      // Auto-populate duration from the completed session
      setDuration(formatDuration(session.duration_seconds || 0));
      setSessionDate(new Date(session.session_timestamp));
      if (session.notes) setNotes(session.notes);
      
      toast.success('Automated session completed! You can review and edit the data before saving.');
    }
  };
  
  const handleSaveSession = async () => {
    if (!distance || !speed || !duration) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    setIsLoading(true);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No active session');
      }
      
      // Convert duration and rest time to PostgreSQL interval format
      const formatInterval = (timeStr: string) => {
        // Assume format is "HH:MM" or "HH:MM:SS"
        if (timeStr.includes(':')) {
          const parts = timeStr.split(':');
          if (parts.length === 2) {
            return `${parts[0]}:${parts[1]}:00`;
          }
          return timeStr;
        }
        // If just minutes, convert to HH:MM:SS
        const minutes = parseInt(timeStr);
        if (!isNaN(minutes)) {
          const hours = Math.floor(minutes / 60);
          const mins = minutes % 60;
          return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:00`;
        }
        return '00:00:00';
      };
      
      const sessionData = {
        animal_id: animalId,
        user_id: session.user.id,
        distance: parseFloat(distance),
        speed: parseFloat(speed),
        duration: formatInterval(duration),
        rest_time: restTime ? formatInterval(restTime) : null,
        intensity_label: intensityLabel,
        session_timestamp: sessionDate.toISOString(),
        notes: notes || null
      };
      
      const { data, error } = await supabase
        .from('training_sessions')
        .insert(sessionData)
        .select()
        .single();
      
      if (error) throw error;
      
      console.log('Training session saved:', data);
      toast.success('Training session logged successfully!');
      
      navigation.goBack();
      
    } catch (error) {
      console.error('Error saving training session:', error);
      toast.error(`Failed to save training session: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };
  
  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setSessionDate(selectedDate);
    }
  };
  
  if (!animal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title="Log Training Session" />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>Animal not found</Text>
        </View>
      </View>
    );
  }
  
  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Header title="Log Training Session" />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={[styles.animalInfo, { backgroundColor: colors.card }]}>
          <Text style={[styles.animalName, { color: colors.text }]}>{animal.name}</Text>
          <Text style={[styles.animalDetails, { color: colors.textLight }]}>
            {animal.breed} • {animal.age} years old
          </Text>
        </View>
        
        {/* Mode Selector */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Recording Mode</Text>
          <View style={styles.modeSelector}>
            <TouchableOpacity
              style={[
                styles.modeButton,
                {
                  backgroundColor: !isAutomatedMode ? colors.primary : colors.card,
                  borderColor: colors.border
                }
              ]}
              onPress={() => setIsAutomatedMode(false)}
              disabled={isRecording}
            >
              <Text style={[
                styles.modeButtonText,
                {
                  color: !isAutomatedMode ? '#FFFFFF' : colors.text
                }
              ]}>
                Manual Entry
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.modeButton,
                {
                  backgroundColor: isAutomatedMode ? colors.primary : colors.card,
                  borderColor: colors.border
                }
              ]}
              onPress={() => setIsAutomatedMode(true)}
              disabled={isRecording}
            >
              <Activity size={16} color={isAutomatedMode ? '#FFFFFF' : colors.text} />
              <Text style={[
                styles.modeButtonText,
                {
                  color: isAutomatedMode ? '#FFFFFF' : colors.text
                }
              ]}>
                Automated
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Automated Recording Controls */}
        {isAutomatedMode && (
          <View style={[styles.automatedSection, { backgroundColor: colors.card }]}>
            <View style={styles.automatedHeader}>
              <Activity size={20} color={colors.primary} />
              <Text style={[styles.automatedTitle, { color: colors.text }]}>Automated Recording</Text>
              {isRecording && (
                <View style={[styles.recordingIndicator, { backgroundColor: colors.error }]}>
                  <View style={styles.recordingDot} />
                </View>
              )}
            </View>
            
            {/* Live Metrics Display */}
            {isRecording && currentData && (
              <View style={styles.liveMetrics}>
                <Text style={[styles.liveMetricsTitle, { color: colors.text }]}>Live Metrics</Text>
                <View style={styles.metricsGrid}>
                  <View style={styles.metricItem}>
                    <Text style={[styles.metricLabel, { color: colors.textLight }]}>Duration</Text>
                    <Text style={[styles.metricValue, { color: colors.text }]}>
                      {formatDuration(currentDuration)}
                    </Text>
                  </View>
                  <View style={styles.metricItem}>
                    <Text style={[styles.metricLabel, { color: colors.textLight }]}>Distance</Text>
                    <Text style={[styles.metricValue, { color: colors.text }]}>
                      {currentData.distance?.toFixed(2) || '0.00'} km
                    </Text>
                  </View>
                  <View style={styles.metricItem}>
                    <Text style={[styles.metricLabel, { color: colors.textLight }]}>Avg Speed</Text>
                    <Text style={[styles.metricValue, { color: colors.text }]}>
                      {currentData.avg_speed?.toFixed(1) || '0.0'} km/h
                    </Text>
                  </View>
                  <View style={styles.metricItem}>
                    <Text style={[styles.metricLabel, { color: colors.textLight }]}>Intensity</Text>
                    <Text style={[styles.metricValue, { color: colors.text }]}>
                      {currentData.avg_intensity?.toFixed(0) || '0'}%
                    </Text>
                  </View>
                </View>
              </View>
            )}
            
            {/* Recording Controls */}
            <View style={styles.recordingControls}>
              {!isRecording ? (
                <TouchableOpacity
                  style={[styles.recordingButton, styles.startButton, { backgroundColor: colors.success }]}
                  onPress={startRecording}
                  disabled={!canStart}
                >
                  <Play size={20} color="#FFFFFF" />
                  <Text style={styles.recordingButtonText}>Start Recording</Text>
                </TouchableOpacity>
              ) : (
                <View style={styles.activeControls}>
                  {!isPaused ? (
                    <TouchableOpacity
                      style={[styles.recordingButton, styles.pauseButton, { backgroundColor: colors.warning }]}
                      onPress={pauseRecording}
                      disabled={!canPause}
                    >
                      <Pause size={20} color="#FFFFFF" />
                      <Text style={styles.recordingButtonText}>Pause</Text>
                    </TouchableOpacity>
                  ) : (
                    <TouchableOpacity
                      style={[styles.recordingButton, styles.resumeButton, { backgroundColor: colors.success }]}
                      onPress={resumeRecording}
                      disabled={!canResume}
                    >
                      <Play size={20} color="#FFFFFF" />
                      <Text style={styles.recordingButtonText}>Resume</Text>
                    </TouchableOpacity>
                  )}
                  
                  <TouchableOpacity
                    style={[styles.recordingButton, styles.stopButton, { backgroundColor: colors.error }]}
                    onPress={handleStopAutomatedSession}
                    disabled={!canStop}
                  >
                    <Square size={20} color="#FFFFFF" />
                    <Text style={styles.recordingButtonText}>Stop & Review</Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
            
            {automatedError && (
              <Text style={[styles.errorText, { color: colors.error }]}>
                {automatedError}
              </Text>
            )}
          </View>
        )}
        
        {/* Session Date */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Session Date & Time</Text>
          <TouchableOpacity
            style={[styles.dateButton, { backgroundColor: colors.card, borderColor: colors.border }]}
            onPress={() => setShowDatePicker(true)}
          >
            <Clock size={20} color={colors.primary} />
            <Text style={[styles.dateText, { color: colors.text }]}>
              {sessionDate.toLocaleDateString()} {sessionDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Text>
          </TouchableOpacity>
        </View>
        
        {showDatePicker && (
          <DateTimePicker
            value={sessionDate}
            mode="datetime"
            display="default"
            onChange={onDateChange}
          />
        )}
        
        {/* Distance */}
        <View style={styles.section}>
          <View style={styles.fieldHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Distance (km) *</Text>
            {isAutomatedMode && currentData.distance && (
              <Text style={[styles.autoFilledLabel, { color: colors.success }]}>Auto-filled</Text>
            )}
          </View>
          <TextInput
            style={[
              styles.input, 
              { 
                backgroundColor: colors.card, 
                borderColor: colors.border, 
                color: colors.text,
                opacity: isRecording ? 0.6 : 1
              }
            ]}
            value={distance}
            onChangeText={setDistance}
            placeholder="e.g., 5.2"
            placeholderTextColor={colors.textLight}
            keyboardType="decimal-pad"
            editable={!isRecording}
          />
        </View>
        
        {/* Speed */}
        <View style={styles.section}>
          <View style={styles.fieldHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Average Speed (km/h) *</Text>
            {isAutomatedMode && currentData.avg_speed && (
              <Text style={[styles.autoFilledLabel, { color: colors.success }]}>Auto-filled</Text>
            )}
          </View>
          <TextInput
            style={[
              styles.input, 
              { 
                backgroundColor: colors.card, 
                borderColor: colors.border, 
                color: colors.text,
                opacity: isRecording ? 0.6 : 1
              }
            ]}
            value={speed}
            onChangeText={setSpeed}
            placeholder="e.g., 15.5"
            placeholderTextColor={colors.textLight}
            keyboardType="decimal-pad"
            editable={!isRecording}
          />
          {isAutomatedMode && currentData.max_speed && (
            <Text style={[styles.additionalInfo, { color: colors.textLight }]}>
              Max Speed: {currentData.max_speed.toFixed(1)} km/h
            </Text>
          )}
        </View>
        
        {/* Duration */}
        <View style={styles.section}>
          <View style={styles.fieldHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Duration *</Text>
            {isAutomatedMode && isRecording && (
              <Text style={[styles.liveLabel, { color: colors.primary }]}>Live: {formatDuration(currentDuration)}</Text>
            )}
          </View>
          <TextInput
            style={[
              styles.input, 
              { 
                backgroundColor: colors.card, 
                borderColor: colors.border, 
                color: colors.text,
                opacity: isRecording ? 0.6 : 1
              }
            ]}
            value={duration}
            onChangeText={setDuration}
            placeholder="e.g., 25:30 or 25 (minutes)"
            placeholderTextColor={colors.textLight}
            editable={!isRecording}
          />
          <Text style={[styles.helpText, { color: colors.textLight }]}>Format: MM:SS or just minutes</Text>
        </View>
        
        {/* Rest Time */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Rest Time (optional)</Text>
          <TextInput
            style={[
              styles.input, 
              { 
                backgroundColor: colors.card, 
                borderColor: colors.border, 
                color: colors.text,
                opacity: isRecording ? 0.6 : 1
              }
            ]}
            value={restTime}
            onChangeText={setRestTime}
            placeholder="e.g., 10:00 or 10 (minutes)"
            placeholderTextColor={colors.textLight}
            editable={!isRecording}
          />
        </View>
        
        {/* Intensity */}
        <View style={styles.section}>
          <View style={styles.fieldHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Intensity Level</Text>
            {isAutomatedMode && currentData.avg_intensity && (
              <Text style={[styles.autoFilledLabel, { color: colors.success }]}>Auto-detected</Text>
            )}
          </View>
          <View style={styles.intensityContainer}>
            {intensityOptions.map((intensity) => (
              <TouchableOpacity
                key={intensity}
                style={[
                  styles.intensityButton,
                  {
                    backgroundColor: intensityLabel === intensity ? colors.primary : colors.card,
                    borderColor: colors.border,
                    opacity: isRecording ? 0.6 : 1
                  }
                ]}
                onPress={() => setIntensityLabel(intensity)}
                disabled={isRecording}
              >
                <Text style={[
                  styles.intensityText,
                  {
                    color: intensityLabel === intensity ? '#FFFFFF' : colors.text
                  }
                ]}>
                  {intensity}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          {isAutomatedMode && currentData.avg_intensity && (
            <Text style={[styles.additionalInfo, { color: colors.textLight }]}>
              Current: {currentData.avg_intensity.toFixed(0)}% • Max: {currentData.max_intensity?.toFixed(0) || 0}%
            </Text>
          )}
        </View>
        
        {/* Enhanced Automated Data Display */}
        {isAutomatedMode && currentData && Object.keys(currentData).length > 0 && (
          <View style={[styles.automatedDataSection, { backgroundColor: colors.card }]}>
            <Text style={[styles.automatedDataTitle, { color: colors.text }]}>Additional Automated Data</Text>
            <View style={styles.automatedDataGrid}>
              {currentData.heart_rate_avg && (
                <View style={styles.automatedDataItem}>
                  <Text style={[styles.automatedDataLabel, { color: colors.textLight }]}>Heart Rate</Text>
                  <Text style={[styles.automatedDataValue, { color: colors.text }]}>
                    Avg: {currentData.heart_rate_avg} BPM
                  </Text>
                  {currentData.heart_rate_max && (
                    <Text style={[styles.automatedDataSubvalue, { color: colors.textLight }]}>
                      Max: {currentData.heart_rate_max} BPM
                    </Text>
                  )}
                </View>
              )}
              
              {currentData.elevation_gain && (
                <View style={styles.automatedDataItem}>
                  <Text style={[styles.automatedDataLabel, { color: colors.textLight }]}>Elevation</Text>
                  <Text style={[styles.automatedDataValue, { color: colors.text }]}>
                    +{currentData.elevation_gain.toFixed(0)}m
                  </Text>
                </View>
              )}
              
              {currentData.session_quality && (
                <View style={styles.automatedDataItem}>
                  <Text style={[styles.automatedDataLabel, { color: colors.textLight }]}>Session Quality</Text>
                  <Text style={[
                    styles.automatedDataValue, 
                    { 
                      color: currentData.session_quality === 'excellent' ? colors.success :
                             currentData.session_quality === 'good' ? colors.primary :
                             currentData.session_quality === 'fair' ? colors.warning : colors.error
                    }
                  ]}>
                    {currentData.session_quality.charAt(0).toUpperCase() + currentData.session_quality.slice(1)}
                  </Text>
                </View>
              )}
              
              {currentData.gps_track && currentData.gps_track.length > 0 && (
                <View style={styles.automatedDataItem}>
                  <Text style={[styles.automatedDataLabel, { color: colors.textLight }]}>GPS Points</Text>
                  <Text style={[styles.automatedDataValue, { color: colors.text }]}>
                    {currentData.gps_track.length} recorded
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}
        
        {/* Notes */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Notes (optional)</Text>
          <TextInput
            style={[
              styles.textArea, 
              { 
                backgroundColor: colors.card, 
                borderColor: colors.border, 
                color: colors.text,
                opacity: isRecording ? 0.6 : 1
              }
            ]}
            value={notes}
            onChangeText={setNotes}
            placeholder={isAutomatedMode ? "Add notes about the automated session..." : "Add any additional notes about the training session..."}
            placeholderTextColor={colors.textLight}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
            editable={!isRecording}
          />
        </View>
        
        {/* Save Button */}
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: colors.primary },
            isLoading && styles.saveButtonDisabled
          ]}
          onPress={handleSaveSession}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <>
              <Save size={20} color="#FFFFFF" />
              <Text style={styles.saveButtonText}>Save Training Session</Text>
            </>
          )}
        </TouchableOpacity>
        
        <View style={styles.bottomPadding} />
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 20,
    textAlign: 'center',
  },
  backButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  animalInfo: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  animalName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  animalDetails: {
    fontSize: 14,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
  },
  helpText: {
    fontSize: 12,
    marginTop: 4,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
  },
  dateText: {
    fontSize: 16,
    marginLeft: 8,
  },
  intensityContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  intensityButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  intensityText: {
    fontSize: 14,
    fontWeight: '500',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginTop: 20,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  bottomPadding: {
    height: 20,
  },
  
  // Mode Selector Styles
  modeSelector: {
    flexDirection: 'row',
    gap: 8,
  },
  modeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    gap: 6,
  },
  modeButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  
  // Field Header Styles
  fieldHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  autoFilledLabel: {
    fontSize: 12,
    fontWeight: '500',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
  },
  liveLabel: {
    fontSize: 12,
    fontWeight: '600',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  additionalInfo: {
    fontSize: 12,
    marginTop: 4,
  },
  
  // Automated Section Styles
  automatedSection: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.2)',
  },
  automatedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  automatedTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  recordingIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  
  // Live Metrics Styles
  liveMetrics: {
    marginBottom: 16,
  },
  liveMetricsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  metricItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
  },
  metricLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  
  // Recording Controls Styles
  recordingControls: {
    gap: 8,
  },
  activeControls: {
    flexDirection: 'row',
    gap: 8,
  },
  recordingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 6,
  },
  startButton: {
    flex: 1,
  },
  pauseButton: {
    flex: 1,
  },
  resumeButton: {
    flex: 1,
  },
  stopButton: {
    flex: 1,
  },
  recordingButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  
  // Automated Data Section Styles
  automatedDataSection: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(34, 197, 94, 0.2)',
  },
  automatedDataTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  automatedDataGrid: {
    gap: 12,
  },
  automatedDataItem: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  automatedDataLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  automatedDataValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  automatedDataSubvalue: {
    fontSize: 12,
    marginTop: 2,
  },
});

export default LogTrainingSessionScreen;