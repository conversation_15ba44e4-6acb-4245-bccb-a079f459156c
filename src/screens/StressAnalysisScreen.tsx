import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  Brain,
  Heart,
  Activity,
  AlertTriangle,
  TrendingUp,
  RefreshCw,
  Clock,
  Thermometer
} from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAnimalStore } from '../store/animalStore';
import { useBehavioralStore } from '../store/behavioralStore';
import { HomeStackParamList } from '../navigation';
import Header from '../components/Header';
import Card from '../components/ui/Card';
import { toast } from 'sonner-native';
import { format } from 'date-fns';
import { useEffect, useState } from 'react';
import StressAnalysisHeader from '../components/behavioral/StressAnalysisHeader';
import StressLevelChart from '../components/charts/StressLevelChart';

type StressAnalysisScreenRouteProp = RouteProp<HomeStackParamList, 'StressAnalysis'>;
type StressAnalysisScreenNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'StressAnalysis'>;

const StressAnalysisScreen = () => {
  const navigation = useNavigation<StressAnalysisScreenNavigationProp>();
  const route = useRoute<StressAnalysisScreenRouteProp>();
  const { colors } = useTheme();
  const { t } = useLanguage();
  // Safe destructuring with fallback for missing params
  const animalId = route.params?.animalId;
  
  // Early return if no animalId provided
  if (!animalId) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('stressAnalysis')} showBackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {t('missingAnimalId')}
          </Text>
        </View>
      </View>
    );
  }
  
  const animals = useAnimalStore(state => state.animals);
  const animal = animals.find(a => a.id === animalId);
  
  const {
    stressAnalyses,
    stressEvents,
    isLoadingStressAnalysis,
    isLoadingStressEvents,
    stressAnalysisError,
    fetchStressAnalyses,
    fetchStressEvents,
    requestStressAnalysis,
    resolveStressEvent,
    getLatestStressAnalysis,
    getActiveStressEvents
  } = useBehavioralStore();
  
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [analysisHours, setAnalysisHours] = useState(24);
  
  const latestStressAnalysis = getLatestStressAnalysis(animalId);
  const activeStressEvents = getActiveStressEvents(animalId);
  
  useEffect(() => {
    loadStressData();
  }, [animalId]);
  
  const loadStressData = async () => {
    try {
      await Promise.all([
        fetchStressAnalyses(animalId),
        fetchStressEvents(animalId)
      ]);
    } catch (error) {
      console.error('Error loading stress data:', error);
    }
  };
  
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadStressData();
    setIsRefreshing(false);
  };
  
  const handleRequestAnalysis = async () => {
    try {
      await requestStressAnalysis(animalId, analysisHours);
    } catch (error) {
      // Error is already handled in the store
    }
  };
  
  const getStressLevelColor = (level: string) => {
    switch (level) {
      case 'very_high':
        return '#DC2626';
      case 'high':
        return '#EF4444';
      case 'moderate':
        return '#F59E0B';
      case 'low':
        return '#10B981';
      case 'very_low':
        return '#059669';
      default:
        return colors.textLight;
    }
  };
  
  const getStressLevelText = (level: string) => {
    switch (level) {
      case 'very_high':
        return t('veryHigh');
      case 'high':
        return t('high');
      case 'moderate':
        return t('moderate');
      case 'low':
        return t('low');
      case 'very_low':
        return t('veryLow');
      default:
        return level;
    }
  };
  
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return '#DC2626';
      case 'severe':
        return '#EF4444';
      case 'moderate':
        return '#F59E0B';
      case 'mild':
        return '#10B981';
      default:
        return colors.textLight;
    }
  };
  
  if (!animal) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Header title={t('stressAnalysis')} showBackButton />
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>{t('animalNotFound')}</Text>
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background} />
      <Header title={t('stressAnalysis')} showBackButton />
      
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Animal Info */}
        <StressAnalysisHeader
          animal={animal}
          isLoading={isLoadingStressAnalysis}
          onAnalyze={handleRequestAnalysis}
        />
        
        {/* Latest Stress Analysis */}
        {latestStressAnalysis && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <Brain size={24} color={getStressLevelColor(latestStressAnalysis.stress_level)} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('currentStressLevel')}</Text>
              <View style={[
                styles.stressLevelBadge,
                { backgroundColor: getStressLevelColor(latestStressAnalysis.stress_level) }
              ]}>
                <Text style={styles.stressLevelText}>
                  {getStressLevelText(latestStressAnalysis.stress_level)}
                </Text>
              </View>
            </View>
            
            <View style={styles.stressScoreContainer}>
              <View style={styles.scoreCircle}>
                <Text style={[styles.scoreValue, { color: getStressLevelColor(latestStressAnalysis.stress_level) }]}>
                  {latestStressAnalysis.stress_score}
                </Text>
                <Text style={[styles.scoreLabel, { color: colors.textLight }]}>/100</Text>
              </View>
              
              <View style={styles.scoreDetails}>
                <Text style={[styles.confidenceText, { color: colors.textLight }]}>
                  {t('confidence')}: {Math.round(latestStressAnalysis.confidence_level * 100)}%
                </Text>
                <Text style={[styles.analysisDate, { color: colors.textLight }]}>
                  {format(new Date(latestStressAnalysis.analysis_timestamp), 'MMM d, yyyy • h:mm a')}
                </Text>
              </View>
            </View>
            
            {/* HRV Metrics */}
            <View style={styles.metricsSection}>
              <Text style={[styles.metricsTitle, { color: colors.text }]}>{t('heartRateVariability')}</Text>
              <View style={styles.metricsGrid}>
                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('avgHeartRate')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {latestStressAnalysis.hrv_metrics.averageHeartRate.toFixed(0)} BPM
                  </Text>
                </View>
                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('hrvScore')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {latestStressAnalysis.hrv_metrics.heartRateVariability.toFixed(1)}
                  </Text>
                </View>
                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('stressIndicator')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {latestStressAnalysis.hrv_metrics.stressIndicator.toFixed(0)}%
                  </Text>
                </View>
              </View>
            </View>
            
            {/* Activity Indicators */}
            <View style={styles.metricsSection}>
              <Text style={[styles.metricsTitle, { color: colors.text }]}>{t('activityIndicators')}</Text>
              <View style={styles.metricsGrid}>
                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('restlessness')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {latestStressAnalysis.activity_indicators.restlessness}%
                  </Text>
                </View>
                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('activityLevel')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {latestStressAnalysis.activity_indicators.activityLevel}%
                  </Text>
                </View>
                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('movementPattern')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {latestStressAnalysis.activity_indicators.movementPatterns}
                  </Text>
                </View>
              </View>
            </View>
            
            {/* Environmental Factors */}
            <View style={styles.metricsSection}>
              <Text style={[styles.metricsTitle, { color: colors.text }]}>{t('environmentalFactors')}</Text>
              <View style={styles.metricsGrid}>
                <View style={styles.metricItem}>
                  <Thermometer size={16} color={colors.textLight} />
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('temperature')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {latestStressAnalysis.environmental_factors.temperature}°C
                  </Text>
                </View>
                <View style={styles.metricItem}>
                  <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('timeOfDay')}</Text>
                  <Text style={[styles.metricValue, { color: colors.text }]}>
                    {latestStressAnalysis.environmental_factors.timeOfDay}
                  </Text>
                </View>
              </View>
            </View>
            
            {/* Stress Triggers */}
            {latestStressAnalysis.stress_triggers.length > 0 && (
              <View style={styles.triggersSection}>
                <Text style={[styles.triggersTitle, { color: colors.text }]}>{t('identifiedTriggers')}</Text>
                {latestStressAnalysis.stress_triggers.map((trigger, index) => (
                  <View key={index} style={[
                    styles.triggerItem,
                    { backgroundColor: colors.card, borderColor: colors.border }
                  ]}>
                    <View style={styles.triggerHeader}>
                      <Text style={[styles.triggerType, { color: colors.text }]}>{trigger.type}</Text>
                      <Text style={[styles.triggerConfidence, { color: colors.textLight }]}>
                        {Math.round(trigger.confidence * 100)}% {t('confidence')}
                      </Text>
                    </View>
                    <Text style={[styles.triggerDescription, { color: colors.textLight }]}>
                      {trigger.description}
                    </Text>
                  </View>
                ))}
              </View>
            )}
            
            {/* Recommendations */}
            {latestStressAnalysis.recommendations && (
              <View style={styles.recommendationsSection}>
                <Text style={[styles.recommendationsTitle, { color: colors.text }]}>{t('recommendations')}</Text>
                <Text style={[styles.recommendationsText, { color: colors.textLight }]}>
                  {latestStressAnalysis.recommendations}
                </Text>
              </View>
            )}
          </Card>
        )}
        
        {/* Active Stress Events */}
        {activeStressEvents.length > 0 && (
          <Card style={styles.section}>
            <View style={styles.sectionHeader}>
              <AlertTriangle size={24} color={colors.warning} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('activeStressEvents')}</Text>
              <View style={[styles.countBadge, { backgroundColor: colors.warning }]}>
                <Text style={styles.countText}>{activeStressEvents.length}</Text>
              </View>
            </View>
            
            {activeStressEvents.slice(0, 5).map((event) => (
              <View key={event.id} style={[
                styles.eventItem,
                { backgroundColor: colors.card, borderColor: colors.border }
              ]}>
                <View style={styles.eventHeader}>
                  <View style={[
                    styles.severityDot,
                    { backgroundColor: getSeverityColor(event.severity_level) }
                  ]} />
                  <Text style={[styles.eventType, { color: colors.text }]}>{event.event_type}</Text>
                  <Text style={[styles.eventSeverity, { color: getSeverityColor(event.severity_level) }]}>
                    {event.severity_level}
                  </Text>
                </View>
                
                {event.trigger_description && (
                  <Text style={[styles.eventDescription, { color: colors.textLight }]}>
                    {event.trigger_description}
                  </Text>
                )}
                
                <View style={styles.eventFooter}>
                  <Text style={[styles.eventTime, { color: colors.textLight }]}>
                    {format(new Date(event.event_timestamp), 'MMM d • h:mm a')}
                  </Text>
                  
                  <TouchableOpacity
                    style={[styles.resolveButton, { backgroundColor: colors.primary }]}
                    onPress={() => resolveStressEvent(event.id, 'Manual intervention')}
                  >
                    <Text style={styles.resolveButtonText}>{t('resolve')}</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </Card>
        )}
        
        {/* Stress History Chart */}
        {stressAnalyses.length > 0 && (
          <StressLevelChart
            data={stressAnalyses}
            onDataPointPress={(analysis) => {
              toast.success(`Stress Level: ${getStressLevelText(analysis.stress_level)} (${analysis.stress_score}/100)`);
            }}
          />
        )}
        
        {/* Empty State */}
        {!latestStressAnalysis && !isLoadingStressAnalysis && (
          <Card style={styles.emptyState}>
            <Brain size={48} color={colors.textLight} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>{t('noStressAnalysis')}</Text>
            <Text style={[styles.emptyDescription, { color: colors.textLight }]}>
              {t('runFirstStressAnalysis')}
            </Text>
            
            <TouchableOpacity
              style={[styles.emptyButton, { backgroundColor: colors.primary }]}
              onPress={handleRequestAnalysis}
            >
              <Brain size={20} color="#FFFFFF" />
              <Text style={styles.emptyButtonText}>{t('analyzeStress')}</Text>
            </TouchableOpacity>
          </Card>
        )}
        
        <View style={styles.bottomPadding} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  animalCard: {
    marginBottom: 16,
  },
  animalInfo: {
    marginBottom: 16,
  },
  animalName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  animalDetails: {
    fontSize: 16,
  },
  analysisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  analysisButtonDisabled: {
    opacity: 0.6,
  },
  analysisButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  stressLevelBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  stressLevelText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  stressScoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    gap: 16,
  },
  scoreCircle: {
    alignItems: 'center',
  },
  scoreValue: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 14,
  },
  scoreDetails: {
    flex: 1,
  },
  confidenceText: {
    fontSize: 14,
    marginBottom: 4,
  },
  analysisDate: {
    fontSize: 12,
  },
  metricsSection: {
    marginBottom: 20,
  },
  metricsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  metricItem: {
    flex: 1,
    minWidth: '30%',
    alignItems: 'center',
    gap: 4,
  },
  metricLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  triggersSection: {
    marginBottom: 20,
  },
  triggersTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  triggerItem: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  triggerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  triggerType: {
    fontSize: 14,
    fontWeight: '600',
  },
  triggerConfidence: {
    fontSize: 12,
  },
  triggerDescription: {
    fontSize: 14,
  },
  recommendationsSection: {
    marginTop: 16,
  },
  recommendationsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  recommendationsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  countBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  countText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  eventItem: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  eventHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  severityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  eventType: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  eventSeverity: {
    fontSize: 12,
    fontWeight: '600',
  },
  eventDescription: {
    fontSize: 14,
    marginBottom: 8,
  },
  eventFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventTime: {
    fontSize: 12,
  },
  resolveButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  resolveButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  historyItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  historyStressLevel: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  historyStressText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  historyScore: {
    fontSize: 16,
    fontWeight: '600',
  },
  historyDate: {
    fontSize: 12,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  emptyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  bottomPadding: {
    height: 20,
  },
});

export default StressAnalysisScreen;