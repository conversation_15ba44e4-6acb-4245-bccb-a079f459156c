-- Purpose: Implement comprehensive Row Level Security (RLS) for devices table
-- This ensures users can only access their own device records and provides admin oversight

-- Add DEFAULT auth.uid() to user_id column for automatic user assignment
ALTER TABLE public.devices ALTER COLUMN user_id SET DEFAULT auth.uid();

-- Enable Row Level Security on the devices table (if not already enabled)
ALTER TABLE public.devices ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts and ensure clean implementation
DROP POLICY IF EXISTS "Users can view their own devices" ON public.devices;
DROP POLICY IF EXISTS "Users can insert their own devices" ON public.devices;
DROP POLICY IF EXISTS "Users can update their own devices" ON public.devices;
DROP POLICY IF EXISTS "Users can delete their own devices" ON public.devices;
DROP POLICY IF EXISTS "Admins can view all devices" ON public.devices;
DROP POLICY IF EXISTS "Admins can insert all devices" ON public.devices;
DROP POLICY IF EXISTS "Admins can update all devices" ON public.devices;
DROP POLICY IF EXISTS "Admins can delete all devices" ON public.devices;

-- User Policies: Allow users to manage only their own device records

-- Purpose: Allow users to select their own device records
CREATE POLICY "Users can view their own devices"
ON public.devices FOR SELECT
USING (auth.uid() = user_id);

-- Purpose: Allow users to insert their own device records
-- The DEFAULT auth.uid() will automatically set user_id, but WITH CHECK provides additional security
CREATE POLICY "Users can insert their own devices"
ON public.devices FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Purpose: Allow users to update their own device records
-- USING ensures they can only update their own records
-- WITH CHECK ensures they can't change user_id to someone else's
CREATE POLICY "Users can update their own devices"
ON public.devices FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Purpose: Allow users to delete their own device records
CREATE POLICY "Users can delete their own devices"
ON public.devices FOR DELETE
USING (auth.uid() = user_id);

-- Admin Policies: Allow super admins to manage all device records for oversight

-- Purpose: Allow super admins to view all device records
CREATE POLICY "Admins can view all devices"
ON public.devices FOR SELECT
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- Purpose: Allow super admins to insert device records for any user
CREATE POLICY "Admins can insert all devices"
ON public.devices FOR INSERT
WITH CHECK (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- Purpose: Allow super admins to update any device records
CREATE POLICY "Admins can update all devices"
ON public.devices FOR UPDATE
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- Purpose: Allow super admins to delete any device records
CREATE POLICY "Admins can delete all devices"
ON public.devices FOR DELETE
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- Verification queries to confirm RLS implementation
-- These will show the policies that have been created

SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'devices' 
ORDER BY policyname;

-- Verify the user_id column now has DEFAULT auth.uid()
SELECT 
  column_name,
  column_default,
  is_nullable,
  data_type
FROM information_schema.columns 
WHERE table_name = 'devices' 
AND column_name = 'user_id';

-- Verify RLS is enabled on the devices table
SELECT 
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables 
WHERE tablename = 'devices';

-- Test query to verify policies work (should only return current user's devices)
-- SELECT * FROM devices; -- Uncomment to test after migration