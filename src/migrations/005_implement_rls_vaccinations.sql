-- Purpose: Implement comprehensive Row Level Security (RLS) policies for vaccinations table
-- This ensures users can only access their own vaccination data and provides admin oversight capabilities

-- =====================================================
-- PHASE 1: ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on vaccinations table (if not already enabled)
ALTER TABLE public.vaccinations ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- PHASE 2: DROP EXISTING POLICIES (IF ANY)
-- =====================================================

-- Drop existing policies for vaccinations table to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own vaccination records" ON public.vaccinations;
DROP POLICY IF EXISTS "Users can insert their own vaccination records" ON public.vaccinations;
DROP POLICY IF EXISTS "Users can update their own vaccination records" ON public.vaccinations;
DROP POLICY IF EXISTS "Users can delete their own vaccination records" ON public.vaccinations;
DROP POLICY IF EXISTS "Admins can view all vaccination records" ON public.vaccinations;
DROP POLICY IF EXISTS "Admins can update all vaccination records" ON public.vaccinations;
DROP POLICY IF EXISTS "Admins can delete all vaccination records" ON public.vaccinations;

-- =====================================================
-- PHASE 3: VACCINATIONS TABLE RLS POLICIES
-- =====================================================

-- SELECT Policy: Users can view their own vaccination records
CREATE POLICY "Users can view their own vaccination records"
ON public.vaccinations FOR SELECT
USING (auth.uid() = user_id);

-- INSERT Policy: Users can insert their own vaccination records
CREATE POLICY "Users can insert their own vaccination records"
ON public.vaccinations FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- UPDATE Policy: Users can update their own vaccination records
CREATE POLICY "Users can update their own vaccination records"
ON public.vaccinations FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- DELETE Policy: Users can delete their own vaccination records
CREATE POLICY "Users can delete their own vaccination records"
ON public.vaccinations FOR DELETE
USING (auth.uid() = user_id);

-- ADMIN SELECT Policy: Super admins can view all vaccination records
CREATE POLICY "Admins can view all vaccination records"
ON public.vaccinations FOR SELECT
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- ADMIN UPDATE Policy: Super admins can update all vaccination records
CREATE POLICY "Admins can update all vaccination records"
ON public.vaccinations FOR UPDATE
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- ADMIN DELETE Policy: Super admins can delete all vaccination records
CREATE POLICY "Admins can delete all vaccination records"
ON public.vaccinations FOR DELETE
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- =====================================================
-- PHASE 4: DATABASE SCHEMA ENHANCEMENTS
-- =====================================================

-- Set default user_id for vaccinations table (simplifies client-side code)
ALTER TABLE public.vaccinations ALTER COLUMN user_id SET DEFAULT auth.uid();

-- =====================================================
-- PHASE 5: VERIFICATION QUERIES
-- =====================================================

-- Verify RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'vaccinations' 
AND schemaname = 'public';

-- Verify policies are created
SELECT schemaname, tablename, policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'vaccinations' 
AND schemaname = 'public'
ORDER BY policyname;

-- =====================================================
-- SECURITY IMPLEMENTATION COMPLETE
-- =====================================================

-- Summary of Security Enhancements:
-- ✅ RLS enabled on vaccinations table
-- ✅ Users can only SELECT their own records (auth.uid() = user_id)
-- ✅ Users can only INSERT records with their own user_id
-- ✅ Users can only UPDATE their own records
-- ✅ Users can only DELETE their own records
-- ✅ Super admins can view all records for oversight
-- ✅ Super admins can update all records for management
-- ✅ Super admins can delete all records for management
-- ✅ Default user_id set to auth.uid() for simplified client code
-- ✅ Comprehensive policy coverage for all CRUD operations
-- ✅ Data isolation and security at database level