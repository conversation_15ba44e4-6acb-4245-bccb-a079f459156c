-- Purpose: Create device ordering system tables with products, orders, and order_items
-- This migration adds e-commerce functionality for device ordering

-- Create products table for orderable devices
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    price NUMERIC NOT NULL CHECK (price >= 0),
    currency TEXT NOT NULL DEFAULT 'USD',
    image_url TEXT,
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    is_active BOOLEAN DEFAULT true,
    category TEXT DEFAULT 'device',
    specifications JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    total_amount NUMERIC NOT NULL CHECK (total_amount >= 0),
    currency TEXT NOT NULL DEFAULT 'USD',
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
    
    -- Shipping address fields
    shipping_address_line1 TEXT NOT NULL,
    shipping_address_line2 TEXT,
    shipping_city TEXT NOT NULL,
    shipping_state_province TEXT NOT NULL,
    shipping_postal_code TEXT NOT NULL,
    shipping_country TEXT NOT NULL DEFAULT 'US',
    
    -- Payment information
    payment_intent_id TEXT,
    payment_method TEXT,
    payment_status TEXT DEFAULT 'pending',
    
    -- Tracking information
    tracking_number TEXT,
    estimated_delivery_date TIMESTAMP WITH TIME ZONE,
    
    -- Order notes
    notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create order_items table
CREATE TABLE IF NOT EXISTS order_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE RESTRICT,
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    price_at_purchase NUMERIC NOT NULL CHECK (price_at_purchase >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION handle_updated_at();

-- Enable Row Level Security
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- RLS Policies for products table
-- Allow public read access for active products
CREATE POLICY "Public can view active products" ON products
    FOR SELECT
    USING (is_active = true);

-- Only admins can manage products
CREATE POLICY "Admins can manage products" ON products
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_super_admin = true
        )
    );

-- RLS Policies for orders table
-- Users can view their own orders
CREATE POLICY "Users can view their own orders" ON orders
    FOR SELECT
    USING (auth.uid() = user_id);

-- Users can create their own orders
CREATE POLICY "Users can create their own orders" ON orders
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Users can update their own pending orders
CREATE POLICY "Users can update their own pending orders" ON orders
    FOR UPDATE
    USING (auth.uid() = user_id AND status = 'pending')
    WITH CHECK (auth.uid() = user_id);

-- Admins can view and manage all orders
CREATE POLICY "Admins can manage all orders" ON orders
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_super_admin = true
        )
    );

-- RLS Policies for order_items table
-- Users can view items from their own orders
CREATE POLICY "Users can view their own order items" ON order_items
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM orders 
            WHERE orders.id = order_items.order_id 
            AND orders.user_id = auth.uid()
        )
    );

-- Users can create items for their own orders
CREATE POLICY "Users can create items for their own orders" ON order_items
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM orders 
            WHERE orders.id = order_items.order_id 
            AND orders.user_id = auth.uid()
        )
    );

-- Admins can manage all order items
CREATE POLICY "Admins can manage all order items" ON order_items
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_super_admin = true
        )
    );

-- Insert sample products for testing
INSERT INTO products (name, description, price, currency, image_url, stock_quantity, category, specifications) VALUES
(
    'GPS Tracker Pro X1',
    'Advanced GPS tracking device with real-time location monitoring, 30-day battery life, and waterproof design. Perfect for horses and livestock.',
    299.99,
    'USD',
    'https://magically.life/api/media/image?query=GPS%20tracker%20device%20for%20animals%20professional%20black%20sleek%20design',
    25,
    'gps_tracker',
    '{
        "battery_life": "30 days",
        "waterproof": true,
        "weight": "45g",
        "dimensions": "8.5 x 4.2 x 1.8 cm",
        "connectivity": ["4G LTE", "GPS", "Bluetooth"],
        "features": ["Real-time tracking", "Geofencing", "Activity monitoring", "SOS alerts"]
    }'
),
(
    'Health Monitor Sensor',
    'Continuous health monitoring sensor that tracks heart rate, temperature, and activity levels. Wireless data transmission to your mobile app.',
    199.99,
    'USD',
    'https://magically.life/api/media/image?query=health%20monitoring%20sensor%20device%20for%20animals%20white%20medical%20design',
    15,
    'health_monitor',
    '{
        "battery_life": "14 days",
        "sensors": ["Heart rate", "Temperature", "Accelerometer"],
        "weight": "28g",
        "dimensions": "6.0 x 3.5 x 1.2 cm",
        "connectivity": ["Bluetooth 5.0", "WiFi"],
        "features": ["Continuous monitoring", "Health alerts", "Data analytics", "Vet integration"]
    }'
),
(
    'Smart Collar Basic',
    'Essential smart collar with GPS tracking and basic activity monitoring. Great starter device for animal tracking.',
    149.99,
    'USD',
    'https://magically.life/api/media/image?query=smart%20collar%20for%20animals%20basic%20blue%20design%20GPS%20tracking',
    40,
    'smart_collar',
    '{
        "battery_life": "7 days",
        "waterproof": true,
        "weight": "65g",
        "adjustable_size": "30-60 cm",
        "connectivity": ["GPS", "2G"],
        "features": ["Location tracking", "Activity monitoring", "Safe zone alerts"]
    }'
),
(
    'Temperature Sensor Kit',
    'Wireless temperature monitoring kit with multiple sensors. Monitor environmental and body temperature remotely.',
    89.99,
    'USD',
    'https://magically.life/api/media/image?query=temperature%20sensor%20kit%20wireless%20monitoring%20devices%20multiple%20sensors',
    30,
    'temperature_sensor',
    '{
        "battery_life": "6 months",
        "sensors_included": 3,
        "range": "100m",
        "accuracy": "±0.1°C",
        "connectivity": ["LoRa", "WiFi"],
        "features": ["Multi-point monitoring", "Alerts", "Data logging", "Cloud sync"]
    }'
);

COMMIT;