-- Purpose: Implement comprehensive Row Level Security (RLS) policies for medications and vitals tables
-- This ensures users can only access their own data and provides admin oversight capabilities

-- =====================================================
-- PHASE 1: ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on medications table (if not already enabled)
ALTER TABLE public.medications ENABLE ROW LEVEL SECURITY;

-- Enable RLS on vitals table (if not already enabled)
ALTER TABLE public.vitals ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- PHASE 2: DROP EXISTING POLICIES (IF ANY)
-- =====================================================

-- Drop existing policies for medications table to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own medication records" ON public.medications;
DROP POLICY IF EXISTS "Users can insert their own medication records" ON public.medications;
DROP POLICY IF EXISTS "Users can update their own medication records" ON public.medications;
DROP POLICY IF EXISTS "Users can delete their own medication records" ON public.medications;
DROP POLICY IF EXISTS "Admins can view all medication records" ON public.medications;

-- Drop existing policies for vitals table to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own vital records" ON public.vitals;
DROP POLICY IF EXISTS "Users can insert their own vital records" ON public.vitals;
DROP POLICY IF EXISTS "Users can update their own vital records" ON public.vitals;
DROP POLICY IF EXISTS "Users can delete their own vital records" ON public.vitals;
DROP POLICY IF EXISTS "Admins can view all vital records" ON public.vitals;

-- =====================================================
-- PHASE 3: MEDICATIONS TABLE RLS POLICIES
-- =====================================================

-- SELECT Policy: Users can view their own medication records
CREATE POLICY "Users can view their own medication records"
ON public.medications FOR SELECT
USING (auth.uid() = user_id);

-- INSERT Policy: Users can insert their own medication records
CREATE POLICY "Users can insert their own medication records"
ON public.medications FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- UPDATE Policy: Users can update their own medication records
CREATE POLICY "Users can update their own medication records"
ON public.medications FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- DELETE Policy: Users can delete their own medication records
CREATE POLICY "Users can delete their own medication records"
ON public.medications FOR DELETE
USING (auth.uid() = user_id);

-- ADMIN SELECT Policy: Super admins can view all medication records
CREATE POLICY "Admins can view all medication records"
ON public.medications FOR SELECT
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- =====================================================
-- PHASE 4: VITALS TABLE RLS POLICIES
-- =====================================================

-- SELECT Policy: Users can view their own vital records
CREATE POLICY "Users can view their own vital records"
ON public.vitals FOR SELECT
USING (auth.uid() = user_id);

-- INSERT Policy: Users can insert their own vital records
CREATE POLICY "Users can insert their own vital records"
ON public.vitals FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- UPDATE Policy: Users can update their own vital records
CREATE POLICY "Users can update their own vital records"
ON public.vitals FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- DELETE Policy: Users can delete their own vital records
CREATE POLICY "Users can delete their own vital records"
ON public.vitals FOR DELETE
USING (auth.uid() = user_id);

-- ADMIN SELECT Policy: Super admins can view all vital records
CREATE POLICY "Admins can view all vital records"
ON public.vitals FOR SELECT
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- =====================================================
-- PHASE 5: DATABASE SCHEMA ENHANCEMENTS
-- =====================================================

-- Set default user_id for medications table (simplifies client-side code)
ALTER TABLE public.medications ALTER COLUMN user_id SET DEFAULT auth.uid();

-- Set default user_id for vitals table (simplifies client-side code)
ALTER TABLE public.vitals ALTER COLUMN user_id SET DEFAULT auth.uid();

-- =====================================================
-- PHASE 6: VERIFICATION QUERIES
-- =====================================================

-- Verify RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('medications', 'vitals') 
AND schemaname = 'public';

-- Verify policies are created
SELECT schemaname, tablename, policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('medications', 'vitals') 
AND schemaname = 'public'
ORDER BY tablename, policyname;

-- =====================================================
-- SECURITY IMPLEMENTATION COMPLETE
-- =====================================================

-- Summary of Security Enhancements:
-- ✅ RLS enabled on medications and vitals tables
-- ✅ Users can only SELECT their own records (auth.uid() = user_id)
-- ✅ Users can only INSERT records with their own user_id
-- ✅ Users can only UPDATE their own records
-- ✅ Users can only DELETE their own records
-- ✅ Super admins can view all records for oversight
-- ✅ Default user_id set to auth.uid() for simplified client code
-- ✅ Comprehensive policy coverage for all CRUD operations
-- ✅ Data isolation and security at database level