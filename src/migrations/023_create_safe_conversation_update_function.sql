-- Migration: Create safe conversation update function
-- This function safely updates conversation metadata when new messages are added

-- Create function to safely update conversation with new message
CREATE OR REPLACE FUNCTION update_conversation_with_message(
  p_conversation_id UUID,
  p_user_id UUID,
  p_last_message TEXT,
  p_message_count_increment INTEGER DEFAULT 2
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update conversation with new message info
  UPDATE ai_chat_conversations 
  SET 
    last_message_at = NOW(),
    total_messages = COALESCE(total_messages, 0) + p_message_count_increment,
    updated_at = NOW(),
    conversation_summary = CASE 
      WHEN LENGTH(p_last_message) > 200 
      THEN LEFT(p_last_message, 200) || '...'
      ELSE p_last_message
    END
  WHERE 
    id = p_conversation_id 
    AND user_id = p_user_id;
  
  -- Return true if update was successful
  RETURN FOUND;
END;
$$;

-- Create function to safely insert chat message
CREATE OR REPLACE FUNCTION insert_chat_message(
  p_conversation_id UUID,
  p_message_type VARCHAR,
  p_message_content TEXT,
  p_message_metadata JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_message_id UUID;
BEGIN
  -- Insert the message
  INSERT INTO ai_chat_messages (
    conversation_id,
    message_type,
    message_content,
    message_metadata,
    created_at
  )
  VALUES (
    p_conversation_id,
    p_message_type,
    p_message_content,
    p_message_metadata,
    NOW()
  )
  RETURNING id INTO v_message_id;
  
  RETURN v_message_id;
END;
$$;

-- Create function to handle complete chat message flow
CREATE OR REPLACE FUNCTION handle_chat_message_flow(
  p_conversation_id UUID,
  p_user_id UUID,
  p_user_message TEXT,
  p_ai_response TEXT,
  p_user_metadata JSONB DEFAULT NULL,
  p_ai_metadata JSONB DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_message_id UUID;
  v_ai_message_id UUID;
  v_conversation_updated BOOLEAN;
  v_result JSONB;
BEGIN
  -- Start transaction
  BEGIN
    -- Insert user message
    SELECT insert_chat_message(
      p_conversation_id,
      'user',
      p_user_message,
      p_user_metadata
    ) INTO v_user_message_id;
    
    -- Insert AI response
    SELECT insert_chat_message(
      p_conversation_id,
      'assistant',
      p_ai_response,
      p_ai_metadata
    ) INTO v_ai_message_id;
    
    -- Update conversation metadata
    SELECT update_conversation_with_message(
      p_conversation_id,
      p_user_id,
      p_ai_response,
      2  -- 2 messages added (user + AI)
    ) INTO v_conversation_updated;
    
    -- Build result
    v_result := jsonb_build_object(
      'success', true,
      'user_message_id', v_user_message_id,
      'ai_message_id', v_ai_message_id,
      'conversation_updated', v_conversation_updated
    );
    
    RETURN v_result;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- Return error info
      v_result := jsonb_build_object(
        'success', false,
        'error', SQLERRM,
        'error_code', SQLSTATE
      );
      
      RETURN v_result;
  END;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION update_conversation_with_message(UUID, UUID, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION insert_chat_message(UUID, VARCHAR, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION handle_chat_message_flow(UUID, UUID, TEXT, TEXT, JSONB, JSONB) TO authenticated;

-- Add helpful comment
COMMENT ON FUNCTION handle_chat_message_flow IS 'Safely handles the complete flow of inserting user message, AI response, and updating conversation metadata in a single atomic operation';