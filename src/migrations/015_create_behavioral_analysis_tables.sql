-- Migration: Create Behavioral Analysis Tables for Phase 2
-- Description: Tables for stress analysis, sleep monitoring, and behavioral patterns
-- Created: 2024

-- Enable RLS
ALTER DATABASE postgres SET row_security = on;

-- Stress Analysis Table
CREATE TABLE IF NOT EXISTS stress_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  analysis_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  stress_level TEXT CHECK (stress_level IN ('very_low', 'low', 'moderate', 'high', 'very_high')),
  stress_score DECIMAL(5,2) CHECK (stress_score >= 0 AND stress_score <= 100),
  hrv_metrics JSONB, -- Heart Rate Variability data
  activity_indicators JSONB, -- Activity-based stress indicators
  environmental_factors JSONB, -- Environmental stress factors
  stress_triggers JSONB, -- Identified stress triggers
  confidence_level DECIMAL(3,2) CHECK (confidence_level >= 0 AND confidence_level <= 1),
  analysis_duration_minutes INTEGER,
  data_quality_score INTEGER CHECK (data_quality_score >= 0 AND data_quality_score <= 100),
  recommendations TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sleep Pattern Analysis Table
CREATE TABLE IF NOT EXISTS sleep_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  sleep_date DATE NOT NULL,
  sleep_start_time TIMESTAMP WITH TIME ZONE,
  sleep_end_time TIMESTAMP WITH TIME ZONE,
  total_sleep_duration_minutes INTEGER,
  sleep_efficiency_percentage DECIMAL(5,2) CHECK (sleep_efficiency_percentage >= 0 AND sleep_efficiency_percentage <= 100),
  deep_sleep_minutes INTEGER,
  light_sleep_minutes INTEGER,
  rem_sleep_minutes INTEGER,
  wake_episodes INTEGER,
  time_to_sleep_minutes INTEGER,
  sleep_quality_score DECIMAL(5,2) CHECK (sleep_quality_score >= 0 AND sleep_quality_score <= 100),
  sleep_disturbances JSONB, -- Identified disturbances
  environmental_conditions JSONB, -- Temperature, noise, lighting
  circadian_rhythm_alignment DECIMAL(3,2) CHECK (circadian_rhythm_alignment >= 0 AND circadian_rhythm_alignment <= 1),
  sleep_recommendations TEXT,
  data_sources JSONB, -- Which devices/sensors provided data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Behavioral Patterns Table
CREATE TABLE IF NOT EXISTS behavioral_patterns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  pattern_type TEXT NOT NULL, -- 'activity', 'feeding', 'social', 'location'
  pattern_category TEXT NOT NULL, -- 'daily', 'weekly', 'seasonal'
  pattern_start_date DATE NOT NULL,
  pattern_end_date DATE,
  pattern_strength DECIMAL(3,2) CHECK (pattern_strength >= 0 AND pattern_strength <= 1),
  pattern_consistency DECIMAL(3,2) CHECK (pattern_consistency >= 0 AND pattern_consistency <= 1),
  pattern_data JSONB, -- Detailed pattern information
  baseline_comparison JSONB, -- Comparison to normal patterns
  anomalies_detected JSONB, -- Detected anomalies in pattern
  pattern_insights TEXT,
  confidence_level DECIMAL(3,2) CHECK (confidence_level >= 0 AND confidence_level <= 1),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Environmental Impact Analysis Table
CREATE TABLE IF NOT EXISTS environmental_impact (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  analysis_date DATE NOT NULL,
  weather_conditions JSONB, -- Temperature, humidity, pressure, etc.
  location_factors JSONB, -- GPS coordinates, elevation, terrain
  seasonal_factors JSONB, -- Season-specific data
  environmental_stress_score DECIMAL(5,2) CHECK (environmental_stress_score >= 0 AND environmental_stress_score <= 100),
  adaptation_score DECIMAL(5,2) CHECK (adaptation_score >= 0 AND adaptation_score <= 100),
  optimal_conditions JSONB, -- Recommended environmental conditions
  impact_on_behavior JSONB, -- How environment affects behavior
  impact_on_health JSONB, -- How environment affects health metrics
  recommendations TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stress Events Table (for tracking specific stress incidents)
CREATE TABLE IF NOT EXISTS stress_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  event_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  event_type TEXT NOT NULL, -- 'acute', 'chronic', 'environmental', 'social'
  severity_level TEXT CHECK (severity_level IN ('mild', 'moderate', 'severe', 'critical')),
  duration_minutes INTEGER,
  trigger_identified BOOLEAN DEFAULT FALSE,
  trigger_description TEXT,
  physiological_response JSONB, -- Heart rate, temperature changes
  behavioral_response JSONB, -- Activity, movement changes
  recovery_time_minutes INTEGER,
  intervention_applied TEXT,
  resolution_status TEXT CHECK (resolution_status IN ('ongoing', 'resolved', 'chronic')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sleep Quality Metrics Table (detailed sleep metrics)
CREATE TABLE IF NOT EXISTS sleep_quality_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sleep_analysis_id UUID REFERENCES sleep_analysis(id) ON DELETE CASCADE,
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  metric_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  heart_rate_during_sleep JSONB, -- HR patterns during sleep
  movement_during_sleep JSONB, -- Movement/restlessness data
  breathing_patterns JSONB, -- Respiratory rate during sleep
  temperature_regulation JSONB, -- Body temperature during sleep
  sleep_position_changes INTEGER,
  sleep_interruption_causes JSONB, -- What caused wake-ups
  sleep_stage_transitions JSONB, -- Transitions between sleep stages
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_stress_analysis_animal_timestamp ON stress_analysis(animal_id, analysis_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_stress_analysis_user_id ON stress_analysis(user_id);
CREATE INDEX IF NOT EXISTS idx_stress_analysis_stress_level ON stress_analysis(stress_level);

CREATE INDEX IF NOT EXISTS idx_sleep_analysis_animal_date ON sleep_analysis(animal_id, sleep_date DESC);
CREATE INDEX IF NOT EXISTS idx_sleep_analysis_user_id ON sleep_analysis(user_id);
CREATE INDEX IF NOT EXISTS idx_sleep_analysis_quality ON sleep_analysis(sleep_quality_score DESC);

CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_animal_type ON behavioral_patterns(animal_id, pattern_type);
CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_user_id ON behavioral_patterns(user_id);
CREATE INDEX IF NOT EXISTS idx_behavioral_patterns_dates ON behavioral_patterns(pattern_start_date, pattern_end_date);

CREATE INDEX IF NOT EXISTS idx_environmental_impact_animal_date ON environmental_impact(animal_id, analysis_date DESC);
CREATE INDEX IF NOT EXISTS idx_environmental_impact_user_id ON environmental_impact(user_id);

CREATE INDEX IF NOT EXISTS idx_stress_events_animal_timestamp ON stress_events(animal_id, event_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_stress_events_user_id ON stress_events(user_id);
CREATE INDEX IF NOT EXISTS idx_stress_events_severity ON stress_events(severity_level);

CREATE INDEX IF NOT EXISTS idx_sleep_quality_metrics_sleep_analysis ON sleep_quality_metrics(sleep_analysis_id);
CREATE INDEX IF NOT EXISTS idx_sleep_quality_metrics_animal ON sleep_quality_metrics(animal_id);

-- Enable Row Level Security
ALTER TABLE stress_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE sleep_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE behavioral_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE environmental_impact ENABLE ROW LEVEL SECURITY;
ALTER TABLE stress_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE sleep_quality_metrics ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies

-- Stress Analysis Policies
CREATE POLICY "Users can view their own stress analysis" ON stress_analysis
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own stress analysis" ON stress_analysis
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own stress analysis" ON stress_analysis
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own stress analysis" ON stress_analysis
  FOR DELETE USING (auth.uid() = user_id);

-- Sleep Analysis Policies
CREATE POLICY "Users can view their own sleep analysis" ON sleep_analysis
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sleep analysis" ON sleep_analysis
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sleep analysis" ON sleep_analysis
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own sleep analysis" ON sleep_analysis
  FOR DELETE USING (auth.uid() = user_id);

-- Behavioral Patterns Policies
CREATE POLICY "Users can view their own behavioral patterns" ON behavioral_patterns
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own behavioral patterns" ON behavioral_patterns
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own behavioral patterns" ON behavioral_patterns
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own behavioral patterns" ON behavioral_patterns
  FOR DELETE USING (auth.uid() = user_id);

-- Environmental Impact Policies
CREATE POLICY "Users can view their own environmental impact" ON environmental_impact
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own environmental impact" ON environmental_impact
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own environmental impact" ON environmental_impact
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own environmental impact" ON environmental_impact
  FOR DELETE USING (auth.uid() = user_id);

-- Stress Events Policies
CREATE POLICY "Users can view their own stress events" ON stress_events
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own stress events" ON stress_events
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own stress events" ON stress_events
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own stress events" ON stress_events
  FOR DELETE USING (auth.uid() = user_id);

-- Sleep Quality Metrics Policies
CREATE POLICY "Users can view their own sleep quality metrics" ON sleep_quality_metrics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sleep quality metrics" ON sleep_quality_metrics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sleep quality metrics" ON sleep_quality_metrics
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own sleep quality metrics" ON sleep_quality_metrics
  FOR DELETE USING (auth.uid() = user_id);