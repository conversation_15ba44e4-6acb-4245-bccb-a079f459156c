-- Migration: Create AI Health Analysis Tables
-- Description: Foundation tables for AI-powered health analysis system
-- Version: 014
-- Date: 2025-01-07

-- Enable RLS
ALTER DATABASE postgres SET row_security = on;

-- AI Health Scores Table
CREATE TABLE IF NOT EXISTS ai_health_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  score_date DATE NOT NULL,
  overall_score INTEGER CHECK (overall_score >= 0 AND overall_score <= 100),
  vitals_score INTEGER CHECK (vitals_score >= 0 AND vitals_score <= 100),
  activity_score INTEGER CHECK (activity_score >= 0 AND activity_score <= 100),
  feeding_score INTEGER CHECK (feeding_score >= 0 AND feeding_score <= 100),
  medication_score INTEGER CHECK (medication_score >= 0 AND medication_score <= 100),
  score_explanation TEXT,
  data_quality_score INTEGER CHECK (data_quality_score >= 0 AND data_quality_score <= 100),
  contributing_factors JSONB DEFAULT '{}',
  recommendations JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(animal_id, score_date)
);

-- Disease Risk Assessments Table
CREATE TABLE IF NOT EXISTS disease_risk_assessments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  assessment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  disease_category TEXT NOT NULL,
  risk_level TEXT CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
  risk_score DECIMAL(5,2) CHECK (risk_score >= 0 AND risk_score <= 100),
  contributing_factors JSONB DEFAULT '{}',
  recommendations TEXT,
  confidence_level DECIMAL(3,2) CHECK (confidence_level >= 0 AND confidence_level <= 1),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Health Trends Table
CREATE TABLE IF NOT EXISTS health_trends (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  trend_type TEXT NOT NULL,
  trend_direction TEXT CHECK (trend_direction IN ('improving', 'stable', 'declining', 'concerning')),
  trend_strength DECIMAL(3,2) CHECK (trend_strength >= 0 AND trend_strength <= 1),
  start_date DATE NOT NULL,
  end_date DATE,
  trend_data JSONB DEFAULT '{}',
  analysis_summary TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Smart Health Alerts Table
CREATE TABLE IF NOT EXISTS smart_health_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  alert_type TEXT NOT NULL,
  priority_level TEXT CHECK (priority_level IN ('low', 'medium', 'high', 'critical')),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  recommended_actions JSONB DEFAULT '[]',
  is_resolved BOOLEAN DEFAULT FALSE,
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolution_notes TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ai_health_scores_animal_date ON ai_health_scores(animal_id, score_date DESC);
CREATE INDEX IF NOT EXISTS idx_ai_health_scores_user ON ai_health_scores(user_id);
CREATE INDEX IF NOT EXISTS idx_disease_risk_assessments_animal ON disease_risk_assessments(animal_id, assessment_date DESC);
CREATE INDEX IF NOT EXISTS idx_disease_risk_assessments_active ON disease_risk_assessments(animal_id, is_active);
CREATE INDEX IF NOT EXISTS idx_health_trends_animal ON health_trends(animal_id, start_date DESC);
CREATE INDEX IF NOT EXISTS idx_health_trends_active ON health_trends(animal_id, is_active);
CREATE INDEX IF NOT EXISTS idx_smart_health_alerts_animal ON smart_health_alerts(animal_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_smart_health_alerts_unresolved ON smart_health_alerts(animal_id, is_resolved, priority_level);

-- Enable Row Level Security
ALTER TABLE ai_health_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE disease_risk_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE health_trends ENABLE ROW LEVEL SECURITY;
ALTER TABLE smart_health_alerts ENABLE ROW LEVEL SECURITY;

-- RLS Policies for ai_health_scores
CREATE POLICY "Users can view their own animal health scores" ON ai_health_scores
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert health scores for their animals" ON ai_health_scores
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own animal health scores" ON ai_health_scores
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own animal health scores" ON ai_health_scores
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for disease_risk_assessments
CREATE POLICY "Users can view their own disease risk assessments" ON disease_risk_assessments
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert disease risk assessments for their animals" ON disease_risk_assessments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own disease risk assessments" ON disease_risk_assessments
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own disease risk assessments" ON disease_risk_assessments
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for health_trends
CREATE POLICY "Users can view their own health trends" ON health_trends
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert health trends for their animals" ON health_trends
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own health trends" ON health_trends
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own health trends" ON health_trends
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for smart_health_alerts
CREATE POLICY "Users can view their own health alerts" ON smart_health_alerts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert health alerts for their animals" ON smart_health_alerts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own health alerts" ON smart_health_alerts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own health alerts" ON smart_health_alerts
  FOR DELETE USING (auth.uid() = user_id);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_ai_health_scores_updated_at BEFORE UPDATE ON ai_health_scores
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_disease_risk_assessments_updated_at BEFORE UPDATE ON disease_risk_assessments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_health_trends_updated_at BEFORE UPDATE ON health_trends
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_smart_health_alerts_updated_at BEFORE UPDATE ON smart_health_alerts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON ai_health_scores TO authenticated;
GRANT ALL ON disease_risk_assessments TO authenticated;
GRANT ALL ON health_trends TO authenticated;
GRANT ALL ON smart_health_alerts TO authenticated;

-- Insert sample data for testing (optional)
INSERT INTO ai_health_scores (animal_id, user_id, score_date, overall_score, vitals_score, activity_score, feeding_score, medication_score, score_explanation, data_quality_score)
SELECT 
  a.id,
  a.user_id,
  CURRENT_DATE,
  85,
  90,
  80,
  85,
  95,
  'Good overall health with excellent vital signs and medication compliance. Activity levels could be improved.',
  88
FROM animals a
LIMIT 3
ON CONFLICT (animal_id, score_date) DO NOTHING;