-- Migration: Create MFA (Multi-Factor Authentication) Tables
-- Description: Creates tables to support TOTP-based two-factor authentication
-- Created: 2024-01-09

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table: user_mfa_settings
-- Stores TOTP secrets and backup codes for users
CREATE TABLE IF NOT EXISTS user_mfa_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    totp_secret TEXT NOT NULL,
    backup_codes TEXT[] NOT NULL DEFAULT '{}',
    is_enabled BOOLEAN NOT NULL DEFAULT false,
    enabled_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT user_mfa_settings_user_id_unique UNIQUE (user_id)
);

-- Table: mfa_challenges
-- Stores temporary challenges for MFA verification during login
CREATE TABLE IF NOT EXISTS mfa_challenges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    challenge_id TEXT NOT NULL UNIQUE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    verification_attempts INTEGER NOT NULL DEFAULT 0,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    verified_at TIMESTAMPTZ,
    last_attempt_at TIMESTAMPTZ
);

-- Table: mfa_session_tokens
-- Stores temporary session tokens after successful MFA verification
CREATE TABLE IF NOT EXISTS mfa_session_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    token TEXT NOT NULL UNIQUE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    used_at TIMESTAMPTZ
);

-- Table: mfa_verification_logs
-- Logs all MFA verification attempts for security auditing
CREATE TABLE IF NOT EXISTS mfa_verification_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    challenge_id TEXT,
    verification_method TEXT NOT NULL CHECK (verification_method IN ('totp', 'backup_code')),
    success BOOLEAN NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    verified_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    error_message TEXT
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_mfa_settings_user_id ON user_mfa_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_challenges_challenge_id ON mfa_challenges(challenge_id);
CREATE INDEX IF NOT EXISTS idx_mfa_challenges_user_id ON mfa_challenges(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_challenges_expires_at ON mfa_challenges(expires_at);
CREATE INDEX IF NOT EXISTS idx_mfa_session_tokens_token ON mfa_session_tokens(token);
CREATE INDEX IF NOT EXISTS idx_mfa_session_tokens_user_id ON mfa_session_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_session_tokens_expires_at ON mfa_session_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_mfa_verification_logs_user_id ON mfa_verification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_verification_logs_verified_at ON mfa_verification_logs(verified_at);

-- Row Level Security (RLS) Policies

-- Enable RLS on all MFA tables
ALTER TABLE user_mfa_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_session_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_verification_logs ENABLE ROW LEVEL SECURITY;

-- Policies for user_mfa_settings
-- Users can only access their own MFA settings
CREATE POLICY "Users can view their own MFA settings" ON user_mfa_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own MFA settings" ON user_mfa_settings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own MFA settings" ON user_mfa_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own MFA settings" ON user_mfa_settings
    FOR DELETE USING (auth.uid() = user_id);

-- Policies for mfa_challenges
-- Only service role can manage challenges (used by edge functions)
CREATE POLICY "Service role can manage MFA challenges" ON mfa_challenges
    FOR ALL USING (auth.role() = 'service_role');

-- Policies for mfa_session_tokens
-- Only service role can manage session tokens (used by edge functions)
CREATE POLICY "Service role can manage MFA session tokens" ON mfa_session_tokens
    FOR ALL USING (auth.role() = 'service_role');

-- Policies for mfa_verification_logs
-- Users can view their own verification logs
CREATE POLICY "Users can view their own MFA verification logs" ON mfa_verification_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Service role can insert verification logs
CREATE POLICY "Service role can insert MFA verification logs" ON mfa_verification_logs
    FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Functions for automatic cleanup

-- Function to clean up expired challenges
CREATE OR REPLACE FUNCTION cleanup_expired_mfa_challenges()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    DELETE FROM mfa_challenges 
    WHERE expires_at < NOW();
END;
$$;

-- Function to clean up expired session tokens
CREATE OR REPLACE FUNCTION cleanup_expired_mfa_session_tokens()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    DELETE FROM mfa_session_tokens 
    WHERE expires_at < NOW();
END;
$$;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Trigger to automatically update updated_at on user_mfa_settings
CREATE TRIGGER update_user_mfa_settings_updated_at
    BEFORE UPDATE ON user_mfa_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create a scheduled job to clean up expired data (runs every hour)
-- Note: This requires pg_cron extension which may not be available in all environments
-- SELECT cron.schedule('cleanup-expired-mfa-data', '0 * * * *', 'SELECT cleanup_expired_mfa_challenges(); SELECT cleanup_expired_mfa_session_tokens();');

-- Comments for documentation
COMMENT ON TABLE user_mfa_settings IS 'Stores TOTP secrets and backup codes for users with MFA enabled';
COMMENT ON TABLE mfa_challenges IS 'Temporary challenges created during MFA verification process';
COMMENT ON TABLE mfa_session_tokens IS 'Temporary session tokens issued after successful MFA verification';
COMMENT ON TABLE mfa_verification_logs IS 'Audit log of all MFA verification attempts';

COMMENT ON COLUMN user_mfa_settings.totp_secret IS 'Base32-encoded TOTP secret key';
COMMENT ON COLUMN user_mfa_settings.backup_codes IS 'Array of one-time backup codes';
COMMENT ON COLUMN mfa_challenges.challenge_id IS 'Unique identifier for the MFA challenge';
COMMENT ON COLUMN mfa_challenges.verification_attempts IS 'Number of verification attempts for this challenge';
COMMENT ON COLUMN mfa_session_tokens.token IS 'Temporary session token for completing login after MFA';
COMMENT ON COLUMN mfa_verification_logs.verification_method IS 'Method used for verification: totp or backup_code';

-- Grant necessary permissions
-- Note: Adjust these based on your specific role setup
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON user_mfa_settings TO authenticated;
GRANT SELECT ON mfa_verification_logs TO authenticated;

-- Service role needs full access for edge functions
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO service_role;