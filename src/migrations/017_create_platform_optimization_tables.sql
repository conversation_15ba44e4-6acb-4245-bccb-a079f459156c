-- Platform Optimization Tables for Phase 4
-- Advanced Integrations and Platform Optimization

-- Real-time Data Streams Table
CREATE TABLE IF NOT EXISTS realtime_data_streams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  stream_type VARCHAR(50) NOT NULL, -- 'vitals', 'activity', 'location', 'environmental'
  data_source VARCHAR(100) NOT NULL, -- 'iot_sensor', 'wearable', 'manual', 'api'
  device_id VARCHAR(100),
  
  -- Stream Data
  stream_data JSONB NOT NULL,
  data_quality_score DECIMAL(3,2) DEFAULT 1.0,
  processing_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'processed', 'error'
  
  -- <PERSON><PERSON><PERSON>
  received_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  processed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- IoT Device Registry Table
CREATE TABLE IF NOT EXISTS iot_devices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  animal_id UUID REFERENCES animals(id) ON DELETE SET NULL,
  
  -- Device Information
  device_name VARCHAR(100) NOT NULL,
  device_type VARCHAR(50) NOT NULL, -- 'collar', 'sensor', 'camera', 'feeder'
  manufacturer VARCHAR(100),
  model VARCHAR(100),
  firmware_version VARCHAR(50),
  
  -- Connection Details
  device_identifier VARCHAR(200) UNIQUE NOT NULL,
  connection_type VARCHAR(20) NOT NULL, -- 'bluetooth', 'wifi', 'cellular', 'zigbee'
  api_endpoint VARCHAR(500),
  authentication_token TEXT,
  
  -- Status
  status VARCHAR(20) DEFAULT 'inactive', -- 'active', 'inactive', 'error', 'maintenance'
  last_seen TIMESTAMPTZ,
  battery_level INTEGER,
  signal_strength INTEGER,
  
  -- Configuration
  data_collection_interval INTEGER DEFAULT 300, -- seconds
  enabled_sensors JSONB,
  device_settings JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data Export Jobs Table
CREATE TABLE IF NOT EXISTS data_export_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Export Configuration
  export_type VARCHAR(50) NOT NULL, -- 'full', 'partial', 'report'
  data_types JSONB NOT NULL, -- ['vitals', 'feeding', 'medications', etc.]
  animal_ids JSONB, -- specific animals or null for all
  date_range_start TIMESTAMPTZ,
  date_range_end TIMESTAMPTZ,
  export_format VARCHAR(20) NOT NULL, -- 'csv', 'json', 'pdf', 'xlsx'
  
  -- Job Status
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  progress_percentage INTEGER DEFAULT 0,
  total_records INTEGER,
  processed_records INTEGER,
  
  -- Results
  file_url TEXT,
  file_size_bytes BIGINT,
  download_expires_at TIMESTAMPTZ,
  download_count INTEGER DEFAULT 0,
  
  -- Error Handling
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- External Service Integrations Table
CREATE TABLE IF NOT EXISTS external_integrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Integration Details
  service_type VARCHAR(50) NOT NULL, -- 'weather_api', 'vet_clinic', 'wearable', 'smart_home'
  service_name VARCHAR(100) NOT NULL,
  integration_status VARCHAR(20) DEFAULT 'inactive', -- 'active', 'inactive', 'error', 'expired'
  
  -- Authentication
  api_key_encrypted TEXT,
  access_token_encrypted TEXT,
  refresh_token_encrypted TEXT,
  token_expires_at TIMESTAMPTZ,
  
  -- Configuration
  integration_config JSONB,
  sync_frequency INTEGER DEFAULT 3600, -- seconds
  last_sync_at TIMESTAMPTZ,
  next_sync_at TIMESTAMPTZ,
  
  -- Monitoring
  sync_success_count INTEGER DEFAULT 0,
  sync_error_count INTEGER DEFAULT 0,
  last_error_message TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Push Notification Subscriptions Table
CREATE TABLE IF NOT EXISTS push_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Device Information
  device_type VARCHAR(20) NOT NULL, -- 'ios', 'android', 'web'
  device_token TEXT NOT NULL,
  device_identifier VARCHAR(200),
  
  -- Subscription Details
  subscription_data JSONB, -- Push subscription object for web
  is_active BOOLEAN DEFAULT true,
  
  -- Notification Preferences
  notification_types JSONB DEFAULT '[]'::jsonb, -- ['health_alerts', 'medication_reminders', etc.]
  quiet_hours_start TIME,
  quiet_hours_end TIME,
  timezone VARCHAR(50),
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notification History Table
CREATE TABLE IF NOT EXISTS notification_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  animal_id UUID REFERENCES animals(id) ON DELETE SET NULL,
  
  -- Notification Details
  notification_type VARCHAR(50) NOT NULL,
  title VARCHAR(200) NOT NULL,
  message TEXT NOT NULL,
  priority VARCHAR(20) DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
  
  -- Delivery Information
  delivery_method VARCHAR(20) NOT NULL, -- 'push', 'email', 'sms', 'in_app'
  delivery_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'failed'
  delivered_at TIMESTAMPTZ,
  
  -- Interaction
  opened_at TIMESTAMPTZ,
  clicked_at TIMESTAMPTZ,
  action_taken VARCHAR(100),
  
  -- Metadata
  notification_data JSONB,
  error_message TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Collaboration Table
CREATE TABLE IF NOT EXISTS user_collaborations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  collaborator_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  
  -- Collaboration Details
  role VARCHAR(20) NOT NULL, -- 'viewer', 'editor', 'admin', 'veterinarian'
  permissions JSONB NOT NULL, -- specific permissions array
  
  -- Status
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'active', 'suspended', 'revoked'
  invited_at TIMESTAMPTZ DEFAULT NOW(),
  accepted_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ,
  
  -- Invitation Details
  invitation_token VARCHAR(100),
  invitation_message TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(collaborator_user_id, animal_id)
);

-- Performance Metrics Table
CREATE TABLE IF NOT EXISTS performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Metric Details
  metric_type VARCHAR(50) NOT NULL, -- 'api_response_time', 'database_query', 'user_action'
  metric_name VARCHAR(100) NOT NULL,
  metric_value DECIMAL(10,4) NOT NULL,
  metric_unit VARCHAR(20), -- 'ms', 'seconds', 'bytes', 'count'
  
  -- Context
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  animal_id UUID REFERENCES animals(id) ON DELETE SET NULL,
  session_id VARCHAR(100),
  
  -- Additional Data
  metadata JSONB,
  tags JSONB,
  
  recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Audit Log Table
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Actor Information
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  user_email VARCHAR(255),
  user_role VARCHAR(50),
  
  -- Action Details
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  resource_id UUID,
  
  -- Changes
  old_values JSONB,
  new_values JSONB,
  
  -- Context
  ip_address INET,
  user_agent TEXT,
  session_id VARCHAR(100),
  
  -- Metadata
  metadata JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_realtime_streams_animal_type ON realtime_data_streams(animal_id, stream_type, received_at DESC);
CREATE INDEX IF NOT EXISTS idx_realtime_streams_processing ON realtime_data_streams(processing_status, received_at);
CREATE INDEX IF NOT EXISTS idx_iot_devices_user_status ON iot_devices(user_id, status);
CREATE INDEX IF NOT EXISTS idx_iot_devices_animal ON iot_devices(animal_id, status);
CREATE INDEX IF NOT EXISTS idx_export_jobs_user_status ON data_export_jobs(user_id, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_external_integrations_user_type ON external_integrations(user_id, service_type, integration_status);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_user_active ON push_subscriptions(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_notification_history_user_type ON notification_history(user_id, notification_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_collaborations_owner ON user_collaborations(owner_user_id, status);
CREATE INDEX IF NOT EXISTS idx_collaborations_collaborator ON user_collaborations(collaborator_user_id, status);
CREATE INDEX IF NOT EXISTS idx_collaborations_animal ON user_collaborations(animal_id, status);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_type_time ON performance_metrics(metric_type, recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action ON audit_logs(user_id, action, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource_type, resource_id, created_at DESC);

-- Row Level Security (RLS)
ALTER TABLE realtime_data_streams ENABLE ROW LEVEL SECURITY;
ALTER TABLE iot_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_export_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE external_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_collaborations ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own realtime data streams" ON realtime_data_streams
  FOR SELECT USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage their own IoT devices" ON iot_devices
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own export jobs" ON data_export_jobs
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own integrations" ON external_integrations
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own push subscriptions" ON push_subscriptions
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view their own notification history" ON notification_history
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can view collaborations they own or are part of" ON user_collaborations
  FOR SELECT USING (
    owner_user_id = auth.uid() OR collaborator_user_id = auth.uid()
  );

CREATE POLICY "Users can manage collaborations they own" ON user_collaborations
  FOR ALL USING (owner_user_id = auth.uid());

CREATE POLICY "Performance metrics are viewable by authenticated users" ON performance_metrics
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Audit logs are viewable by the user who performed the action" ON audit_logs
  FOR SELECT USING (user_id = auth.uid());