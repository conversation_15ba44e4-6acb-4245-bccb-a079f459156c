-- Migration: Enhance training_sessions table for automated data capture
-- Description: Add fields for automated training session data from devices

-- Add new columns to existing training_sessions table
ALTER TABLE public.training_sessions 
ADD COLUMN IF NOT EXISTS avg_speed DECIMAL(6,2), -- Average speed in km/h
ADD COLUMN IF NOT EXISTS max_speed DECIMAL(6,2), -- Maximum speed in km/h
ADD COLUMN IF NOT EXISTS avg_intensity DECIMAL(4,2), -- Average intensity (0-100)
ADD COLUMN IF NOT EXISTS max_intensity DECIMAL(4,2), -- Maximum intensity (0-100)
ADD COLUMN IF NOT EXISTS calories_burned INTEGER, -- Estimated calories burned
ADD COLUMN IF NOT EXISTS heart_rate_avg INTEGER, -- Average heart rate (BPM)
ADD COLUMN IF NOT EXISTS heart_rate_max INTEGER, -- Maximum heart rate (BPM)
ADD COLUMN IF NOT EXISTS elevation_gain DECIMAL(6,2), -- Elevation gain in meters
ADD COLUMN IF NOT EXISTS device_id TEXT, -- ID of the recording device
ADD COLUMN IF NOT EXISTS is_automated BOOLEAN DEFAULT FALSE, -- True if captured automatically
ADD COLUMN IF NOT EXISTS session_data JSONB, -- Raw session data from device
ADD COLUMN IF NOT EXISTS gps_track JSONB, -- GPS tracking data (array of coordinates)
ADD COLUMN IF NOT EXISTS weather_conditions JSONB, -- Weather during session
ADD COLUMN IF NOT EXISTS session_quality TEXT, -- Session quality rating
ADD COLUMN IF NOT EXISTS auto_paused_time INTEGER DEFAULT 0, -- Time auto-paused in seconds
ADD COLUMN IF NOT EXISTS manual_adjustments JSONB, -- User modifications to automated data
ADD COLUMN IF NOT EXISTS duration_seconds INTEGER; -- Duration in seconds for easier calculations

-- Add constraints separately to avoid IF NOT EXISTS syntax issues
DO $$
BEGIN
    -- Add session quality constraint
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_session_quality') THEN
        ALTER TABLE public.training_sessions 
        ADD CONSTRAINT check_session_quality CHECK (session_quality IN ('excellent', 'good', 'fair', 'poor'));
    END IF;
    
    -- Add distance constraint
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_distance_positive') THEN
        ALTER TABLE public.training_sessions 
        ADD CONSTRAINT check_distance_positive CHECK (distance IS NULL OR distance >= 0);
    END IF;
    
    -- Add speed constraints
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_speeds_positive') THEN
        ALTER TABLE public.training_sessions 
        ADD CONSTRAINT check_speeds_positive CHECK (
            (avg_speed IS NULL OR avg_speed >= 0) AND 
            (max_speed IS NULL OR max_speed >= 0) AND
            (max_speed IS NULL OR avg_speed IS NULL OR max_speed >= avg_speed)
        );
    END IF;
    
    -- Add intensity constraints
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_intensity_range') THEN
        ALTER TABLE public.training_sessions 
        ADD CONSTRAINT check_intensity_range CHECK (
            (avg_intensity IS NULL OR (avg_intensity >= 0 AND avg_intensity <= 100)) AND
            (max_intensity IS NULL OR (max_intensity >= 0 AND max_intensity <= 100)) AND
            (max_intensity IS NULL OR avg_intensity IS NULL OR max_intensity >= avg_intensity)
        );
    END IF;
    
    -- Add heart rate constraints
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_heart_rate_range') THEN
        ALTER TABLE public.training_sessions 
        ADD CONSTRAINT check_heart_rate_range CHECK (
            (heart_rate_avg IS NULL OR (heart_rate_avg >= 30 AND heart_rate_avg <= 300)) AND
            (heart_rate_max IS NULL OR (heart_rate_max >= 30 AND heart_rate_max <= 300)) AND
            (heart_rate_max IS NULL OR heart_rate_avg IS NULL OR heart_rate_max >= heart_rate_avg)
        );
    END IF;
    
    -- Add other constraints
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_calories_positive') THEN
        ALTER TABLE public.training_sessions 
        ADD CONSTRAINT check_calories_positive CHECK (calories_burned IS NULL OR calories_burned >= 0);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_auto_paused_positive') THEN
        ALTER TABLE public.training_sessions 
        ADD CONSTRAINT check_auto_paused_positive CHECK (auto_paused_time >= 0);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'check_duration_seconds_positive') THEN
        ALTER TABLE public.training_sessions 
        ADD CONSTRAINT check_duration_seconds_positive CHECK (duration_seconds IS NULL OR duration_seconds >= 0);
    END IF;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_training_sessions_is_automated ON public.training_sessions(is_automated);
CREATE INDEX IF NOT EXISTS idx_training_sessions_device_id ON public.training_sessions(device_id);
CREATE INDEX IF NOT EXISTS idx_training_sessions_distance ON public.training_sessions(distance DESC);
CREATE INDEX IF NOT EXISTS idx_training_sessions_avg_speed ON public.training_sessions(avg_speed DESC);
CREATE INDEX IF NOT EXISTS idx_training_sessions_session_timestamp_automated ON public.training_sessions(session_timestamp DESC, is_automated);

-- Create function to extract seconds from interval
CREATE OR REPLACE FUNCTION interval_to_seconds(duration_interval INTERVAL)
RETURNS INTEGER AS $$
BEGIN
    RETURN EXTRACT(EPOCH FROM duration_interval)::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- Update duration_seconds from existing duration data
UPDATE public.training_sessions 
SET duration_seconds = interval_to_seconds(duration)
WHERE duration IS NOT NULL AND duration_seconds IS NULL;

-- Create function to calculate training metrics
CREATE OR REPLACE FUNCTION calculate_training_metrics(
    p_distance DECIMAL,
    p_duration_seconds INTEGER,
    p_avg_speed DECIMAL,
    p_avg_intensity DECIMAL,
    p_animal_weight DECIMAL DEFAULT 500
)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    estimated_calories INTEGER;
    pace_per_km DECIMAL;
    effort_score DECIMAL;
BEGIN
    -- Calculate pace (minutes per km)
    IF p_avg_speed > 0 THEN
        pace_per_km := 60.0 / p_avg_speed;
    ELSE
        pace_per_km := NULL;
    END IF;
    
    -- Estimate calories burned
    IF p_duration_seconds > 0 AND p_avg_intensity > 0 THEN
        estimated_calories := ROUND(
            (p_animal_weight * 0.8) * 
            (p_duration_seconds / 3600.0) * 
            (1 + (p_avg_intensity / 100.0) * 2)
        );
    ELSE
        estimated_calories := NULL;
    END IF;
    
    -- Calculate effort score
    IF p_avg_intensity > 0 AND p_duration_seconds > 0 THEN
        effort_score := LEAST(100, 
            (p_avg_intensity * 0.7) + 
            (LEAST(p_duration_seconds / 3600.0, 2) * 15)
        );
    ELSE
        effort_score := NULL;
    END IF;
    
    result := jsonb_build_object(
        'pace_per_km', pace_per_km,
        'estimated_calories', estimated_calories,
        'effort_score', effort_score,
        'calculated_at', NOW()
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create trigger function for auto-calculating metrics
CREATE OR REPLACE FUNCTION auto_calculate_training_metrics()
RETURNS TRIGGER AS $$
BEGIN
    -- Sync duration_seconds with duration interval
    IF NEW.duration IS NOT NULL AND NEW.duration_seconds IS NULL THEN
        NEW.duration_seconds := interval_to_seconds(NEW.duration);
    END IF;
    
    -- Calculate metrics for automated sessions
    IF NEW.is_automated = TRUE AND NEW.distance IS NOT NULL AND NEW.duration_seconds IS NOT NULL THEN
        DECLARE
            animal_weight DECIMAL := 500;
            calculated_metrics JSONB;
        BEGIN
            calculated_metrics := calculate_training_metrics(
                NEW.distance,
                NEW.duration_seconds,
                NEW.avg_speed,
                NEW.avg_intensity,
                animal_weight
            );
            
            -- Update calories if not manually set
            IF NEW.calories_burned IS NULL THEN
                NEW.calories_burned := (calculated_metrics->>'estimated_calories')::INTEGER;
            END IF;
            
            -- Store calculated metrics
            NEW.session_data := COALESCE(NEW.session_data, '{}'::JSONB) || 
                               jsonb_build_object('calculated_metrics', calculated_metrics);
        END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_auto_calculate_training_metrics ON public.training_sessions;
CREATE TRIGGER trigger_auto_calculate_training_metrics
    BEFORE INSERT OR UPDATE ON public.training_sessions
    FOR EACH ROW
    EXECUTE FUNCTION auto_calculate_training_metrics();

-- Add helpful comments
COMMENT ON COLUMN public.training_sessions.distance IS 'Distance covered in kilometers';
COMMENT ON COLUMN public.training_sessions.avg_speed IS 'Average speed during session in km/h';
COMMENT ON COLUMN public.training_sessions.max_speed IS 'Maximum speed reached in km/h';
COMMENT ON COLUMN public.training_sessions.avg_intensity IS 'Average training intensity (0-100%)';
COMMENT ON COLUMN public.training_sessions.is_automated IS 'True if session was automatically recorded by device';
COMMENT ON COLUMN public.training_sessions.session_data IS 'Raw session data and calculated metrics from device';
COMMENT ON COLUMN public.training_sessions.gps_track IS 'GPS tracking data as array of coordinates';
COMMENT ON COLUMN public.training_sessions.duration_seconds IS 'Duration in seconds for easier calculations';

-- Insert sample automated training sessions
INSERT INTO public.training_sessions (
    animal_id,
    user_id,
    session_timestamp,
    duration,
    duration_seconds,
    intensity_label,
    distance,
    avg_speed,
    max_speed,
    avg_intensity,
    max_intensity,
    calories_burned,
    heart_rate_avg,
    heart_rate_max,
    elevation_gain,
    device_id,
    is_automated,
    session_quality,
    notes,
    weather_conditions,
    gps_track
)
SELECT 
    a.id,
    a.user_id,
    NOW() - INTERVAL '1 day' * (random() * 30)::INTEGER,
    INTERVAL '1 second' * (1800 + random() * 3600)::INTEGER,
    (1800 + random() * 3600)::INTEGER,
    CASE 
        WHEN random() < 0.3 THEN 'light'
        WHEN random() < 0.7 THEN 'moderate'
        ELSE 'intense'
    END,
    (5 + random() * 15)::DECIMAL(8,2),
    (8 + random() * 12)::DECIMAL(6,2),
    (12 + random() * 18)::DECIMAL(6,2),
    (40 + random() * 50)::DECIMAL(4,2),
    (60 + random() * 40)::DECIMAL(4,2),
    NULL, -- Will be calculated by trigger
    (120 + random() * 60)::INTEGER,
    (140 + random() * 80)::INTEGER,
    (10 + random() * 100)::DECIMAL(6,2),
    'GPS-TRACKER-' || LPAD((random() * 9999)::INTEGER::TEXT, 4, '0'),
    TRUE,
    CASE 
        WHEN random() < 0.5 THEN 'excellent'
        WHEN random() < 0.8 THEN 'good'
        WHEN random() < 0.95 THEN 'fair'
        ELSE 'poor'
    END,
    CASE 
        WHEN random() < 0.3 THEN 'Great automated session'
        WHEN random() < 0.6 THEN 'Good pace maintained'
        ELSE NULL
    END,
    jsonb_build_object(
        'temperature', (15 + random() * 20)::INTEGER,
        'humidity', (40 + random() * 40)::INTEGER,
        'wind_speed', (0 + random() * 15)::INTEGER,
        'conditions', CASE 
            WHEN random() < 0.6 THEN 'clear'
            WHEN random() < 0.8 THEN 'partly_cloudy'
            WHEN random() < 0.95 THEN 'overcast'
            ELSE 'light_rain'
        END
    ),
    jsonb_build_array(
        jsonb_build_object('lat', 40.7128 + (random() - 0.5) * 0.01, 'lng', -74.0060 + (random() - 0.5) * 0.01, 'timestamp', extract(epoch from NOW() - INTERVAL '1 hour'), 'elevation', 100 + random() * 50),
        jsonb_build_object('lat', 40.7128 + (random() - 0.5) * 0.01, 'lng', -74.0060 + (random() - 0.5) * 0.01, 'timestamp', extract(epoch from NOW() - INTERVAL '30 minutes'), 'elevation', 100 + random() * 50),
        jsonb_build_object('lat', 40.7128 + (random() - 0.5) * 0.01, 'lng', -74.0060 + (random() - 0.5) * 0.01, 'timestamp', extract(epoch from NOW()), 'elevation', 100 + random() * 50)
    )
FROM public.animals a
WHERE a.id IN (SELECT id FROM public.animals LIMIT 3)
AND random() < 0.8;