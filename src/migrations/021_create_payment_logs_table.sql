-- Create payment_logs table for tracking payment events
CREATE TABLE IF NOT EXISTS payment_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  action TEXT NOT NULL, -- 'checkout_created', 'payment_completed', 'payment_failed', 'refund_processed'
  provider TEXT NOT NULL, -- 'lemonsqueezy', 'stripe', 'paypal'
  checkout_id TEXT,
  checkout_url TEXT,
  order_id TEXT,
  plan_type TEXT, -- 'monthly', 'yearly'
  product_id TEXT,
  amount DECIMAL(10,2),
  currency TEXT DEFAULT 'USD',
  status TEXT, -- 'pending', 'completed', 'failed', 'cancelled'
  metadata JSONB DEFAULT '{}',
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_payment_logs_user_id ON payment_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_logs_action ON payment_logs(action);
CREATE INDEX IF NOT EXISTS idx_payment_logs_provider ON payment_logs(provider);
CREATE INDEX IF NOT EXISTS idx_payment_logs_status ON payment_logs(status);
CREATE INDEX IF NOT EXISTS idx_payment_logs_created_at ON payment_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_logs_checkout_id ON payment_logs(checkout_id);
CREATE INDEX IF NOT EXISTS idx_payment_logs_order_id ON payment_logs(order_id);

-- Enable RLS
ALTER TABLE payment_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own payment logs" ON payment_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all payment logs" ON payment_logs
  FOR ALL USING (auth.role() = 'service_role');

-- Create function to automatically update updated_at
CREATE OR REPLACE FUNCTION update_payment_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER payment_logs_updated_at_trigger
  BEFORE UPDATE ON payment_logs
  FOR EACH ROW
  EXECUTE FUNCTION update_payment_logs_updated_at();

-- Add helpful comments
COMMENT ON TABLE payment_logs IS 'Tracks all payment-related events and transactions';
COMMENT ON COLUMN payment_logs.action IS 'Type of payment action performed';
COMMENT ON COLUMN payment_logs.provider IS 'Payment service provider used';
COMMENT ON COLUMN payment_logs.metadata IS 'Additional payment-specific data in JSON format';
COMMENT ON COLUMN payment_logs.status IS 'Current status of the payment transaction';

-- Insert sample data for testing (optional)
-- This will be removed in production
INSERT INTO payment_logs (user_id, action, provider, plan_type, amount, status, metadata)
SELECT 
  id,
  'checkout_created',
  'lemonsqueezy',
  'monthly',
  14.99,
  'pending',
  '{"test": true}'
FROM users 
WHERE email LIKE '%test%' 
LIMIT 1
ON CONFLICT DO NOTHING;