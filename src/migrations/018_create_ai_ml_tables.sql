-- Advanced AI and Machine Learning Tables for Phase 5
-- Computer Vision, NLP, and Advanced ML Features

-- AI Image Analysis Table
CREATE TABLE IF NOT EXISTS ai_image_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Image Information
  image_url TEXT NOT NULL,
  image_type VARCHAR(50) NOT NULL, -- 'health_check', 'body_condition', 'behavior', 'symptom'
  image_metadata JSONB,
  
  -- AI Analysis Results
  analysis_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  confidence_score DECIMAL(3,2),
  
  -- Health Assessment Results
  body_condition_score DECIMAL(3,1),
  health_indicators JSONB, -- detected health indicators
  behavioral_markers JSONB, -- detected behaviors
  symptom_detection JSONB, -- detected symptoms
  
  -- Facial Recognition
  facial_features JSONB,
  emotion_analysis JSONB,
  identity_confidence DECIMAL(3,2),
  
  -- Model Information
  model_version VARCHAR(50),
  processing_time_ms INTEGER,
  
  -- Quality Metrics
  image_quality_score DECIMAL(3,2),
  lighting_conditions VARCHAR(20),
  image_clarity VARCHAR(20),
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Chat Conversations Table
CREATE TABLE IF NOT EXISTS ai_chat_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  animal_id UUID REFERENCES animals(id) ON DELETE SET NULL,
  
  -- Conversation Details
  conversation_title VARCHAR(200),
  conversation_type VARCHAR(50) DEFAULT 'general', -- 'general', 'health_consultation', 'emergency', 'symptom_analysis'
  conversation_status VARCHAR(20) DEFAULT 'active', -- 'active', 'completed', 'archived'
  
  -- AI Model Information
  ai_model_version VARCHAR(50),
  conversation_context JSONB,
  
  -- Conversation Metadata
  total_messages INTEGER DEFAULT 0,
  last_message_at TIMESTAMPTZ,
  conversation_summary TEXT,
  
  -- Health Context
  health_concerns JSONB,
  mentioned_symptoms JSONB,
  recommended_actions JSONB,
  urgency_level VARCHAR(20), -- 'low', 'medium', 'high', 'emergency'
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Chat Messages Table
CREATE TABLE IF NOT EXISTS ai_chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID NOT NULL REFERENCES ai_chat_conversations(id) ON DELETE CASCADE,
  
  -- Message Details
  message_type VARCHAR(20) NOT NULL, -- 'user', 'ai', 'system'
  message_content TEXT NOT NULL,
  message_metadata JSONB,
  
  -- AI Processing
  intent_analysis JSONB,
  sentiment_score DECIMAL(3,2),
  confidence_score DECIMAL(3,2),
  
  -- Health Information Extraction
  extracted_symptoms JSONB,
  extracted_concerns JSONB,
  medical_entities JSONB,
  
  -- Response Generation
  response_time_ms INTEGER,
  model_version VARCHAR(50),
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ML Model Registry Table
CREATE TABLE IF NOT EXISTS ml_model_registry (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Model Information
  model_name VARCHAR(100) NOT NULL,
  model_type VARCHAR(50) NOT NULL, -- 'health_prediction', 'image_analysis', 'nlp', 'anomaly_detection'
  model_version VARCHAR(50) NOT NULL,
  model_description TEXT,
  
  -- Model Artifacts
  model_url TEXT,
  model_config JSONB,
  model_metadata JSONB,
  
  -- Performance Metrics
  accuracy_score DECIMAL(5,4),
  precision_score DECIMAL(5,4),
  recall_score DECIMAL(5,4),
  f1_score DECIMAL(5,4),
  
  -- Training Information
  training_data_size INTEGER,
  training_duration_minutes INTEGER,
  training_completed_at TIMESTAMPTZ,
  
  -- Deployment Status
  deployment_status VARCHAR(20) DEFAULT 'development', -- 'development', 'staging', 'production', 'deprecated'
  deployed_at TIMESTAMPTZ,
  
  -- Usage Statistics
  prediction_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(model_name, model_version)
);

-- ML Predictions Table
CREATE TABLE IF NOT EXISTS ml_predictions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  model_id UUID NOT NULL REFERENCES ml_model_registry(id) ON DELETE CASCADE,
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Prediction Details
  prediction_type VARCHAR(50) NOT NULL,
  input_features JSONB NOT NULL,
  prediction_result JSONB NOT NULL,
  confidence_score DECIMAL(5,4),
  
  -- Model Performance
  processing_time_ms INTEGER,
  model_version VARCHAR(50),
  
  -- Validation
  actual_outcome JSONB,
  prediction_accuracy DECIMAL(5,4),
  feedback_provided BOOLEAN DEFAULT false,
  
  -- Context
  prediction_context JSONB,
  business_impact VARCHAR(50),
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Automated Health Assessments Table
CREATE TABLE IF NOT EXISTS automated_health_assessments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  
  -- Assessment Details
  assessment_type VARCHAR(50) NOT NULL, -- 'routine', 'symptom_based', 'emergency', 'comprehensive'
  assessment_trigger VARCHAR(50), -- 'scheduled', 'user_request', 'anomaly_detected', 'symptom_reported'
  
  -- AI Analysis
  overall_health_score DECIMAL(5,2),
  health_status VARCHAR(20), -- 'excellent', 'good', 'fair', 'poor', 'critical'
  risk_level VARCHAR(20), -- 'low', 'moderate', 'high', 'critical'
  
  -- Detailed Assessments
  vital_signs_assessment JSONB,
  behavioral_assessment JSONB,
  physical_condition_assessment JSONB,
  environmental_assessment JSONB,
  
  -- Recommendations
  immediate_actions JSONB,
  care_recommendations JSONB,
  veterinary_consultation_needed BOOLEAN DEFAULT false,
  urgency_level VARCHAR(20),
  
  -- AI Model Information
  assessment_models JSONB, -- models used for assessment
  confidence_scores JSONB,
  
  -- Follow-up
  follow_up_required BOOLEAN DEFAULT false,
  follow_up_date TIMESTAMPTZ,
  follow_up_notes TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Training Data Table
CREATE TABLE IF NOT EXISTS ai_training_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Data Classification
  data_type VARCHAR(50) NOT NULL, -- 'image', 'text', 'vitals', 'behavioral'
  data_category VARCHAR(50) NOT NULL,
  data_source VARCHAR(100),
  
  -- Data Content
  data_url TEXT,
  data_content JSONB,
  data_metadata JSONB,
  
  -- Labels and Annotations
  ground_truth_labels JSONB,
  annotation_confidence DECIMAL(3,2),
  annotated_by VARCHAR(100),
  annotation_date TIMESTAMPTZ,
  
  -- Quality Metrics
  data_quality_score DECIMAL(3,2),
  validation_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'validated', 'rejected'
  
  -- Usage Tracking
  used_in_training BOOLEAN DEFAULT false,
  training_sessions JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI Insights and Recommendations Table
CREATE TABLE IF NOT EXISTS ai_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Insight Details
  insight_type VARCHAR(50) NOT NULL, -- 'health_trend', 'behavioral_pattern', 'risk_factor', 'optimization'
  insight_category VARCHAR(50),
  insight_title VARCHAR(200) NOT NULL,
  insight_description TEXT NOT NULL,
  
  -- AI Analysis
  confidence_level DECIMAL(3,2),
  importance_score DECIMAL(3,2),
  evidence_strength VARCHAR(20), -- 'weak', 'moderate', 'strong', 'very_strong'
  
  -- Supporting Data
  supporting_data JSONB,
  data_sources JSONB,
  analysis_period_start TIMESTAMPTZ,
  analysis_period_end TIMESTAMPTZ,
  
  -- Recommendations
  recommended_actions JSONB,
  expected_outcomes JSONB,
  implementation_difficulty VARCHAR(20), -- 'easy', 'moderate', 'difficult'
  
  -- User Interaction
  viewed_by_user BOOLEAN DEFAULT false,
  user_feedback VARCHAR(20), -- 'helpful', 'not_helpful', 'implemented'
  implementation_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'in_progress', 'completed', 'dismissed'
  
  -- Validation
  insight_accuracy DECIMAL(3,2),
  outcome_validation JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Genetic Analysis Table
CREATE TABLE IF NOT EXISTS genetic_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  
  -- Genetic Information
  genetic_profile JSONB,
  breed_composition JSONB,
  genetic_markers JSONB,
  
  -- Health Predispositions
  disease_predispositions JSONB,
  trait_predictions JSONB,
  behavioral_tendencies JSONB,
  
  -- Risk Assessments
  genetic_health_risks JSONB,
  carrier_status JSONB,
  breeding_recommendations JSONB,
  
  -- Analysis Metadata
  analysis_provider VARCHAR(100),
  analysis_date TIMESTAMPTZ,
  analysis_version VARCHAR(50),
  confidence_scores JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Population Analytics Table
CREATE TABLE IF NOT EXISTS population_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Population Segmentation
  species VARCHAR(50),
  breed VARCHAR(100),
  age_group VARCHAR(20),
  geographic_region VARCHAR(100),
  
  -- Health Statistics
  population_size INTEGER,
  health_metrics JSONB,
  common_conditions JSONB,
  mortality_statistics JSONB,
  
  -- Trends and Patterns
  health_trends JSONB,
  seasonal_patterns JSONB,
  environmental_correlations JSONB,
  
  -- Comparative Analysis
  benchmark_metrics JSONB,
  percentile_rankings JSONB,
  
  -- Analysis Period
  analysis_period_start TIMESTAMPTZ,
  analysis_period_end TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_ai_image_analysis_animal_type ON ai_image_analysis(animal_id, image_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_image_analysis_status ON ai_image_analysis(analysis_status, created_at);
CREATE INDEX IF NOT EXISTS idx_ai_chat_conversations_user ON ai_chat_conversations(user_id, conversation_status, last_message_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_chat_messages_conversation ON ai_chat_messages(conversation_id, created_at);
CREATE INDEX IF NOT EXISTS idx_ml_model_registry_type_status ON ml_model_registry(model_type, deployment_status);
CREATE INDEX IF NOT EXISTS idx_ml_predictions_model_animal ON ml_predictions(model_id, animal_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_automated_assessments_animal ON automated_health_assessments(animal_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_training_data_type_status ON ai_training_data(data_type, validation_status);
CREATE INDEX IF NOT EXISTS idx_ai_insights_animal_type ON ai_insights(animal_id, insight_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_genetic_analysis_animal ON genetic_analysis(animal_id);
CREATE INDEX IF NOT EXISTS idx_population_analytics_segment ON population_analytics(species, breed, analysis_period_end DESC);

-- Row Level Security (RLS)
ALTER TABLE ai_image_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE ml_model_registry ENABLE ROW LEVEL SECURITY;
ALTER TABLE ml_predictions ENABLE ROW LEVEL SECURITY;
ALTER TABLE automated_health_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_training_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE genetic_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE population_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own AI image analysis" ON ai_image_analysis
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own AI image analysis" ON ai_image_analysis
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view their own AI chat conversations" ON ai_chat_conversations
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own AI chat conversations" ON ai_chat_conversations
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view messages from their conversations" ON ai_chat_messages
  FOR SELECT USING (
    conversation_id IN (
      SELECT id FROM ai_chat_conversations WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can add messages to their conversations" ON ai_chat_messages
  FOR INSERT WITH CHECK (
    conversation_id IN (
      SELECT id FROM ai_chat_conversations WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "ML models are viewable by authenticated users" ON ml_model_registry
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can view predictions for their animals" ON ml_predictions
  FOR SELECT USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    ) OR user_id = auth.uid()
  );

CREATE POLICY "Users can view assessments for their animals" ON automated_health_assessments
  FOR SELECT USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Training data is viewable by authenticated users" ON ai_training_data
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can view insights for their animals" ON ai_insights
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage insights for their animals" ON ai_insights
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view genetic analysis for their animals" ON genetic_analysis
  FOR SELECT USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Population analytics are viewable by authenticated users" ON population_analytics
  FOR SELECT USING (auth.role() = 'authenticated');