-- Advanced Analytics Dashboard and Reporting Tables for Phase 6
-- Interactive Dashboards, Reports, and Business Intelligence

-- Custom Dashboards Table
CREATE TABLE IF NOT EXISTS custom_dashboards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Dashboard Details
  dashboard_name VA<PERSON>HAR(200) NOT NULL,
  dashboard_description TEXT,
  dashboard_type VARCHAR(50) DEFAULT 'custom', -- 'custom', 'template', 'shared'
  dashboard_category VARCHAR(50), -- 'health', 'activity', 'nutrition', 'comprehensive'
  
  -- Dashboard Configuration
  layout_config JSONB NOT NULL, -- Dashboard layout and widget positions
  widget_configs JSONB NOT NULL, -- Individual widget configurations
  filter_config JSONB, -- Global dashboard filters
  refresh_interval INTEGER DEFAULT 300, -- Auto-refresh interval in seconds
  
  -- Sharing and Access
  is_public BOOLEAN DEFAULT false,
  sharing_token VARCHAR(100),
  shared_with J<PERSON><PERSON><PERSON>, -- Array of user IDs with access
  access_level VARCHAR(20) DEFAULT 'private', -- 'private', 'shared', 'public'
  
  -- Metadata
  last_viewed_at TIMESTAMPTZ,
  view_count INTEGER DEFAULT 0,
  is_favorite BOOLEAN DEFAULT false,
  tags JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Dashboard Widgets Table
CREATE TABLE IF NOT EXISTS dashboard_widgets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dashboard_id UUID NOT NULL REFERENCES custom_dashboards(id) ON DELETE CASCADE,
  
  -- Widget Details
  widget_name VARCHAR(200) NOT NULL,
  widget_type VARCHAR(50) NOT NULL, -- 'chart', 'kpi', 'table', 'heatmap', 'gauge'
  widget_category VARCHAR(50), -- 'health', 'activity', 'nutrition', 'environmental'
  
  -- Widget Configuration
  data_source VARCHAR(100) NOT NULL, -- Table or view name
  query_config JSONB NOT NULL, -- Query parameters and filters
  visualization_config JSONB NOT NULL, -- Chart type, colors, etc.
  display_config JSONB, -- Size, position, styling
  
  -- Data Processing
  aggregation_type VARCHAR(50), -- 'sum', 'avg', 'count', 'min', 'max'
  time_range VARCHAR(50) DEFAULT '30d', -- '1d', '7d', '30d', '90d', '1y'
  grouping_config JSONB, -- Group by configurations
  
  -- Widget State
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Scheduled Reports Table
CREATE TABLE IF NOT EXISTS scheduled_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Report Details
  report_name VARCHAR(200) NOT NULL,
  report_description TEXT,
  report_type VARCHAR(50) NOT NULL, -- 'health_summary', 'activity_report', 'comprehensive'
  report_template VARCHAR(100), -- Template identifier
  
  -- Report Configuration
  data_filters JSONB, -- Animal IDs, date ranges, etc.
  report_config JSONB NOT NULL, -- Report structure and content
  format_config JSONB, -- Styling, branding, layout
  
  -- Scheduling
  schedule_type VARCHAR(20) NOT NULL, -- 'daily', 'weekly', 'monthly', 'quarterly'
  schedule_config JSONB, -- Specific scheduling parameters
  timezone VARCHAR(50) DEFAULT 'UTC',
  is_active BOOLEAN DEFAULT true,
  
  -- Delivery
  delivery_method VARCHAR(20) DEFAULT 'email', -- 'email', 'dashboard', 'both'
  email_recipients JSONB, -- Array of email addresses
  
  -- Execution Tracking
  last_generated_at TIMESTAMPTZ,
  next_generation_at TIMESTAMPTZ,
  generation_count INTEGER DEFAULT 0,
  last_error_message TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Report Instances Table
CREATE TABLE IF NOT EXISTS report_instances (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  scheduled_report_id UUID REFERENCES scheduled_reports(id) ON DELETE SET NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Report Details
  report_name VARCHAR(200) NOT NULL,
  report_type VARCHAR(50) NOT NULL,
  generation_trigger VARCHAR(50), -- 'scheduled', 'manual', 'api'
  
  -- Report Content
  report_data JSONB NOT NULL, -- Generated report data
  report_metadata JSONB, -- Generation metadata
  
  -- File Information
  file_url TEXT,
  file_format VARCHAR(20), -- 'pdf', 'excel', 'csv', 'html'
  file_size_bytes BIGINT,
  
  -- Status
  generation_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'generating', 'completed', 'failed'
  generation_started_at TIMESTAMPTZ,
  generation_completed_at TIMESTAMPTZ,
  generation_duration_ms INTEGER,
  error_message TEXT,
  
  -- Access
  download_count INTEGER DEFAULT 0,
  last_downloaded_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Analytics Queries Table
CREATE TABLE IF NOT EXISTS analytics_queries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Query Details
  query_name VARCHAR(200),
  query_type VARCHAR(50) NOT NULL, -- 'dashboard', 'report', 'export', 'api'
  query_category VARCHAR(50), -- 'health', 'activity', 'nutrition'
  
  -- Query Configuration
  data_source VARCHAR(100) NOT NULL,
  query_sql TEXT,
  query_parameters JSONB,
  filters_applied JSONB,
  
  -- Performance
  execution_time_ms INTEGER,
  rows_returned INTEGER,
  cache_hit BOOLEAN DEFAULT false,
  
  -- Context
  dashboard_id UUID REFERENCES custom_dashboards(id) ON DELETE SET NULL,
  widget_id UUID REFERENCES dashboard_widgets(id) ON DELETE SET NULL,
  session_id VARCHAR(100),
  
  executed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data Visualizations Table
CREATE TABLE IF NOT EXISTS data_visualizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Visualization Details
  visualization_name VARCHAR(200) NOT NULL,
  visualization_type VARCHAR(50) NOT NULL, -- 'line_chart', 'bar_chart', 'pie_chart', 'heatmap'
  visualization_category VARCHAR(50),
  
  -- Data Configuration
  data_source VARCHAR(100) NOT NULL,
  data_query JSONB NOT NULL,
  data_filters JSONB,
  
  -- Visual Configuration
  chart_config JSONB NOT NULL, -- Chart-specific configuration
  styling_config JSONB, -- Colors, fonts, themes
  interaction_config JSONB, -- Zoom, drill-down, tooltips
  
  -- Sharing
  is_public BOOLEAN DEFAULT false,
  sharing_token VARCHAR(100),
  
  -- Usage
  view_count INTEGER DEFAULT 0,
  last_viewed_at TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Analytics Insights Table
CREATE TABLE IF NOT EXISTS analytics_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  animal_id UUID REFERENCES animals(id) ON DELETE CASCADE,
  
  -- Insight Details
  insight_type VARCHAR(50) NOT NULL, -- 'trend', 'anomaly', 'correlation', 'prediction'
  insight_category VARCHAR(50), -- 'health', 'activity', 'nutrition', 'behavior'
  insight_title VARCHAR(200) NOT NULL,
  insight_description TEXT NOT NULL,
  
  -- Analytics Data
  statistical_significance DECIMAL(5,4),
  confidence_interval JSONB,
  trend_direction VARCHAR(20), -- 'increasing', 'decreasing', 'stable', 'volatile'
  trend_strength DECIMAL(3,2), -- 0-1 scale
  
  -- Supporting Data
  data_points JSONB, -- Raw data points used
  statistical_tests JSONB, -- Applied statistical tests
  correlation_coefficients JSONB,
  
  -- Time Context
  analysis_period_start TIMESTAMPTZ,
  analysis_period_end TIMESTAMPTZ,
  forecast_period_start TIMESTAMPTZ,
  forecast_period_end TIMESTAMPTZ,
  
  -- Recommendations
  recommended_actions JSONB,
  priority_level VARCHAR(20), -- 'low', 'medium', 'high', 'critical'
  
  -- Validation
  insight_accuracy DECIMAL(3,2),
  validation_date TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Dashboard Analytics Table
CREATE TABLE IF NOT EXISTS dashboard_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  dashboard_id UUID NOT NULL REFERENCES custom_dashboards(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Usage Metrics
  session_id VARCHAR(100),
  view_duration_seconds INTEGER,
  interactions_count INTEGER DEFAULT 0,
  widgets_viewed JSONB, -- Array of widget IDs viewed
  
  -- Performance Metrics
  load_time_ms INTEGER,
  query_count INTEGER,
  total_query_time_ms INTEGER,
  cache_hit_rate DECIMAL(3,2),
  
  -- User Behavior
  entry_point VARCHAR(100), -- How user accessed dashboard
  exit_point VARCHAR(100), -- Last action before leaving
  scroll_depth DECIMAL(3,2), -- How much of dashboard was viewed
  
  -- Device Context
  device_type VARCHAR(20), -- 'mobile', 'tablet', 'desktop'
  screen_resolution VARCHAR(20),
  browser_info VARCHAR(100),
  
  viewed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Report Analytics Table
CREATE TABLE IF NOT EXISTS report_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  report_instance_id UUID NOT NULL REFERENCES report_instances(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Access Metrics
  access_type VARCHAR(20), -- 'view', 'download', 'share'
  access_duration_seconds INTEGER,
  pages_viewed INTEGER,
  
  -- Sharing Metrics
  shared_with_count INTEGER DEFAULT 0,
  sharing_method VARCHAR(50), -- 'email', 'link', 'download'
  
  -- Device Context
  device_type VARCHAR(20),
  access_location VARCHAR(100), -- Geographic location if available
  
  accessed_at TIMESTAMPTZ DEFAULT NOW()
);

-- Benchmark Data Table
CREATE TABLE IF NOT EXISTS benchmark_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Benchmark Classification
  benchmark_type VARCHAR(50) NOT NULL, -- 'species', 'breed', 'age_group', 'weight_class'
  benchmark_category VARCHAR(50) NOT NULL, -- 'health', 'activity', 'nutrition'
  classification_criteria JSONB NOT NULL, -- Criteria for this benchmark
  
  -- Statistical Data
  sample_size INTEGER NOT NULL,
  metric_name VARCHAR(100) NOT NULL,
  metric_unit VARCHAR(50),
  
  -- Statistical Measures
  mean_value DECIMAL(10,4),
  median_value DECIMAL(10,4),
  std_deviation DECIMAL(10,4),
  min_value DECIMAL(10,4),
  max_value DECIMAL(10,4),
  percentile_25 DECIMAL(10,4),
  percentile_75 DECIMAL(10,4),
  percentile_90 DECIMAL(10,4),
  percentile_95 DECIMAL(10,4),
  
  -- Data Quality
  confidence_level DECIMAL(3,2),
  data_quality_score DECIMAL(3,2),
  last_updated TIMESTAMPTZ,
  
  -- Metadata
  data_source VARCHAR(100),
  calculation_method TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_custom_dashboards_user ON custom_dashboards(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_custom_dashboards_public ON custom_dashboards(is_public, access_level);
CREATE INDEX IF NOT EXISTS idx_dashboard_widgets_dashboard ON dashboard_widgets(dashboard_id, sort_order);
CREATE INDEX IF NOT EXISTS idx_scheduled_reports_user_active ON scheduled_reports(user_id, is_active, next_generation_at);
CREATE INDEX IF NOT EXISTS idx_report_instances_user_date ON report_instances(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_report_instances_scheduled ON report_instances(scheduled_report_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_analytics_queries_user_date ON analytics_queries(user_id, executed_at DESC);
CREATE INDEX IF NOT EXISTS idx_analytics_queries_performance ON analytics_queries(execution_time_ms, rows_returned);
CREATE INDEX IF NOT EXISTS idx_data_visualizations_user_type ON data_visualizations(user_id, visualization_type);
CREATE INDEX IF NOT EXISTS idx_analytics_insights_animal_type ON analytics_insights(animal_id, insight_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_analytics_insights_priority ON analytics_insights(priority_level, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_dashboard_analytics_dashboard ON dashboard_analytics(dashboard_id, viewed_at DESC);
CREATE INDEX IF NOT EXISTS idx_report_analytics_report ON report_analytics(report_instance_id, accessed_at DESC);
CREATE INDEX IF NOT EXISTS idx_benchmark_data_type_category ON benchmark_data(benchmark_type, benchmark_category, metric_name);

-- Row Level Security (RLS)
ALTER TABLE custom_dashboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE dashboard_widgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduled_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE report_instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_queries ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_visualizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE dashboard_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE report_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE benchmark_data ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can manage their own dashboards" ON custom_dashboards
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view public dashboards" ON custom_dashboards
  FOR SELECT USING (is_public = true OR user_id = auth.uid());

CREATE POLICY "Users can manage widgets for their dashboards" ON dashboard_widgets
  FOR ALL USING (
    dashboard_id IN (
      SELECT id FROM custom_dashboards WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view widgets for accessible dashboards" ON dashboard_widgets
  FOR SELECT USING (
    dashboard_id IN (
      SELECT id FROM custom_dashboards 
      WHERE user_id = auth.uid() OR is_public = true
    )
  );

CREATE POLICY "Users can manage their own scheduled reports" ON scheduled_reports
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view their own report instances" ON report_instances
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create their own report instances" ON report_instances
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can view their own analytics queries" ON analytics_queries
  FOR SELECT USING (user_id = auth.uid() OR user_id IS NULL);

CREATE POLICY "Users can create analytics queries" ON analytics_queries
  FOR INSERT WITH CHECK (user_id = auth.uid() OR user_id IS NULL);

CREATE POLICY "Users can manage their own visualizations" ON data_visualizations
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can view public visualizations" ON data_visualizations
  FOR SELECT USING (is_public = true OR user_id = auth.uid());

CREATE POLICY "Users can view insights for their animals" ON analytics_insights
  FOR SELECT USING (
    user_id = auth.uid() OR 
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create insights for their animals" ON analytics_insights
  FOR INSERT WITH CHECK (
    user_id = auth.uid() AND 
    (animal_id IS NULL OR animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    ))
  );

CREATE POLICY "Dashboard analytics are viewable by dashboard owners" ON dashboard_analytics
  FOR SELECT USING (
    dashboard_id IN (
      SELECT id FROM custom_dashboards WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Dashboard analytics can be created by anyone" ON dashboard_analytics
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Report analytics are viewable by report owners" ON report_analytics
  FOR SELECT USING (
    report_instance_id IN (
      SELECT id FROM report_instances WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Report analytics can be created by anyone" ON report_analytics
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Benchmark data is viewable by authenticated users" ON benchmark_data
  FOR SELECT USING (auth.role() = 'authenticated');