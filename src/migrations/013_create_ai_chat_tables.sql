-- Create AI Chat Tables
-- This migration creates the necessary tables for the AI chat functionality

-- Create ai_conversations table
CREATE TABLE IF NOT EXISTS ai_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  animal_id UUID REFERENCES animals(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  last_message TEXT,
  last_message_at TIMESTAMPTZ DEFAULT NOW(),
  message_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create ai_messages table
CREATE TABLE IF NOT EXISTS ai_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID NOT NULL REFERENCES ai_conversations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  animal_id UUID REFERENCES animals(id) ON DELETE SET NULL,
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
  content TEXT NOT NULL,
  attachments JSONB,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ai_conversations_user_id ON ai_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_conversations_animal_id ON ai_conversations(animal_id);
CREATE INDEX IF NOT EXISTS idx_ai_conversations_last_message_at ON ai_conversations(last_message_at DESC);

CREATE INDEX IF NOT EXISTS idx_ai_messages_conversation_id ON ai_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_ai_messages_user_id ON ai_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_messages_animal_id ON ai_messages(animal_id);
CREATE INDEX IF NOT EXISTS idx_ai_messages_created_at ON ai_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_messages_role ON ai_messages(role);

-- Create updated_at trigger for ai_conversations
CREATE OR REPLACE FUNCTION update_ai_conversations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_ai_conversations_updated_at
  BEFORE UPDATE ON ai_conversations
  FOR EACH ROW
  EXECUTE FUNCTION update_ai_conversations_updated_at();

-- Enable Row Level Security (RLS)
ALTER TABLE ai_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_messages ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for ai_conversations
CREATE POLICY "Users can view their own conversations" ON ai_conversations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own conversations" ON ai_conversations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own conversations" ON ai_conversations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own conversations" ON ai_conversations
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for ai_messages
CREATE POLICY "Users can view their own messages" ON ai_messages
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own messages" ON ai_messages
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own messages" ON ai_messages
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own messages" ON ai_messages
  FOR DELETE USING (auth.uid() = user_id);

-- Create storage bucket for AI uploads
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'ai_uploads',
  'ai_uploads',
  true,
  10485760, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf', 'text/plain']
) ON CONFLICT (id) DO NOTHING;

-- Create storage policies for ai_uploads bucket
CREATE POLICY "Users can upload their own files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'ai_uploads' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their own files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'ai_uploads' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own files" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'ai_uploads' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'ai_uploads' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Create function to clean up old conversations and messages
CREATE OR REPLACE FUNCTION cleanup_old_ai_conversations()
RETURNS void AS $$
BEGIN
  -- Delete conversations older than 6 months with no recent activity
  DELETE FROM ai_conversations
  WHERE last_message_at < NOW() - INTERVAL '6 months'
    AND message_count < 5;
  
  -- Delete orphaned messages (shouldn't happen with CASCADE, but just in case)
  DELETE FROM ai_messages
  WHERE conversation_id NOT IN (SELECT id FROM ai_conversations);
END;
$$ LANGUAGE plpgsql;

-- Create function to get conversation summary
CREATE OR REPLACE FUNCTION get_conversation_summary(conversation_uuid UUID)
RETURNS TABLE (
  id UUID,
  title TEXT,
  animal_name TEXT,
  message_count BIGINT,
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    c.title,
    a.name as animal_name,
    COUNT(m.id) as message_count,
    c.last_message,
    c.last_message_at,
    c.created_at
  FROM ai_conversations c
  LEFT JOIN animals a ON c.animal_id = a.id
  LEFT JOIN ai_messages m ON c.id = m.conversation_id
  WHERE c.id = conversation_uuid
    AND c.user_id = auth.uid()
  GROUP BY c.id, c.title, a.name, c.last_message, c.last_message_at, c.created_at;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to search conversations
CREATE OR REPLACE FUNCTION search_conversations(search_term TEXT)
RETURNS TABLE (
  id UUID,
  title TEXT,
  animal_name TEXT,
  last_message TEXT,
  last_message_at TIMESTAMPTZ,
  relevance REAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    c.title,
    a.name as animal_name,
    c.last_message,
    c.last_message_at,
    (
      CASE WHEN c.title ILIKE '%' || search_term || '%' THEN 1.0 ELSE 0.0 END +
      CASE WHEN c.last_message ILIKE '%' || search_term || '%' THEN 0.8 ELSE 0.0 END +
      CASE WHEN a.name ILIKE '%' || search_term || '%' THEN 0.6 ELSE 0.0 END
    ) as relevance
  FROM ai_conversations c
  LEFT JOIN animals a ON c.animal_id = a.id
  WHERE c.user_id = auth.uid()
    AND (
      c.title ILIKE '%' || search_term || '%' OR
      c.last_message ILIKE '%' || search_term || '%' OR
      a.name ILIKE '%' || search_term || '%'
    )
  ORDER BY relevance DESC, c.last_message_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ai_conversations TO authenticated;
GRANT ALL ON ai_messages TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_ai_conversations() TO authenticated;
GRANT EXECUTE ON FUNCTION get_conversation_summary(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION search_conversations(TEXT) TO authenticated;

-- Add helpful comments
COMMENT ON TABLE ai_conversations IS 'Stores AI chat conversations between users and the AI assistant';
COMMENT ON TABLE ai_messages IS 'Stores individual messages within AI conversations';
COMMENT ON COLUMN ai_messages.attachments IS 'JSON array of file attachments (images, documents, etc.)';
COMMENT ON COLUMN ai_messages.metadata IS 'Additional metadata for messages (tokens used, model version, etc.)';
COMMENT ON FUNCTION cleanup_old_ai_conversations() IS 'Cleans up old conversations and orphaned messages';
COMMENT ON FUNCTION get_conversation_summary(UUID) IS 'Returns detailed summary of a specific conversation';
COMMENT ON FUNCTION search_conversations(TEXT) IS 'Searches conversations by title, content, or animal name';

-- Create some sample data for testing (optional)
-- This will be removed in production
/*
INSERT INTO ai_conversations (user_id, title, last_message, message_count)
SELECT 
  auth.uid(),
  'Welcome Chat',
  'Hello! I''m your AI assistant. How can I help you with your pets today?',
  1
WHERE auth.uid() IS NOT NULL;
*/