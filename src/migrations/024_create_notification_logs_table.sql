-- Migration: Create notification logs table for tracking push notifications
-- Description: Creates table to log all push notification attempts and results
-- Version: 024
-- Date: 2024-12-06

-- Create notification_logs table
CREATE TABLE IF NOT EXISTS notification_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  animal_id UUID REFERENCES animals(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  priority TEXT NOT NULL CHECK (priority IN ('low', 'normal', 'high', 'critical')),
  alert_type TEXT,
  push_token TEXT,
  expo_response JSONB,
  status TEXT NOT NULL CHECK (status IN ('sent', 'failed', 'no_token', 'disabled')),
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- <PERSON>reate indexes for performance
CREATE INDEX IF NOT EXISTS idx_notification_logs_user_id ON notification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_logs_animal_id ON notification_logs(animal_id);
CREATE INDEX IF NOT EXISTS idx_notification_logs_status ON notification_logs(status);
CREATE INDEX IF NOT EXISTS idx_notification_logs_priority ON notification_logs(priority);
CREATE INDEX IF NOT EXISTS idx_notification_logs_created_at ON notification_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_notification_logs_alert_type ON notification_logs(alert_type);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_notification_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_notification_logs_updated_at
  BEFORE UPDATE ON notification_logs
  FOR EACH ROW
  EXECUTE FUNCTION update_notification_logs_updated_at();

-- Add push notification fields to users table if they don't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS push_token TEXT,
ADD COLUMN IF NOT EXISTS push_notifications_enabled BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{}'::jsonb;

-- Create index on push_token for performance
CREATE INDEX IF NOT EXISTS idx_users_push_token ON users(push_token) WHERE push_token IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_push_notifications_enabled ON users(push_notifications_enabled);

-- Enable RLS on notification_logs
ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for notification_logs

-- Users can view their own notification logs
CREATE POLICY "Users can view own notification logs" ON notification_logs
  FOR SELECT
  USING (user_id = auth.uid());

-- Service role can insert notification logs
CREATE POLICY "Service role can insert notification logs" ON notification_logs
  FOR INSERT
  WITH CHECK (true);

-- Service role can update notification logs
CREATE POLICY "Service role can update notification logs" ON notification_logs
  FOR UPDATE
  USING (true);

-- Users can update their own notification preferences
CREATE POLICY "Users can update own notification preferences" ON users
  FOR UPDATE
  USING (id = auth.uid())
  WITH CHECK (id = auth.uid());

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON notification_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON notification_logs TO service_role;

-- Create function to clean up old notification logs (older than 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_notification_logs()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM notification_logs 
  WHERE created_at < NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get notification stats for a user
CREATE OR REPLACE FUNCTION get_user_notification_stats(user_uuid UUID)
RETURNS TABLE (
  total_notifications BIGINT,
  sent_notifications BIGINT,
  failed_notifications BIGINT,
  critical_notifications BIGINT,
  last_notification_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_notifications,
    COUNT(*) FILTER (WHERE status = 'sent') as sent_notifications,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_notifications,
    COUNT(*) FILTER (WHERE priority = 'critical') as critical_notifications,
    MAX(created_at) as last_notification_at
  FROM notification_logs 
  WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get recent notifications for a user
CREATE OR REPLACE FUNCTION get_recent_notifications(user_uuid UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
  id UUID,
  title TEXT,
  body TEXT,
  priority TEXT,
  alert_type TEXT,
  status TEXT,
  created_at TIMESTAMPTZ,
  animal_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    nl.id,
    nl.title,
    nl.body,
    nl.priority,
    nl.alert_type,
    nl.status,
    nl.created_at,
    a.name as animal_name
  FROM notification_logs nl
  LEFT JOIN animals a ON nl.animal_id = a.id
  WHERE nl.user_id = user_uuid
  ORDER BY nl.created_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION cleanup_old_notification_logs() TO service_role;
GRANT EXECUTE ON FUNCTION get_user_notification_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_recent_notifications(UUID, INTEGER) TO authenticated;

-- Add helpful comments
COMMENT ON TABLE notification_logs IS 'Logs all push notification attempts and their results';
COMMENT ON COLUMN notification_logs.priority IS 'Priority level: low, normal, high, critical';
COMMENT ON COLUMN notification_logs.status IS 'Delivery status: sent, failed, no_token, disabled';
COMMENT ON COLUMN notification_logs.expo_response IS 'Response from Expo Push API';
COMMENT ON COLUMN users.push_token IS 'Expo push notification token';
COMMENT ON COLUMN users.push_notifications_enabled IS 'Whether user has enabled push notifications';
COMMENT ON COLUMN users.notification_preferences IS 'User preferences for different notification types';

-- Create view for notification analytics
CREATE OR REPLACE VIEW notification_analytics AS
SELECT 
  DATE_TRUNC('day', created_at) as date,
  priority,
  status,
  alert_type,
  COUNT(*) as count
FROM notification_logs
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', created_at), priority, status, alert_type
ORDER BY date DESC;

GRANT SELECT ON notification_analytics TO authenticated;
GRANT SELECT ON notification_analytics TO service_role;

COMMENT ON VIEW notification_analytics IS 'Analytics view for notification delivery metrics over the last 30 days';

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Migration 024: Notification logs table and related functions created successfully';
END $$;