-- Purpose: Create devices table with RLS policies for device management
-- This migration creates a comprehensive device management system with proper security

-- 0. Create ENUM types if they don't exist (idempotent)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'device_status_enum') THEN
        CREATE TYPE device_status_enum AS ENUM ('disconnected', 'connected', 'paired', 'low_battery', 'error', 'unknown');
    END IF;
END
$$;

-- 1. Create the 'devices' table
CREATE TABLE IF NOT EXISTS public.devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    battery_level INTEGER CHECK (battery_level >= 0 AND battery_level <= 100),
    status device_status_enum NOT NULL DEFAULT 'unknown',
    last_sync_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Add comments to the table and columns for clarity
COMMENT ON TABLE public.devices IS 'Stores information about user-registered devices for animal monitoring.';
COMMENT ON COLUMN public.devices.id IS 'Unique identifier for the device.';
COMMENT ON COLUMN public.devices.user_id IS 'Identifier of the user who owns/registered the device.';
COMMENT ON COLUMN public.devices.name IS 'User-friendly name for the device (e.g., "Blue Collar")';
COMMENT ON COLUMN public.devices.type IS 'Type of the device (e.g., "Collar", "Tag", "Implant")';
COMMENT ON COLUMN public.devices.battery_level IS 'Current battery level of the device (0-100)';
COMMENT ON COLUMN public.devices.status IS 'Current status of the device';
COMMENT ON COLUMN public.devices.last_sync_time IS 'Timestamp of the last successful data sync from the device';
COMMENT ON COLUMN public.devices.created_at IS 'Timestamp of when the device was registered';
COMMENT ON COLUMN public.devices.updated_at IS 'Timestamp of the last update to the device record';

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_devices_user_id ON public.devices(user_id);
CREATE INDEX IF NOT EXISTS idx_devices_status ON public.devices(status);
CREATE INDEX IF NOT EXISTS idx_devices_type ON public.devices(type);

-- 4. Create a trigger to automatically update 'updated_at'
CREATE OR REPLACE FUNCTION public.handle_devices_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS on_devices_updated ON public.devices;
CREATE TRIGGER on_devices_updated
BEFORE UPDATE ON public.devices
FOR EACH ROW
EXECUTE FUNCTION public.handle_devices_updated_at();

-- 5. Enable RLS on the 'devices' table
ALTER TABLE public.devices ENABLE ROW LEVEL SECURITY;

-- 6. RLS Policies for 'devices' table:

-- Users can view their own devices
DROP POLICY IF EXISTS "Users can view their own devices" ON public.devices;
CREATE POLICY "Users can view their own devices"
ON public.devices FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Users can insert their own devices
DROP POLICY IF EXISTS "Users can insert their own devices" ON public.devices;
CREATE POLICY "Users can insert their own devices"
ON public.devices FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Users can update their own devices
DROP POLICY IF EXISTS "Users can update their own devices" ON public.devices;
CREATE POLICY "Users can update their own devices"
ON public.devices FOR UPDATE
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Users can delete their own devices
DROP POLICY IF EXISTS "Users can delete their own devices" ON public.devices;
CREATE POLICY "Users can delete their own devices"
ON public.devices FOR DELETE
TO authenticated
USING (auth.uid() = user_id);

-- 7. Admin access policies (for super admins)
DROP POLICY IF EXISTS "Admins can manage all devices" ON public.devices;
CREATE POLICY "Admins can manage all devices"
ON public.devices FOR ALL
TO authenticated
USING ( 
    EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() 
        AND is_super_admin = TRUE
    )
)
WITH CHECK ( 
    EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() 
        AND is_super_admin = TRUE
    )
);

-- 8. Verification: Check that the table and policies were created successfully
SELECT 
    'Devices table created successfully' as status,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies 
WHERE tablename = 'devices' 
AND schemaname = 'public'
ORDER BY policyname;

-- 9. Show table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'devices' 
AND table_schema = 'public'
ORDER BY ordinal_position;