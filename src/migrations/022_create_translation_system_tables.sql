-- Migration: Create Translation System Tables
-- Description: Tables for automated translation logging, cost tracking, and quality management

-- Translation logs table
CREATE TABLE IF NOT EXISTS translation_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  source_text TEXT NOT NULL,
  translated_text TEXT NOT NULL,
  source_language VARCHAR(10) NOT NULL,
  target_language VARCHAR(10) NOT NULL,
  provider VARCHAR(20) NOT NULL CHECK (provider IN ('google', 'deepl', 'openai', 'azure')),
  confidence DECIMAL(3,2) DEFAULT 0.0,
  cost DECIMAL(10,6) DEFAULT 0.0,
  characters_used INTEGER DEFAULT 0,
  context VARCHAR(50),
  domain VARCHAR(50),
  translation_key TEXT, -- The actual key being translated (e.g., 'quickActions')
  batch_id UUID, -- For grouping batch translations
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Translation batch logs table
CREATE TABLE IF NOT EXISTS translation_batch_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  provider VARCHAR(20) NOT NULL,
  request_count INTEGER NOT NULL DEFAULT 0,
  total_cost DECIMAL(10,6) DEFAULT 0.0,
  total_characters INTEGER DEFAULT 0,
  average_confidence DECIMAL(3,2) DEFAULT 0.0,
  status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Translation quality reviews table
CREATE TABLE IF NOT EXISTS translation_quality_reviews (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  translation_log_id UUID REFERENCES translation_logs(id) ON DELETE CASCADE,
  reviewer_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  quality_score INTEGER CHECK (quality_score >= 1 AND quality_score <= 5),
  review_notes TEXT,
  approved BOOLEAN DEFAULT FALSE,
  suggested_translation TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Translation provider configurations table
CREATE TABLE IF NOT EXISTS translation_provider_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  provider VARCHAR(20) NOT NULL UNIQUE CHECK (provider IN ('google', 'deepl', 'openai', 'azure')),
  is_enabled BOOLEAN DEFAULT FALSE,
  api_key_configured BOOLEAN DEFAULT FALSE,
  max_requests_per_minute INTEGER DEFAULT 60,
  max_characters_per_request INTEGER DEFAULT 5000,
  cost_per_character DECIMAL(10,8) DEFAULT 0.00002,
  priority INTEGER DEFAULT 1, -- Lower number = higher priority
  capabilities JSONB DEFAULT '{}',
  last_tested_at TIMESTAMP WITH TIME ZONE,
  test_status VARCHAR(20) DEFAULT 'unknown' CHECK (test_status IN ('unknown', 'success', 'failed')),
  test_error TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Translation usage statistics table
CREATE TABLE IF NOT EXISTS translation_usage_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  provider VARCHAR(20) NOT NULL,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  request_count INTEGER DEFAULT 0,
  character_count INTEGER DEFAULT 0,
  total_cost DECIMAL(10,6) DEFAULT 0.0,
  average_confidence DECIMAL(3,2) DEFAULT 0.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, provider, date)
);

-- Translation cost budgets table
CREATE TABLE IF NOT EXISTS translation_cost_budgets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  monthly_budget DECIMAL(10,2) DEFAULT 10.00,
  current_month_spending DECIMAL(10,6) DEFAULT 0.0,
  budget_period_start DATE DEFAULT DATE_TRUNC('month', CURRENT_DATE),
  budget_period_end DATE DEFAULT (DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day'),
  alert_threshold DECIMAL(3,2) DEFAULT 0.8, -- Alert at 80% of budget
  budget_exceeded BOOLEAN DEFAULT FALSE,
  last_alert_sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, budget_period_start)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_translation_logs_user_id ON translation_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_translation_logs_provider ON translation_logs(provider);
CREATE INDEX IF NOT EXISTS idx_translation_logs_created_at ON translation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_translation_logs_batch_id ON translation_logs(batch_id);
CREATE INDEX IF NOT EXISTS idx_translation_logs_translation_key ON translation_logs(translation_key);

CREATE INDEX IF NOT EXISTS idx_translation_batch_logs_user_id ON translation_batch_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_translation_batch_logs_provider ON translation_batch_logs(provider);
CREATE INDEX IF NOT EXISTS idx_translation_batch_logs_created_at ON translation_batch_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_translation_quality_reviews_translation_log_id ON translation_quality_reviews(translation_log_id);
CREATE INDEX IF NOT EXISTS idx_translation_quality_reviews_reviewer_id ON translation_quality_reviews(reviewer_id);

CREATE INDEX IF NOT EXISTS idx_translation_usage_stats_user_id ON translation_usage_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_translation_usage_stats_date ON translation_usage_stats(date);
CREATE INDEX IF NOT EXISTS idx_translation_usage_stats_provider ON translation_usage_stats(provider);

CREATE INDEX IF NOT EXISTS idx_translation_cost_budgets_user_id ON translation_cost_budgets(user_id);
CREATE INDEX IF NOT EXISTS idx_translation_cost_budgets_period ON translation_cost_budgets(budget_period_start, budget_period_end);

-- Insert default provider configurations
INSERT INTO translation_provider_configs (provider, priority, max_requests_per_minute, max_characters_per_request, cost_per_character, capabilities)
VALUES 
  ('deepl', 1, 50, 5000, 0.00002, '{"batchTranslation": true, "highQuality": true, "supportedLanguages": ["ar", "fr", "ja", "it", "tr", "nl", "es"]}'),
  ('google', 2, 100, 5000, 0.00002, '{"batchTranslation": true, "languageDetection": true, "supportedLanguages": ["ar", "fr", "ja", "it", "tr", "nl", "es"]}'),
  ('openai', 3, 20, 2000, 0.00005, '{"contextAware": true, "customPrompts": true, "supportedLanguages": ["ar", "fr", "ja", "it", "tr", "nl", "es"]}'),
  ('azure', 4, 60, 10000, 0.00002, '{"batchTranslation": true, "languageDetection": true, "supportedLanguages": ["ar", "fr", "ja", "it", "tr", "nl", "es"]}')
ON CONFLICT (provider) DO NOTHING;

-- Create RLS policies
ALTER TABLE translation_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_batch_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_quality_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_usage_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_cost_budgets ENABLE ROW LEVEL SECURITY;

-- Translation logs policies
CREATE POLICY "Users can view their own translation logs" ON translation_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own translation logs" ON translation_logs
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own translation logs" ON translation_logs
  FOR UPDATE USING (auth.uid() = user_id);

-- Translation batch logs policies
CREATE POLICY "Users can view their own batch logs" ON translation_batch_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own batch logs" ON translation_batch_logs
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own batch logs" ON translation_batch_logs
  FOR UPDATE USING (auth.uid() = user_id);

-- Translation quality reviews policies
CREATE POLICY "Users can view quality reviews for their translations" ON translation_quality_reviews
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM translation_logs tl 
      WHERE tl.id = translation_log_id AND tl.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create quality reviews" ON translation_quality_reviews
  FOR INSERT WITH CHECK (auth.uid() = reviewer_id);

CREATE POLICY "Reviewers can update their own reviews" ON translation_quality_reviews
  FOR UPDATE USING (auth.uid() = reviewer_id);

-- Translation usage stats policies
CREATE POLICY "Users can view their own usage stats" ON translation_usage_stats
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own usage stats" ON translation_usage_stats
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own usage stats" ON translation_usage_stats
  FOR UPDATE USING (auth.uid() = user_id);

-- Translation cost budgets policies
CREATE POLICY "Users can view their own budgets" ON translation_cost_budgets
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own budgets" ON translation_cost_budgets
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own budgets" ON translation_cost_budgets
  FOR UPDATE USING (auth.uid() = user_id);

-- Translation provider configs are public read-only
ALTER TABLE translation_provider_configs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view provider configs" ON translation_provider_configs
  FOR SELECT USING (true);

-- Only authenticated users can modify provider configs (admin check can be added later)
CREATE POLICY "Authenticated users can modify provider configs" ON translation_provider_configs
  FOR ALL USING (auth.uid() IS NOT NULL);

-- Create functions for translation management

-- Function to get translation secrets (for edge functions)
CREATE OR REPLACE FUNCTION get_translation_secrets()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- This function would typically access a secure secrets store
  -- For now, it returns a placeholder structure
  RETURN jsonb_build_object(
    'google_translate_api_key', NULL,
    'deepl_api_key', NULL,
    'openai_api_key', NULL,
    'azure_api_key', NULL
  );
END;
$$;

-- Function to update usage statistics
CREATE OR REPLACE FUNCTION update_translation_usage_stats(
  p_user_id UUID,
  p_provider VARCHAR(20),
  p_character_count INTEGER,
  p_cost DECIMAL(10,6),
  p_confidence DECIMAL(3,2)
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO translation_usage_stats (
    user_id, provider, date, request_count, character_count, total_cost, average_confidence
  )
  VALUES (
    p_user_id, p_provider, CURRENT_DATE, 1, p_character_count, p_cost, p_confidence
  )
  ON CONFLICT (user_id, provider, date)
  DO UPDATE SET
    request_count = translation_usage_stats.request_count + 1,
    character_count = translation_usage_stats.character_count + p_character_count,
    total_cost = translation_usage_stats.total_cost + p_cost,
    average_confidence = (
      (translation_usage_stats.average_confidence * translation_usage_stats.request_count + p_confidence) /
      (translation_usage_stats.request_count + 1)
    ),
    updated_at = NOW();
END;
$$;

-- Function to check and update budget status
CREATE OR REPLACE FUNCTION check_translation_budget(
  p_user_id UUID,
  p_cost DECIMAL(10,6)
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_budget RECORD;
  v_new_spending DECIMAL(10,6);
  v_budget_percentage DECIMAL(5,2);
  v_result JSONB;
BEGIN
  -- Get or create current month's budget
  INSERT INTO translation_cost_budgets (user_id)
  VALUES (p_user_id)
  ON CONFLICT (user_id, budget_period_start) DO NOTHING;
  
  -- Get current budget info
  SELECT * INTO v_budget
  FROM translation_cost_budgets
  WHERE user_id = p_user_id
    AND budget_period_start = DATE_TRUNC('month', CURRENT_DATE);
  
  -- Calculate new spending
  v_new_spending := v_budget.current_month_spending + p_cost;
  v_budget_percentage := (v_new_spending / v_budget.monthly_budget) * 100;
  
  -- Update spending
  UPDATE translation_cost_budgets
  SET 
    current_month_spending = v_new_spending,
    budget_exceeded = (v_new_spending > monthly_budget),
    updated_at = NOW()
  WHERE id = v_budget.id;
  
  -- Return budget status
  v_result := jsonb_build_object(
    'withinBudget', v_new_spending <= v_budget.monthly_budget,
    'budgetPercentage', v_budget_percentage,
    'remainingBudget', GREATEST(0, v_budget.monthly_budget - v_new_spending),
    'shouldAlert', v_budget_percentage >= (v_budget.alert_threshold * 100)
  );
  
  RETURN v_result;
END;
$$;

-- Function to get translation analytics
CREATE OR REPLACE FUNCTION get_translation_analytics(
  p_user_id UUID,
  p_days INTEGER DEFAULT 30
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_result JSONB;
BEGIN
  WITH analytics AS (
    SELECT 
      COUNT(*) as total_translations,
      SUM(character_count) as total_characters,
      SUM(total_cost) as total_cost,
      AVG(average_confidence) as avg_confidence,
      jsonb_object_agg(provider, jsonb_build_object(
        'requests', request_count,
        'characters', character_count,
        'cost', total_cost,
        'confidence', average_confidence
      )) as by_provider
    FROM translation_usage_stats
    WHERE user_id = p_user_id
      AND date >= CURRENT_DATE - INTERVAL '1 day' * p_days
  )
  SELECT jsonb_build_object(
    'totalTranslations', COALESCE(total_translations, 0),
    'totalCharacters', COALESCE(total_characters, 0),
    'totalCost', COALESCE(total_cost, 0),
    'averageConfidence', COALESCE(avg_confidence, 0),
    'byProvider', COALESCE(by_provider, '{}'),
    'period', p_days || ' days'
  ) INTO v_result
  FROM analytics;
  
  RETURN COALESCE(v_result, '{"totalTranslations": 0, "totalCharacters": 0, "totalCost": 0, "averageConfidence": 0, "byProvider": {}}');
END;
$$;

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_translation_logs_updated_at BEFORE UPDATE ON translation_logs
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_translation_quality_reviews_updated_at BEFORE UPDATE ON translation_quality_reviews
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_translation_provider_configs_updated_at BEFORE UPDATE ON translation_provider_configs
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_translation_usage_stats_updated_at BEFORE UPDATE ON translation_usage_stats
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_translation_cost_budgets_updated_at BEFORE UPDATE ON translation_cost_budgets
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;