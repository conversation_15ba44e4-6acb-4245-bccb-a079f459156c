-- Environmental Analysis Tables for Phase 3
-- Advanced Environmental Monitoring and Predictive Insights

-- Environmental Data Collection Table
CREATE TABLE IF NOT EXISTS environmental_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  
  -- Weather Data
  temperature_celsius DECIMAL(5,2),
  humidity_percentage DECIMAL(5,2),
  barometric_pressure_hpa DECIMAL(7,2),
  wind_speed_kmh DECIMAL(5,2),
  wind_direction_degrees INTEGER,
  precipitation_mm DECIMAL(5,2),
  uv_index DECIMAL(3,1),
  air_quality_index INTEGER,
  
  -- Indoor Environment
  indoor_temperature_celsius DECIMAL(5,2),
  indoor_humidity_percentage DECIMAL(5,2),
  indoor_air_quality_ppm DECIMAL(8,2),
  noise_level_db DECIMAL(5,2),
  light_intensity_lux DECIMAL(8,2),
  co2_level_ppm DECIMAL(8,2),
  
  -- Location Context
  location_name VARCHAR(100),
  altitude_meters DECIMAL(8,2),
  latitude DECIMAL(10,8),
  longitude DECIMAL(11,8),
  timezone VARCHAR(50),
  
  -- Data Quality
  data_source VARCHAR(50) NOT NULL, -- 'weather_api', 'iot_sensor', 'manual_entry'
  confidence_score DECIMAL(3,2) DEFAULT 1.0,
  
  recorded_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Environmental Impact Analysis Table
CREATE TABLE IF NOT EXISTS environmental_impact_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  analysis_period_start TIMESTAMPTZ NOT NULL,
  analysis_period_end TIMESTAMPTZ NOT NULL,
  
  -- Weather Impact Scores (0-100)
  temperature_impact_score INTEGER,
  humidity_impact_score INTEGER,
  pressure_impact_score INTEGER,
  air_quality_impact_score INTEGER,
  
  -- Seasonal Analysis
  seasonal_pattern_detected BOOLEAN DEFAULT FALSE,
  seasonal_health_trend VARCHAR(20), -- 'improving', 'declining', 'stable'
  seasonal_risk_factors JSONB,
  
  -- Location-Based Insights
  location_health_correlation DECIMAL(3,2),
  optimal_environment_suggestions JSONB,
  environmental_stress_indicators JSONB,
  
  -- Overall Environmental Health Score
  environmental_health_score INTEGER NOT NULL,
  environmental_risk_level VARCHAR(20) NOT NULL, -- 'low', 'moderate', 'high', 'critical'
  
  -- AI Analysis Metadata
  analysis_model_version VARCHAR(20),
  confidence_level DECIMAL(3,2),
  data_points_analyzed INTEGER,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Predictive Health Insights Table
CREATE TABLE IF NOT EXISTS predictive_health_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  
  -- Prediction Timeframe
  prediction_date TIMESTAMPTZ NOT NULL,
  prediction_horizon_days INTEGER NOT NULL, -- 7, 30, 90, 365
  
  -- Health Predictions
  predicted_health_score INTEGER,
  predicted_weight_kg DECIMAL(5,2),
  predicted_activity_level VARCHAR(20),
  predicted_stress_level VARCHAR(20),
  predicted_sleep_quality INTEGER,
  
  -- Risk Predictions
  disease_risk_probability DECIMAL(3,2),
  injury_risk_probability DECIMAL(3,2),
  behavioral_issue_risk DECIMAL(3,2),
  environmental_stress_risk DECIMAL(3,2),
  
  -- Specific Health Predictions
  vital_signs_forecast JSONB, -- Heart rate, temperature, respiratory rate predictions
  weight_trend_forecast JSONB,
  activity_pattern_forecast JSONB,
  
  -- Confidence and Accuracy
  prediction_confidence DECIMAL(3,2),
  model_accuracy_score DECIMAL(3,2),
  contributing_factors JSONB,
  
  -- Recommendations
  preventive_actions JSONB,
  environmental_adjustments JSONB,
  care_schedule_recommendations JSONB,
  
  -- Model Metadata
  prediction_model_version VARCHAR(20),
  training_data_period_days INTEGER,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Environmental Optimization Recommendations Table
CREATE TABLE IF NOT EXISTS environmental_optimization (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  
  -- Optimization Category
  optimization_type VARCHAR(50) NOT NULL, -- 'temperature', 'humidity', 'lighting', 'noise', 'air_quality'
  priority_level VARCHAR(20) NOT NULL, -- 'low', 'medium', 'high', 'critical'
  
  -- Current vs Optimal Conditions
  current_value DECIMAL(8,2),
  optimal_value_min DECIMAL(8,2),
  optimal_value_max DECIMAL(8,2),
  improvement_potential_score INTEGER, -- 0-100
  
  -- Recommendations
  recommendation_title VARCHAR(200) NOT NULL,
  recommendation_description TEXT,
  implementation_difficulty VARCHAR(20), -- 'easy', 'moderate', 'difficult'
  estimated_cost_category VARCHAR(20), -- 'free', 'low', 'medium', 'high'
  expected_health_improvement INTEGER, -- 0-100
  
  -- Implementation Details
  implementation_steps JSONB,
  required_equipment JSONB,
  monitoring_requirements JSONB,
  
  -- Tracking
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'in_progress', 'completed', 'dismissed'
  implemented_at TIMESTAMPTZ,
  effectiveness_score INTEGER, -- Post-implementation effectiveness (0-100)
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Predictive Alerts Table
CREATE TABLE IF NOT EXISTS predictive_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  animal_id UUID NOT NULL REFERENCES animals(id) ON DELETE CASCADE,
  
  -- Alert Details
  alert_type VARCHAR(50) NOT NULL, -- 'health_decline', 'disease_risk', 'environmental_stress', 'behavioral_change'
  severity_level VARCHAR(20) NOT NULL, -- 'info', 'warning', 'critical', 'emergency'
  
  -- Prediction Details
  predicted_event VARCHAR(100) NOT NULL,
  probability_percentage DECIMAL(5,2) NOT NULL,
  predicted_timeframe_days INTEGER,
  confidence_level DECIMAL(3,2),
  
  -- Alert Content
  alert_title VARCHAR(200) NOT NULL,
  alert_message TEXT NOT NULL,
  recommended_actions JSONB,
  
  -- Contributing Factors
  environmental_factors JSONB,
  health_indicators JSONB,
  behavioral_patterns JSONB,
  
  -- Alert Management
  status VARCHAR(20) DEFAULT 'active', -- 'active', 'acknowledged', 'resolved', 'dismissed'
  acknowledged_at TIMESTAMPTZ,
  resolved_at TIMESTAMPTZ,
  
  -- Follow-up
  follow_up_required BOOLEAN DEFAULT FALSE,
  follow_up_date TIMESTAMPTZ,
  outcome_recorded BOOLEAN DEFAULT FALSE,
  actual_outcome VARCHAR(100),
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_environmental_data_animal_recorded ON environmental_data(animal_id, recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_environmental_data_location ON environmental_data(location_name, recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_environmental_impact_animal_period ON environmental_impact_analysis(animal_id, analysis_period_start, analysis_period_end);
CREATE INDEX IF NOT EXISTS idx_predictive_insights_animal_date ON predictive_health_insights(animal_id, prediction_date DESC);
CREATE INDEX IF NOT EXISTS idx_predictive_insights_horizon ON predictive_health_insights(prediction_horizon_days, prediction_date DESC);
CREATE INDEX IF NOT EXISTS idx_environmental_optimization_animal_priority ON environmental_optimization(animal_id, priority_level, status);
CREATE INDEX IF NOT EXISTS idx_predictive_alerts_animal_status ON predictive_alerts(animal_id, status, severity_level);
CREATE INDEX IF NOT EXISTS idx_predictive_alerts_timeframe ON predictive_alerts(predicted_timeframe_days, created_at DESC);

-- Row Level Security (RLS)
ALTER TABLE environmental_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE environmental_impact_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE predictive_health_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE environmental_optimization ENABLE ROW LEVEL SECURITY;
ALTER TABLE predictive_alerts ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own environmental data" ON environmental_data
  FOR SELECT USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert their own environmental data" ON environmental_data
  FOR INSERT WITH CHECK (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view their own environmental impact analysis" ON environmental_impact_analysis
  FOR SELECT USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view their own predictive insights" ON predictive_health_insights
  FOR SELECT USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view their own environmental optimization" ON environmental_optimization
  FOR SELECT USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own environmental optimization" ON environmental_optimization
  FOR UPDATE USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view their own predictive alerts" ON predictive_alerts
  FOR SELECT USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own predictive alerts" ON predictive_alerts
  FOR UPDATE USING (
    animal_id IN (
      SELECT id FROM animals WHERE user_id = auth.uid()
    )
  );