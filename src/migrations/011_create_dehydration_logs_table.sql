-- Migration: Create dehydration_logs table for MAX30009 sensor data
-- Description: Stores bioimpedance sensor readings for animal hydration monitoring

-- Create dehydration_logs table
CREATE TABLE IF NOT EXISTS public.dehydration_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    animal_id UUID NOT NULL REFERENCES public.animals(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Hydration metrics
    hydration_level DECIMAL(5,2) NOT NULL CHECK (hydration_level >= 0 AND hydration_level <= 100),
    hydration_status TEXT NOT NULL CHECK (hydration_status IN ('optimal', 'mild_dehydration', 'moderate_dehydration', 'severe_dehydration')),
    
    -- Raw sensor data from MAX30009
    bioimpedance_reading DECIMAL(10,4) NOT NULL, -- Raw bioimpedance value in ohms
    skin_conductance DECIMAL(8,4), -- Skin conductance in microsiemens
    body_temperature DECIMAL(4,2), -- Body temperature in Celsius
    ambient_temperature DECIMAL(4,2), -- Environmental temperature
    humidity_level DECIMAL(5,2), -- Environmental humidity percentage
    
    -- Device and measurement info
    device_id TEXT, -- ID of the measuring device
    measurement_duration INTEGER DEFAULT 30, -- Duration of measurement in seconds
    signal_quality TEXT CHECK (signal_quality IN ('excellent', 'good', 'fair', 'poor')),
    
    -- Metadata
    notes TEXT,
    is_manual_entry BOOLEAN DEFAULT FALSE, -- True if manually entered, false if from device
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_dehydration_logs_animal_id ON public.dehydration_logs(animal_id);
CREATE INDEX idx_dehydration_logs_user_id ON public.dehydration_logs(user_id);
CREATE INDEX idx_dehydration_logs_created_at ON public.dehydration_logs(created_at DESC);
CREATE INDEX idx_dehydration_logs_hydration_status ON public.dehydration_logs(hydration_status);
CREATE INDEX idx_dehydration_logs_animal_created ON public.dehydration_logs(animal_id, created_at DESC);

-- Enable Row Level Security
ALTER TABLE public.dehydration_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users can view their own animals' dehydration logs
CREATE POLICY "Users can view own animals dehydration logs" ON public.dehydration_logs
    FOR SELECT USING (
        user_id = auth.uid() OR 
        animal_id IN (
            SELECT id FROM public.animals WHERE user_id = auth.uid()
        )
    );

-- Users can insert dehydration logs for their own animals
CREATE POLICY "Users can insert dehydration logs for own animals" ON public.dehydration_logs
    FOR INSERT WITH CHECK (
        user_id = auth.uid() AND
        animal_id IN (
            SELECT id FROM public.animals WHERE user_id = auth.uid()
        )
    );

-- Users can update their own dehydration logs
CREATE POLICY "Users can update own dehydration logs" ON public.dehydration_logs
    FOR UPDATE USING (
        user_id = auth.uid() OR 
        animal_id IN (
            SELECT id FROM public.animals WHERE user_id = auth.uid()
        )
    ) WITH CHECK (
        user_id = auth.uid() AND
        animal_id IN (
            SELECT id FROM public.animals WHERE user_id = auth.uid()
        )
    );

-- Users can delete their own dehydration logs
CREATE POLICY "Users can delete own dehydration logs" ON public.dehydration_logs
    FOR DELETE USING (
        user_id = auth.uid() OR 
        animal_id IN (
            SELECT id FROM public.animals WHERE user_id = auth.uid()
        )
    );

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_dehydration_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_dehydration_logs_updated_at
    BEFORE UPDATE ON public.dehydration_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_dehydration_logs_updated_at();

-- Create function to calculate hydration status based on bioimpedance
CREATE OR REPLACE FUNCTION calculate_hydration_status(bioimpedance_value DECIMAL)
RETURNS TEXT AS $$
BEGIN
    -- Bioimpedance thresholds for horses (example values - would need calibration)
    -- Higher bioimpedance = lower hydration
    IF bioimpedance_value <= 300 THEN
        RETURN 'optimal';
    ELSIF bioimpedance_value <= 400 THEN
        RETURN 'mild_dehydration';
    ELSIF bioimpedance_value <= 500 THEN
        RETURN 'moderate_dehydration';
    ELSE
        RETURN 'severe_dehydration';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to calculate hydration percentage
CREATE OR REPLACE FUNCTION calculate_hydration_level(bioimpedance_value DECIMAL)
RETURNS DECIMAL AS $$
BEGIN
    -- Convert bioimpedance to hydration percentage (inverse relationship)
    -- This is a simplified calculation - real implementation would use calibrated curves
    RETURN GREATEST(0, LEAST(100, 100 - ((bioimpedance_value - 200) / 4)));
END;
$$ LANGUAGE plpgsql;

-- Add helpful comments
COMMENT ON TABLE public.dehydration_logs IS 'Stores bioimpedance sensor readings for animal hydration monitoring using MAX30009 sensor';
COMMENT ON COLUMN public.dehydration_logs.hydration_level IS 'Calculated hydration percentage (0-100%)';
COMMENT ON COLUMN public.dehydration_logs.bioimpedance_reading IS 'Raw bioimpedance measurement from MAX30009 sensor in ohms';
COMMENT ON COLUMN public.dehydration_logs.hydration_status IS 'Categorized hydration status based on bioimpedance reading';
COMMENT ON COLUMN public.dehydration_logs.signal_quality IS 'Quality of the sensor reading (excellent/good/fair/poor)';

-- Insert some sample data for testing
INSERT INTO public.dehydration_logs (
    animal_id, 
    user_id, 
    hydration_level, 
    hydration_status, 
    bioimpedance_reading, 
    skin_conductance, 
    body_temperature, 
    ambient_temperature, 
    humidity_level, 
    device_id, 
    signal_quality, 
    notes, 
    is_manual_entry
) 
SELECT 
    a.id,
    a.user_id,
    calculate_hydration_level(350 + (random() * 200)::DECIMAL), -- Random bioimpedance 350-550
    calculate_hydration_status(350 + (random() * 200)::DECIMAL),
    350 + (random() * 200)::DECIMAL, -- Random bioimpedance reading
    15 + (random() * 10)::DECIMAL, -- Random skin conductance
    37.5 + (random() * 2)::DECIMAL, -- Random body temp 37.5-39.5°C
    20 + (random() * 15)::DECIMAL, -- Random ambient temp 20-35°C
    40 + (random() * 40)::DECIMAL, -- Random humidity 40-80%
    'MAX30009-' || LPAD((random() * 9999)::INTEGER::TEXT, 4, '0'),
    CASE 
        WHEN random() < 0.6 THEN 'excellent'
        WHEN random() < 0.8 THEN 'good'
        WHEN random() < 0.95 THEN 'fair'
        ELSE 'poor'
    END,
    CASE 
        WHEN random() < 0.3 THEN 'Routine hydration check'
        WHEN random() < 0.6 THEN 'Post-exercise measurement'
        ELSE NULL
    END,
    random() < 0.1 -- 10% chance of manual entry
FROM public.animals a
WHERE a.id IN (SELECT id FROM public.animals LIMIT 5) -- Sample data for first 5 animals
AND random() < 0.8; -- 80% chance per animal