-- Purpose: Implement comprehensive Row Level Security (RLS) policies for animals and feeding_schedules tables
-- This ensures complete data security with user isolation and admin oversight capabilities

-- =====================================================
-- PHASE 1: ANIMALS TABLE RLS ENHANCEMENTS
-- =====================================================

-- Note: animals table already has a trigger 'set_user_id_on_animal_insert' that sets user_id = auth.uid()
-- Therefore, we do NOT add DEFAULT auth.uid() to avoid conflicts with the existing trigger

-- Drop and recreate User INSERT policy for animals with explicit WITH CHECK
DROP POLICY IF EXISTS "Users can insert their own animals" ON public.animals;
CREATE POLICY "Users can insert their own animals"
ON public.animals FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Add Admin UPDATE policy for animals (missing from current setup)
DROP POLICY IF EXISTS "Admins can update all animal records" ON public.animals;
CREATE POLICY "Admins can update all animal records"
ON public.animals FOR UPDATE
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- Add Admin DELETE policy for animals (missing from current setup)
DROP POLICY IF EXISTS "Admins can delete all animal records" ON public.animals;
CREATE POLICY "Admins can delete all animal records"
ON public.animals FOR DELETE
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- =====================================================
-- PHASE 2: FEEDING_SCHEDULES TABLE COMPREHENSIVE RLS
-- =====================================================

-- Set default user_id for feeding_schedules table (simplifies client-side code)
ALTER TABLE public.feeding_schedules ALTER COLUMN user_id SET DEFAULT auth.uid();

-- Enable Row Level Security on feeding_schedules table (if not already enabled)
ALTER TABLE public.feeding_schedules ENABLE ROW LEVEL SECURITY;

-- Drop existing policies for feeding_schedules table to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own feeding schedules" ON public.feeding_schedules;
DROP POLICY IF EXISTS "Users can insert their own feeding schedules" ON public.feeding_schedules;
DROP POLICY IF EXISTS "Users can update their own feeding schedules" ON public.feeding_schedules;
DROP POLICY IF EXISTS "Users can delete their own feeding schedules" ON public.feeding_schedules;
DROP POLICY IF EXISTS "Admins can update all feeding schedules" ON public.feeding_schedules;
DROP POLICY IF EXISTS "Admins can delete all feeding schedules" ON public.feeding_schedules;

-- User SELECT policy for feeding_schedules
CREATE POLICY "Users can view their own feeding schedules"
ON public.feeding_schedules FOR SELECT
USING (auth.uid() = user_id);

-- User INSERT policy for feeding_schedules
CREATE POLICY "Users can insert their own feeding schedules"
ON public.feeding_schedules FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- User UPDATE policy for feeding_schedules
CREATE POLICY "Users can update their own feeding schedules"
ON public.feeding_schedules FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- User DELETE policy for feeding_schedules
CREATE POLICY "Users can delete their own feeding schedules"
ON public.feeding_schedules FOR DELETE
USING (auth.uid() = user_id);

-- Admin UPDATE policy for feeding_schedules
-- Note: Admin SELECT policy "Admins can view all feeding schedules" already exists
CREATE POLICY "Admins can update all feeding schedules"
ON public.feeding_schedules FOR UPDATE
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- Admin DELETE policy for feeding_schedules
CREATE POLICY "Admins can delete all feeding schedules"
ON public.feeding_schedules FOR DELETE
USING (
  (SELECT is_super_admin FROM public.users WHERE id = auth.uid()) = true
);

-- =====================================================
-- PHASE 3: VERIFICATION QUERIES
-- =====================================================

-- Verify RLS is enabled on both tables
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('animals', 'feeding_schedules') 
AND schemaname = 'public'
ORDER BY tablename;

-- Verify policies are created for animals table
SELECT schemaname, tablename, policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'animals' 
AND schemaname = 'public'
ORDER BY policyname;

-- Verify policies are created for feeding_schedules table
SELECT schemaname, tablename, policyname, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'feeding_schedules' 
AND schemaname = 'public'
ORDER BY policyname;

-- =====================================================
-- SECURITY IMPLEMENTATION COMPLETE
-- =====================================================

-- Summary of Security Enhancements:
-- 
-- ANIMALS TABLE:
-- ✅ Enhanced INSERT policy with explicit WITH CHECK
-- ✅ Added admin UPDATE policy for management capabilities
-- ✅ Added admin DELETE policy for management capabilities
-- ✅ Preserved existing trigger for user_id assignment
-- ✅ All existing user policies remain intact
-- 
-- FEEDING_SCHEDULES TABLE:
-- ✅ RLS enabled with comprehensive policy coverage
-- ✅ Default user_id set to auth.uid() for simplified client code
-- ✅ Users can only SELECT their own records (auth.uid() = user_id)
-- ✅ Users can only INSERT records with their own user_id
-- ✅ Users can only UPDATE their own records
-- ✅ Users can only DELETE their own records
-- ✅ Super admins can view all records for oversight (existing policy preserved)
-- ✅ Super admins can update all records for management
-- ✅ Super admins can delete all records for management
-- ✅ Data isolation and security at database level