{"name": "hoofbeat", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~50.0.4", "expo-status-bar": "*", "expo-linear-gradient": "*", "@expo/vector-icons": "*", "@react-navigation/native": "*", "@react-navigation/native-stack": "*", "react-native-safe-area-context": "*", "react-native-screens": "*", "@react-navigation/bottom-tabs": "*", "expo-camera": "*", "react-native-reanimated": "*", "expo-clipboard": "*", "@shopify/flash-list": "*", "expo-av": "*", "react-native-gesture-handler": "*", "lucide-react-native": "*", "react-native-svg": "15.11.2", "@react-native-async-storage/async-storage": "*", "@supabase/supabase-js": "*", "react-hook-form": "*", "sonner-native": "*", "expo-auth-session": "*", "expo-crypto": "*", "expo-haptics": "*", "@react-navigation/drawer": "*", "@react-native-community/slider": "*", "expo-web-browser": "*", "expo-linking": "*", "expo-constants": "*", "expo-image-picker": "*", "@react-native-community/datetimepicker": "*", "expo-sensors": "*", "@react-native-community/netinfo": "*", "expo-audio": "*", "expo-blur": "*", "expo-file-system": "*", "expo-image-manipulator": "*", "expo-location": "*", "zustand": "*", "date-fns": "*", "react-native-webview": "*"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "typescript": "^5.1.3"}, "private": true}