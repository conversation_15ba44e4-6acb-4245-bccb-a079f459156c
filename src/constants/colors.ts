
// Color palette for the HoofBeat app
export const COLORS = {
  // Primary colors
  primary: '#3D8C91', // Teal
  primaryLight: '#B4D6D9',
  primaryDark: '#2A6165',
  
  // Secondary colors
  secondary: '#E07A5F', // Terracotta
  secondaryLight: '#F2C4B6',
  secondaryDark: '#B85A42',
  
  // Accent colors
  accent1: '#F9C74F', // Golden yellow
  accent2: '#90BE6D', // Sage green
  accent3: '#577590', // Steel blue
  
  // Neutrals - Light mode
  background: '#F8F9FA',
  card: '#FFFFFF',
  text: '#2D3436',
  textSecondary: '#636E72',
  textLight: '#636E72', // Alias for backward compatibility
  border: '#E2E8F0',
  
  // Feedback colors
  error: '#E53E3E',
  success: '#38A169',
  warning: '#F6AD55',
  info: '#4299E1',
  
  // Common colors
  white: '#FFFFFF',
  black: '#000000',
};

// Dark mode variants
export const DARK_COLORS = {
  // Primary colors remain the same for brand consistency
  primary: '#3D8C91',
  primaryLight: '#2A6165',
  primaryDark: '#B4D6D9',
  
  // Secondary colors
  secondary: '#E07A5F',
  secondaryLight: '#B85A42',
  secondaryDark: '#F2C4B6',
  
  // Accent colors
  accent1: '#F9C74F',
  accent2: '#90BE6D',
  accent3: '#577590',
  
  // Neutrals - Dark mode
  background: '#1A202C',
  card: '#2D3748',
  text: '#F7FAFC',
  textSecondary: '#A0AEC0',
  textLight: '#A0AEC0', // Alias for backward compatibility
  border: '#4A5568',
  
  // Feedback colors
  error: '#FC8181',
  success: '#68D391',
  warning: '#F6AD55',
  info: '#63B3ED',
  
  // Common colors
  white: '#FFFFFF',
  black: '#000000',
};
