
import React, { createContext, useContext, useState, useEffect } from 'react';
import { Session, User } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../supabase/client';
import { toast } from 'sonner-native';

interface AuthContextType {
  session: Session | null;
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any; requiresMfa?: boolean; challengeId?: string }>;
  signUp: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType>({
  session: null,
  user: null,
  isLoading: true,
  signIn: async () => ({ error: null }),
  signUp: async () => ({ error: null }),
  signOut: async () => {},
  resetPassword: async () => ({ error: null }),
});

export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    // Check for active session on mount
    const getSession = async () => {
      try {
        setIsLoading(true);
        
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
          return;
        }
        
        setSession(data.session);
        setUser(data.session?.user || null);
      } catch (error) {
        console.error('Unexpected error during getSession:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    getSession();
    
    // Subscribe to auth state changes
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        console.log('Auth state changed:', event);
        setSession(newSession);
        setUser(newSession?.user || null);
      }
    );
    
    // Cleanup subscription
    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, []);
  
  const signIn = async (email: string, password: string) => {
    try {
      // First, check if user requires MFA
      const { data: mfaChallenge, error: mfaChallengeError } = await supabase.functions.invoke('mfa-challenge', {
        body: { email }
      });
      
      if (mfaChallengeError) {
        console.error('Error checking MFA status:', mfaChallengeError);
        // Continue with normal login if MFA check fails
      } else if (mfaChallenge.success && mfaChallenge.requires_mfa) {
        // User has MFA enabled, verify password first
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password,
        });
        
        if (signInError) {
          // Password is wrong, record failed attempt
          try {
            await supabase.functions.invoke('auth-security', {
              body: {
                action: 'failed_attempt',
                email: email,
              },
            });
          } catch (functionError) {
            console.error('Error recording failed attempt:', functionError);
          }
          return { error: signInError };
        }
        
        // Password is correct but user needs MFA
        // Sign out immediately and return MFA challenge
        await supabase.auth.signOut();
        
        return { 
          error: null, 
          requiresMfa: true, 
          challengeId: mfaChallenge.challenge_id 
        };
      }
      
      // Normal login flow (no MFA or MFA check failed)
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      // If sign-in is successful, reset login attempts
      if (!signInError && signInData.session) {
        try {
          const { error: resetError } = await supabase.functions.invoke('auth-security', {
            body: {
              action: 'reset_attempts',
              email: email,
            },
          });
          
          if (resetError) {
            console.error('Failed to reset login attempts for user:', email, resetError);
            // Non-critical error, user is already logged in
            // The primary login functionality shouldn't be blocked
          } else {
            console.log('Login attempts reset successfully for user:', email);
          }
        } catch (functionError) {
          console.error('Error invoking auth-security to reset attempts:', functionError);
          // Non-critical error, continue with successful login
        }
      } else if (signInError) {
        // Record failed attempt
        try {
          await supabase.functions.invoke('auth-security', {
            body: {
              action: 'failed_attempt',
              email: email,
            },
          });
        } catch (functionError) {
          console.error('Error recording failed attempt:', functionError);
        }
      }
      
      return { error: signInError };
    } catch (error) {
      console.error('Error signing in:', error);
      return { error };
    }
  };
  
  const signUp = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
      });
      
      return { error };
    } catch (error) {
      console.error('Error signing up:', error);
      return { error };
    }
  };
  
  const signOut = async () => {
    try {
      setIsLoading(true);
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('Error signing out:', error);
        toast.error('Failed to sign out');
        throw error;
      }
      
      // Clear any local storage if needed
      await AsyncStorage.removeItem('hoofbeat-theme-mode');
      
      // Force update state to ensure UI updates
      setSession(null);
      setUser(null);
      
      toast.success('Signed out successfully');
    } catch (error) {
      console.error('Unexpected error during signOut:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'https://hoofbeat.app/reset-password',
      });
      
      return { error };
    } catch (error) {
      console.error('Error resetting password:', error);
      return { error };
    }
  };
  
  const value = {
    session,
    user,
    isLoading,
    signIn,
    signUp,
    signOut,
    resetPassword,
  };
  
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
