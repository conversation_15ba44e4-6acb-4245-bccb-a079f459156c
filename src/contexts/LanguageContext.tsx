import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { I18nManager } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { toast } from 'sonner-native';
import { 
  translations, 
  LanguageCode, 
  TranslationKey, 
  languageInfo,
  isRTLLanguage 
} from '../assets/translations/newIndex';

interface LanguageContextType {
  currentLanguage: LanguageCode;
  setLanguage: (language: LanguageCode) => Promise<void>;
  t: (key: TranslationKey, params?: Record<string, string | number>) => string;
  isRTL: boolean;
  availableLanguages: Array<{
    code: LanguageCode;
    name: string;
    nativeName: string;
    isRTL: boolean;
  }>;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const LANGUAGE_STORAGE_KEY = 'app-language-preference-2b237ff5';
const DEFAULT_LANGUAGE: LanguageCode = 'en';

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>(DEFAULT_LANGUAGE);
  const [isLoading, setIsLoading] = useState(true);
  const [isRTL, setIsRTL] = useState(false);

  // Load saved language preference on app start
  useEffect(() => {
    loadSavedLanguage();
  }, []);

  const loadSavedLanguage = async () => {
    try {
      const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
      if (savedLanguage && savedLanguage in translations) {
        const language = savedLanguage as LanguageCode;
        await applyLanguage(language, false); // Don't show toast on initial load
      }
    } catch (error) {
      console.error('Error loading saved language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const applyLanguage = async (language: LanguageCode, showToast = true) => {
    try {
      const isRTLLang = isRTLLanguage(language);
      
      // Update state
      setCurrentLanguage(language);
      setIsRTL(isRTLLang);
      
      // Apply RTL settings
      if (I18nManager.isRTL !== isRTLLang) {
        I18nManager.allowRTL(isRTLLang);
        I18nManager.forceRTL(isRTLLang);
        
        // Note: For full RTL support, the app may need to be restarted
        // This is a React Native limitation for complete RTL layout changes
      }
      
      // Save to storage
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);
      
      if (showToast) {
        // Use the new language for the success message
        const successMessage = translations[language].languageChanged;
        toast.success(successMessage);
      }
      
    } catch (error) {
      console.error('Error applying language:', error);
      if (showToast) {
        toast.error('Failed to change language');
      }
    }
  };

  const setLanguage = async (language: LanguageCode): Promise<void> => {
    if (language === currentLanguage) {
      return; // No change needed
    }
    
    if (!(language in translations)) {
      console.error(`Unsupported language: ${language}`);
      toast.error('Unsupported language selected');
      return;
    }
    
    await applyLanguage(language, true);
  };

  // Helper function to get nested value from object using dot notation
  const getNestedValue = (obj: any, path: string): string | undefined => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  };

  // Translation function with fallback and nested key support
  const t = (key: TranslationKey, params?: Record<string, string | number>): string => {
    try {
      // Try to get translation for current language (support both flat and nested keys)
      let translation = getNestedValue(translations[currentLanguage], key) || translations[currentLanguage]?.[key];
      
      if (!translation) {
        // Fallback to English if translation not found
        translation = getNestedValue(translations.en, key) || translations.en[key];
        if (translation) {
          console.warn(`Translation missing for key '${key}' in language '${currentLanguage}', using English fallback`);
        }
      }
      
      if (!translation) {
        // Last resort: return the key itself
        console.error(`Translation missing for key '${key}' in both '${currentLanguage}' and 'en'`);
        return key;
      }
      
      // Handle parameter interpolation
      if (params) {
        let result = translation;
        Object.keys(params).forEach(paramKey => {
          const placeholder = `{{${paramKey}}}`;
          result = result.replace(new RegExp(placeholder, 'g'), String(params[paramKey]));
        });
        return result;
      }
      
      return translation;
      
    } catch (error) {
      console.error(`Error getting translation for key '${key}':`, error);
      return key;
    }
  };

  // Get available languages with metadata
  const availableLanguages = Object.entries(languageInfo).map(([code, info]) => ({
    code: code as LanguageCode,
    ...info,
  }));

  const contextValue: LanguageContextType = {
    currentLanguage,
    setLanguage,
    t,
    isRTL,
    availableLanguages,
    isLoading,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use language context
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Helper hook for translation only (lighter alternative)
export const useTranslation = () => {
  const { t, currentLanguage, isRTL } = useLanguage();
  return { t, currentLanguage, isRTL };
};