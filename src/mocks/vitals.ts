
// Mock data for vital records
export interface VitalRecord {
  id: string;
  animalId: string;
  timestamp: string;
  temperature: number;
  heartRate?: number;
  respirationRate?: number;
  weight?: number;
  notes?: string;
  isFromDevice?: boolean;
  isLive?: boolean;
}

export const vitals: VitalRecord[] = [
  {
    id: '1',
    animalId: '1',
    timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    temperature: 37.8,
    heartRate: 42,
    respirationRate: 12,
    weight: 450,
    notes: 'Regular checkup',
    isFromDevice: false,
  },
  {
    id: '2',
    animalId: '1',
    timestamp: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
    temperature: 38.1,
    heartRate: 45,
    respirationRate: 14,
    isFromDevice: true,
  },
  {
    id: '3',
    animalId: '1',
    timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
    temperature: 38.3,
    heartRate: 48,
    respirationRate: 15,
    notes: 'Slightly elevated after exercise',
    isFromDevice: true,
  },
  {
    id: '4',
    animalId: '2',
    timestamp: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    temperature: 37.5,
    heartRate: 38,
    respirationRate: 10,
    weight: 520,
    notes: 'Annual checkup',
    isFromDevice: false,
  },
  {
    id: '5',
    animalId: '3',
    timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
    temperature: 38.0,
    heartRate: 44,
    respirationRate: 13,
    isFromDevice: true,
  },
];
