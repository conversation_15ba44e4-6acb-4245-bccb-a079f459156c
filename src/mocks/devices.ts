
// Mock data for connected devices
export interface Device {
  id: string;
  name: string;
  type: string;
  batteryLevel: number;
  status: 'connected' | 'disconnected' | 'pairing' | 'low_battery';
  lastSyncTime?: number;
}

export const devices: Device[] = [
  {
    id: 'dev1',
    name: 'HoofMonitor Pro',
    type: 'Heart Rate Monitor',
    batteryLevel: 85,
    status: 'connected',
    lastSyncTime: Date.now() - 1800000, // 30 minutes ago
  },
  {
    id: 'dev2',
    name: 'Hoof<PERSON><PERSON><PERSON> Li<PERSON>',
    type: 'Temperature Sensor',
    batteryLevel: 12,
    status: 'low_battery',
    lastSyncTime: Date.now() - 7200000, // 2 hours ago
  },
  {
    id: 'dev3',
    name: 'EquiTrack GPS',
    type: 'Location Tracker',
    batteryLevel: 65,
    status: 'disconnected',
    lastSyncTime: Date.now() - 86400000, // 1 day ago
  },
];
