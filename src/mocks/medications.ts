
// Mock data for medication records
export interface MedicationRecord {
  id: string;
  animalId: string;
  medicationName: string;
  dosage: string;
  dosageUnit: string;
  frequency: string;
  startDate: string;
  endDate?: string;
  time: string;
  notes?: string;
  reminder: boolean;
  reminderTime?: string;
  completed?: boolean;
}

export const medications: MedicationRecord[] = [
  {
    id: '1',
    animalId: '1',
    medicationName: 'Equine Antibiotic',
    dosage: '10',
    dosageUnit: 'ml',
    frequency: 'daily',
    startDate: new Date(Date.now() - 604800000).toISOString(), // 7 days ago
    endDate: new Date(Date.now() + 604800000).toISOString(), // 7 days from now
    time: '08:00',
    notes: 'For minor infection',
    reminder: true,
    reminderTime: '07:45',
    completed: false,
  },
  {
    id: '2',
    animalId: '3',
    medicationName: 'Joint Supplement',
    dosage: '15',
    dosageUnit: 'g',
    frequency: 'daily',
    startDate: new Date(Date.now() - 1209600000).toISOString(), // 14 days ago
    time: '18:00',
    notes: 'Mix with evening feed',
    reminder: true,
    reminderTime: '17:45',
    completed: false,
  },
  {
    id: '3',
    animalId: '2',
    medicationName: 'Parasite Treatment',
    dosage: '20',
    dosageUnit: 'ml',
    frequency: 'once',
    startDate: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    time: '09:00',
    notes: 'Quarterly treatment',
    reminder: false,
    completed: true,
  },
];
