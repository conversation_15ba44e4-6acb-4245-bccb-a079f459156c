
// Mock data for animals
export type AnimalType = 'horse' | 'camel' | 'other';
export type Gender = 'male' | 'female';
export type DeviceStatus = 'connected' | 'disconnected' | 'low_battery';

export interface Animal {
  id: string;
  name: string;
  type: AnimalType;
  breed: string;
  age?: number;
  gender?: Gender;
  color?: string;
  imageUrl: string;
  microchipId?: string;
  dam?: string; // For horses
  sire?: string; // For horses
  notes?: string;
  deviceId?: string;
  deviceName?: string;
  deviceStatus?: DeviceStatus;
  lastSyncTime?: number;
  location?: {
    latitude: number;
    longitude: number;
    timestamp: number;
  };
  passportImageUrl?: string;
  speed?: number;
  speedUnit?: string;
  speedUpdatedAt?: string;
}

export const animals: Animal[] = [
  {
    id: '1',
    name: 'Thunder',
    type: 'horse',
    breed: 'Arabian',
    age: 7,
    gender: 'male',
    color: 'Bay',
    imageUrl: 'https://magically.life/api/media/image?query=beautiful%20arabian%20bay%20horse%20standing%20proudly%20in%20a%20field',
    microchipId: '*********',
    dam: 'Desert Rose',
    sire: 'Storm King',
    notes: 'Champion show horse, very gentle temperament',
    deviceName: 'HoofMonitor Pro',
    deviceStatus: 'connected',
    lastSyncTime: Date.now() - 1800000, // 30 minutes ago
    passportImageUrl: 'https://magically.life/api/media/image?query=horse%20passport%20document',
    speed: 12.5,
    speedUnit: 'km/h',
    speedUpdatedAt: new Date(Date.now() - 600000).toISOString() // 10 minutes ago
  },
  {
    id: '2',
    name: 'Sahara',
    type: 'camel',
    breed: 'Dromedary',
    age: 10,
    gender: 'female',
    color: 'Light Brown',
    imageUrl: 'https://magically.life/api/media/image?query=dromedary%20camel%20with%20light%20brown%20fur%20in%20desert%20setting',
    microchipId: '*********',
    notes: 'Very calm, used for educational programs',
    deviceStatus: 'disconnected',
    speed: 0,
    speedUnit: 'km/h',
    speedUpdatedAt: new Date(Date.now() - 86400000).toISOString() // 1 day ago
  },
  {
    id: '3',
    name: 'Midnight',
    type: 'horse',
    breed: 'Friesian',
    age: 5,
    gender: 'male',
    color: 'Black',
    imageUrl: 'https://magically.life/api/media/image?query=majestic%20black%20friesian%20horse%20with%20flowing%20mane',
    dam: 'Starlight',
    sire: 'Dark Knight',
    notes: 'Excellent dressage prospect',
    deviceName: 'HoofMonitor Lite',
    deviceStatus: 'low_battery',
    lastSyncTime: Date.now() - 7200000, // 2 hours ago
    passportImageUrl: 'https://magically.life/api/media/image?query=horse%20passport%20document%20with%20black%20horse',
    speed: 3.2,
    speedUnit: 'km/h',
    speedUpdatedAt: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
  },
  {
    id: '4',
    name: 'Luna',
    type: 'horse',
    breed: 'Appaloosa',
    age: 4,
    gender: 'female',
    color: 'Spotted',
    imageUrl: 'https://magically.life/api/media/image?query=beautiful%20spotted%20appaloosa%20horse%20in%20green%20pasture',
    microchipId: '*********',
    notes: 'Very friendly, good with children',
    speed: 8.7,
    speedUnit: 'km/h',
    speedUpdatedAt: new Date(Date.now() - 1800000).toISOString() // 30 minutes ago
  },
];
