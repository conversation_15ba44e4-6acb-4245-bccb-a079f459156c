
// Mock data for feeding entries
export interface FeedingEntry {
  id: string;
  animalId: string;
  time: string;
  feedType: string;
  amount: string;
  notes?: string;
  reminder: boolean;
  reminderTime?: string;
}

export const feedingEntries: FeedingEntry[] = [
  {
    id: '1',
    animalId: '1',
    time: '07:00',
    feedType: 'Hay',
    amount: '2 kg',
    notes: 'Morning feeding',
    reminder: true,
    reminderTime: '06:45',
  },
  {
    id: '2',
    animalId: '1',
    time: '12:00',
    feedType: 'Grain Mix',
    amount: '1 kg',
    notes: 'Midday feeding',
    reminder: true,
    reminderTime: '11:45',
  },
  {
    id: '3',
    animalId: '1',
    time: '18:00',
    feedType: 'Hay',
    amount: '2 kg',
    notes: 'Evening feeding',
    reminder: true,
    reminderTime: '17:45',
  },
  {
    id: '4',
    animalId: '2',
    time: '08:00',
    feedType: 'Camel Feed',
    amount: '3 kg',
    reminder: false,
  },
  {
    id: '5',
    animalId: '3',
    time: '07:30',
    feedType: 'Premium Horse Feed',
    amount: '1.5 kg',
    reminder: true,
    reminderTime: '07:15',
  },
];
