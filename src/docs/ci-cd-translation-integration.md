# 🚀 CI/CD Translation Integration Guide

This guide explains how to integrate automated translation validation into your CI/CD pipeline for the magically app.

## 📋 Overview

The translation validation system provides:

- **🔍 Automated Detection**: Finds missing translation keys across all languages
- **🛡️ Build Protection**: Prevents merging code with translation issues
- **🔧 Auto-Fix Capability**: Automatically adds English placeholders for missing keys
- **📊 Detailed Reporting**: Comprehensive reports for developers and translators
- **🎯 Flexible Configuration**: Strict or lenient validation modes

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Developer     │    │   CI/CD         │    │   Translators   │
│   Commits       │───▶│   Pipeline      │───▶│   Get Issues    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Pre-commit      │    │ Translation     │    │ Manual          │
│ Validation      │    │ Validation      │    │ Translation     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Setup Instructions

### 1. Install Git Hooks (Local Development)

```bash
# Make setup script executable
chmod +x scripts/setup-git-hooks.sh

# Run setup
./scripts/setup-git-hooks.sh
```

This installs:
- Pre-commit hook for translation validation
- Commit message hook for translation commits
- .gitignore entries for reports

### 2. Add NPM Scripts

Add these scripts to your `package.json`:

```json
{
  "scripts": {
    "translations:check": "node scripts/translation-utils.js check",
    "translations:fix": "node scripts/translation-utils.js fix",
    "translations:validate": "node scripts/translation-utils.js validate",
    "translations:ci": "node scripts/ci-translation-check.js",
    "setup:hooks": "./scripts/setup-git-hooks.sh"
  }
}
```

### 3. Configure CI Environment

Copy and customize the environment configuration:

```bash
cp scripts/ci-config.env .env.ci
# Edit .env.ci with your settings
```

### 4. GitHub Actions (Automatic)

The GitHub Actions workflow is automatically configured in `.github/workflows/translation-validation.yml`.

## 🔧 Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `TRANSLATION_STRICT_MODE` | `false` | Fail on any missing keys |
| `MAX_MISSING_KEYS` | `0` | Max allowed missing keys |
| `TRANSLATION_OUTPUT_FORMAT` | `text` | Output format (`text` or `json`) |
| `SAVE_TRANSLATION_REPORT` | `true` | Save detailed reports |
| `TRANSLATION_REPORT_DIR` | `./reports` | Report directory |

### Validation Modes

#### Strict Mode (`TRANSLATION_STRICT_MODE=true`)
- ❌ Fails on ANY missing translation keys
- ❌ Fails on structural inconsistencies
- ✅ Best for production releases
- ✅ Ensures complete translations

#### Lenient Mode (`TRANSLATION_STRICT_MODE=false`)
- ⚠️ Allows up to `MAX_MISSING_KEYS` missing keys
- ✅ Passes with warnings for minor issues
- ✅ Good for development branches
- ✅ Allows incremental translation work

## 🔄 Workflow Examples

### Developer Workflow

1. **Add New Feature with Translations**
   ```bash
   # Add English translations
   # Modify components/screens
   git add .
   git commit -m "Add new feature"
   # Pre-commit hook runs automatically
   ```

2. **Handle Missing Translations**
   ```bash
   # If pre-commit hook finds missing keys
   npm run translations:fix
   git add assets/translations/
   git commit -m "🌍 Add translation placeholders"
   ```

3. **Validate Before Push**
   ```bash
   npm run translations:validate
   git push origin feature-branch
   ```

### CI/CD Workflow

1. **Pull Request Created**
   - GitHub Actions runs translation validation
   - Comments added to PR if issues found
   - Build blocked if validation fails

2. **Auto-Fix Available**
   ```bash
   # Trigger auto-fix workflow
   gh workflow run translation-validation.yml
   ```

3. **Manual Translation**
   - Translators receive GitHub issues
   - Replace `[EN]` prefixed keys
   - Validation passes on next commit

### Translator Workflow

1. **Receive Notification**
   - GitHub issue created automatically
   - Lists files needing translation

2. **Translate Keys**
   ```typescript
   // Before
   quickActions: '[EN] Quick Actions',
   
   // After
   quickActions: 'Acciones Rápidas',
   ```

3. **Validate Translation**
   ```bash
   npm run translations:validate
   ```

## 📊 Reports and Monitoring

### GitHub Actions Reports

- **Validation Status**: Pass/Fail with details
- **Missing Keys Count**: Exact numbers per language
- **Detailed Breakdown**: Which keys are missing where
- **Artifacts**: Downloadable reports for analysis

### Local Reports

```bash
# Generate detailed report
TRANSLATION_OUTPUT_FORMAT=json npm run translations:ci > report.json

# View summary
npm run translations:check
```

### Report Structure

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": "warning",
  "summary": {
    "totalLanguages": 7,
    "totalKeys": 156,
    "missingKeys": 12,
    "validLanguages": 5,
    "invalidLanguages": 2
  },
  "languages": {
    "ar": {
      "status": "missing_keys",
      "missingKeys": 8,
      "missingKeysList": ["quickActions", "healthDashboard"]
    }
  }
}
```

## 🚨 Troubleshooting

### Common Issues

#### "Pre-commit hook not running"
```bash
# Re-run setup
./scripts/setup-git-hooks.sh

# Check hook permissions
ls -la .git/hooks/pre-commit
```

#### "CI validation failing unexpectedly"
```bash
# Test locally with same settings
TRANSLATION_STRICT_MODE=true npm run translations:ci

# Check environment variables
echo $TRANSLATION_STRICT_MODE
```

#### "Translation files not parsing"
```bash
# Validate syntax
node -c assets/translations/en/index.ts

# Check for common issues
npm run translations:validate
```

### Debug Mode

```bash
# Enable verbose output
DEBUG=translation:* npm run translations:check

# Save debug report
SAVE_TRANSLATION_REPORT=true npm run translations:ci
```

## 🔧 Advanced Configuration

### Custom Validation Rules

```javascript
// scripts/custom-validation.js
module.exports = {
  rules: {
    requireAllLanguages: true,
    allowExtraKeys: false,
    validateNestedStructure: true,
    maxKeyLength: 100,
    forbiddenPatterns: [/TODO/, /FIXME/]
  }
};
```

### Branch-Specific Settings

```yaml
# .github/workflows/translation-validation.yml
env:
  TRANSLATION_STRICT_MODE: ${{ github.ref == 'refs/heads/main' && 'true' || 'false' }}
  MAX_MISSING_KEYS: ${{ github.ref == 'refs/heads/main' && '0' || '10' }}
```

### Integration with Other Tools

#### Slack Notifications
```bash
# Add to CI environment
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
```

#### Jira Integration
```bash
# Create Jira tickets for missing translations
JIRA_PROJECT_KEY=TRANS
JIRA_API_TOKEN=...
```

## 📈 Best Practices

### Development

1. **Always add English translations first**
2. **Run `translations:fix` after adding features**
3. **Validate before pushing to shared branches**
4. **Use descriptive translation keys**
5. **Group related translations logically**

### CI/CD

1. **Use strict mode for production branches**
2. **Allow some missing keys in development**
3. **Generate reports for translation teams**
4. **Set up notifications for translation issues**
5. **Regular validation on schedule**

### Translation Management

1. **Use the `[EN]` prefix system consistently**
2. **Translate in batches by language**
3. **Validate after each translation session**
4. **Keep translation keys stable across versions**
5. **Document context for complex translations**

## 🔄 Migration Guide

### From Manual Translation Management

1. **Install the system**
   ```bash
   ./scripts/setup-git-hooks.sh
   ```

2. **Run initial validation**
   ```bash
   npm run translations:check
   ```

3. **Fix existing issues**
   ```bash
   npm run translations:fix
   ```

4. **Configure CI/CD**
   - Update environment variables
   - Test with sample PR

5. **Train team**
   - Share workflow documentation
   - Practice with test translations

### Rollback Plan

```bash
# Disable hooks temporarily
mv .git/hooks/pre-commit .git/hooks/pre-commit.disabled

# Skip CI validation
TRANSLATION_STRICT_MODE=false

# Re-enable when ready
mv .git/hooks/pre-commit.disabled .git/hooks/pre-commit
```

## 📞 Support

### Getting Help

1. **Check the logs**: Look at CI output and local reports
2. **Run diagnostics**: Use `npm run translations:validate`
3. **Review configuration**: Check environment variables
4. **Test locally**: Reproduce CI issues locally

### Contributing

To improve the translation validation system:

1. **Report issues**: Create GitHub issues with detailed logs
2. **Suggest features**: Propose improvements via discussions
3. **Submit PRs**: Follow the contribution guidelines
4. **Update docs**: Keep documentation current

---

*This integration ensures your app maintains high-quality translations across all supported languages while providing a smooth developer experience.*