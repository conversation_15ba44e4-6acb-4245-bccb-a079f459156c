
# Setting Up Google Authentication for HoofBeat

This guide will walk you through the process of setting up Google Sign-In for your HoofBeat app.

## 1. Generate SHA-1 Fingerprint

### For Debug Certificate (Development):

1. Open a terminal/command prompt
2. Run this command:
   ```
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
   ```
3. Look for the "SHA1" value in the output - it will look something like: `AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD`

### For Release Certificate (Production):

1. Find your keystore file used for signing your app
2. Run:
   ```
   keytool -list -v -keystore YOUR_KEYSTORE_PATH -alias YOUR_ALIAS
   ```
3. Enter your keystore password when prompted
4. Copy the SHA-1 fingerprint from the output

## 2. Configure Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select your existing project
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "OAuth client ID"
5. Select "Android" as the application type
6. Enter your app's package name (e.g., `com.hoofbeat.app`)
7. Enter the SHA-1 fingerprint you obtained earlier
8. Click "Create"

9. Now create a Web client ID (also required):
   - Click "Create Credentials" > "OAuth client ID" again
   - Select "Web application" as the application type
   - Add the following authorized redirect URI:
     ```
     https://hfqhqymuenbuzndkdcqf.supabase.co/auth/v1/callback
     ```
   - Click "Create" and note down the Client ID and Client Secret

## 3. Configure Supabase Auth

1. Go to your [Supabase dashboard](https://supabase.com/dashboard/project/hfqhqymuenbuzndkdcqf/auth/providers)
2. Find Google in the list and enable it
3. Enter the Client ID and Client Secret from your Web client
4. Save the changes

## 4. Create google-services.json

1. In Google Cloud Console, go to "Firebase" and add your project
2. Register your Android app with the package name `com.hoofbeat.app`
3. Download the google-services.json file
4. Place it in the root of your project

## 5. Update app.json

Update your app.json file with the Web Client ID:

```json
{
  "expo": {
    "extra": {
      "googleWebClientId": "YOUR_WEB_CLIENT_ID_HERE"
    }
  }
}
```

## 6. Implement Google Sign-In in your app

Add the following code to your LoginScreen.tsx file:

```typescript
import * as Google from 'expo-auth-session/providers/google';
import * as WebBrowser from 'expo-web-browser';
import Constants from 'expo-constants';

// Initialize Google Sign-In
const [request, response, promptAsync] = Google.useAuthRequest({
  expoClientId: Constants.expoConfig?.extra?.googleWebClientId,
  androidClientId: Constants.expoConfig?.extra?.googleWebClientId,
  webClientId: Constants.expoConfig?.extra?.googleWebClientId,
});

// Handle Google Sign-In
const handleGoogleSignIn = async () => {
  try {
    const result = await promptAsync();
    if (result.type === 'success') {
      const { id_token } = result.params;
      
      // Sign in with Supabase using the Google token
      const { data, error } = await supabase.auth.signInWithIdToken({
        provider: 'google',
        token: id_token,
      });
      
      if (error) throw error;
      
      // User is now signed in
      toast.success('Signed in with Google successfully');
    }
  } catch (error) {
    console.error('Google sign-in error:', error);
    toast.error('Failed to sign in with Google');
  }
};
```

## Testing

After setting everything up:

1. Make sure your app is using the correct package name
2. Verify the SHA-1 fingerprint matches your development or production keystore
3. Test the Google Sign-In flow in your app

If you encounter any issues, double-check:
- The package name matches exactly
- The SHA-1 fingerprint is correct
- The redirect URI is properly configured
- Your app is using the correct client ID
