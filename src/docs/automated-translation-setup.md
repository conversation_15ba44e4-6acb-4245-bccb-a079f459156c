# 🤖 Automated Translation Services Setup Guide

This guide explains how to set up and use automated translation services to automatically translate `[EN]` prefixed keys in your magically app.

## 📋 Overview

The automated translation system provides:

- **🔄 Multiple Providers**: Google Translate, DeepL, OpenAI GPT, Azure Translator
- **🎯 Context-Aware Translation**: Detects UI, medical, navigation contexts
- **💰 Cost Management**: Budget tracking and usage monitoring
- **📊 Quality Control**: Confidence scoring and review system
- **🔒 Secure API Key Management**: Encrypted storage and validation
- **⚡ Batch Processing**: Efficient bulk translation capabilities

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Translation   │    │   API Gateway   │    │   Providers     │
│   Manager       │───▶│   (Supabase)    │───▶│   - Google      │
│                 │    │                 │    │   - DeepL       │
└─────────────────┘    └─────────────────┘    │   - OpenAI      │
         │                       │             │   - Azure       │
         ▼                       ▼             └─────────────────┘
┌─────────────────┐    ┌─────────────────┐
│   Usage Stats   │    │   Cost Budget   │
│   & Analytics   │    │   Management    │
└─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# The translation services are already included in the project
# No additional dependencies needed
```

### 2. Set Up API Keys

Choose one or more translation providers and get their API keys:

#### Google Translate API
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the Cloud Translation API
3. Create credentials (API Key)
4. Set environment variable: `GOOGLE_TRANSLATE_API_KEY`

#### DeepL API
1. Sign up at [DeepL API](https://www.deepl.com/pro-api)
2. Get your API key from the account settings
3. Set environment variable: `DEEPL_API_KEY`

#### OpenAI API
1. Sign up at [OpenAI](https://platform.openai.com/)
2. Generate an API key
3. Set environment variable: `OPENAI_API_KEY`

#### Azure Translator
1. Create an Azure account and Translator resource
2. Get the API key and region
3. Set environment variables: `AZURE_API_KEY`, `AZURE_REGION`

### 3. Configure Environment Variables

```bash
# Add to your .env file or CI/CD environment
GOOGLE_TRANSLATE_API_KEY=your_google_api_key_here
DEEPL_API_KEY=your_deepl_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
AZURE_API_KEY=your_azure_api_key_here
AZURE_REGION=your_azure_region_here
```

### 4. Test the Setup

```bash
# Test translation utility
node scripts/auto-translate.js scan

# Perform a dry run
node scripts/auto-translate.js translate --dry-run

# Translate with specific provider
node scripts/auto-translate.js translate --provider=deepl
```

## 💻 Usage

### Command Line Interface

```bash
# Scan for keys needing translation
node scripts/auto-translate.js scan

# Automatic translation (uses best available provider)
node scripts/auto-translate.js translate

# Dry run (show what would be translated)
node scripts/auto-translate.js translate --dry-run

# Use specific provider
node scripts/auto-translate.js translate --provider=deepl

# Batch processing with custom settings
node scripts/auto-translate.js translate --batch-size=5 --delay=2000
```

### NPM Scripts

Add these to your `package.json`:

```json
{
  "scripts": {
    "translate:scan": "node scripts/auto-translate.js scan",
    "translate:auto": "node scripts/auto-translate.js translate",
    "translate:dry-run": "node scripts/auto-translate.js translate --dry-run",
    "translate:google": "node scripts/auto-translate.js translate --provider=google",
    "translate:deepl": "node scripts/auto-translate.js translate --provider=deepl",
    "translate:openai": "node scripts/auto-translate.js translate --provider=openai"
  }
}
```

### Programmatic Usage

```typescript
import { translationManager } from './services/translationServices';

// Translate a single text
const result = await translationManager.translate(
  'Quick Actions',
  'en',
  'es',
  {
    context: 'This is a UI button in a mobile app',
    domain: 'ui'
  }
);

console.log(result.translatedText); // "Acciones Rápidas"

// Batch translation
const requests = [
  { text: 'Health Dashboard', sourceLanguage: 'en', targetLanguage: 'fr' },
  { text: 'Add Animal', sourceLanguage: 'en', targetLanguage: 'fr' }
];

const results = await translationManager.translateBatch(requests);
```

## ⚙️ Configuration

### Provider Priority

The system automatically chooses the best provider based on:

1. **DeepL** - Highest quality, good for European languages
2. **Google Translate** - Most reliable, supports all languages
3. **OpenAI GPT** - Context-aware, best for complex translations
4. **Azure Translator** - Good for enterprise scenarios

### Context Detection

The system automatically detects context based on translation keys and content:

- **UI**: `button`, `menu`, `tab`, `screen`, `view`
- **Medical**: `health`, `vital`, `medication`, `vaccine`
- **Navigation**: `home`, `back`, `next`, `navigate`
- **Action**: `add`, `edit`, `delete`, `save`, `submit`
- **Status**: `status`, `active`, `pending`, `condition`
- **Alert**: `alert`, `warning`, `error`, `notification`
- **Form**: `input`, `field`, `label`, `required`

### Cost Management

```typescript
// Set monthly budget (default: $10)
const budget = {
  monthlyBudget: 25.00,
  alertThreshold: 0.8 // Alert at 80%
};

// Check current usage
const usage = translationManager.getUsageStats();
console.log(`Total cost this month: $${usage.totalCost}`);
```

## 🔧 Advanced Features

### Custom Context Templates

```typescript
// Add custom context for better translations
const customContext = {
  veterinary: 'This is veterinary medical terminology for animal healthcare professionals.',
  petOwner: 'This is user-friendly text for pet owners using a mobile app.'
};
```

### Quality Review System

```typescript
// Review and approve translations
const review = {
  translationId: 'uuid',
  qualityScore: 4, // 1-5 scale
  approved: true,
  notes: 'Good translation, maintains technical accuracy'
};
```

### Batch Processing with Rate Limiting

```typescript
const batchOptions = {
  batchSize: 10,
  delayBetweenRequests: 1000, // 1 second
  maxConcurrentRequests: 3
};
```

## 📊 Monitoring and Analytics

### Usage Dashboard

```typescript
// Get translation analytics
const analytics = await supabase.rpc('get_translation_analytics', {
  p_user_id: userId,
  p_days: 30
});

console.log(analytics.data);
// {
//   totalTranslations: 150,
//   totalCharacters: 2500,
//   totalCost: 0.05,
//   averageConfidence: 0.92,
//   byProvider: {
//     deepl: { requests: 100, cost: 0.03 },
//     google: { requests: 50, cost: 0.02 }
//   }
// }
```

### Cost Tracking

```typescript
// Check budget status
const budgetStatus = await supabase.rpc('check_translation_budget', {
  p_user_id: userId,
  p_cost: 0.01
});

if (budgetStatus.data.shouldAlert) {
  console.log('⚠️ Translation budget alert!');
}
```

## 🔒 Security

### API Key Management

- API keys are stored securely in Supabase secrets
- Keys are validated before use
- Masked keys shown in UI for security
- Automatic key rotation support

### Access Control

- User-based translation logs
- Admin-only provider configuration
- Budget limits per user
- Audit trail for all translations

## 🚨 Error Handling

### Common Issues

#### "API key not found"
```bash
# Check environment variables
echo $GOOGLE_TRANSLATE_API_KEY

# Test API key validity
node scripts/auto-translate.js translate --dry-run
```

#### "Rate limit exceeded"
```bash
# Increase delay between requests
node scripts/auto-translate.js translate --delay=2000

# Reduce batch size
node scripts/auto-translate.js translate --batch-size=5
```

#### "Translation quality low"
```bash
# Use context-aware provider
node scripts/auto-translate.js translate --provider=openai

# Add custom context
# Edit the context detection patterns in auto-translate.js
```

### Fallback Strategy

1. **Primary Provider Fails** → Try secondary provider
2. **All Providers Fail** → Keep `[EN]` prefix for manual translation
3. **Budget Exceeded** → Stop automatic translation, alert user
4. **Low Confidence** → Flag for manual review

## 🔄 Integration with CI/CD

### GitHub Actions Integration

```yaml
# Add to .github/workflows/translation-validation.yml
- name: Auto-translate missing keys
  if: github.event_name == 'workflow_dispatch'
  run: |
    node scripts/auto-translate.js translate
    git add assets/translations/
    git commit -m "🤖 Auto-translate missing keys" || exit 0
    git push
  env:
    DEEPL_API_KEY: ${{ secrets.DEEPL_API_KEY }}
    GOOGLE_TRANSLATE_API_KEY: ${{ secrets.GOOGLE_TRANSLATE_API_KEY }}
    OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
```

### Pre-commit Hook Integration

```bash
# Add to scripts/pre-commit-translations.sh
if [ "$choice" = "1" ]; then
    echo "🤖 Auto-translating with AI..."
    if node scripts/auto-translate.js translate; then
        echo "✅ AI translation completed"
        git add assets/translations/
    else
        echo "⚠️ AI translation failed, using placeholders"
        node scripts/translation-utils.js fix
        git add assets/translations/
    fi
fi
```

## 📈 Best Practices

### Translation Quality

1. **Use DeepL for European languages** (highest quality)
2. **Use OpenAI for context-sensitive translations** (medical, technical)
3. **Use Google for broad language support** (most reliable)
4. **Always review AI translations** before production
5. **Maintain consistent terminology** across languages

### Cost Optimization

1. **Set reasonable budgets** ($10-25/month for most apps)
2. **Use batch processing** for efficiency
3. **Cache translations** to avoid re-translating
4. **Monitor usage regularly** to avoid surprises
5. **Use free tiers** when available (DeepL free: 500k chars/month)

### Development Workflow

1. **Add English translations first**
2. **Run auto-translation in development**
3. **Review and refine translations**
4. **Test with native speakers**
5. **Update translations incrementally**

## 🎯 Provider Comparison

| Provider | Quality | Speed | Cost | Languages | Context-Aware |
|----------|---------|-------|------|-----------|---------------|
| DeepL | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| Google | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| OpenAI | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Azure | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |

## 🆘 Support

### Getting Help

1. **Check the logs**: Look for error messages in console output
2. **Validate API keys**: Test with provider's official tools
3. **Review usage**: Check if you've hit rate limits or budgets
4. **Test connectivity**: Ensure network access to APIs

### Troubleshooting Commands

```bash
# Test all providers
for provider in google deepl openai; do
  echo "Testing $provider..."
  node scripts/auto-translate.js translate --provider=$provider --dry-run
done

# Check translation logs
node -e "console.log(require('./services/translationServices').translationManager.getUsageStats())"

# Validate configuration
node scripts/auto-translate.js help
```

---

*This automated translation system ensures your app maintains high-quality translations across all supported languages while minimizing manual effort and costs.*