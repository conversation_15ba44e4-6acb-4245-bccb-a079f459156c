
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { AlertTriangle, Heart, MapPin, Thermometer } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

type AlertType = 'heartRate' | 'temperature' | 'location' | 'general';

interface AlertBannerProps {
  type: AlertType;
  animalName: string;
  message: string;
  timestamp: string;
  onPress?: () => void;
}

const AlertBanner: React.FC<AlertBannerProps> = ({
  type,
  animalName,
  message,
  timestamp,
  onPress
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  const getIcon = () => {
    switch (type) {
      case 'heartRate':
        return <Heart size={20} color={colors.error} />;
      case 'temperature':
        return <Thermometer size={20} color={colors.error} />;
      case 'location':
        return <MapPin size={20} color={colors.error} />;
      case 'general':
      default:
        return <AlertTriangle size={20} color={colors.error} />;
    }
  };
  
  const getTitle = () => {
    switch (type) {
      case 'heartRate':
        return t('heartRateAlert');
      case 'temperature':
        return t('temperatureAlert');
      case 'location':
        return t('locationAlert');
      case 'general':
      default:
        return t('healthAlert');
    }
  };
  
  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.iconContainer}>
        {getIcon()}
      </View>
      
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.error }]}>{getTitle()}</Text>
        <Text style={[styles.animalName, { color: colors.text }]}>{animalName}</Text>
        <Text style={[styles.message, { color: colors.text }]}>{message}</Text>
        <Text style={[styles.timestamp, { color: colors.textSecondary }]}>{timestamp}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: 'rgba(229, 62, 62, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(229, 62, 62, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  animalName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    marginBottom: 4,
  },
  timestamp: {
    fontSize: 12,
  },
});

export default AlertBanner;
