import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface LoginHeaderProps {
  isSignUp: boolean;
  isResetPassword: boolean;
}

/**
 * @magic_description Login screen header component displaying app title and context-aware subtitle
 * Shows different subtitle text based on current authentication mode (sign in, sign up, reset password)
 */
const LoginHeader: React.FC<LoginHeaderProps> = ({ isSignUp, isResetPassword }) => {
  const { colors } = useTheme();
  
  const getSubtitle = () => {
    if (isSignUp) return 'Create an account';
    if (isResetPassword) return 'Reset your password';
    return 'Sign in to your account';
  };
  
  return (
    <View style={styles.header}>
      <Text style={[styles.title, { color: colors.text }]}>HoofBeat</Text>
      <Text style={[styles.subtitle, { color: colors.textLight }]}>
        {getSubtitle()}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
  },
});

export default LoginHeader;