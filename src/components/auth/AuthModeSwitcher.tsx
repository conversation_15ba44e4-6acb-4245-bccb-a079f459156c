import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface AuthModeSwitcherProps {
  isSignUp: boolean;
  isResetPassword: boolean;
  onToggleSignUp: () => void;
  onToggleResetPassword: () => void;
  onBackToSignIn: () => void;
}

const AuthModeSwitcher: React.FC<AuthModeSwitcherProps> = ({
  isSignUp,
  isResetPassword,
  onToggleSignUp,
  onToggleResetPassword,
  onBackToSignIn
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.container}>
      {!isResetPassword && (
        <TouchableOpacity
          style={styles.linkButton}
          onPress={onToggleSignUp}
        >
          <Text style={[styles.linkText, { color: colors.primary }]}>
            {isSignUp ? 'Already have an account? Sign In' : 'Don\'t have an account? Sign Up'}
          </Text>
        </TouchableOpacity>
      )}
      
      {!isSignUp && !isResetPassword && (
        <TouchableOpacity
          style={styles.forgotButton}
          onPress={onToggleResetPassword}
        >
          <Text style={[styles.forgotText, { color: colors.textLight }]}>
            Forgot your password?
          </Text>
        </TouchableOpacity>
      )}
      
      {isResetPassword && (
        <TouchableOpacity
          style={styles.linkButton}
          onPress={onBackToSignIn}
        >
          <Text style={[styles.linkText, { color: colors.primary }]}>
            Back to Sign In
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  linkButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  linkText: {
    fontSize: 14,
    fontWeight: '500',
  },
  forgotButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  forgotText: {
    fontSize: 14,
  },
});

export default AuthModeSwitcher;