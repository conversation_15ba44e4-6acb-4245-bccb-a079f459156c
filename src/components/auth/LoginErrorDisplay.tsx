import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface LoginErrorDisplayProps {
  errorMessage: string;
}

/**
 * @magic_description Login error display component
 * Shows error messages with appropriate styling and icons
 */
const LoginErrorDisplay: React.FC<LoginErrorDisplayProps> = ({
  errorMessage
}) => {
  const { colors } = useTheme();
  
  if (!errorMessage) {
    return null;
  }
  
  return (
    <View style={[styles.errorContainer, { backgroundColor: colors.error + '20' }]}>
      <AlertTriangle size={20} color={colors.error} />
      <Text style={[styles.errorText, { color: colors.error }]}>{errorMessage}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
});

export default LoginErrorDisplay;