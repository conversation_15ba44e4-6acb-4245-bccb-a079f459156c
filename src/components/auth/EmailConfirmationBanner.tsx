import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface EmailConfirmationBannerProps {
  needsEmailConfirmation: boolean;
  onResendConfirmation: () => void;
  isLoading: boolean;
}

const EmailConfirmationBanner: React.FC<EmailConfirmationBannerProps> = ({
  needsEmailConfirmation,
  onResendConfirmation,
  isLoading
}) => {
  const { colors } = useTheme();
  
  if (!needsEmailConfirmation) {
    return null;
  }
  
  return (
    <View style={[styles.infoContainer, { backgroundColor: colors.warning + '20' }]}>
      <AlertTriangle size={20} color={colors.warning} />
      <Text style={[styles.infoText, { color: colors.text }]}>
        Please check your email to confirm your account before logging in.
      </Text>
      <TouchableOpacity 
        style={[styles.resendButton, { backgroundColor: colors.warning }]}
        onPress={onResendConfirmation}
        disabled={isLoading}
      >
        <Text style={[styles.resendButtonText, { color: colors.card }]}>
          Resend Confirmation Email
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  infoContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
  },
  infoText: {
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 8,
  },
  resendButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  resendButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default EmailConfirmationBanner;