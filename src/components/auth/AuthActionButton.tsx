import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface AuthActionButtonProps {
  isSignUp: boolean;
  isResetPassword: boolean;
  isLoading: boolean;
  isAccountLocked: boolean;
  onPress: () => void;
}

const AuthActionButton: React.FC<AuthActionButtonProps> = ({
  isSignUp,
  isResetPassword,
  isLoading,
  isAccountLocked,
  onPress
}) => {
  const { colors } = useTheme();
  
  const getButtonText = () => {
    if (isSignUp) return 'Sign Up';
    if (isResetPassword) return 'Send Reset Link';
    return 'Sign In';
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.button,
        { backgroundColor: colors.primary },
        (isLoading || isAccountLocked) && styles.buttonDisabled
      ]}
      onPress={onPress}
      disabled={isLoading || isAccountLocked}
    >
      {isLoading ? (
        <ActivityIndicator color="#FFFFFF" />
      ) : (
        <Text style={styles.buttonText}>
          {getButtonText()}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AuthActionButton;