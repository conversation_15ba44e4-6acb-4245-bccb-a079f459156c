import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface LoginSecurityWarningsProps {
  isAccountLocked: boolean;
  lockUntil: Date | null;
  loginAttempts: number;
}

/**
 * @magic_description Login security warnings component displaying account lock status and attempt warnings
 * Shows account lock messages and warns users about failed login attempts
 */
const LoginSecurityWarnings: React.FC<LoginSecurityWarningsProps> = ({
  isAccountLocked,
  lockUntil,
  loginAttempts
}) => {
  const { colors } = useTheme();
  
  if (!isAccountLocked && loginAttempts <= 2) {
    return null;
  }
  
  return (
    <>
      {isAccountLocked && (
        <View style={[styles.lockedMessage, { backgroundColor: colors.error + '20' }]}>
          <Text style={[styles.lockedText, { color: colors.error }]}>
            Account locked due to too many failed attempts.
            {lockUntil && ` Try again after ${lockUntil.toLocaleTimeString()}.`}
          </Text>
        </View>
      )}
      
      {loginAttempts > 2 && !isAccountLocked && (
        <View style={[styles.warningMessage, { backgroundColor: colors.warning + '20' }]}>
          <Text style={[styles.warningText, { color: colors.warning }]}>
            {`Warning: ${loginAttempts} failed login attempts. Your account will be locked after 5 attempts.`}
          </Text>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  lockedMessage: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  lockedText: {
    fontSize: 14,
    textAlign: 'center',
  },
  warningMessage: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  warningText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default LoginSecurityWarnings;