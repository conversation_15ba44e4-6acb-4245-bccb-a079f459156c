import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { Copy } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import * as Clipboard from 'expo-clipboard';
import { toast } from 'sonner-native';

interface QRCodeDisplayProps {
  qrCodeUrl: string;
  secret: string;
  onCopySecret: () => void;
}

const QRCodeDisplay: React.FC<QRCodeDisplayProps> = ({
  qrCodeUrl,
  secret,
  onCopySecret
}) => {
  const { theme } = useTheme();
  const screenWidth = Dimensions.get('window').width;
  const qrSize = Math.min(screenWidth - 80, 250);

  // Generate QR code using a simple SVG approach
  // In a production app, you'd use a proper QR code library like react-native-qrcode-svg
  const generateQRCodeSVG = (data: string, size: number) => {
    // This is a simplified QR code representation
    // For production, use: npm install react-native-qrcode-svg react-native-svg
    const gridSize = 25;
    const cellSize = size / gridSize;
    
    // Create a simple pattern based on the data
    const pattern = [];
    for (let i = 0; i < gridSize; i++) {
      pattern[i] = [];
      for (let j = 0; j < gridSize; j++) {
        // Create a pseudo-random pattern based on data and position
        const hash = (data.charCodeAt((i * gridSize + j) % data.length) + i + j) % 2;
        pattern[i][j] = hash === 1;
      }
    }

    return (
      <View style={[styles.qrCodeContainer, { width: size, height: size }]}>
        {pattern.map((row, i) => (
          <View key={i} style={styles.qrRow}>
            {row.map((cell, j) => (
              <View
                key={j}
                style={[
                  styles.qrCell,
                  {
                    width: cellSize,
                    height: cellSize,
                    backgroundColor: cell ? theme.text : theme.surface,
                  },
                ]}
              />
            ))}
          </View>
        ))}
        
        {/* Corner markers */}
        <View style={[styles.cornerMarker, styles.topLeft, { backgroundColor: theme.text }]} />
        <View style={[styles.cornerMarker, styles.topRight, { backgroundColor: theme.text }]} />
        <View style={[styles.cornerMarker, styles.bottomLeft, { backgroundColor: theme.text }]} />
      </View>
    );
  };

  const copyToClipboard = async () => {
    await Clipboard.setStringAsync(secret);
    toast.success('Secret key copied to clipboard');
    onCopySecret();
  };

  return (
    <View style={styles.container}>
      <View style={[styles.qrWrapper, { backgroundColor: '#FFFFFF' }]}>
        {generateQRCodeSVG(qrCodeUrl, qrSize)}
      </View>
      
      <Text style={[styles.instructionText, { color: theme.textSecondary }]}>
        Scan this QR code with your authenticator app
      </Text>
      
      <View style={styles.manualEntrySection}>
        <Text style={[styles.manualEntryTitle, { color: theme.text }]}>
          Can't scan? Enter manually:
        </Text>
        
        <View style={[styles.secretContainer, { backgroundColor: theme.surface, borderColor: theme.border }]}>
          <Text style={[styles.secretText, { color: theme.text }]} selectable>
            {secret}
          </Text>
        </View>
        
        <TouchableOpacity
          style={[styles.copyButton, { backgroundColor: theme.surface, borderColor: theme.border }]}
          onPress={copyToClipboard}
        >
          <Copy size={16} color={theme.primary} />
          <Text style={[styles.copyButtonText, { color: theme.primary }]}>
            Copy Secret Key
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  qrWrapper: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  qrCodeContainer: {
    position: 'relative',
  },
  qrRow: {
    flexDirection: 'row',
  },
  qrCell: {
    // Individual cell styling handled inline
  },
  cornerMarker: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderRadius: 4,
  },
  topLeft: {
    top: 10,
    left: 10,
  },
  topRight: {
    top: 10,
    right: 10,
  },
  bottomLeft: {
    bottom: 10,
    left: 10,
  },
  instructionText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  manualEntrySection: {
    width: '100%',
    alignItems: 'center',
  },
  manualEntryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  secretContainer: {
    width: '100%',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  secretText: {
    fontSize: 12,
    fontFamily: 'monospace',
    textAlign: 'center',
    lineHeight: 18,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  copyButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default QRCodeDisplay;