import React from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import BiometricLoginButton from './BiometricLoginButton';
import { toast } from 'sonner-native';

interface LoginActionButtonsProps {
  isSignUp: boolean;
  isResetPassword: boolean;
  isLoading: boolean;
  isGoogleLoading?: boolean;
  isGoogleModuleLoading?: boolean;
  googleModuleReady?: boolean;
  googleModuleLoadFailed?: boolean;
  isAccountLocked: boolean;
  onSubmit: () => void;
  onToggleMode: () => void;
  onForgotPassword: () => void;
  onBackToSignIn: () => void;
  onGoogleSignIn?: () => void;
  onBiometricSuccess?: () => void;
}

/**
 * @magic_description Login action buttons component with main submit button and navigation links
 * Handles different button states and navigation between sign in, sign up, and reset password modes
 */
const LoginActionButtons: React.FC<LoginActionButtonsProps> = ({
  isSignUp,
  isResetPassword,
  isLoading,
  isGoogleLoading = false,
  isGoogleModuleLoading = false,
  googleModuleReady = false,
  googleModuleLoadFailed = false,
  isAccountLocked,
  onSubmit,
  onToggleMode,
  onForgotPassword,
  onBackToSignIn,
  onGoogleSignIn,
  onBiometricSuccess
}) => {
  const { colors } = useTheme();
  
  const getButtonText = () => {
    if (isSignUp) return 'Sign Up';
    if (isResetPassword) return 'Send Reset Link';
    return 'Sign In';
  };
  
  const getToggleText = () => {
    return isSignUp 
      ? 'Already have an account? Sign In' 
      : 'Don\'t have an account? Sign Up';
  };
  
  const handleBiometricSuccess = () => {
    if (onBiometricSuccess) {
      onBiometricSuccess();
    } else {
      toast.success('Biometric authentication successful!');
    }
  };
  
  const handleBiometricError = (error: string) => {
    toast.error(error);
  };
  
  return (
    <>
      <TouchableOpacity
        style={[
          styles.button,
          { backgroundColor: colors.primary },
          isLoading && styles.buttonDisabled
        ]}
        onPress={onSubmit}
        disabled={isLoading || isAccountLocked}
      >
        {isLoading ? (
          <ActivityIndicator color="#FFFFFF" />
        ) : (
          <Text style={styles.buttonText}>
            {getButtonText()}
          </Text>
        )}
      </TouchableOpacity>
      
      {/* Google Sign-In Button - only show for sign in, not sign up or reset password */}
      {!isSignUp && !isResetPassword && onGoogleSignIn && (
        <TouchableOpacity
          style={[
            styles.googleButton,
            { backgroundColor: colors.card, borderColor: colors.border },
            (isLoading || isGoogleLoading || isGoogleModuleLoading || isAccountLocked) && styles.buttonDisabled
          ]}
          onPress={onGoogleSignIn}
          disabled={isLoading || isGoogleLoading || isGoogleModuleLoading || isAccountLocked || googleModuleLoadFailed}
        >
          {isGoogleLoading || isGoogleModuleLoading ? (
            <ActivityIndicator color={colors.text} />
          ) : (
            <>
              <Text style={[styles.googleButtonText, { color: colors.text }]}>
                {googleModuleLoadFailed ? 'Google Sign-In Failed to Load' :
                 isGoogleModuleLoading ? 'Loading Google...' : 
                 !googleModuleReady ? 'Google Sign-In Unavailable' :
                 'Continue with Google'}
              </Text>
            </>
          )}
        </TouchableOpacity>
      )}
      
      {!isResetPassword && (
        <TouchableOpacity
          style={styles.linkButton}
          onPress={onToggleMode}
        >
          <Text style={[styles.linkText, { color: colors.primary }]}>
            {getToggleText()}
          </Text>
        </TouchableOpacity>
      )}
      
      {!isSignUp && !isResetPassword && (
        <TouchableOpacity
          style={styles.forgotButton}
          onPress={onForgotPassword}
        >
          <Text style={[styles.forgotText, { color: colors.textLight }]}>
            Forgot your password?
          </Text>
        </TouchableOpacity>
      )}
      
      {isResetPassword && (
        <TouchableOpacity
          style={styles.linkButton}
          onPress={onBackToSignIn}
        >
          <Text style={[styles.linkText, { color: colors.primary }]}>
            Back to Sign In
          </Text>
        </TouchableOpacity>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  googleButton: {
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    borderWidth: 1,
    flexDirection: 'row',
  },
  googleButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  linkButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  linkText: {
    fontSize: 14,
    fontWeight: '500',
  },
  forgotButton: {
    marginTop: 16,
    alignItems: 'center',
  },
  forgotText: {
    fontSize: 14,
  },
});

export default LoginActionButtons;