import React, { useState, useEffect } from 'react';
import { View, TextInput, TouchableOpacity, StyleSheet } from 'react-native';
import { Lock, Mail, Eye, EyeOff } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface LoginFormInputsProps {
  isSignUp: boolean;
  isResetPassword: boolean;
  isLoading: boolean;
  onFormDataChange: (data: { email: string; password: string; confirmPassword: string }) => void;
  initialEmail?: string;
  initialPassword?: string;
  initialConfirmPassword?: string;
}

/**
 * @magic_description Login form inputs component with email, password, and confirm password fields
 * Handles input validation, password visibility toggle, and conditional field rendering
 */
const LoginFormInputs: React.FC<LoginFormInputsProps> = ({
  isSignUp,
  isResetPassword,
  isLoading,
  onFormDataChange,
  initialEmail = '',
  initialPassword = '',
  initialConfirmPassword = ''
}) => {
  const { colors } = useTheme();
  
  // Internal form state
  const [email, setEmail] = useState(initialEmail);
  const [password, setPassword] = useState(initialPassword);
  const [confirmPassword, setConfirmPassword] = useState(initialConfirmPassword);
  const [showPassword, setShowPassword] = useState(false);
  
  // Update parent component when form data changes
  useEffect(() => {
    onFormDataChange({ email, password, confirmPassword });
  }, [email, password, confirmPassword, onFormDataChange]);
  
  // Reset form when mode changes
  useEffect(() => {
    if (isSignUp || isResetPassword) {
      setPassword('');
      setConfirmPassword('');
    }
  }, [isSignUp, isResetPassword]);
  
  // Sync with initial values when they change from parent
  useEffect(() => {
    setEmail(initialEmail);
  }, [initialEmail]);
  
  useEffect(() => {
    setPassword(initialPassword);
  }, [initialPassword]);
  
  useEffect(() => {
    setConfirmPassword(initialConfirmPassword);
  }, [initialConfirmPassword]);
  
  return (
    <>
      <View style={styles.inputContainer}>
        <Mail size={20} color={colors.primary} style={styles.inputIcon} />
        <TextInput
          style={[styles.input, { backgroundColor: colors.card, color: colors.text }]}
          placeholder="Email"
          placeholderTextColor={colors.textLight}
          value={email}
          onChangeText={setEmail}
          autoCapitalize="none"
          keyboardType="email-address"
          editable={!isLoading}
        />
      </View>
      
      {!isResetPassword && (
        <View style={styles.inputContainer}>
          <Lock size={20} color={colors.primary} style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { backgroundColor: colors.card, color: colors.text }]}
            placeholder="Password"
            placeholderTextColor={colors.textLight}
            value={password}
            onChangeText={setPassword}
            secureTextEntry={!showPassword}
            editable={!isLoading}
          />
          <TouchableOpacity 
            style={styles.eyeIcon}
            onPress={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff size={20} color={colors.textLight} />
            ) : (
              <Eye size={20} color={colors.textLight} />
            )}
          </TouchableOpacity>
        </View>
      )}
      
      {isSignUp && (
        <View style={styles.inputContainer}>
          <Lock size={20} color={colors.primary} style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { backgroundColor: colors.card, color: colors.text }]}
            placeholder="Confirm Password"
            placeholderTextColor={colors.textLight}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry={!showPassword}
            editable={!isLoading}
          />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    position: 'relative',
  },
  inputIcon: {
    position: 'absolute',
    left: 16,
    zIndex: 1,
  },
  input: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 48,
    fontSize: 16,
  },
  eyeIcon: {
    position: 'absolute',
    right: 16,
    zIndex: 1,
  },
});

export default LoginFormInputs;