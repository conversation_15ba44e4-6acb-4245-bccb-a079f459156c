import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Switch, Alert } from 'react-native';
import { Fingerprint, Shield, AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useBiometricAuth } from '../../hooks/useBiometricAuth';
import { toast } from 'sonner-native';

interface BiometricSettingsProps {
  userEmail?: string;
  userPassword?: string;
  onBiometricToggle?: (enabled: boolean) => void;
}

const BiometricSettings: React.FC<BiometricSettingsProps> = ({
  userEmail,
  userPassword,
  onBiometricToggle
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  const {
    isAvailable,
    isEnabled,
    isLoading,
    capabilities,
    enableBiometric,
    disableBiometric
  } = useBiometricAuth();
  
  const [isToggling, setIsToggling] = useState(false);

  const handleToggleBiometric = async () => {
    if (isToggling) return;
    
    setIsToggling(true);
    
    try {
      if (isEnabled) {
        // Disable biometric
        Alert.alert(
          t('disableBiometricLogin'),
          t('disableBiometricConfirmation'),
          [
            {
              text: t('cancel'),
              style: 'cancel'
            },
            {
              text: t('disable'),
              style: 'destructive',
              onPress: async () => {
                await disableBiometric();
                onBiometricToggle?.(false);
              }
            }
          ]
        );
      } else {
        // Enable biometric
        if (!userEmail || !userPassword) {
          toast.error(t('signInPasswordFirstBiometric'));
          return;
        }
        
        const success = await enableBiometric(userEmail, userPassword);
        if (success) {
          onBiometricToggle?.(true);
        }
      }
    } catch (error) {
      console.error('Error toggling biometric:', error);
      toast.error(t('failedUpdateBiometricSettings'));
    } finally {
      setIsToggling(false);
    }
  };

  const getBiometricTypeText = () => {
    if (capabilities.supportedTypes.includes('Face ID')) {
      return t('faceId');
    } else if (capabilities.supportedTypes.includes('Fingerprint')) {
      return t('fingerprint');
    } else {
      return t('biometric');
    }
  };

  const getBiometricIcon = () => {
    if (capabilities.supportedTypes.includes('Face ID')) {
      return '🔒'; // Face ID emoji
    } else {
      return <Fingerprint size={24} color={colors.primary} />;
    }
  };

  // Don't render if biometric hardware is not available
  if (!capabilities.hasHardware) {
    return null;
  }

  // Show enrollment message if biometrics are not enrolled
  if (!capabilities.isEnrolled) {
    return (
      <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <View style={styles.header}>
          <AlertTriangle size={24} color={colors.warning} />
          <Text style={[styles.title, { color: colors.text }]}>{t('biometricSetupRequired')}</Text>
        </View>
        <Text style={[styles.description, { color: colors.textLight }]}>
          {t('biometricSetupInstructions', { type: getBiometricTypeText() })}
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          {typeof getBiometricIcon() === 'string' ? (
            <Text style={styles.faceIdIcon}>{getBiometricIcon()}</Text>
          ) : (
            getBiometricIcon()
          )}
        </View>
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: colors.text }]}>
            {t('biometricLogin', { type: getBiometricTypeText() })}
          </Text>
          <Text style={[styles.subtitle, { color: colors.textLight }]}>
            {isEnabled ? t('enabled') : t('disabled')}
          </Text>
        </View>
        <Switch
          value={isEnabled}
          onValueChange={handleToggleBiometric}
          disabled={isLoading || isToggling || !isAvailable}
          trackColor={{ false: colors.border, true: colors.primary + '40' }}
          thumbColor={isEnabled ? colors.primary : colors.textLight}
        />
      </View>
      
      <Text style={[styles.description, { color: colors.textLight }]}>
        {isEnabled
          ? t('biometricEnabledDescription', { type: getBiometricTypeText().toLowerCase() })
          : t('biometricDisabledDescription', { type: getBiometricTypeText().toLowerCase() })
        }
      </Text>
      
      {isEnabled && (
        <View style={[styles.securityNote, { backgroundColor: colors.primary + '10' }]}>
          <Shield size={16} color={colors.primary} />
          <Text style={[styles.securityText, { color: colors.primary }]}>
            {t('biometricSecurityNote')}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    marginRight: 12,
  },
  faceIdIcon: {
    fontSize: 24,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  securityNote: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  securityText: {
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
    lineHeight: 16,
  },
});

export default BiometricSettings;