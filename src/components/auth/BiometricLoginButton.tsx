import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator, View } from 'react-native';
import { Fingerprint } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useBiometricAuth } from '../../hooks/useBiometricAuth';

interface BiometricLoginButtonProps {
  onBiometricSuccess: () => void;
  onBiometricError: (error: string) => void;
  disabled?: boolean;
}

const BiometricLoginButton: React.FC<BiometricLoginButtonProps> = ({
  onBiometricSuccess,
  onBiometricError,
  disabled = false
}) => {
  const { colors } = useTheme();
  const {
    isAvailable,
    isEnabled,
    isLoading,
    capabilities,
    authenticateWithBiometric
  } = useBiometricAuth();

  const handleBiometricLogin = async () => {
    if (!isAvailable || !isEnabled || disabled) {
      return;
    }

    try {
      const result = await authenticateWithBiometric();
      
      if (result.success) {
        onBiometricSuccess();
      } else if (result.cancelled) {
        // User cancelled, don't show error
        return;
      } else {
        onBiometricError(result.error || 'Biometric authentication failed');
      }
    } catch (error: any) {
      onBiometricError('Biometric authentication failed');
    }
  };

  // Don't render if biometric is not available or not enabled
  if (!isAvailable || !isEnabled) {
    return null;
  }

  const getBiometricIcon = () => {
    if (capabilities.supportedTypes.includes('Face ID')) {
      return '🔒'; // Face ID emoji
    } else if (capabilities.supportedTypes.includes('Fingerprint')) {
      return <Fingerprint size={24} color={colors.primary} />;
    } else {
      return <Fingerprint size={24} color={colors.primary} />;
    }
  };

  const getBiometricLabel = () => {
    if (capabilities.supportedTypes.includes('Face ID')) {
      return 'Sign in with Face ID';
    } else if (capabilities.supportedTypes.includes('Fingerprint')) {
      return 'Sign in with Fingerprint';
    } else {
      return 'Sign in with Biometric';
    }
  };

  return (
    <View style={styles.container}>
      <View style={[styles.divider, { backgroundColor: colors.border }]}>
        <Text style={[styles.dividerText, { color: colors.textLight }]}>or</Text>
      </View>
      
      <TouchableOpacity
        style={[
          styles.biometricButton,
          {
            backgroundColor: colors.card,
            borderColor: colors.border,
          },
          disabled && styles.disabled
        ]}
        onPress={handleBiometricLogin}
        disabled={disabled || isLoading}
        activeOpacity={0.7}
      >
        {isLoading ? (
          <ActivityIndicator size="small" color={colors.primary} />
        ) : (
          <View style={styles.iconContainer}>
            {typeof getBiometricIcon() === 'string' ? (
              <Text style={styles.faceIdIcon}>{getBiometricIcon()}</Text>
            ) : (
              getBiometricIcon()
            )}
          </View>
        )}
        
        <Text style={[
          styles.biometricText,
          { color: disabled ? colors.textLight : colors.text }
        ]}>
          {getBiometricLabel()}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
  },
  divider: {
    height: 1,
    marginVertical: 20,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dividerText: {
    backgroundColor: 'transparent',
    paddingHorizontal: 15,
    fontSize: 14,
    position: 'absolute',
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 10,
  },
  disabled: {
    opacity: 0.5,
  },
  iconContainer: {
    marginRight: 12,
  },
  faceIdIcon: {
    fontSize: 24,
  },
  biometricText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default BiometricLoginButton;