
import React from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useDataReadiness } from '../hooks/useDataReadiness';
import CheckListItem from './CheckListItem';
import DataReadinessHeader from './dataReadiness/DataReadinessHeader';
import DataReadinessSummary from './dataReadiness/DataReadinessSummary';

interface DataReadinessCheckProps {
  animalId: string;
  onRefresh?: () => void;
}

/**
 * Component to check if various data sources are ready to receive data
 */
/**
 * @magic_description Component for checking and displaying data readiness status
 * Shows device, database, and animal-specific data readiness with refresh capability
 */
const DataReadinessCheck: React.FC<DataReadinessCheckProps> = ({ 
  animalId,
  onRefresh
}) => {
  const { colors } = useTheme();
  
  // Use consolidated data readiness hook
  const {
    isLoading,
    isRefreshing,
    allReady,
    checkItems,
    handleRefresh
  } = useDataReadiness(animalId, onRefresh);
  
  // Show loading state during initial checks
  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer, { backgroundColor: colors.card }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <DataReadinessHeader 
        isRefreshing={isRefreshing}
        onRefresh={handleRefresh}
      />
      
      <View style={styles.checkList}>
        {checkItems.map(check => (
          <CheckListItem
            key={check.id}
            icon={<check.icon size={20} color={colors.primary} />}
            title={check.title}
            message={check.message}
            isReady={check.isReady}
            themeColors={colors}
          />
        ))}
      </View>
      
      <DataReadinessSummary allReady={allReady} />
    </View>
  );
};

// Check if all systems are ready
const allReady = vitalsDeviceReady && vitalsDbReady && vaccineReady && gpsReady && speedReady;

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 200,
  },
  checkList: {
    marginBottom: 16,
  },
});

export default DataReadinessCheck;
