
import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Modal,
  Pressable
} from 'react-native';
import { Upload, Link, X, Camera } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';

interface ImagePickerModalProps {
  visible: boolean;
  onClose: () => void;
  onPickFromGallery: () => void;
  onPickFromUrl: () => void;
  onTakePhoto?: () => void;
}

const ImagePickerModal: React.FC<ImagePickerModalProps> = ({
  visible,
  onClose,
  onPickFromGallery,
  onPickFromUrl,
  onTakePhoto
}) => {
  const { colors } = useTheme();
  
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <Pressable 
        style={styles.overlay}
        onPress={onClose}
      >
        <View 
          style={[styles.modalContainer, { backgroundColor: colors.background }]}
          onStartShouldSetResponder={() => true}
          onTouchEnd={(e) => e.stopPropagation()}
        >
          <Text style={[styles.modalTitle, { color: colors.text }]}>Select Image</Text>
          
          <TouchableOpacity 
            style={styles.option}
            onPress={onPickFromGallery}
          >
            <Upload size={24} color={colors.primary} />
            <Text style={[styles.optionText, { color: colors.text }]}>Choose from Gallery</Text>
          </TouchableOpacity>
          
          {onTakePhoto && (
            <TouchableOpacity 
              style={styles.option}
              onPress={onTakePhoto}
            >
              <Camera size={24} color={colors.primary} />
              <Text style={[styles.optionText, { color: colors.text }]}>Take Photo</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity 
            style={styles.option}
            onPress={onPickFromUrl}
          >
            <Link size={24} color={colors.primary} />
            <Text style={[styles.optionText, { color: colors.text }]}>Enter Image URL</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.cancelButton, { backgroundColor: colors.error + '20' }]}
            onPress={onClose}
          >
            <Text style={[styles.cancelButtonText, { color: colors.error }]}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '80%',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 20,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  optionText: {
    fontSize: 16,
    marginLeft: 16,
  },
  cancelButton: {
    marginTop: 20,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    width: '100%',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default ImagePickerModal;
