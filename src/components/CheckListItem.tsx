import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { CheckCircle, XCircle } from 'lucide-react-native';

interface CheckListItemProps {
  icon: React.ReactNode;
  title: string;
  message: string;
  isReady: boolean;
  themeColors: {
    text: string;
    textLight: string;
    success: string;
    error: string;
    primary: string;
  };
}

const CheckListItem: React.FC<CheckListItemProps> = ({
  icon,
  title,
  message,
  isReady,
  themeColors
}) => {
  return (
    <View style={styles.checkItem}>
      <View style={styles.checkIcon}>
        {icon}
      </View>
      <View style={styles.checkContent}>
        <Text style={[styles.checkTitle, { color: themeColors.text }]}>
          {title}
        </Text>
        <Text style={[styles.checkMessage, { color: themeColors.textLight }]}>
          {message}
        </Text>
      </View>
      <View style={styles.statusIcon}>
        {isReady ? (
          <CheckCircle size={20} color={themeColors.success} />
        ) : (
          <XCircle size={20} color={themeColors.error} />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  checkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    marginRight: 12,
  },
  checkContent: {
    flex: 1,
  },
  checkTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  checkMessage: {
    fontSize: 14,
  },
  statusIcon: {
    marginLeft: 8,
  },
});

export default CheckListItem;