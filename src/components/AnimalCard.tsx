
import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  TouchableOpacity, 
  Dimensions,
  Platform 
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Heart, AlertTriangle, Shield } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

import { Animal } from '../mocks/animals';
import { HomeStackParamList } from '../navigation';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

interface AnimalCardProps {
  animal: Animal;
  compact?: boolean;
}

type AnimalCardNavigationProp = NativeStackNavigationProp<HomeStackParamList, 'Home'>;

const AnimalCard: React.FC<AnimalCardProps> = ({ animal, compact = false }) => {
  const navigation = useNavigation<AnimalCardNavigationProp>();
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  
  const handlePress = () => {
    navigation.navigate('AnimalDetail', { id: animal.id });
  };
  
  if (compact) {
    return (
      <TouchableOpacity 
        style={[styles.compactContainer, { 
          backgroundColor: colors.card,
          shadowColor: isDarkMode ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.15)'
        }]}
        onPress={handlePress}
        activeOpacity={0.9}
      >
        <View style={styles.compactImageContainer}>
          <Image 
            source={{ uri: animal.imageUrl }} 
            style={styles.compactImage} 
          />
          {animal.microchipId && (
            <View style={[styles.badgeContainer, { backgroundColor: colors.primary }]}>
              <Shield size={10} color="#FFF" />
            </View>
          )}
        </View>
        <View style={styles.compactContent}>
          <Text 
            style={[styles.compactName, { color: colors.text }]}
            numberOfLines={1}
          >
            {animal.name}
          </Text>
          <Text 
            style={[styles.compactBreed, { color: colors.textLight }]}
            numberOfLines={1}
          >
            {animal.breed}
          </Text>
        </View>
      </TouchableOpacity>
    );
  }
  
  return (
    <TouchableOpacity 
      style={[styles.container, { 
        backgroundColor: colors.card,
        shadowColor: isDarkMode ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.15)'
      }]}
      onPress={handlePress}
      activeOpacity={0.9}
    >
      <View style={styles.imageWrapper}>
        <Image 
          source={{ uri: animal.imageUrl }} 
          style={styles.image} 
        />
        <LinearGradient
          colors={['rgba(0,0,0,0.4)', 'transparent']}
          style={styles.imageGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 0.6 }}
        />
        {animal.deviceStatus === 'low_battery' && (
          <View style={styles.statusBadge}>
            <AlertTriangle size={14} color="#FFF" />
          </View>
        )}
      </View>
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[styles.name, { color: colors.text }]}>{animal.name}</Text>
          {animal.microchipId && (
            <View style={[styles.chipBadge, { backgroundColor: colors.primary }]}>
              <Shield size={12} color="#FFF" />
              <Text style={styles.chipText}>{t('id')}</Text>
            </View>
          )}
        </View>
        
        <Text style={[styles.breed, { color: colors.textLight }]}>{animal.breed}</Text>
        
        <View style={styles.details}>
          {animal.age && (
            <View style={[styles.detailBadge, { backgroundColor: isDarkMode ? colors.card : colors.background }]}>
              <Text style={[styles.detailText, { color: colors.textLight }]}>
                {t('ageYears', { age: animal.age })}
              </Text>
            </View>
          )}
          
          {animal.gender && (
            <View style={[styles.detailBadge, { backgroundColor: isDarkMode ? colors.card : colors.background }]}>
              <Text style={[styles.detailText, { color: colors.textLight }]}>
                {animal.gender === 'male' ? '♂' : '♀'}
              </Text>
            </View>
          )}
          
          {animal.location && (
            <View style={[styles.detailBadge, { backgroundColor: isDarkMode ? colors.card : colors.background }]}>
              <Text style={[styles.detailText, { color: colors.textLight }]}>
                {t('gps')}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  imageWrapper: {
    position: 'relative',
    height: 140,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  statusBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: '#F6AD55', // warning color
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  name: {
    fontSize: 18,
    fontWeight: '700',
    flex: 1,
  },
  chipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  chipText: {
    color: '#FFF',
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  breed: {
    fontSize: 14,
    marginBottom: 12,
  },
  details: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  detailText: {
    fontSize: 12,
    fontWeight: '500',
  },
  compactContainer: {
    width: width * 0.28,
    borderRadius: 16,
    overflow: 'hidden',
    marginRight: 12,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 2,
  },
  compactImageContainer: {
    position: 'relative',
  },
  compactImage: {
    width: '100%',
    height: width * 0.28,
    resizeMode: 'cover',
  },
  badgeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
  },
  compactContent: {
    padding: 10,
  },
  compactName: {
    fontSize: 14,
    fontWeight: '700',
    marginBottom: 2,
  },
  compactBreed: {
    fontSize: 12,
  },
});

export default AnimalCard;
