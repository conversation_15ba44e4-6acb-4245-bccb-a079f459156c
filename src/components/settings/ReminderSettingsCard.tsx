import React from 'react';
import { View, Text, StyleSheet, Switch, TextInput } from 'react-native';
import { Bell } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface ReminderSettingsCardProps {
  medicationReminders: boolean;
  onMedicationRemindersChange: (value: boolean) => void;
  feedingReminders: boolean;
  onFeedingRemindersChange: (value: boolean) => void;
  vaccinationReminders: boolean;
  onVaccinationRemindersChange: (value: boolean) => void;
  reminderTime: string;
  onReminderTimeChange: (value: string) => void;
}

const ReminderSettingsCard: React.FC<ReminderSettingsCardProps> = ({
  medicationReminders,
  onMedicationRemindersChange,
  feedingReminders,
  onFeedingRemindersChange,
  vaccinationReminders,
  onVaccinationRemindersChange,
  reminderTime,
  onReminderTimeChange
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.settingCard, { backgroundColor: colors.card }]}>
      <View style={styles.settingHeader}>
        <View style={styles.settingTitleContainer}>
          <Bell size={20} color={colors.primary} />
          <Text style={[styles.settingTitle, { color: colors.text }]}>Reminder Settings</Text>
        </View>
      </View>
      
      <View style={styles.reminderItem}>
        <Text style={[styles.reminderLabel, { color: colors.text }]}>Medication Reminders</Text>
        <Switch
          value={medicationReminders}
          onValueChange={onMedicationRemindersChange}
          trackColor={{ false: '#E2E8F0', true: colors.primaryLight }}
          thumbColor={medicationReminders ? colors.primary : '#f4f3f4'}
        />
      </View>
      
      <View style={styles.reminderItem}>
        <Text style={[styles.reminderLabel, { color: colors.text }]}>Feeding Reminders</Text>
        <Switch
          value={feedingReminders}
          onValueChange={onFeedingRemindersChange}
          trackColor={{ false: '#E2E8F0', true: colors.primaryLight }}
          thumbColor={feedingReminders ? colors.primary : '#f4f3f4'}
        />
      </View>
      
      <View style={styles.reminderItem}>
        <Text style={[styles.reminderLabel, { color: colors.text }]}>Vaccination Reminders</Text>
        <Switch
          value={vaccinationReminders}
          onValueChange={onVaccinationRemindersChange}
          trackColor={{ false: '#E2E8F0', true: colors.primaryLight }}
          thumbColor={vaccinationReminders ? colors.primary : '#f4f3f4'}
        />
      </View>
      
      <View style={[styles.divider, { backgroundColor: colors.border }]} />
      
      <View style={styles.inputContainer}>
        <Text style={[styles.inputLabel, { color: colors.textLight }]}>
          Remind me (minutes) before scheduled time:
        </Text>
        <TextInput
          style={[
            styles.textInput,
            {
              backgroundColor: colors.background,
              borderColor: colors.border,
              color: colors.text
            }
          ]}
          value={reminderTime}
          onChangeText={onReminderTimeChange}
          keyboardType="number-pad"
          placeholderTextColor={colors.textLight}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  settingCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  settingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  settingTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  reminderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  reminderLabel: {
    fontSize: 16,
  },
  divider: {
    height: 1,
    marginVertical: 16,
  },
  inputContainer: {
    marginTop: 8,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  textInput: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 10,
    fontSize: 16,
  },
});

export default ReminderSettingsCard;