import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  ActivityIndicator,
  Pressable
} from 'react-native';
import { X, Check, Globe } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { LanguageCode } from '../../assets/translations';

// Define explicit type for language info
type LanguageInfoType = {
  code: LanguageCode;
  name: string;
  nativeName: string;
  isRTL: boolean;
};

interface LanguageSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  availableLanguages: LanguageInfoType[];
  currentLanguage: LanguageCode;
  onLanguageSelect: (code: LanguageCode) => void;
  isChangingLanguage: boolean;
  t: (key: string) => string;
}

const LanguageSelectionModal: React.FC<LanguageSelectionModalProps> = ({
  visible,
  onClose,
  availableLanguages,
  currentLanguage,
  onLanguageSelect,
  isChangingLanguage,
  t
}) => {
  const { colors } = useTheme();

  const renderLanguageItem = ({ item }: { item: LanguageInfoType }) => {
    const isSelected = item.code === currentLanguage;
    
    return (
      <TouchableOpacity
        style={[
          styles.languageItem,
          {
            backgroundColor: isSelected ? colors.primary + '20' : 'transparent',
            borderBottomColor: colors.border,
          },
        ]}
        onPress={() => onLanguageSelect(item.code)}
        disabled={isChangingLanguage}
      >
        <View style={styles.languageItemContent}>
          <View style={styles.languageInfo}>
            <Text style={[styles.languageName, { color: colors.text }]}>
              {item.nativeName}
            </Text>
            <Text style={[styles.languageSubname, { color: colors.textLight }]}>
              {item.name}
            </Text>
          </View>
          
          {isSelected ? (
            <Check size={20} color={colors.primary} />
          ) : null}
          
          {item.isRTL ? (
            <View style={[styles.rtlBadge, { backgroundColor: colors.secondary }]}>
              <Text style={[styles.rtlBadgeText, { color: colors.card }]}>RTL</Text>
            </View>
          ) : null}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
        {/* Modal Header */}
        <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
          <View style={styles.headerContent}>
            <Globe size={24} color={colors.primary} />
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {t('selectLanguage')}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            disabled={isChangingLanguage}
          >
            <X size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        {/* Language List */}
        <FlatList
          data={availableLanguages}
          renderItem={renderLanguageItem}
          keyExtractor={(item) => item.code}
          style={styles.languageList}
          showsVerticalScrollIndicator={false}
        />

        {/* Loading Overlay */}
        {isChangingLanguage && (
          <View style={[styles.loadingOverlay, { backgroundColor: colors.background + 'CC' }]}>
            <View style={[styles.loadingContent, { backgroundColor: colors.card }]}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.text }]}>
                {t('changingLanguage')}
              </Text>
            </View>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginLeft: 12,
  },
  closeButton: {
    padding: 4,
  },
  languageList: {
    flex: 1,
  },
  languageItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  languageItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  languageSubname: {
    fontSize: 14,
  },
  rtlBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  rtlBadgeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    fontWeight: '500',
  },
});

export default LanguageSelectionModal;