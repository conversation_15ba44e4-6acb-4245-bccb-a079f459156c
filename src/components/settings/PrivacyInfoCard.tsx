import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Shield } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

const PrivacyInfoCard: React.FC = () => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.infoCard, { backgroundColor: colors.card }]}>
      <Shield size={24} color={colors.primary} />
      <Text style={[styles.infoText, { color: colors.text }]}>
        Your privacy is important to us. Control how your data is used and shared.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  infoCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  infoText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
  },
});

export default PrivacyInfoCard;