import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface DeleteAccountModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirmDelete: () => void;
}

const DeleteAccountModal: React.FC<DeleteAccountModalProps> = ({
  visible,
  onCancel,
  onConfirmDelete
}) => {
  const { colors } = useTheme();
  
  if (!visible) return null;
  
  return (
    <View style={styles.confirmOverlay}>
      <View style={[styles.confirmCard, { backgroundColor: colors.card }]}>
        <AlertTriangle size={48} color={colors.error} />
        <Text style={[styles.confirmTitle, { color: colors.error }]}>Delete Account?</Text>
        <Text style={[styles.confirmText, { color: colors.text }]}>
          This will permanently delete your account and all associated data. This action cannot be undone.
        </Text>
        
        <View style={styles.confirmButtons}>
          <TouchableOpacity 
            style={[styles.cancelButton, { borderColor: colors.border }]}
            onPress={onCancel}
          >
            <Text style={[styles.cancelButtonText, { color: colors.text }]}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.confirmDeleteButton, { backgroundColor: colors.error }]}
            onPress={onConfirmDelete}
          >
            <Text style={[styles.confirmDeleteText, { color: colors.card }]}>Delete</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  confirmOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmCard: {
    width: '80%',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
  },
  confirmTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  confirmText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  confirmButtons: {
    flexDirection: 'row',
    width: '100%',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    marginRight: 8,
    borderWidth: 1,
    borderRadius: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  confirmDeleteButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    marginLeft: 8,
    borderRadius: 8,
  },
  confirmDeleteText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default DeleteAccountModal;