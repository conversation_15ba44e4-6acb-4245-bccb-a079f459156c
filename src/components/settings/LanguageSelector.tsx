import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { ChevronDown, Globe } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { LanguageCode } from '../../assets/translations';
import LanguageSelectionModal from './LanguageSelectionModal';

// Define explicit type for language info
type LanguageInfoType = {
  code: LanguageCode;
  name: string;
  nativeName: string;
  isRTL: boolean;
};

interface LanguageSelectorProps {
  style?: any;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ style }) => {
  const { colors } = useTheme();
  const { currentLanguage, setLanguage, availableLanguages, t, isRTL } = useLanguage();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);

  const handleLanguageSelect = async (languageCode: LanguageCode) => {
    if (languageCode === currentLanguage) {
      setIsModalVisible(false);
      return;
    }

    try {
      setIsChangingLanguage(true);
      await setLanguage(languageCode);
      setIsModalVisible(false);
    } catch (error) {
      console.error('Error changing language:', error);
    } finally {
      setIsChangingLanguage(false);
    }
  };

  const currentLanguageInfo = availableLanguages.find(lang => lang.code === currentLanguage);



  return (
    <View style={[styles.container, style]}>
      {/* Language Setting Row */}
      <TouchableOpacity
        style={[
          styles.selectorButton,
          {
            backgroundColor: colors.card,
            borderColor: colors.border,
            flexDirection: isRTL ? 'row-reverse' : 'row',
          },
        ]}
        onPress={() => setIsModalVisible(true)}
        disabled={isChangingLanguage}
      >
        <View style={[styles.selectorContent, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
          <Globe size={20} color={colors.primary} style={isRTL ? styles.iconRTL : styles.icon} />
          
          <View style={[styles.selectorTextContainer, { alignItems: isRTL ? 'flex-end' : 'flex-start' }]}>
            <Text style={[styles.selectorLabel, { color: colors.text }]}>
              {t('language')}
            </Text>
            <Text style={[styles.selectorValue, { color: colors.textLight }]}>
              {currentLanguageInfo?.nativeName || 'English'}
            </Text>
          </View>
        </View>
        
        <View style={styles.selectorRight}>
          {isChangingLanguage ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <ChevronDown size={20} color={colors.textLight} />
          )}
        </View>
      </TouchableOpacity>

      {/* Language Selection Modal */}
      <LanguageSelectionModal
        visible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        availableLanguages={availableLanguages}
        currentLanguage={currentLanguage}
        onLanguageSelect={handleLanguageSelect}
        isChangingLanguage={isChangingLanguage}
        t={t}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 1.41,
  },
  selectorContent: {
    flex: 1,
    alignItems: 'center',
  },
  icon: {
    marginRight: 12,
  },
  iconRTL: {
    marginLeft: 12,
  },
  selectorTextContainer: {
    flex: 1,
  },
  selectorLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  selectorValue: {
    fontSize: 14,
  },
  selectorRight: {
    marginLeft: 8,
  },
});

export default LanguageSelector;