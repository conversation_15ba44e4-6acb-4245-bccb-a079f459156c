import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Trash2 } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface DeleteAccountSectionProps {
  onShowConfirm: () => void;
}

const DeleteAccountSection: React.FC<DeleteAccountSectionProps> = ({ onShowConfirm }) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.dangerZone}>
      <Text style={[styles.dangerZoneTitle, { color: colors.error }]}>Danger Zone</Text>
      
      <TouchableOpacity 
        style={[styles.deleteButton, { backgroundColor: 'rgba(229, 62, 62, 0.1)' }]}
        onPress={onShowConfirm}
      >
        <Trash2 size={20} color={colors.error} />
        <Text style={[styles.deleteButtonText, { color: colors.error }]}>Delete Account</Text>
      </TouchableOpacity>
      
      <Text style={[styles.dangerZoneDescription, { color: colors.textLight }]}>
        Deleting your account will permanently remove all your data, including animal records, 
        vital signs, and medication schedules. This action cannot be undone.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  dangerZone: {
    marginTop: 16,
    marginBottom: 24,
  },
  dangerZoneTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingVertical: 12,
    marginBottom: 16,
  },
  deleteButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  dangerZoneDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default DeleteAccountSection;