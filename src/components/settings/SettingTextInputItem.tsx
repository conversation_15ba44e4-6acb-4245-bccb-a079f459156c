import React from 'react';
import { View, Text, StyleSheet, TextInput, KeyboardTypeOptions } from 'react-native';
import { LucideIcon } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface SettingTextInputItemProps {
  icon: LucideIcon;
  title: string;
  description?: string;
  value: string;
  onValueChange: (value: string) => void;
  keyboardType?: KeyboardTypeOptions;
  placeholder?: string;
  label?: string;
  iconColor?: string;
}

const SettingTextInputItem: React.FC<SettingTextInputItemProps> = ({
  icon: Icon,
  title,
  description,
  value,
  onValueChange,
  keyboardType = 'default',
  placeholder,
  label,
  iconColor
}) => {
  const { colors } = useTheme();
  const finalIconColor = iconColor || colors.primary;
  
  return (
    <View style={[styles.settingCard, { backgroundColor: colors.card }]}>
      <View style={styles.settingHeader}>
        <View style={styles.settingTitleContainer}>
          <Icon size={20} color={finalIconColor} />
          <Text style={[styles.settingTitle, { color: colors.text }]}>{title}</Text>
        </View>
      </View>
      
      {description && (
        <Text style={[styles.settingDescription, { color: colors.textLight }]}>
          {description}
        </Text>
      )}
      
      <View style={styles.inputContainer}>
        {label && (
          <Text style={[styles.inputLabel, { color: colors.textLight }]}>{label}</Text>
        )}
        <TextInput
          style={[
            styles.textInput,
            {
              backgroundColor: colors.background,
              borderColor: colors.border,
              color: colors.text
            }
          ]}
          value={value}
          onChangeText={onValueChange}
          keyboardType={keyboardType}
          placeholder={placeholder}
          placeholderTextColor={colors.textLight}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  settingCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  settingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  settingTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  settingDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  inputContainer: {
    marginTop: 8,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  textInput: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 10,
    fontSize: 16,
  },
});

export default SettingTextInputItem;