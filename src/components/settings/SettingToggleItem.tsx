import React from 'react';
import { View, Text, StyleSheet, Switch } from 'react-native';
import { LucideIcon } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface SettingToggleItemProps {
  icon: LucideIcon;
  title: string;
  description?: string;
  value: boolean;
  onValueChange: (value: boolean) => void;
  iconColor?: string;
}

const SettingToggleItem: React.FC<SettingToggleItemProps> = ({
  icon: Icon,
  title,
  description,
  value,
  onValueChange,
  iconColor
}) => {
  const { colors } = useTheme();
  const finalIconColor = iconColor || colors.primary;
  
  return (
    <View style={[styles.settingCard, { backgroundColor: colors.card }]}>
      <View style={styles.settingHeader}>
        <View style={styles.settingTitleContainer}>
          <Icon size={20} color={finalIconColor} />
          <Text style={[styles.settingTitle, { color: colors.text }]}>{title}</Text>
        </View>
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#E2E8F0', true: colors.primaryLight }}
          thumbColor={value ? colors.primary : '#f4f3f4'}
        />
      </View>
      {description && (
        <Text style={[styles.settingDescription, { color: colors.textLight }]}>
          {description}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  settingCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  settingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  settingTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
    flex: 1,
  },
  settingDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default SettingToggleItem;