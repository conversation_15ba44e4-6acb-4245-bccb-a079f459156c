import React from 'react';
import { View, Text, StyleSheet, TextInput, KeyboardTypeOptions } from 'react-native';
import { LucideIcon } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface SettingRangeInputItemProps {
  icon: LucideIcon;
  title: string;
  description?: string;
  minLabel: string;
  minValue: string;
  onMinChange: (value: string) => void;
  maxLabel: string;
  maxValue: string;
  onMaxChange: (value: string) => void;
  keyboardType?: KeyboardTypeOptions;
  minPlaceholder?: string;
  maxPlaceholder?: string;
  iconColor?: string;
}

const SettingRangeInputItem: React.FC<SettingRangeInputItemProps> = ({
  icon: Icon,
  title,
  description,
  minLabel,
  minValue,
  onMinChange,
  maxLabel,
  maxValue,
  onMaxChange,
  keyboardType = 'numeric',
  minPlaceholder,
  maxPlaceholder,
  iconColor
}) => {
  const { colors } = useTheme();
  const finalIconColor = iconColor || colors.primary;
  
  return (
    <View style={[styles.settingCard, { backgroundColor: colors.card }]}>
      <View style={styles.settingHeader}>
        <View style={styles.settingTitleContainer}>
          <Icon size={20} color={finalIconColor} />
          <Text style={[styles.settingTitle, { color: colors.text }]}>{title}</Text>
        </View>
      </View>
      
      {description && (
        <Text style={[styles.settingDescription, { color: colors.textLight }]}>
          {description}
        </Text>
      )}
      
      <View style={styles.rangeContainer}>
        <View style={styles.rangeInputGroup}>
          <Text style={[styles.rangeLabel, { color: colors.textLight }]}>{minLabel}</Text>
          <TextInput
            style={[
              styles.rangeInput,
              {
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text
              }
            ]}
            value={minValue}
            onChangeText={onMinChange}
            keyboardType={keyboardType}
            placeholder={minPlaceholder}
            placeholderTextColor={colors.textLight}
          />
        </View>
        
        <View style={styles.rangeInputGroup}>
          <Text style={[styles.rangeLabel, { color: colors.textLight }]}>{maxLabel}</Text>
          <TextInput
            style={[
              styles.rangeInput,
              {
                backgroundColor: colors.background,
                borderColor: colors.border,
                color: colors.text
              }
            ]}
            value={maxValue}
            onChangeText={onMaxChange}
            keyboardType={keyboardType}
            placeholder={maxPlaceholder}
            placeholderTextColor={colors.textLight}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  settingCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  settingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  settingTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  settingDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  rangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rangeInputGroup: {
    width: '48%',
  },
  rangeLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  rangeInput: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 10,
    fontSize: 16,
  },
});

export default SettingRangeInputItem;