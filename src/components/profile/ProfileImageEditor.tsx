import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Camera, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { useTheme } from '../../contexts/ThemeContext';
import { toast } from 'sonner-native';
import { useUserStore } from '../../store/userStore';
import { checkStorageSetup, initializeStorage } from '../../utils/setupStorage';

export interface ProfileImageEditorProps {
  imageUrl: string;
  onImageChange: (imageUrl: string) => void;
  userEmail?: string;
}

const ProfileImageEditor: React.FC<ProfileImageEditorProps> = ({
  imageUrl,
  onImageChange,
  userEmail
}) => {
  const { colors } = useTheme();
  const { uploadProfileImage, isLoading: userStoreLoading } = useUserStore();
  const [isUploading, setIsUploading] = useState(false);
  const [storageChecked, setStorageChecked] = useState(false);
  
  const getProfileImage = () => {
    // Use user's profile image if available, otherwise use default
    if (imageUrl) {
      return { uri: imageUrl };
    }
    
    // Default profile image based on email's first letter
    const letter = userEmail?.charAt(0).toUpperCase() || 'U';
    return { uri: `https://ui-avatars.com/api/?name=${letter}&background=3D8C91&color=fff&size=200` };
  };
  
  const handlePickImage = async () => {
    try {
      // Check storage setup first
      if (!storageChecked) {
        const isSetup = await checkStorageSetup();
        if (!isSetup) {
          toast.error('Setting up storage...');
          const setupSuccess = await initializeStorage();
          if (!setupSuccess) {
            toast.error('Storage setup failed. Please try again later.');
            return;
          }
        }
        setStorageChecked(true);
      }
      
      // Request permission
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        toast.error('Permission to access gallery was denied');
        return;
      }
      
      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        allowsMultipleSelection: false,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setIsUploading(true);
        
        try {
          const imageUri = result.assets[0].uri;
          
          // Upload to Supabase Storage and update profile
          const uploadedUrl = await uploadProfileImage(imageUri);
          
          // Update the parent component with the new URL
          onImageChange(uploadedUrl);
          
          // Success toast is handled by uploadProfileImage
        } catch (error) {
          console.error('Error uploading image:', error);
          // Error toast is handled by uploadProfileImage
        } finally {
          setIsUploading(false);
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      toast.error('Failed to pick image');
      setIsUploading(false);
    }
  };
  
  const handleRemoveImage = () => {
    Alert.alert(
      'Remove Profile Picture',
      'Are you sure you want to remove your profile picture?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Remove', 
          style: 'destructive',
          onPress: async () => {
            try {
              setIsUploading(true);
              
              // Update profile to remove image URL using the store
              const { updateProfile } = useUserStore.getState();
              await updateProfile({ imageUrl: '' });
              onImageChange('');
              
              toast.success('Profile picture removed');
            } catch (error) {
              console.error('Error removing image:', error);
              toast.error('Failed to remove profile picture');
            } finally {
              setIsUploading(false);
            }
          }
        }
      ]
    );
  };
  
  return (
    <View style={styles.imageSection}>
      <View style={styles.imageContainer}>
        <Image 
          source={getProfileImage()}
          style={styles.profileImage}
        />
        
        {(isUploading || userStoreLoading) && (
          <View style={[styles.uploadingOverlay, { backgroundColor: colors.background + 'CC' }]}>
            <ActivityIndicator color={colors.primary} size="large" />
          </View>
        )}
      </View>
      
      <View style={styles.imageActions}>
        <TouchableOpacity
          style={[styles.imageButton, { backgroundColor: colors.primary }]}
          onPress={handlePickImage}
          disabled={isUploading || userStoreLoading}
        >
          <Camera size={18} color="#FFF" />
          <Text style={styles.imageButtonText}>Gallery</Text>
        </TouchableOpacity>
        
        {imageUrl ? (
          <TouchableOpacity
            style={[styles.imageButton, { backgroundColor: colors.error }]}
            onPress={handleRemoveImage}
            disabled={isUploading || userStoreLoading}
          >
            <X size={18} color="#FFF" />
            <Text style={styles.imageButtonText}>Remove</Text>
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  imageSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  imageContainer: {
    position: 'relative',
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 16,
    overflow: 'hidden',
  },
  profileImage: {
    width: '100%',
    height: '100%',
    borderRadius: 60,
  },
  uploadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 60,
  },
  imageActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
  },
  imageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    gap: 6,
  },
  imageButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ProfileImageEditor;