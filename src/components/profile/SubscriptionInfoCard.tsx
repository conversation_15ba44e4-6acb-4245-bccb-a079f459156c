import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Crown } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';

export interface SubscriptionInfoCardProps {
  subscriptionType?: string;
  subscriptionEndDate?: string;
  onManageSubscriptionPress: () => void;
}

const SubscriptionInfoCard: React.FC<SubscriptionInfoCardProps> = ({
  subscriptionType,
  subscriptionEndDate,
  onManageSubscriptionPress
}) => {
  const { colors } = useTheme();
  
  const formatSubscriptionDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };
  
  return (
    <Card style={styles.subscriptionCard}>
      <View style={styles.subscriptionHeader}>
        <Crown size={20} color={colors.accent1} />
        <Text style={[styles.subscriptionTitle, { color: colors.text }]}>
          Premium Subscription
        </Text>
      </View>
      
      <View style={styles.subscriptionDetails}>
        <View style={styles.subscriptionItem}>
          <Text style={[styles.subscriptionLabel, { color: colors.textLight }]}>
            Type
          </Text>
          <Text style={[styles.subscriptionValue, { color: colors.text }]}>
            {subscriptionType?.charAt(0).toUpperCase() + subscriptionType?.slice(1) || 'Monthly'}
          </Text>
        </View>
        
        <View style={styles.subscriptionItem}>
          <Text style={[styles.subscriptionLabel, { color: colors.textLight }]}>
            Renewal Date
          </Text>
          <Text style={[styles.subscriptionValue, { color: colors.text }]}>
            {formatSubscriptionDate(subscriptionEndDate)}
          </Text>
        </View>
      </View>
      
      <TouchableOpacity
        style={[styles.manageSubscriptionButton, { backgroundColor: colors.accent1 + '20' }]}
        onPress={onManageSubscriptionPress}
      >
        <Text style={[styles.manageSubscriptionText, { color: colors.accent1 }]}>
          Manage Subscription
        </Text>
      </TouchableOpacity>
    </Card>
  );
};

const styles = StyleSheet.create({
  subscriptionCard: {
    padding: 16,
    marginBottom: 24,
  },
  subscriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  subscriptionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  subscriptionDetails: {
    marginBottom: 16,
  },
  subscriptionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  subscriptionLabel: {
    fontSize: 14,
  },
  subscriptionValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  manageSubscriptionButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  manageSubscriptionText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SubscriptionInfoCard;