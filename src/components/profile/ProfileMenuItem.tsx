import React from 'react';
import { Text, StyleSheet } from 'react-native';
import { ChevronRight, LucideIcon } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';

export interface ProfileMenuItemProps {
  icon: LucideIcon;
  text: string;
  onPress: () => void;
  iconColor?: string;
}

const ProfileMenuItem: React.FC<ProfileMenuItemProps> = ({
  icon: Icon,
  text,
  onPress,
  iconColor
}) => {
  const { colors } = useTheme();
  const finalIconColor = iconColor || colors.primary;
  
  return (
    <Card style={styles.menuCard} onPress={onPress}>
      <Icon size={20} color={finalIconColor} />
      <Text style={[styles.menuItemText, { color: colors.text }]}>{text}</Text>
      <ChevronRight size={20} color={colors.textLight} />
    </Card>
  );
};

const styles = StyleSheet.create({
  menuCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
    marginLeft: 12,
  },
});

export default ProfileMenuItem;