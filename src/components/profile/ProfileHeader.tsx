import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Crown } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

export interface ProfileHeaderProps {
  user: {
    imageUrl?: string;
    name?: string;
    email?: string;
    isPremium?: boolean;
  } | null;
  onEditProfilePress: () => void;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({ user, onEditProfilePress }) => {
  const { colors } = useTheme();
  
  const getProfileImage = () => {
    // Use user's profile image if available, otherwise use default
    if (user?.imageUrl) {
      return { uri: user.imageUrl };
    }
    
    // Default profile image based on email's first letter
    const letter = user?.email?.charAt(0).toUpperCase() || 'U';
    return { uri: `https://ui-avatars.com/api/?name=${letter}&background=3D8C91&color=fff&size=200` };
  };
  
  const getDisplayName = () => {
    // Use user's name if available, otherwise use email
    if (user?.name) {
      return user.name;
    }
    
    // Extract name from email
    if (user?.email) {
      const emailName = user.email.split('@')[0];
      // Convert to title case
      return emailName
        .split(/[._-]/)
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
    
    return 'User';
  };
  
  return (
    <View style={styles.header}>
      <View style={styles.profileImageContainer}>
        <Image 
          source={getProfileImage()}
          style={styles.profileImage}
        />
        {user?.isPremium && (
          <View style={[styles.premiumBadge, { backgroundColor: colors.accent1, borderColor: colors.white }]}>
            <Crown size={12} color={colors.white} />
          </View>
        )}
      </View>
      
      <Text style={[styles.userName, { color: colors.text }]}>
        {getDisplayName()}
      </Text>
      
      <Text style={[styles.userEmail, { color: colors.textLight }]}>
        {user?.email}
      </Text>
      
      <TouchableOpacity
        style={[styles.editProfileButton, { backgroundColor: colors.primary }]}
        onPress={onEditProfilePress}
      >
        <Text style={[styles.editProfileButtonText, { color: colors.white }]}>Edit Profile</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: 24,
    marginBottom: 24,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  premiumBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFF',
  },
  userName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
    textAlign: 'center',
  },
  userEmail: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  editProfileButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  editProfileButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProfileHeader;