import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Modal,
  ScrollView
} from 'react-native';
import { 
  Lightbulb, 
  ChevronRight, 
  X,
  DollarSign,
  Clock,
  Wrench,
  Target
} from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';

interface PersonalizedRecommendation {
  title: string;
  description: string;
  category: 'temperature' | 'humidity' | 'air_quality' | 'shelter' | 'nutrition';
  difficulty: 'easy' | 'moderate' | 'difficult';
  cost_estimate: 'low' | 'medium' | 'high';
  timeframe: 'immediate' | '1-2_weeks' | '1-3_months';
}

interface PersonalizedRecommendationsCardProps {
  recommendations: PersonalizedRecommendation[];
  onImplement?: (recommendation: PersonalizedRecommendation) => void;
}

const PersonalizedRecommendationsCard: React.FC<PersonalizedRecommendationsCardProps> = ({
  recommendations,
  onImplement
}) => {
  const { colors } = useTheme();
  const [selectedRecommendation, setSelectedRecommendation] = useState<PersonalizedRecommendation | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'temperature': return '🌡️';
      case 'humidity': return '💧';
      case 'air_quality': return '🌬️';
      case 'shelter': return '🏠';
      case 'nutrition': return '🌿';
      default: return '📊';
    }
  };
  
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#10B981';
      case 'moderate': return '#F59E0B';
      case 'difficult': return '#EF4444';
      default: return colors.textLight;
    }
  };
  
  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'low': return '#10B981';
      case 'medium': return '#F59E0B';
      case 'high': return '#EF4444';
      default: return colors.textLight;
    }
  };
  
  const getTimeframeText = (timeframe: string) => {
    switch (timeframe) {
      case 'immediate': return 'Start Today';
      case '1-2_weeks': return '1-2 Weeks';
      case '1-3_months': return '1-3 Months';
      default: return 'Schedule';
    }
  };
  
  const handleRecommendationPress = (recommendation: PersonalizedRecommendation) => {
    setSelectedRecommendation(recommendation);
    setModalVisible(true);
  };
  
  const handleImplement = () => {
    if (selectedRecommendation && onImplement) {
      onImplement(selectedRecommendation);
    }
    setModalVisible(false);
    setSelectedRecommendation(null);
  };
  
  const renderRecommendationItem = ({ item }: { item: PersonalizedRecommendation }) => (
    <TouchableOpacity
      style={[styles.recommendationItem, { borderColor: colors.border }]}
      onPress={() => handleRecommendationPress(item)}
    >
      <View style={styles.recommendationHeader}>
        <View style={styles.categoryContainer}>
          <Text style={styles.categoryEmoji}>
            {getCategoryIcon(item.category)}
          </Text>
          <Text style={[styles.categoryText, { color: colors.textLight }]}>
            {item.category.replace('_', ' ').toUpperCase()}
          </Text>
        </View>
        
        <ChevronRight size={16} color={colors.textLight} />
      </View>
      
      <Text style={[styles.recommendationTitle, { color: colors.text }]}>
        {item.title}
      </Text>
      
      <Text style={[styles.recommendationDescription, { color: colors.textLight }]} numberOfLines={2}>
        {item.description}
      </Text>
      
      <View style={styles.recommendationMeta}>
        <View style={styles.metaItem}>
          <Wrench size={12} color={getDifficultyColor(item.difficulty)} />
          <Text style={[styles.metaText, { color: getDifficultyColor(item.difficulty) }]}>
            {item.difficulty}
          </Text>
        </View>
        
        <View style={styles.metaItem}>
          <DollarSign size={12} color={getCostColor(item.cost_estimate)} />
          <Text style={[styles.metaText, { color: getCostColor(item.cost_estimate) }]}>
            {item.cost_estimate} cost
          </Text>
        </View>
        
        <View style={styles.metaItem}>
          <Clock size={12} color={colors.primary} />
          <Text style={[styles.metaText, { color: colors.primary }]}>
            {getTimeframeText(item.timeframe)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
  
  if (!recommendations || recommendations.length === 0) {
    return (
      <Card style={styles.container}>
        <View style={styles.header}>
          <Lightbulb size={20} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>
            Personalized Recommendations
          </Text>
        </View>
        
        <View style={styles.emptyState}>
          <Target size={32} color={colors.success} />
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            Environment is well-optimized
          </Text>
          <Text style={[styles.emptySubtext, { color: colors.textLight }]}>
            No specific recommendations at this time
          </Text>
        </View>
      </Card>
    );
  }
  
  return (
    <>
      <Card style={styles.container}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Lightbulb size={20} color={colors.primary} />
            <Text style={[styles.title, { color: colors.text }]}>
              Personalized Recommendations
            </Text>
          </View>
          
          <View style={styles.recommendationCount}>
            <Text style={[styles.countText, { color: colors.primary }]}>
              {recommendations.length} tip{recommendations.length !== 1 ? 's' : ''}
            </Text>
          </View>
        </View>
        
        <FlatList
          data={recommendations.slice(0, 3)}
          renderItem={renderRecommendationItem}
          keyExtractor={(_, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          style={styles.recommendationsList}
        />
        
        {recommendations.length > 3 && (
          <TouchableOpacity
            style={[styles.showMoreButton, { borderColor: colors.border }]}
            onPress={() => {
              // Could navigate to full recommendations screen
            }}
          >
            <Text style={[styles.showMoreText, { color: colors.primary }]}>
              View {recommendations.length - 3} more recommendations
            </Text>
            <ChevronRight size={16} color={colors.primary} />
          </TouchableOpacity>
        )}
      </Card>
      
      {/* Recommendation Detail Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Recommendation Details
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setModalVisible(false)}
            >
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          {selectedRecommendation && (
            <ScrollView style={styles.modalContent}>
              <View style={styles.modalCategoryContainer}>
                <Text style={styles.modalCategoryEmoji}>
                  {getCategoryIcon(selectedRecommendation.category)}
                </Text>
                <Text style={[styles.modalCategoryText, { color: colors.textLight }]}>
                  {selectedRecommendation.category.replace('_', ' ').toUpperCase()}
                </Text>
              </View>
              
              <Text style={[styles.modalRecommendationTitle, { color: colors.text }]}>
                {selectedRecommendation.title}
              </Text>
              
              <Text style={[styles.modalRecommendationDescription, { color: colors.text }]}>
                {selectedRecommendation.description}
              </Text>
              
              <View style={styles.modalMetaContainer}>
                <View style={[styles.modalMetaItem, { backgroundColor: colors.card }]}>
                  <Wrench size={16} color={getDifficultyColor(selectedRecommendation.difficulty)} />
                  <Text style={[styles.modalMetaLabel, { color: colors.textLight }]}>Difficulty</Text>
                  <Text style={[styles.modalMetaValue, { color: getDifficultyColor(selectedRecommendation.difficulty) }]}>
                    {selectedRecommendation.difficulty}
                  </Text>
                </View>
                
                <View style={[styles.modalMetaItem, { backgroundColor: colors.card }]}>
                  <DollarSign size={16} color={getCostColor(selectedRecommendation.cost_estimate)} />
                  <Text style={[styles.modalMetaLabel, { color: colors.textLight }]}>Cost</Text>
                  <Text style={[styles.modalMetaValue, { color: getCostColor(selectedRecommendation.cost_estimate) }]}>
                    {selectedRecommendation.cost_estimate}
                  </Text>
                </View>
                
                <View style={[styles.modalMetaItem, { backgroundColor: colors.card }]}>
                  <Clock size={16} color={colors.primary} />
                  <Text style={[styles.modalMetaLabel, { color: colors.textLight }]}>Timeframe</Text>
                  <Text style={[styles.modalMetaValue, { color: colors.primary }]}>
                    {getTimeframeText(selectedRecommendation.timeframe)}
                  </Text>
                </View>
              </View>
              
              {onImplement && (
                <TouchableOpacity
                  style={[styles.implementButton, { backgroundColor: colors.primary }]}
                  onPress={handleImplement}
                >
                  <Target size={20} color="#FFFFFF" />
                  <Text style={styles.implementButtonText}>
                    Start Implementation
                  </Text>
                </TouchableOpacity>
              )}
            </ScrollView>
          )}
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  recommendationCount: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  countText: {
    fontSize: 12,
    fontWeight: '600',
  },
  recommendationsList: {
    maxHeight: 400,
  },
  recommendationItem: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  recommendationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  categoryEmoji: {
    fontSize: 16,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '600',
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 6,
  },
  recommendationDescription: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 8,
  },
  recommendationMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 10,
    fontWeight: '500',
  },
  showMoreButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    marginTop: 8,
    gap: 4,
  },
  showMoreText: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalCategoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  modalCategoryEmoji: {
    fontSize: 24,
  },
  modalCategoryText: {
    fontSize: 14,
    fontWeight: '600',
  },
  modalRecommendationTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  modalRecommendationDescription: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 24,
  },
  modalMetaContainer: {
    gap: 12,
    marginBottom: 24,
  },
  modalMetaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  modalMetaLabel: {
    fontSize: 14,
    flex: 1,
  },
  modalMetaValue: {
    fontSize: 14,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  implementButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  implementButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PersonalizedRecommendationsCard;