import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList
} from 'react-native';
import { 
  Zap, 
  Clock, 
  Calendar, 
  CheckCircle,
  AlertTriangle,
  ArrowRight
} from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';

interface PriorityAction {
  action: string;
  urgency: 'immediate' | 'within_week' | 'within_month';
  expected_benefit: string;
}

interface PriorityActionsCardProps {
  actions: PriorityAction[];
  onActionPress?: (action: PriorityAction) => void;
}

const PriorityActionsCard: React.FC<PriorityActionsCardProps> = ({
  actions,
  onActionPress
}) => {
  const { colors } = useTheme();
  
  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return <AlertTriangle size={16} color="#EF4444" />;
      case 'within_week': return <Clock size={16} color="#F59E0B" />;
      case 'within_month': return <Calendar size={16} color="#3B82F6" />;
      default: return <Clock size={16} color={colors.textLight} />;
    }
  };
  
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return '#EF4444';
      case 'within_week': return '#F59E0B';
      case 'within_month': return '#3B82F6';
      default: return colors.textLight;
    }
  };
  
  const getUrgencyText = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return 'Act Now';
      case 'within_week': return 'This Week';
      case 'within_month': return 'This Month';
      default: return 'Schedule';
    }
  };
  
  const renderActionItem = ({ item, index }: { item: PriorityAction; index: number }) => (
    <TouchableOpacity
      style={[styles.actionItem, { borderColor: colors.border }]}
      onPress={() => onActionPress?.(item)}
      disabled={!onActionPress}
    >
      <View style={styles.actionHeader}>
        <View style={styles.urgencyContainer}>
          {getUrgencyIcon(item.urgency)}
          <Text style={[
            styles.urgencyText,
            { color: getUrgencyColor(item.urgency) }
          ]}>
            {getUrgencyText(item.urgency)}
          </Text>
        </View>
        
        <View style={styles.actionNumber}>
          <Text style={[styles.actionNumberText, { color: colors.primary }]}>
            #{index + 1}
          </Text>
        </View>
      </View>
      
      <Text style={[styles.actionText, { color: colors.text }]}>
        {item.action}
      </Text>
      
      <View style={styles.benefitContainer}>
        <CheckCircle size={14} color={colors.success} />
        <Text style={[styles.benefitText, { color: colors.textLight }]}>
          {item.expected_benefit}
        </Text>
      </View>
      
      {onActionPress && (
        <View style={styles.actionFooter}>
          <ArrowRight size={16} color={colors.primary} />
          <Text style={[styles.actionButtonText, { color: colors.primary }]}>
            Take Action
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
  
  if (!actions || actions.length === 0) {
    return (
      <Card style={styles.container}>
        <View style={styles.header}>
          <Zap size={20} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>
            Priority Actions
          </Text>
        </View>
        
        <View style={styles.emptyState}>
          <CheckCircle size={32} color={colors.success} />
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            No immediate actions needed
          </Text>
          <Text style={[styles.emptySubtext, { color: colors.textLight }]}>
            Your animal's environment is well-optimized!
          </Text>
        </View>
      </Card>
    );
  }
  
  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Zap size={20} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>
            Priority Actions
          </Text>
        </View>
        
        <View style={styles.actionCount}>
          <Text style={[styles.countText, { color: colors.primary }]}>
            {actions.length} action{actions.length !== 1 ? 's' : ''}
          </Text>
        </View>
      </View>
      
      <FlatList
        data={actions}
        renderItem={renderActionItem}
        keyExtractor={(_, index) => index.toString()}
        showsVerticalScrollIndicator={false}
        style={styles.actionsList}
      />
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  actionCount: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  countText: {
    fontSize: 12,
    fontWeight: '600',
  },
  actionsList: {
    maxHeight: 400,
  },
  actionItem: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 12,
  },
  actionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  urgencyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  urgencyText: {
    fontSize: 12,
    fontWeight: '600',
  },
  actionNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionNumberText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 18,
    marginBottom: 8,
  },
  benefitContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 8,
  },
  benefitText: {
    fontSize: 12,
    lineHeight: 16,
    flex: 1,
  },
  actionFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 4,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default PriorityActionsCard;