import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView
} from 'react-native';
import { 
  Brain, 
  Lightbulb, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  ChevronRight 
} from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';

interface EnvironmentalInsight {
  category: 'temperature' | 'humidity' | 'air_quality' | 'seasonal' | 'stress';
  finding: string;
  impact_level: 'low' | 'moderate' | 'high' | 'critical';
  confidence: number;
  actionable: boolean;
}

interface EnvironmentalInsightsCardProps {
  insights: EnvironmentalInsight[];
  explanation: string;
  onViewDetails?: () => void;
}

const EnvironmentalInsightsCard: React.FC<EnvironmentalInsightsCardProps> = ({
  insights,
  explanation,
  onViewDetails
}) => {
  const { colors } = useTheme();
  
  const getInsightIcon = (category: string) => {
    switch (category) {
      case 'temperature': return '🌡️';
      case 'humidity': return '💧';
      case 'air_quality': return '🌬️';
      case 'seasonal': return '🍃';
      case 'stress': return '😰';
      default: return '📊';
    }
  };
  
  const getImpactColor = (level: string) => {
    switch (level) {
      case 'critical': return '#EF4444';
      case 'high': return '#F59E0B';
      case 'moderate': return '#3B82F6';
      case 'low': return '#10B981';
      default: return colors.textLight;
    }
  };
  
  const getConfidenceLevel = (confidence: number) => {
    if (confidence >= 0.8) return 'High';
    if (confidence >= 0.6) return 'Medium';
    return 'Low';
  };
  
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#10B981';
    if (confidence >= 0.6) return '#F59E0B';
    return '#EF4444';
  };
  
  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Brain size={20} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>
            AI Environmental Insights
          </Text>
        </View>
        
        {onViewDetails && (
          <TouchableOpacity
            style={styles.viewDetailsButton}
            onPress={onViewDetails}
          >
            <Text style={[styles.viewDetailsText, { color: colors.primary }]}>
              View All
            </Text>
            <ChevronRight size={16} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>
      
      {/* AI Explanation */}
      {explanation && (
        <View style={[styles.explanationContainer, { backgroundColor: colors.background }]}>
          <Text style={[styles.explanationText, { color: colors.text }]}>
            {explanation}
          </Text>
        </View>
      )}
      
      {/* Insights List */}
      <ScrollView 
        style={styles.insightsContainer}
        showsVerticalScrollIndicator={false}
      >
        {insights.slice(0, 3).map((insight, index) => (
          <View key={index} style={[styles.insightItem, { borderColor: colors.border }]}>
            <View style={styles.insightHeader}>
              <View style={styles.insightCategory}>
                <Text style={styles.categoryEmoji}>
                  {getInsightIcon(insight.category)}
                </Text>
                <Text style={[styles.categoryText, { color: colors.text }]}>
                  {insight.category.replace('_', ' ').toUpperCase()}
                </Text>
              </View>
              
              <View style={styles.insightMeta}>
                <View style={[
                  styles.impactBadge,
                  { backgroundColor: getImpactColor(insight.impact_level) }
                ]}>
                  <Text style={styles.impactText}>
                    {insight.impact_level.toUpperCase()}
                  </Text>
                </View>
                
                <View style={styles.confidenceContainer}>
                  <Text style={[styles.confidenceLabel, { color: colors.textLight }]}>
                    Confidence:
                  </Text>
                  <Text style={[
                    styles.confidenceValue,
                    { color: getConfidenceColor(insight.confidence) }
                  ]}>
                    {getConfidenceLevel(insight.confidence)}
                  </Text>
                </View>
              </View>
            </View>
            
            <Text style={[styles.insightFinding, { color: colors.text }]}>
              {insight.finding}
            </Text>
            
            {insight.actionable && (
              <View style={styles.actionableIndicator}>
                <CheckCircle size={14} color={colors.success} />
                <Text style={[styles.actionableText, { color: colors.success }]}>
                  Actionable insight
                </Text>
              </View>
            )}
          </View>
        ))}
      </ScrollView>
      
      {insights.length > 3 && (
        <TouchableOpacity
          style={[styles.showMoreButton, { borderColor: colors.border }]}
          onPress={onViewDetails}
        >
          <Text style={[styles.showMoreText, { color: colors.primary }]}>
            View {insights.length - 3} more insights
          </Text>
          <ChevronRight size={16} color={colors.primary} />
        </TouchableOpacity>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  viewDetailsText: {
    fontSize: 14,
    fontWeight: '500',
  },
  explanationContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  explanationText: {
    fontSize: 14,
    lineHeight: 20,
  },
  insightsContainer: {
    maxHeight: 300,
  },
  insightItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    marginBottom: 8,
  },
  insightHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  insightCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    flex: 1,
  },
  categoryEmoji: {
    fontSize: 16,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '600',
  },
  insightMeta: {
    alignItems: 'flex-end',
    gap: 4,
  },
  impactBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  impactText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  confidenceLabel: {
    fontSize: 10,
  },
  confidenceValue: {
    fontSize: 10,
    fontWeight: '600',
  },
  insightFinding: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 6,
  },
  actionableIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  actionableText: {
    fontSize: 12,
    fontWeight: '500',
  },
  showMoreButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    marginTop: 8,
    gap: 4,
  },
  showMoreText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default EnvironmentalInsightsCard;