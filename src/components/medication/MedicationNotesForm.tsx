import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface MedicationNotesFormProps {
  onDataChange: (notes: string) => void;
  initialNotes?: string;
}

const MedicationNotesForm: React.FC<MedicationNotesFormProps> = ({
  onDataChange,
  initialNotes = ''
}) => {
  const { colors } = useTheme();
  
  // Form state
  const [notes, setNotes] = useState(initialNotes);
  
  // Update parent component when data changes
  useEffect(() => {
    onDataChange(notes);
  }, [notes, onDataChange]);
  
  return (
    <View style={styles.formGroup}>
      <Text style={[styles.label, { color: colors.text }]}>Notes (Optional)</Text>
      <TextInput
        style={[
          styles.textArea, 
          { backgroundColor: colors.card, color: colors.text, borderColor: colors.border }
        ]}
        value={notes}
        onChangeText={setNotes}
        placeholder="Add any additional notes"
        placeholderTextColor={colors.textLight}
        multiline
        numberOfLines={4}
        textAlignVertical="top"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  textArea: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 12,
    borderWidth: 1,
    fontSize: 16,
    minHeight: 100,
  },
});

export default MedicationNotesForm;