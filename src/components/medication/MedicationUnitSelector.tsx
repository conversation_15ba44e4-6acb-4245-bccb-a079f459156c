import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface MedicationUnitSelectorProps {
  value: string;
  onValueChange: (unit: string) => void;
}

/**
 * @magic_description Medication dosage unit selector component
 * Handles selection between ml, mg, and g units with button interface
 */
const MedicationUnitSelector: React.FC<MedicationUnitSelectorProps> = ({
  value,
  onValueChange
}) => {
  const { colors } = useTheme();
  
  const units = ['ml', 'mg', 'g'];
  
  return (
    <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
      <Text style={[styles.label, { color: colors.text }]}>Unit</Text>
      <View style={[styles.selectContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
        {units.map((unit) => (
          <TouchableOpacity
            key={unit}
            style={[
              styles.selectOption,
              value === unit && { backgroundColor: colors.primary }
            ]}
            onPress={() => onValueChange(unit)}
          >
            <Text style={[
              styles.selectOptionText,
              { color: value === unit ? '#FFF' : colors.text }
            ]}>{unit}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  selectContainer: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  selectOption: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default MedicationUnitSelector;