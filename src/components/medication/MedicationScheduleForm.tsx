import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform
} from 'react-native';
import { Calendar, Clock } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../../contexts/ThemeContext';

interface MedicationScheduleFormProps {
  onDataChange: (data: {
    startDate: Date;
    endDate: Date | null;
    time: Date;
  }) => void;
  initialData?: {
    startDate?: Date;
    endDate?: Date | null;
    time?: Date;
  };
}

const MedicationScheduleForm: React.FC<MedicationScheduleFormProps> = ({
  onDataChange,
  initialData = {}
}) => {
  const { colors } = useTheme();
  
  // Form state
  const [startDate, setStartDate] = useState(initialData.startDate || new Date());
  const [endDate, setEndDate] = useState<Date | null>(initialData.endDate || null);
  const [time, setTime] = useState(initialData.time || new Date());
  
  // UI state
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  
  // Update parent component when data changes
  useEffect(() => {
    onDataChange({
      startDate,
      endDate,
      time
    });
  }, [startDate, endDate, time, onDataChange]);
  
  const onStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setStartDate(selectedDate);
    }
  };
  
  const onEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setEndDate(selectedDate);
    }
  };
  
  const onTimeChange = (event: any, selectedTime?: Date) => {
    setShowTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      setTime(selectedTime);
    }
  };
  
  const formatDate = (date: Date) => {
    return date.toLocaleDateString();
  };
  
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  return (
    <View>
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Start Date</Text>
        <TouchableOpacity
          style={[styles.dateInput, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => setShowStartDatePicker(true)}
        >
          <Text style={[styles.dateText, { color: colors.text }]}>
            {formatDate(startDate)}
          </Text>
          <Calendar size={20} color={colors.primary} />
        </TouchableOpacity>
        
        {showStartDatePicker && (
          <DateTimePicker
            value={startDate}
            mode="date"
            display="default"
            onChange={onStartDateChange}
            minimumDate={new Date()}
          />
        )}
      </View>
      
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>End Date (Optional)</Text>
        <TouchableOpacity
          style={[styles.dateInput, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => setShowEndDatePicker(true)}
        >
          <Text style={[styles.dateText, { color: endDate ? colors.text : colors.textLight }]}>
            {endDate ? formatDate(endDate) : 'Select End Date'}
          </Text>
          <Calendar size={20} color={colors.primary} />
        </TouchableOpacity>
        
        {showEndDatePicker && (
          <DateTimePicker
            value={endDate || new Date()}
            mode="date"
            display="default"
            onChange={onEndDateChange}
            minimumDate={startDate}
          />
        )}
      </View>
      
      <View style={styles.formGroup}>
        <Text style={[styles.label, { color: colors.text }]}>Time</Text>
        <TouchableOpacity
          style={[styles.dateInput, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => setShowTimePicker(true)}
        >
          <Text style={[styles.dateText, { color: colors.text }]}>
            {formatTime(time)}
          </Text>
          <Clock size={20} color={colors.primary} />
        </TouchableOpacity>
        
        {showTimePicker && (
          <DateTimePicker
            value={time}
            mode="time"
            display="default"
            onChange={onTimeChange}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  dateInput: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateText: {
    fontSize: 16,
  },
});

export default MedicationScheduleForm;