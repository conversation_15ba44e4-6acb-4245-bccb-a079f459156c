import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface MedicationDosageInputProps {
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
}

/**
 * @magic_description Medication dosage input component with validation
 * Handles numeric dosage input with error display
 */
const MedicationDosageInput: React.FC<MedicationDosageInputProps> = ({
  value,
  onChangeText,
  error
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
      <Text style={[styles.label, { color: colors.text }]}>Dosage</Text>
      <TextInput
        style={[
          styles.input, 
          { 
            backgroundColor: colors.card, 
            color: colors.text,
            borderColor: error ? colors.error : colors.border 
          }
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder="Amount"
        placeholderTextColor={colors.textLight}
        keyboardType="numeric"
      />
      {error && (
        <Text style={[styles.errorText, { color: colors.error }]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    fontSize: 16,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
});

export default MedicationDosageInput;