import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface MedicationFrequencySelectorProps {
  value: string;
  onValueChange: (frequency: string) => void;
}

/**
 * @magic_description Medication frequency selector component
 * Handles selection between Daily, Weekly, and Once frequencies
 */
const MedicationFrequencySelector: React.FC<MedicationFrequencySelectorProps> = ({
  value,
  onValueChange
}) => {
  const { colors } = useTheme();
  
  const frequencies = [
    { value: 'daily', label: 'Daily' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'once', label: 'Once' }
  ];
  
  return (
    <View style={styles.formGroup}>
      <Text style={[styles.label, { color: colors.text }]}>Frequency</Text>
      <View style={[styles.selectContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
        {frequencies.map((freq) => (
          <TouchableOpacity
            key={freq.value}
            style={[
              styles.selectOption,
              value === freq.value && { backgroundColor: colors.primary }
            ]}
            onPress={() => onValueChange(freq.value)}
          >
            <Text style={[
              styles.selectOptionText,
              { color: value === freq.value ? '#FFF' : colors.text }
            ]}>{freq.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  selectContainer: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  selectOption: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default MedicationFrequencySelector;