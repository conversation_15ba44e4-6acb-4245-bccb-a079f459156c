import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface MedicationNameInputProps {
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
}

/**
 * @magic_description Medication name input component with validation
 * Handles medication name input with error display
 */
const MedicationNameInput: React.FC<MedicationNameInputProps> = ({
  value,
  onChangeText,
  error
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.formGroup}>
      <Text style={[styles.label, { color: colors.text }]}>Medication Name</Text>
      <TextInput
        style={[
          styles.input, 
          { 
            backgroundColor: colors.card, 
            color: colors.text,
            borderColor: error ? colors.error : colors.border 
          }
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder="Enter medication name"
        placeholderTextColor={colors.textLight}
      />
      {error && (
        <Text style={[styles.errorText, { color: colors.error }]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    fontSize: 16,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
});

export default MedicationNameInput;