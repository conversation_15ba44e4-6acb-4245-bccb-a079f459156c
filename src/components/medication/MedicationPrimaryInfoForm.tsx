import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import MedicationNameInput from './MedicationNameInput';
import MedicationDosageInput from './MedicationDosageInput';
import MedicationUnitSelector from './MedicationUnitSelector';
import MedicationFrequencySelector from './MedicationFrequencySelector';

interface MedicationPrimaryInfoFormProps {
  onDataChange: (data: {
    medicationName: string;
    dosage: string;
    dosageUnit: string;
    frequency: string;
    isValid: boolean;
  }) => void;
  initialData?: {
    medicationName?: string;
    dosage?: string;
    dosageUnit?: string;
    frequency?: string;
  };
}

const MedicationPrimaryInfoForm: React.FC<MedicationPrimaryInfoFormProps> = ({
  onDataChange,
  initialData = {}
}) => {
  const { colors } = useTheme();
  
  // Form state
  const [medicationName, setMedicationName] = useState(initialData.medicationName || '');
  const [dosage, setDosage] = useState(initialData.dosage || '');
  const [dosageUnit, setDosageUnit] = useState(initialData.dosageUnit || 'ml');
  const [frequency, setFrequency] = useState(initialData.frequency || 'daily');
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  
  // Validation
  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    if (!medicationName.trim()) {
      newErrors.medicationName = 'Medication name is required';
    }
    
    if (!dosage.trim()) {
      newErrors.dosage = 'Dosage is required';
    } else if (isNaN(Number(dosage))) {
      newErrors.dosage = 'Dosage must be a number';
    }
    
    if (!dosageUnit.trim()) {
      newErrors.dosageUnit = 'Dosage unit is required';
    }
    
    if (!frequency.trim()) {
      newErrors.frequency = 'Frequency is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Update parent component when data changes
  useEffect(() => {
    const isValid = validateForm();
    onDataChange({
      medicationName,
      dosage,
      dosageUnit,
      frequency,
      isValid
    });
  }, [medicationName, dosage, dosageUnit, frequency, onDataChange]);
  
  return (
    <View>
      <MedicationNameInput
        value={medicationName}
        onChangeText={setMedicationName}
        error={errors.medicationName}
      />
      
      <View style={styles.row}>
        <MedicationDosageInput
          value={dosage}
          onChangeText={setDosage}
          error={errors.dosage}
        />
        
        <MedicationUnitSelector
          value={dosageUnit}
          onValueChange={setDosageUnit}
        />
      </View>
      
      <MedicationFrequencySelector
        value={frequency}
        onValueChange={setFrequency}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

export default MedicationPrimaryInfoForm;