import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface MedicationReminderFormProps {
  onDataChange: (data: {
    reminder: boolean;
    reminderTime: string;
  }) => void;
  initialData?: {
    reminder?: boolean;
    reminderTime?: string;
  };
}

const MedicationReminderForm: React.FC<MedicationReminderFormProps> = ({
  onDataChange,
  initialData = {}
}) => {
  const { colors, isDarkMode } = useTheme();
  
  // Form state
  const [reminder, setReminder] = useState(initialData.reminder ?? true);
  const [reminderTime, setReminderTime] = useState(initialData.reminderTime || '15'); // minutes before
  
  // Update parent component when data changes
  useEffect(() => {
    onDataChange({
      reminder,
      reminderTime
    });
  }, [reminder, reminderTime, onDataChange]);
  
  return (
    <View style={styles.formGroup}>
      <View style={styles.checkboxRow}>
        <TouchableOpacity
          style={[
            styles.checkbox,
            { borderColor: colors.primary },
            reminder && { backgroundColor: colors.primary }
          ]}
          onPress={() => setReminder(!reminder)}
        >
          {reminder && (
            <Text style={styles.checkmark}>✓</Text>
          )}
        </TouchableOpacity>
        <Text style={[styles.checkboxLabel, { color: colors.text }]}>
          Set Reminder
        </Text>
      </View>
      
      {reminder && (
        <View style={[styles.reminderContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <Text style={[styles.reminderText, { color: colors.text }]}>
            Remind me
          </Text>
          <TextInput
            style={[styles.reminderInput, { backgroundColor: isDarkMode ? colors.background : colors.card, color: colors.text }]}
            value={reminderTime}
            onChangeText={setReminderTime}
            keyboardType="numeric"
            maxLength={3}
          />
          <Text style={[styles.reminderText, { color: colors.text }]}>
            minutes before
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 16,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  checkmark: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 16,
  },
  reminderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  reminderText: {
    fontSize: 14,
  },
  reminderInput: {
    width: 40,
    height: 32,
    borderRadius: 4,
    textAlign: 'center',
    marginHorizontal: 8,
    fontSize: 14,
  },
});

export default MedicationReminderForm;