import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Text,
  ActivityIndicator
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { toast } from 'sonner-native';
import MedicationPrimaryInfoForm from './MedicationPrimaryInfoForm';
import MedicationScheduleForm from './MedicationScheduleForm';
import MedicationReminderForm from './MedicationReminderForm';
import MedicationNotesForm from './MedicationNotesForm';

interface AddMedicationFormProps {
  animalId: string;
  addMedicationFunction: (medicationData: any) => Promise<void>;
  onSaveSuccess: () => void;
}

interface PrimaryInfoData {
  medicationName: string;
  dosage: string;
  dosageUnit: string;
  frequency: string;
  isValid: boolean;
}

interface ScheduleInfoData {
  startDate: Date;
  endDate: Date | null;
  time: Date;
}

interface ReminderInfoData {
  reminder: boolean;
  reminderTime: string;
}

interface MedicationFormData {
  primaryInfo: PrimaryInfoData;
  scheduleInfo: ScheduleInfoData;
  reminderInfo: ReminderInfoData;
  notes: string;
}

const AddMedicationForm: React.FC<AddMedicationFormProps> = ({
  animalId,
  addMedicationFunction,
  onSaveSuccess
}) => {
  const { colors } = useTheme();
  
  // Consolidated form data state
  const [formData, setFormData] = useState<MedicationFormData>({
    primaryInfo: {
      medicationName: '',
      dosage: '',
      dosageUnit: 'ml',
      frequency: 'daily',
      isValid: false
    },
    scheduleInfo: {
      startDate: new Date(),
      endDate: null,
      time: new Date()
    },
    reminderInfo: {
      reminder: true,
      reminderTime: '15'
    },
    notes: ''
  });
  
  // UI state
  const [isLoading, setIsLoading] = useState(false);
  
  // Callback handlers for child components
  const handlePrimaryInfoChange = useCallback((data: PrimaryInfoData) => {
    setFormData(prev => ({ ...prev, primaryInfo: data }));
  }, []);
  
  const handleScheduleInfoChange = useCallback((data: ScheduleInfoData) => {
    setFormData(prev => ({ ...prev, scheduleInfo: data }));
  }, []);
  
  const handleReminderInfoChange = useCallback((data: ReminderInfoData) => {
    setFormData(prev => ({ ...prev, reminderInfo: data }));
  }, []);
  
  const handleNotesChange = useCallback((notesData: string) => {
    setFormData(prev => ({ ...prev, notes: notesData }));
  }, []);
  
  const handleSave = async () => {
    if (!formData.primaryInfo.isValid) {
      toast.error('Please fix the errors before saving');
      return;
    }
    
    if (!animalId) {
      toast.error('No animal selected');
      return;
    }
    
    setIsLoading(true);
    
    try {
      const timeString = `${formData.scheduleInfo.time.getHours().toString().padStart(2, '0')}:${formData.scheduleInfo.time.getMinutes().toString().padStart(2, '0')}`;
      
      const newMedication = {
        id: `med-${Date.now()}`,
        animalId,
        medicationName: formData.primaryInfo.medicationName,
        dosage: formData.primaryInfo.dosage,
        dosageUnit: formData.primaryInfo.dosageUnit,
        frequency: formData.primaryInfo.frequency,
        startDate: formData.scheduleInfo.startDate.toISOString(),
        endDate: formData.scheduleInfo.endDate ? formData.scheduleInfo.endDate.toISOString() : undefined,
        time: timeString,
        notes: formData.notes,
        reminder: formData.reminderInfo.reminder,
        reminderTime: formData.reminderInfo.reminder ? `${timeString.split(':')[0]}:${(parseInt(timeString.split(':')[1]) - parseInt(formData.reminderInfo.reminderTime)).toString().padStart(2, '0')}` : undefined,
        completed: false
      };
      
      await addMedicationFunction(newMedication);
      
      toast.success('Medication added successfully');
      onSaveSuccess();
    } catch (error) {
      console.error('Error adding medication:', error);
      toast.error('Failed to add medication');
    } finally {
      setIsLoading(false);
    }
  };
  

  
  return (
    <ScrollView 
      style={styles.scrollView}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
    >
      <MedicationPrimaryInfoForm
        initialData={formData.primaryInfo}
        onDataChange={handlePrimaryInfoChange}
      />
      
      <MedicationScheduleForm
        initialData={formData.scheduleInfo}
        onDataChange={handleScheduleInfoChange}
      />
      
      <MedicationReminderForm
        initialData={formData.reminderInfo}
        onDataChange={handleReminderInfoChange}
      />
      
      <MedicationNotesForm
        initialNotes={formData.notes}
        onNotesChange={handleNotesChange}
      />
      
      <TouchableOpacity
        style={[
          styles.saveButton,
          { backgroundColor: colors.primary },
          (!animalId || isLoading) && { opacity: 0.7 }
        ]}
        onPress={handleSave}
        disabled={!animalId || isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#FFF" size="small" />
        ) : (
          <Text style={styles.saveButtonText}>Save Medication</Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  saveButton: {
    height: 48,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  saveButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AddMedicationForm;