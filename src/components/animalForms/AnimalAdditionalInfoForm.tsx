import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface AnimalAdditionalInfoFormProps {
  microchipId: string;
  setMicrochipId: (value: string) => void;
  dam: string;
  setDam: (value: string) => void;
  sire: string;
  setSire: (value: string) => void;
  notes: string;
  setNotes: (value: string) => void;
}

const AnimalAdditionalInfoForm: React.FC<AnimalAdditionalInfoFormProps> = ({
  microchipId,
  setMicrochipId,
  dam,
  setDam,
  sire,
  setSire,
  notes,
  setNotes
}) => {
  const { colors } = useTheme();

  return (
    <View style={styles.formSection}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>Additional Information</Text>
      
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textLight }]}>Microchip ID</Text>
        <TextInput
          style={[styles.input, { backgroundColor: colors.card, color: colors.text, borderColor: colors.border }]}
          value={microchipId}
          onChangeText={setMicrochipId}
          placeholder="Enter microchip ID"
          placeholderTextColor={colors.textLight}
        />
      </View>
      
      <View style={styles.rowInputs}>
        <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
          <Text style={[styles.label, { color: colors.textLight }]}>Dam</Text>
          <TextInput
            style={[styles.input, { backgroundColor: colors.card, color: colors.text, borderColor: colors.border }]}
            value={dam}
            onChangeText={setDam}
            placeholder="Dam"
            placeholderTextColor={colors.textLight}
          />
        </View>
        
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={[styles.label, { color: colors.textLight }]}>Sire</Text>
          <TextInput
            style={[styles.input, { backgroundColor: colors.card, color: colors.text, borderColor: colors.border }]}
            value={sire}
            onChangeText={setSire}
            placeholder="Sire"
            placeholderTextColor={colors.textLight}
          />
        </View>
      </View>
      
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textLight }]}>Notes</Text>
        <TextInput
          style={[styles.textArea, { backgroundColor: colors.card, color: colors.text, borderColor: colors.border }]}
          value={notes}
          onChangeText={setNotes}
          placeholder="Enter any additional notes"
          placeholderTextColor={colors.textLight}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
  },
  rowInputs: {
    flexDirection: 'row',
    marginBottom: 16,
  },
});

export default AnimalAdditionalInfoForm;