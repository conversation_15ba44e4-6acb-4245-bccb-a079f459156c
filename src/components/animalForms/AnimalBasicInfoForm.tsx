import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface AnimalBasicInfoFormProps {
  name: string;
  setName: (value: string) => void;
  type: string;
  setType: (value: string) => void;
  breed: string;
  setBreed: (value: string) => void;
  age: string;
  setAge: (value: string) => void;
  color: string;
  setColor: (value: string) => void;
  gender: string;
  setGender: (value: string) => void;
}

const AnimalBasicInfoForm: React.FC<AnimalBasicInfoFormProps> = ({
  name,
  setName,
  type,
  setType,
  breed,
  setBreed,
  age,
  setAge,
  color,
  setColor,
  gender,
  setGender
}) => {
  const { colors } = useTheme();

  return (
    <View style={styles.formSection}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>Basic Information</Text>
      
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textLight }]}>Name *</Text>
        <TextInput
          style={[styles.input, { backgroundColor: colors.card, color: colors.text, borderColor: colors.border }]}
          value={name}
          onChangeText={setName}
          placeholder="Enter name"
          placeholderTextColor={colors.textLight}
        />
      </View>
      
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textLight }]}>Type *</Text>
        <View style={styles.typeContainer}>
          <TouchableOpacity 
            style={[
              styles.typeButton, 
              type === 'horse' && [styles.activeTypeButton, { backgroundColor: colors.primary }]
            ]}
            onPress={() => setType('horse')}
          >
            <Text 
              style={[
                styles.typeButtonText, 
                { color: type === 'horse' ? '#FFF' : colors.text }
              ]}
            >
              Horse
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.typeButton, 
              type === 'camel' && [styles.activeTypeButton, { backgroundColor: colors.primary }]
            ]}
            onPress={() => setType('camel')}
          >
            <Text 
              style={[
                styles.typeButtonText, 
                { color: type === 'camel' ? '#FFF' : colors.text }
              ]}
            >
              Camel
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.typeButton, 
              type === 'other' && [styles.activeTypeButton, { backgroundColor: colors.primary }]
            ]}
            onPress={() => setType('other')}
          >
            <Text 
              style={[
                styles.typeButtonText, 
                { color: type === 'other' ? '#FFF' : colors.text }
              ]}
            >
              Other
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textLight }]}>Breed *</Text>
        <TextInput
          style={[styles.input, { backgroundColor: colors.card, color: colors.text, borderColor: colors.border }]}
          value={breed}
          onChangeText={setBreed}
          placeholder="Enter breed"
          placeholderTextColor={colors.textLight}
        />
      </View>
      
      <View style={styles.rowInputs}>
        <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
          <Text style={[styles.label, { color: colors.textLight }]}>Age</Text>
          <TextInput
            style={[styles.input, { backgroundColor: colors.card, color: colors.text, borderColor: colors.border }]}
            value={age}
            onChangeText={setAge}
            placeholder="Age"
            placeholderTextColor={colors.textLight}
            keyboardType="numeric"
          />
        </View>
        
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={[styles.label, { color: colors.textLight }]}>Color</Text>
          <TextInput
            style={[styles.input, { backgroundColor: colors.card, color: colors.text, borderColor: colors.border }]}
            value={color}
            onChangeText={setColor}
            placeholder="Color"
            placeholderTextColor={colors.textLight}
          />
        </View>
      </View>
      
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textLight }]}>Gender</Text>
        <View style={styles.genderContainer}>
          <TouchableOpacity 
            style={[
              styles.genderButton, 
              gender === 'male' && [styles.activeGenderButton, { backgroundColor: colors.primary }]
            ]}
            onPress={() => setGender('male')}
          >
            <Text 
              style={[
                styles.genderButtonText, 
                { color: gender === 'male' ? '#FFF' : colors.text }
              ]}
            >
              Male
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.genderButton, 
              gender === 'female' && [styles.activeGenderButton, { backgroundColor: colors.primary }]
            ]}
            onPress={() => setGender('female')}
          >
            <Text 
              style={[
                styles.genderButtonText, 
                { color: gender === 'female' ? '#FFF' : colors.text }
              ]}
            >
              Female
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  rowInputs: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  typeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  typeButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  activeTypeButton: {
    borderColor: 'transparent',
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  genderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  genderButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  activeGenderButton: {
    borderColor: 'transparent',
  },
  genderButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default AnimalBasicInfoForm;