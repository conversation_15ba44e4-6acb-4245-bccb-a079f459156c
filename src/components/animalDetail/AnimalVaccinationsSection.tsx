import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Shield } from 'lucide-react-native';
import { Animal } from '../../mocks/animals';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface VaccinationRecord {
  id: string;
  animalId: string;
  vaccineName: string;
  date: string;
  nextDue?: string;
}

interface AnimalVaccinationsSectionProps {
  animal: Animal;
  vaccinations: VaccinationRecord[];
  onAddVaccination: () => void;
  onPrintVaccinations: () => void;
}

/**
 * @magic_description Vaccinations section component for animal detail screen
 * Displays vaccination records with add and print options (requires microchip)
 */
const AnimalVaccinationsSection: React.FC<AnimalVaccinationsSectionProps> = ({
  animal,
  vaccinations,
  onAddVaccination,
  onPrintVaccinations
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('animalDetailVaccinations')}</Text>
      </View>
      
      {vaccinations.length > 0 ? (
        <>
          {vaccinations.slice(0, 2).map(vaccination => (
            <Card key={vaccination.id} style={styles.scheduleCard}>
              <View style={styles.scheduleHeader}>
                <View style={[styles.scheduleIconContainer, { backgroundColor: colors.accent1 + '20' }]}>
                  <Shield size={20} color={colors.accent1} />
                </View>
                <View style={styles.scheduleContent}>
                  <Text style={[styles.scheduleTitle, { color: colors.text }]}>
                    {vaccination.vaccineName}
                  </Text>
                  <Text style={[styles.scheduleSubtitle, { color: colors.textLight }]}>
                    {new Date(vaccination.date).toLocaleDateString()}
                  </Text>
                </View>
                <Text style={[styles.scheduleTime, { color: colors.accent1 }]}>
                  {vaccination.nextDue ? `${t('animalDetailDue')} ${new Date(vaccination.nextDue).toLocaleDateString()}` : t('animalDetailNoRenewal')}
                </Text>
              </View>
            </Card>
          ))}
          
          <TouchableOpacity 
            style={[styles.printButton, { backgroundColor: colors.accent3 }]}
            onPress={onPrintVaccinations}
          >
            <Text style={[styles.printButtonText, { color: colors.white }]}>{t('animalDetailPrintVaccinationRecord')}</Text>
          </TouchableOpacity>
        </>
      ) : (
        <View style={[styles.emptyState, { backgroundColor: colors.card }]}>
          <Text style={[styles.emptyStateText, { color: colors.textLight }]}>
            {t('animalDetailNoVaccinationsRecorded')}
          </Text>
          {animal.microchipId ? (
            <TouchableOpacity 
              style={[styles.emptyStateButton, { backgroundColor: colors.primary }]}
              onPress={onAddVaccination}
            >
              <Text style={[styles.emptyStateButtonText, { color: colors.white }]}>{t('animalDetailAddVaccination')}</Text>
            </TouchableOpacity>
          ) : (
            <Text style={[styles.emptyStateSubtext, { color: colors.textLight }]}>
              {t('animalDetailMicrochipRequired')}
            </Text>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  sectionAction: {
    fontSize: 14,
    fontWeight: '500',
  },
  scheduleCard: {
    marginBottom: 8,
  },
  scheduleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scheduleIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  scheduleContent: {
    flex: 1,
  },
  scheduleTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  scheduleSubtitle: {
    fontSize: 14,
  },
  scheduleTime: {
    fontSize: 14,
    fontWeight: '500',
  },
  printButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 8,
  },
  printButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 14,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 12,
    textAlign: 'center',
  },
  emptyStateButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default AnimalVaccinationsSection;