import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Brain, Target, Heart, Lightbulb, ChevronRight } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAIStore } from '../../store/aiStore';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface AnimalAIInsightsSectionProps {
  animalId: string;
  onViewAIAssistant: () => void;
  onRequestAnalysis: () => void;
}

const AnimalAIInsightsSection: React.FC<AnimalAIInsightsSectionProps> = ({
  animalId,
  onViewAIAssistant,
  onRequestAnalysis
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  const {
    isLoadingAnalysis,
    getLatestHealthAssessment,
    getLatestTrainingPlan,
    getLatestReadinessScore,
    getUnreadTips,
    fetchHealthAssessments,
    fetchTrainingPlans,
    fetchReadinessScores,
    fetchCoachingTips
  } = useAIStore();
  
  const latestHealthAssessment = getLatestHealthAssessment(animalId);
  const latestTrainingPlan = getLatestTrainingPlan(animalId);
  const latestReadinessScore = getLatestReadinessScore(animalId);
  const unreadTips = getUnreadTips(animalId);
  
  useEffect(() => {
    // Load AI data when component mounts
    const loadAIData = async () => {
      await Promise.all([
        fetchHealthAssessments(animalId),
        fetchTrainingPlans(animalId),
        fetchReadinessScores(animalId),
        fetchCoachingTips(animalId),
      ]);
    };
    
    loadAIData();
  }, [animalId]);
  
  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'high':
      case 'critical':
        return '#EF4444';
      case 'medium':
      case 'moderate':
        return '#F59E0B';
      case 'low':
      case 'normal':
        return '#10B981';
      default:
        return colors.textLight;
    }
  };
  
  const hasAnyData = latestHealthAssessment || latestTrainingPlan || latestReadinessScore || unreadTips.length > 0;
  
  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <Brain size={24} color={colors.primary} />
        <Text style={[styles.title, { color: colors.text }]}>{t('animalDetailAIAssistant')}</Text>
        {unreadTips.length > 0 && (
          <View style={[styles.badge, { backgroundColor: colors.primary }]}>
            <Text style={styles.badgeText}>{unreadTips.length}</Text>
          </View>
        )}
      </View>
      
      {hasAnyData ? (
        <>
          {/* Readiness Score */}
          {latestReadinessScore && (
            <View style={[styles.insightItem, { borderBottomColor: colors.border }]}>
              <View style={styles.insightHeader}>
                <Target size={20} color={colors.primary} />
                <Text style={[styles.insightTitle, { color: colors.text }]}>{t('animalDetailReadinessScore')}</Text>
                <Text style={[styles.scoreValue, { color: colors.primary }]}>
                  {Math.round(latestReadinessScore.score_value * 100)}%
                </Text>
              </View>
              <Text style={[styles.insightDate, { color: colors.textLight }]}>
                {format(new Date(latestReadinessScore.generated_at), 'MMM d, yyyy')}
              </Text>
            </View>
          )}
          
          {/* Health Assessment */}
          {latestHealthAssessment && (
            <View style={[styles.insightItem, { borderBottomColor: colors.border }]}>
              <View style={styles.insightHeader}>
                <Heart size={20} color={getSeverityColor(latestHealthAssessment.severity_level)} />
                <Text style={[styles.insightTitle, { color: colors.text }]}>{t('animalDetailHealthStatus')}</Text>
                <View style={[
                  styles.severityBadge,
                  { backgroundColor: getSeverityColor(latestHealthAssessment.severity_level) }
                ]}>
                  <Text style={styles.severityText}>
                    {latestHealthAssessment.severity_level}
                  </Text>
                </View>
              </View>
              <Text style={[styles.insightText, { color: colors.text }]} numberOfLines={2}>
                {latestHealthAssessment.assessment_text}
              </Text>
              <Text style={[styles.insightDate, { color: colors.textLight }]}>
                {format(new Date(latestHealthAssessment.generated_at), 'MMM d, yyyy')}
              </Text>
            </View>
          )}
          
          {/* Coaching Tips */}
          {unreadTips.length > 0 && (
            <View style={styles.insightItem}>
              <View style={styles.insightHeader}>
                <Lightbulb size={20} color={colors.primary} />
                <Text style={[styles.insightTitle, { color: colors.text }]}>{t('animalDetailNewTips')}</Text>
                <Text style={[styles.tipCount, { color: colors.primary }]}>
                  {unreadTips.length} {unreadTips.length > 1 ? t('animalDetailTips') : t('animalDetailTip')}
                </Text>
              </View>
              <Text style={[styles.insightText, { color: colors.text }]} numberOfLines={2}>
                {unreadTips[0].tip_text}
              </Text>
              {unreadTips.length > 1 && (
                <Text style={[styles.moreTips, { color: colors.textLight }]}>
                  +{unreadTips.length - 1} {unreadTips.length > 2 ? t('animalDetailMoreTips') : t('animalDetailMoreTip')}
                </Text>
              )}
            </View>
          )}
        </>
      ) : (
        <View style={styles.emptyState}>
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            {t('animalDetailNoAIInsights')}
          </Text>
        </View>
      )}
      
      <View style={styles.actions}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.primaryButton,
            { backgroundColor: colors.primary },
            isLoadingAnalysis && styles.actionButtonDisabled
          ]}
          onPress={onRequestAnalysis}
          disabled={isLoadingAnalysis}
        >
          {isLoadingAnalysis ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <Brain size={16} color="#FFFFFF" />
          )}
          <Text style={styles.primaryButtonText}>
            {isLoadingAnalysis ? t('animalDetailAnalyzing') : t('animalDetailGetAIAnalysis')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.secondaryButton, { borderColor: colors.border }]}
          onPress={onViewAIAssistant}
        >
          <Text style={[styles.secondaryButtonText, { color: colors.text }]}>{t('animalDetailViewAllInsights')}</Text>
          <ChevronRight size={16} color={colors.text} />
        </TouchableOpacity>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
    flex: 1,
  },
  badge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  insightItem: {
    paddingBottom: 12,
    marginBottom: 12,
    borderBottomWidth: 1,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  scoreValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  severityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  severityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  tipCount: {
    fontSize: 12,
    fontWeight: '600',
  },
  insightText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  insightDate: {
    fontSize: 12,
  },
  moreTips: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  emptyState: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  primaryButton: {
    // backgroundColor set dynamically
  },
  secondaryButton: {
    borderWidth: 1,
  },
  actionButtonDisabled: {
    opacity: 0.6,
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
});

export default AnimalAIInsightsSection;