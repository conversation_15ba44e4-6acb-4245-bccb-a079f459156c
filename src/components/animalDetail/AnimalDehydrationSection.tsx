import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useDehydrationStore } from '../../store/dehydrationStore';
import { useAIStore } from '../../store/aiStore';
import { Animal } from '../../mocks/animals';
import Card from '../ui/Card';
import DehydrationHeader from './dehydration/DehydrationHeader';
import DehydrationAlerts from './dehydration/DehydrationAlerts';
import DehydrationLatestReading from './dehydration/DehydrationLatestReading';
import DehydrationControls from './dehydration/DehydrationControls';
import DehydrationAIInsights from './dehydration/DehydrationAIInsights';
import DehydrationRecentReadings from './dehydration/DehydrationRecentReadings';

interface AnimalDehydrationSectionProps {
  animal: Animal;
  onViewDetails?: () => void;
}

const AnimalDehydrationSection: React.FC<AnimalDehydrationSectionProps> = ({
  animal,
  onViewDetails
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  const {
    readings,
    isLoading,
    isRecording,
    isSyncing,
    error,
    currentSession,
    alerts,
    fetchReadings,
    simulateReading,
    startMonitoringSession,
    stopMonitoringSession,
    getLatestReading,
    fetchAlerts,
    clearError
  } = useDehydrationStore();
  
  const {
    dehydrationAnalyses,
    isLoadingDehydrationAnalysis,
    requestDehydrationAnalysis,
    getLatestDehydrationAnalysis
  } = useAIStore();

  const [showDetails, setShowDetails] = useState(false);
  const [showAIInsights, setShowAIInsights] = useState(false);
  const latestReading = getLatestReading(animal.id);
  const activeAlerts = alerts.filter(alert => alert.animal_id === animal.id && !alert.acknowledged);
  const aiAnalysis = getLatestDehydrationAnalysis(animal.id);

  useEffect(() => {
    fetchReadings(animal.id, 10);
    fetchAlerts(animal.id);
    // Request AI analysis if we have readings but no recent analysis
    if (readings.length > 0 && (!aiAnalysis || 
        new Date().getTime() - new Date(aiAnalysis.generated_at).getTime() > 24 * 60 * 60 * 1000)) {
      requestDehydrationAnalysis(animal.id, 7);
    }
  }, [animal.id, readings.length]);

  // Event handlers
  const handleToggleAIInsights = () => setShowAIInsights(!showAIInsights);
  const handleToggleDetails = () => setShowDetails(!showDetails);

  const handleStartMonitoring = async () => {
    await startMonitoringSession(animal.id);
    // Start taking readings every 30 seconds during monitoring
    const interval = setInterval(async () => {
      if (!useDehydrationStore.getState().isRecording) {
        clearInterval(interval);
        return;
      }
      await simulateReading(animal.id);
    }, 30000);
  };

  const handleStopMonitoring = async () => {
    await stopMonitoringSession();
  };

  const handleTakeReading = async () => {
    await simulateReading(animal.id);
  };

  // Component is now using modular sub-components

  if (error) {
    return (
      <Card style={styles.container}>
        <View style={styles.errorContainer}>
          <AlertTriangle size={24} color={colors.error} />
          <Text style={[styles.errorText, { color: colors.error }]}>
            {t('failedToLoadHydrationData')}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              clearError();
              fetchReadings(animal.id, 10);
            }}
          >
            <Text style={[styles.retryButtonText, { color: colors.white }]}>{t('retry')}</Text>
          </TouchableOpacity>
        </View>
      </Card>
    );
  }

  return (
    <Card style={styles.container}>
      <DehydrationHeader
        isRecording={isRecording}
        hasAIAnalysis={!!aiAnalysis}
        showAIInsights={showAIInsights}
        showDetails={showDetails}
        onToggleAIInsights={handleToggleAIInsights}
        onToggleDetails={handleToggleDetails}
      />

      <DehydrationAlerts alerts={activeAlerts} />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            {t('loadingHydrationData')}
          </Text>
        </View>
      ) : (
        <>
          <DehydrationLatestReading reading={latestReading} />
          
          <DehydrationControls
            isRecording={isRecording}
            isSyncing={isSyncing}
            onTakeReading={handleTakeReading}
            onStartMonitoring={handleStartMonitoring}
            onStopMonitoring={handleStopMonitoring}
          />
          
          {showAIInsights && (
            <DehydrationAIInsights
              aiAnalysis={aiAnalysis}
              isLoadingAnalysis={isLoadingDehydrationAnalysis}
              onRefreshAnalysis={() => requestDehydrationAnalysis(animal.id, 7)}
            />
          )}
          
          <DehydrationRecentReadings
            readings={readings}
            animalId={animal.id}
            showDetails={showDetails}
          />
        </>
      )}

      {currentSession && (
        <View style={[styles.sessionInfo, { backgroundColor: colors.primary + '20' }]}>
          <Text style={[styles.sessionText, { color: colors.primary }]}>
            📊 Monitoring session active • {currentSession.readings.length} readings
          </Text>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  sessionInfo: {
    padding: 8,
    borderRadius: 6,
    marginTop: 8,
  },
  sessionText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  errorContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 8,
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default AnimalDehydrationSection;