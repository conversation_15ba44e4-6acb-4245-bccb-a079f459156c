import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface VitalRecord {
  id: string;
  animalId: string;
  temperature: number;
  heartRate?: number;
  respirationRate?: number;
  weight?: number;
  notes?: string;
  timestamp: string;
}

interface AnimalVitalsSectionProps {
  latestVital: VitalRecord | null;
}

/**
 * @magic_description Vitals section component for animal detail screen
 * Displays latest vital signs or empty state
 */
const AnimalVitalsSection: React.FC<AnimalVitalsSectionProps> = ({
  latestVital
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('animalDetailLatestVitals')}</Text>
        <Text style={[styles.deviceOnlyText, { color: colors.warning }]}>{t('animalDetailDeviceOnly')}</Text>
      </View>
      
      {latestVital ? (
        <Card style={styles.vitalCard}>
          <View style={styles.vitalHeader}>
            <Text style={[styles.vitalTitle, { color: colors.text }]}>
              {t('animalDetailRecordedOn')} {new Date(latestVital.timestamp).toLocaleDateString()}
            </Text>
            <Text style={[styles.vitalTime, { color: colors.textLight }]}>
              {new Date(latestVital.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Text>
          </View>
          
          <View style={styles.vitalGrid}>
            <View style={styles.vitalItem}>
              <Text style={[styles.vitalLabel, { color: colors.textLight }]}>{t('animalDetailTemperature')}</Text>
              <Text style={[styles.vitalValue, { color: colors.text }]}>
                {latestVital.temperature}°C
              </Text>
            </View>
            
            {latestVital.heartRate && (
              <View style={styles.vitalItem}>
                <Text style={[styles.vitalLabel, { color: colors.textLight }]}>{t('animalDetailHeartRate')}</Text>
                <Text style={[styles.vitalValue, { color: colors.text }]}>
                  {latestVital.heartRate} bpm
                </Text>
              </View>
            )}
            
            {latestVital.respirationRate && (
              <View style={styles.vitalItem}>
                <Text style={[styles.vitalLabel, { color: colors.textLight }]}>{t('animalDetailRespiration')}</Text>
                <Text style={[styles.vitalValue, { color: colors.text }]}>
                  {latestVital.respirationRate} bpm
                </Text>
              </View>
            )}
            
            {latestVital.weight && (
              <View style={styles.vitalItem}>
                <Text style={[styles.vitalLabel, { color: colors.textLight }]}>{t('animalDetailWeight')}</Text>
                <Text style={[styles.vitalValue, { color: colors.text }]}>
                  {latestVital.weight} kg
                </Text>
              </View>
            )}
          </View>
          
          {latestVital.notes && (
            <View style={[styles.vitalNotes, { borderTopColor: colors.border }]}>
              <Text style={[styles.vitalNotesLabel, { color: colors.textLight }]}>{t('animalDetailNotes')}</Text>
              <Text style={[styles.vitalNotesText, { color: colors.text }]}>{latestVital.notes}</Text>
            </View>
          )}
        </Card>
      ) : (
        <View style={[styles.emptyState, { backgroundColor: colors.card }]}>
          <Text style={[styles.emptyStateText, { color: colors.textLight }]}>
            {t('animalDetailNoVitalsRecorded')}
          </Text>
          <Text style={[styles.emptyStateSubtext, { color: colors.textLight }]}>
            {t('animalDetailConnectDevice')}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  deviceOnlyText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  vitalCard: {
    marginBottom: 8,
  },
  vitalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  vitalTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  vitalTime: {
    fontSize: 14,
  },
  vitalGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  vitalItem: {
    width: '50%',
    marginBottom: 16,
  },
  vitalLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  vitalValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  vitalNotes: {
    borderTopWidth: StyleSheet.hairlineWidth,
    paddingTop: 16,
  },
  vitalNotesLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  vitalNotesText: {
    fontSize: 14,
    lineHeight: 20,
  },
  emptyState: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 14,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default AnimalVitalsSection;