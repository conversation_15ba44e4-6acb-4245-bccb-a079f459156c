import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  StatusBar,
  Platform,
  Dimensions
} from 'react-native';
import { Edit, Trash2 } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Animal } from '../../mocks/animals';

interface AnimalDetailHeaderProps {
  animal: Animal;
  onBack: () => void;
  onEdit: () => void;
  onDelete: () => void;
}

/**
 * @magic_description Header component for animal detail screen
 * Displays animal image, name, breed, and action buttons (back, edit, delete)
 */
const AnimalDetailHeader: React.FC<AnimalDetailHeaderProps> = ({
  animal,
  onBack,
  onEdit,
  onDelete
}) => {
  return (
    <View style={styles.header}>
      <Image 
        source={{ uri: animal.imageUrl }} 
        style={styles.headerImage} 
      />
      <LinearGradient
        colors={['rgba(0,0,0,0.7)', 'transparent']}
        style={styles.headerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      />
      
      <TouchableOpacity 
        style={styles.backButton}
        onPress={onBack}
      >
        <View style={styles.backButtonInner}>
          <Text style={styles.backButtonText}>←</Text>
        </View>
      </TouchableOpacity>
      
      <View style={styles.headerActions}>
        <TouchableOpacity 
          style={styles.headerAction}
          onPress={onEdit}
        >
          <Edit size={20} color="#FFF" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.headerAction}
          onPress={onDelete}
        >
          <Trash2 size={20} color="#FFF" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.headerContent}>
        <Text style={styles.animalName}>{animal.name}</Text>
        <Text style={styles.animalBreed}>{animal.breed}</Text>
      </View>
    </View>
  );
};

const { height } = Dimensions.get('window');

const styles = StyleSheet.create({
  header: {
    height: height * 0.3,
    position: 'relative',
  },
  headerImage: {
    width: '100%',
    height: '100%',
  },
  headerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '100%',
  },
  backButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0,
    left: 16,
    zIndex: 10,
  },
  backButtonInner: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: '#FFF',
    fontSize: 20,
  },
  headerActions: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0,
    right: 16,
    flexDirection: 'row',
    zIndex: 10,
  },
  headerAction: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  headerContent: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
  },
  animalName: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFF',
    marginBottom: 4,
  },
  animalBreed: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
});

export default AnimalDetailHeader;