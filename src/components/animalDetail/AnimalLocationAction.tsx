import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MapPin } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface AnimalLocationActionProps {
  onViewLocation: () => void;
}

/**
 * @magic_description Location action component for animal detail screen
 * Displays the "View Location" button
 */
const AnimalLocationAction: React.FC<AnimalLocationActionProps> = ({
  onViewLocation
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.actionsSection}>
      <TouchableOpacity 
        style={[styles.actionButton, { backgroundColor: colors.secondary }]}
        onPress={onViewLocation}
      >
        <MapPin size={20} color="#FFF" />
        <Text style={styles.actionButtonText}>View Location</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  actionsSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
  },
  actionButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default AnimalLocationAction;