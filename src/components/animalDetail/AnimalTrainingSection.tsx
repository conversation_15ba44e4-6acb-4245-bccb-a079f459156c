import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Zap, Plus, Clock, MapPin, TrendingUp } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { supabase } from '../../supabase/client';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface TrainingSession {
  session_id: string;
  distance: number;
  speed: number;
  duration: string;
  intensity_label: string;
  session_timestamp: string;
}

interface AnimalTrainingSectionProps {
  animalId: string;
  onLogTrainingSession: () => void;
}

const AnimalTrainingSection: React.FC<AnimalTrainingSectionProps> = ({
  animalId,
  onLogTrainingSession
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  const [recentSessions, setRecentSessions] = useState<TrainingSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    fetchRecentSessions();
  }, [animalId]);
  
  const fetchRecentSessions = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('training_sessions')
        .select('*')
        .eq('animal_id', animalId)
        .order('session_timestamp', { ascending: false })
        .limit(3);
      
      if (error) throw error;
      
      setRecentSessions(data || []);
    } catch (error) {
      console.error('Error fetching training sessions:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const getIntensityColor = (intensity: string) => {
    switch (intensity?.toLowerCase()) {
      case 'high intensity':
      case 'vigorous':
        return '#EF4444';
      case 'moderate':
        return '#F59E0B';
      case 'light':
        return '#10B981';
      case 'endurance':
        return '#8B5CF6';
      default:
        return colors.textLight;
    }
  };
  
  const formatDuration = (duration: string) => {
    // Convert PostgreSQL interval to readable format
    if (duration.includes(':')) {
      const parts = duration.split(':');
      const hours = parseInt(parts[0]);
      const minutes = parseInt(parts[1]);
      
      if (hours > 0) {
        return `${hours}h ${minutes}m`;
      }
      return `${minutes}m`;
    }
    return duration;
  };
  
  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <Zap size={24} color={colors.primary} />
        <Text style={[styles.title, { color: colors.text }]}>{t('animalDetailTrainingSessions')}</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={onLogTrainingSession}
        >
          <Plus size={16} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textLight }]}>{t('animalDetailLoadingSessions')}</Text>
        </View>
      ) : recentSessions.length > 0 ? (
        <>
          {recentSessions.map((session, index) => (
            <View 
              key={session.session_id} 
              style={[
                styles.sessionItem,
                { borderBottomColor: colors.border },
                index === recentSessions.length - 1 && styles.lastSessionItem
              ]}
            >
              <View style={styles.sessionHeader}>
                <View style={styles.sessionInfo}>
                  <Text style={[styles.sessionDate, { color: colors.text }]}>
                    {format(new Date(session.session_timestamp), 'MMM d, yyyy')}
                  </Text>
                  <View style={[
                    styles.intensityBadge,
                    { backgroundColor: getIntensityColor(session.intensity_label) }
                  ]}>
                    <Text style={styles.intensityText}>{session.intensity_label}</Text>
                  </View>
                </View>
              </View>
              
              <View style={styles.sessionStats}>
                <View style={styles.statItem}>
                  <MapPin size={14} color={colors.textLight} />
                  <Text style={[styles.statValue, { color: colors.text }]}>{session.distance} km</Text>
                </View>
                
                <View style={styles.statItem}>
                  <TrendingUp size={14} color={colors.textLight} />
                  <Text style={[styles.statValue, { color: colors.text }]}>{session.speed} km/h</Text>
                </View>
                
                <View style={styles.statItem}>
                  <Clock size={14} color={colors.textLight} />
                  <Text style={[styles.statValue, { color: colors.text }]}>
                    {formatDuration(session.duration)}
                  </Text>
                </View>
              </View>
            </View>
          ))}
          
          <TouchableOpacity
            style={styles.viewAllButton}
            onPress={onLogTrainingSession}
          >
            <Text style={[styles.viewAllText, { color: colors.primary }]}>{t('animalDetailLogNewSession')}</Text>
          </TouchableOpacity>
        </>
      ) : (
        <View style={styles.emptyState}>
          <Zap size={32} color={colors.textLight} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>{t('animalDetailNoTrainingSessions')}</Text>
          <Text style={[styles.emptyDescription, { color: colors.textLight }]}>
            {t('animalDetailTrainingDescription')}
          </Text>
          
          <TouchableOpacity
            style={[styles.logFirstButton, { backgroundColor: colors.primary }]}
            onPress={onLogTrainingSession}
          >
            <Plus size={16} color="#FFFFFF" />
            <Text style={styles.logFirstButtonText}>{t('animalDetailLogFirstSession')}</Text>
          </TouchableOpacity>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
    flex: 1,
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
  },
  sessionItem: {
    paddingBottom: 12,
    marginBottom: 12,
    borderBottomWidth: 1,
  },
  lastSessionItem: {
    borderBottomWidth: 0,
    marginBottom: 0,
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  sessionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sessionDate: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
  },
  intensityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  intensityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  sessionStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statValue: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  viewAllButton: {
    alignItems: 'center',
    paddingVertical: 8,
    marginTop: 8,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    marginBottom: 4,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  logFirstButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  logFirstButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
});

export default AnimalTrainingSection;