import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Syringe } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface MedicationRecord {
  id: string;
  animalId: string;
  medicationName: string;
  dosage: string;
  dosageUnit: string;
  frequency: string;
  time: string;
}

interface AnimalMedicationsSectionProps {
  medications: MedicationRecord[];
  onAddMedication: () => void;
  onPrintMedications: () => void;
}

/**
 * @magic_description Medications section component for animal detail screen
 * Displays current medications with add and print options
 */
const AnimalMedicationsSection: React.FC<AnimalMedicationsSectionProps> = ({
  medications,
  onAddMedication,
  onPrintMedications
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('animalDetailMedications')}</Text>
        <TouchableOpacity onPress={onAddMedication}>
          <Text style={[styles.sectionAction, { color: colors.primary }]}>{t('animalDetailAdd')}</Text>
        </TouchableOpacity>
      </View>
      
      {medications.length > 0 ? (
        <>
          {medications.slice(0, 2).map(medication => (
            <Card key={medication.id} style={styles.scheduleCard}>
              <View style={styles.scheduleHeader}>
                <View style={[styles.scheduleIconContainer, { backgroundColor: colors.secondary + '20' }]}>
                  <Syringe size={20} color={colors.secondary} />
                </View>
                <View style={styles.scheduleContent}>
                  <Text style={[styles.scheduleTitle, { color: colors.text }]}>
                    {medication.medicationName}
                  </Text>
                  <Text style={[styles.scheduleSubtitle, { color: colors.textLight }]}>
                    {medication.dosage}{medication.dosageUnit} • {medication.frequency}
                  </Text>
                </View>
                <Text style={[styles.scheduleTime, { color: colors.secondary }]}>
                  {medication.time}
                </Text>
              </View>
            </Card>
          ))}
          
          <TouchableOpacity 
            style={[styles.printButton, { backgroundColor: colors.accent3 }]}
            onPress={onPrintMedications}
          >
            <Text style={styles.printButtonText}>{t('animalDetailPrintMedicationSchedule')}</Text>
          </TouchableOpacity>
        </>
      ) : (
        <View style={[styles.emptyState, { backgroundColor: colors.card }]}>
          <Text style={[styles.emptyStateText, { color: colors.textLight }]}>
            {t('animalDetailNoMedicationsAdded')}
          </Text>
          <TouchableOpacity 
            style={[styles.emptyStateButton, { backgroundColor: colors.primary }]}
            onPress={onAddMedication}
          >
            <Text style={styles.emptyStateButtonText}>{t('animalDetailAddMedication')}</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  sectionAction: {
    fontSize: 14,
    fontWeight: '500',
  },
  scheduleCard: {
    marginBottom: 8,
  },
  scheduleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scheduleIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  scheduleContent: {
    flex: 1,
  },
  scheduleTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  scheduleSubtitle: {
    fontSize: 14,
  },
  scheduleTime: {
    fontSize: 14,
    fontWeight: '500',
  },
  printButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 8,
  },
  printButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 14,
    marginBottom: 8,
  },
  emptyStateButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default AnimalMedicationsSection;