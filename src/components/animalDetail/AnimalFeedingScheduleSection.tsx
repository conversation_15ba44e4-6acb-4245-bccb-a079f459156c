import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Coffee } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface FeedingEntry {
  id: string;
  animalId: string;
  feedType: string;
  amount: string;
  time: string;
}

interface AnimalFeedingScheduleSectionProps {
  todayFeedings: FeedingEntry[];
  onViewFeedSchedule: () => void;
  onPrintFeedings: () => void;
}

/**
 * @magic_description Feeding schedule section component for animal detail screen
 * Displays today's feeding schedule with print and manage options
 */
const AnimalFeedingScheduleSection: React.FC<AnimalFeedingScheduleSectionProps> = ({
  todayFeedings,
  onViewFeedSchedule,
  onPrintFeedings
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('animalDetailFeedingSchedule')}</Text>
        <TouchableOpacity onPress={onViewFeedSchedule}>
          <Text style={[styles.sectionAction, { color: colors.primary }]}>{t('animalDetailViewAll')}</Text>
        </TouchableOpacity>
      </View>
      
      {todayFeedings.length > 0 ? (
        <>
          {todayFeedings.slice(0, 2).map(feeding => (
            <Card key={feeding.id} style={styles.scheduleCard}>
              <View style={styles.scheduleHeader}>
                <View style={styles.scheduleIconContainer}>
                  <Coffee size={20} color={colors.primary} />
                </View>
                <View style={styles.scheduleContent}>
                  <Text style={[styles.scheduleTitle, { color: colors.text }]}>
                    {feeding.feedType}
                  </Text>
                  <Text style={[styles.scheduleSubtitle, { color: colors.textLight }]}>
                    {feeding.amount}
                  </Text>
                </View>
                <Text style={[styles.scheduleTime, { color: colors.primary }]}>
                  {feeding.time}
                </Text>
              </View>
            </Card>
          ))}
          
          <TouchableOpacity 
            style={[styles.printButton, { backgroundColor: colors.accent3 }]}
            onPress={onPrintFeedings}
          >
            <Text style={styles.printButtonText}>{t('animalDetailPrintFeedingSchedule')}</Text>
          </TouchableOpacity>
        </>
      ) : (
        <View style={[styles.emptyState, { backgroundColor: colors.card }]}>
          <Text style={[styles.emptyStateText, { color: colors.textLight }]}>
            {t('animalDetailNoFeedingSchedule')}
          </Text>
          <TouchableOpacity 
            style={[styles.emptyStateButton, { backgroundColor: colors.primary }]}
            onPress={onViewFeedSchedule}
          >
            <Text style={styles.emptyStateButtonText}>{t('animalDetailSetFeedingSchedule')}</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  sectionAction: {
    fontSize: 14,
    fontWeight: '500',
  },
  scheduleCard: {
    marginBottom: 8,
  },
  scheduleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scheduleIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(61, 140, 145, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  scheduleContent: {
    flex: 1,
  },
  scheduleTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  scheduleSubtitle: {
    fontSize: 14,
  },
  scheduleTime: {
    fontSize: 14,
    fontWeight: '500',
  },
  printButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 8,
  },
  printButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 14,
    marginBottom: 8,
  },
  emptyStateButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default AnimalFeedingScheduleSection;