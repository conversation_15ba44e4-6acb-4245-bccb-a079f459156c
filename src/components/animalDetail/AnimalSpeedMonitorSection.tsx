import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Animal } from '../../mocks/animals';
import { useTheme } from '../../contexts/ThemeContext';
import SpeedMonitor from '../SpeedMonitor';

interface AnimalSpeedMonitorSectionProps {
  animal: Animal;
  onRefreshSpeed: () => void;
  isRefreshingSpeed: boolean;
}

/**
 * @magic_description Speed monitor section component for animal detail screen
 * Displays the speed monitor with refresh functionality
 */
const AnimalSpeedMonitorSection: React.FC<AnimalSpeedMonitorSectionProps> = ({
  animal,
  onRefreshSpeed,
  isRefreshingSpeed
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Speed Monitor</Text>
      </View>
      
      <SpeedMonitor 
        animal={animal} 
        onRefresh={onRefreshSpeed}
        isRefreshing={isRefreshingSpeed}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
});

export default AnimalSpeedMonitorSection;