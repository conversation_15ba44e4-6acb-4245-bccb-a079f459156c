import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Shield, AlertTriangle } from 'lucide-react-native';
import { Animal } from '../../mocks/animals';
import { useTheme } from '../../contexts/ThemeContext';

interface AnimalInfoSectionProps {
  animal: Animal;
}

/**
 * @magic_description Info section component for animal detail screen
 * Displays animal's age, gender, type, and microchip information
 */
const AnimalInfoSection: React.FC<AnimalInfoSectionProps> = ({ animal }) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.infoSection}>
      <View style={styles.infoRow}>
        {animal.age && (
          <View style={styles.infoItem}>
            <Text style={[styles.infoLabel, { color: colors.textLight }]}>Age</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>{animal.age} years</Text>
          </View>
        )}
        
        {animal.gender && (
          <View style={styles.infoItem}>
            <Text style={[styles.infoLabel, { color: colors.textLight }]}>Gender</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>
              {animal.gender.charAt(0).toUpperCase() + animal.gender.slice(1)}
            </Text>
          </View>
        )}
        
        {animal.type && (
          <View style={styles.infoItem}>
            <Text style={[styles.infoLabel, { color: colors.textLight }]}>Type</Text>
            <Text style={[styles.infoValue, { color: colors.text }]}>
              {animal.type.charAt(0).toUpperCase() + animal.type.slice(1)}
            </Text>
          </View>
        )}
      </View>
      
      {animal.microchipId ? (
        <View style={[styles.microchipBadge, { backgroundColor: colors.primary + '20' }]}>
          <Shield size={16} color={colors.primary} />
          <Text style={[styles.microchipText, { color: colors.primary }]}>
            Microchip ID: {animal.microchipId}
          </Text>
        </View>
      ) : (
        <View style={[styles.microchipBadge, { backgroundColor: colors.warning + '20' }]}>
          <AlertTriangle size={16} color={colors.warning} />
          <Text style={[styles.microchipText, { color: colors.warning }]}>
            No microchip ID - Vaccinations unavailable
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  infoSection: {
    padding: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  infoItem: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  microchipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  microchipText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default AnimalInfoSection;