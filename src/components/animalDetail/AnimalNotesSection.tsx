import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface AnimalNotesSectionProps {
  animalNotes: string;
}

/**
 * @magic_description Notes section component for animal detail screen
 * Displays animal notes if they exist
 */
const AnimalNotesSection: React.FC<AnimalNotesSectionProps> = ({
  animalNotes
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  if (!animalNotes) {
    return null;
  }
  
  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('animalDetailNotes')}</Text>
      </View>
      
      <Card style={styles.notesCard}>
        <Text style={[styles.notesText, { color: colors.text }]}>{animalNotes}</Text>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  notesCard: {
    padding: 16,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default AnimalNotesSection;