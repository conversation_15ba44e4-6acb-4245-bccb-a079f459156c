import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Droplets, Activity, TrendingUp } from 'lucide-react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useLanguage } from '../../../contexts/LanguageContext';

interface DehydrationHeaderProps {
  isRecording: boolean;
  hasAIAnalysis: boolean;
  showAIInsights: boolean;
  showDetails: boolean;
  onToggleAIInsights: () => void;
  onToggleDetails: () => void;
}

const DehydrationHeader: React.FC<DehydrationHeaderProps> = ({
  isRecording,
  hasAIAnalysis,
  showAIInsights,
  showDetails,
  onToggleAIInsights,
  onToggleDetails
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  return (
    <View style={styles.header}>
      <View style={styles.titleContainer}>
        <Droplets size={20} color={colors.primary} />
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('dehydrationMonitoring')}
        </Text>
        {isRecording && (
          <View style={[styles.recordingIndicator, { backgroundColor: colors.error }]}>
            <Activity size={12} color={colors.white} />
          </View>
        )}
        {hasAIAnalysis && (
          <View style={[styles.aiIndicator, { backgroundColor: colors.success }]}>
            <Text style={[styles.aiIndicatorText, { color: colors.white }]}>AI</Text>
          </View>
        )}
      </View>
      
      <View style={styles.headerActions}>
        {hasAIAnalysis && (
          <TouchableOpacity
            onPress={onToggleAIInsights}
            style={[
              styles.actionButton, 
              { backgroundColor: showAIInsights ? colors.primary : 'transparent' }
            ]}
          >
            <Text style={[
              styles.actionButtonText, 
              { color: showAIInsights ? colors.white : colors.primary }
            ]}>
              🧠
            </Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          onPress={onToggleDetails}
          style={[
            styles.actionButton, 
            { backgroundColor: showDetails ? colors.primary : 'transparent' }
          ]}
        >
          <TrendingUp size={16} color={showDetails ? colors.white : colors.primary} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  recordingIndicator: {
    marginLeft: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  aiIndicator: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 8,
  },
  aiIndicatorText: {
    fontSize: 10,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default DehydrationHeader;