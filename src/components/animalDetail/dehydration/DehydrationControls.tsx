import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Droplets, Play, Square } from 'lucide-react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useLanguage } from '../../../contexts/LanguageContext';

interface DehydrationControlsProps {
  isRecording: boolean;
  isSyncing: boolean;
  onTakeReading: () => void;
  onStartMonitoring: () => void;
  onStopMonitoring: () => void;
}

const DehydrationControls: React.FC<DehydrationControlsProps> = ({
  isRecording,
  isSyncing,
  onTakeReading,
  onStartMonitoring,
  onStopMonitoring
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  return (
    <View style={styles.controlsContainer}>
      {!isRecording ? (
        <>
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.primary }]}
            onPress={onTakeReading}
            disabled={isSyncing}
          >
            {isSyncing ? (
              <ActivityIndicator size="small" color={colors.white} />
            ) : (
              <Droplets size={16} color={colors.white} />
            )}
            <Text style={[styles.controlButtonText, { color: colors.white }]}>
              {isSyncing ? t('reading') : t('takeReading')}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.success }]}
            onPress={onStartMonitoring}
            disabled={isSyncing}
          >
            <Play size={16} color={colors.white} />
            <Text style={[styles.controlButtonText, { color: colors.white }]}>
              {t('startMonitoring')}
            </Text>
          </TouchableOpacity>
        </>
      ) : (
        <TouchableOpacity
          style={[styles.controlButton, { backgroundColor: colors.error }]}
          onPress={onStopMonitoring}
        >
          <Square size={16} color={colors.white} />
          <Text style={[styles.controlButtonText, { color: colors.white }]}>
            {t('stopMonitoring')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  controlsContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  controlButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  controlButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
  },
});

export default DehydrationControls;