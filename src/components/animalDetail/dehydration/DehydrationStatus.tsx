import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Droplets, AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { DehydrationReading } from '../../../store/dehydrationStore';

interface DehydrationStatusProps {
  reading: DehydrationReading;
}

const DehydrationStatus: React.FC<DehydrationStatusProps> = ({ reading }) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  const getHydrationColor = (status: string, level: number) => {
    switch (status) {
      case 'optimal':
        return colors.success;
      case 'mild_dehydration':
        return colors.warning;
      case 'moderate_dehydration':
        return colors.error;
      case 'severe_dehydration':
        return '#8B0000'; // Dark red
      default:
        return colors.textSecondary;
    }
  };

  const getHydrationIcon = (status: string) => {
    switch (status) {
      case 'optimal':
        return <Droplets size={20} color={colors.success} />;
      case 'mild_dehydration':
        return <Droplets size={20} color={colors.warning} />;
      case 'moderate_dehydration':
      case 'severe_dehydration':
        return <AlertTriangle size={20} color={colors.error} />;
      default:
        return <Droplets size={20} color={colors.textSecondary} />;
    }
  };

  const formatHydrationStatus = (status: string) => {
    switch (status) {
      case 'optimal':
        return t('optimal');
      case 'mild_dehydration':
        return t('mildDehydration');
      case 'moderate_dehydration':
        return t('moderateDehydration');
      case 'severe_dehydration':
        return t('severeDehydration');
      default:
        return t('unknown');
    }
  };

  const hydrationColor = getHydrationColor(reading.hydration_status, reading.hydration_level);

  return (
    <View style={styles.statusContainer}>
      {getHydrationIcon(reading.hydration_status)}
      <View style={styles.statusText}>
        <Text style={[styles.hydrationLevel, { color: hydrationColor }]}>
          {reading.hydration_level.toFixed(1)}%
        </Text>
        <Text style={[styles.hydrationStatus, { color: hydrationColor }]}>
          {formatHydrationStatus(reading.hydration_status)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    marginLeft: 8,
  },
  hydrationLevel: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  hydrationStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default DehydrationStatus;