import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { DehydrationAlert } from '../../../store/dehydrationStore';

interface DehydrationAlertsProps {
  alerts: DehydrationAlert[];
}

const DehydrationAlerts: React.FC<DehydrationAlertsProps> = ({ alerts }) => {
  const { colors } = useTheme();
  
  if (alerts.length === 0) return null;

  return (
    <View style={styles.alertsContainer}>
      {alerts.slice(0, 2).map(alert => (
        <View key={alert.id} style={[styles.alert, { backgroundColor: colors.error + '20' }]}>
          <AlertTriangle size={16} color={colors.error} />
          <Text style={[styles.alertText, { color: colors.error }]}>
            {alert.message}
          </Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  alertsContainer: {
    marginBottom: 12,
  },
  alert: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 6,
    marginBottom: 4,
  },
  alertText: {
    marginLeft: 6,
    fontSize: 12,
    fontWeight: '500',
  },
});

export default DehydrationAlerts;