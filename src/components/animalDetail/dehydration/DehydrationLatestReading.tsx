import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Droplets } from 'lucide-react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { DehydrationReading } from '../../../store/dehydrationStore';
import { format } from 'date-fns';
import DehydrationStatus from './DehydrationStatus';

interface DehydrationLatestReadingProps {
  reading: DehydrationReading | null;
}

const DehydrationLatestReading: React.FC<DehydrationLatestReadingProps> = ({ reading }) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  if (!reading) {
    return (
      <View style={styles.noDataContainer}>
        <Droplets size={32} color={colors.textSecondary} />
        <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
          {t('noHydrationData')}
        </Text>
        <Text style={[styles.noDataSubtext, { color: colors.textLight }]}>
          {t('takeReadingToStart')}
        </Text>
      </View>
    );
  }

  const timeSince = format(new Date(reading.created_at), 'MMM dd, HH:mm');

  return (
    <View style={styles.readingContainer}>
      <View style={styles.readingHeader}>
        <DehydrationStatus reading={reading} />
        <Text style={[styles.timestamp, { color: colors.textSecondary }]}>
          {timeSince}
        </Text>
      </View>

      {/* Additional metrics */}
      <View style={styles.metricsContainer}>
        <View style={styles.metric}>
          <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
            {t('bodyTemp')}
          </Text>
          <Text style={[styles.metricValue, { color: colors.text }]}>
            {reading.body_temperature?.toFixed(1) || '--'}°C
          </Text>
        </View>
        <View style={styles.metric}>
          <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
            {t('bioimpedance')}
          </Text>
          <Text style={[styles.metricValue, { color: colors.text }]}>
            {reading.bioimpedance_reading.toFixed(0)}Ω
          </Text>
        </View>
        <View style={styles.metric}>
          <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
            {t('signalQuality')}
          </Text>
          <Text style={[styles.metricValue, { color: colors.text }]}>
            {reading.signal_quality || '--'}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  noDataText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 8,
  },
  noDataSubtext: {
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
  },
  readingContainer: {
    marginBottom: 16,
  },
  readingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  timestamp: {
    fontSize: 12,
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metric: {
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default DehydrationLatestReading;