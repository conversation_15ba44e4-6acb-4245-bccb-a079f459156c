import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useLanguage } from '../../../contexts/LanguageContext';

interface AIAnalysis {
  overall_status: string;
  trend: string;
  average_hydration: number;
  hydration_variance: number;
  alerts: Array<{ message: string; severity: string }>;
  recommendations: string[];
}

interface DehydrationAIInsightsProps {
  aiAnalysis: AIAnalysis | null;
  isLoadingAnalysis: boolean;
  onRefreshAnalysis: () => void;
}

const DehydrationAIInsights: React.FC<DehydrationAIInsightsProps> = ({
  aiAnalysis,
  isLoadingAnalysis,
  onRefreshAnalysis
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  if (!aiAnalysis) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return colors.success;
      case 'good': return colors.primary;
      case 'concerning': return colors.warning;
      case 'critical': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return '📈';
      case 'declining': return '📉';
      case 'stable': return '➡️';
      default: return '❓';
    }
  };

  return (
    <View style={[styles.aiInsightsContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <Text style={[styles.sectionSubtitle, { color: colors.text }]}>
        🧠 {t('aiAnalysis')}
      </Text>
      
      {/* Overall Status */}
      <View style={styles.aiStatusContainer}>
        <View style={styles.aiStatusItem}>
          <Text style={[styles.aiStatusLabel, { color: colors.textSecondary }]}>
            {t('overallStatus')}
          </Text>
          <Text style={[styles.aiStatusValue, { color: getStatusColor(aiAnalysis.overall_status) }]}>
            {t(aiAnalysis.overall_status) || aiAnalysis.overall_status.charAt(0).toUpperCase() + aiAnalysis.overall_status.slice(1)}
          </Text>
        </View>
        <View style={styles.aiStatusItem}>
          <Text style={[styles.aiStatusLabel, { color: colors.textSecondary }]}>
            {t('trend')}
          </Text>
          <Text style={[styles.aiStatusValue, { color: colors.text }]}>
            {getTrendIcon(aiAnalysis.trend)} {t(aiAnalysis.trend) || aiAnalysis.trend.charAt(0).toUpperCase() + aiAnalysis.trend.slice(1)}
          </Text>
        </View>
      </View>

      {/* Key Metrics */}
      <View style={styles.aiMetricsContainer}>
        <View style={styles.aiMetric}>
          <Text style={[styles.aiMetricLabel, { color: colors.textSecondary }]}>
            {t('avgHydration')}
          </Text>
          <Text style={[styles.aiMetricValue, { color: colors.text }]}>
            {aiAnalysis.average_hydration.toFixed(1)}%
          </Text>
        </View>
        <View style={styles.aiMetric}>
          <Text style={[styles.aiMetricLabel, { color: colors.textSecondary }]}>
            {t('variability')}
          </Text>
          <Text style={[styles.aiMetricValue, { color: colors.text }]}>
            {aiAnalysis.hydration_variance.toFixed(1)}
          </Text>
        </View>
      </View>

      {/* Alerts */}
      {aiAnalysis.alerts.length > 0 && (
        <View style={styles.aiAlertsContainer}>
          <Text style={[styles.aiAlertsTitle, { color: colors.text }]}>
            ⚠️ {t('aiAlerts')}
          </Text>
          {aiAnalysis.alerts.slice(0, 2).map((alert, index) => {
            const alertColor = alert.severity === 'high' ? colors.error : 
                             alert.severity === 'medium' ? colors.warning : colors.primary;
            return (
              <View key={index} style={[styles.aiAlert, { backgroundColor: alertColor + '20' }]}>
                <Text style={[styles.aiAlertText, { color: alertColor }]}>
                  {alert.message}
                </Text>
              </View>
            );
          })}
        </View>
      )}

      {/* Top Recommendations */}
      {aiAnalysis.recommendations.length > 0 && (
        <View style={styles.aiRecommendationsContainer}>
          <Text style={[styles.aiRecommendationsTitle, { color: colors.text }]}>
            💡 {t('recommendations')}
          </Text>
          {aiAnalysis.recommendations.slice(0, 3).map((rec, index) => (
            <View key={index} style={styles.aiRecommendation}>
              <Text style={[styles.aiRecommendationText, { color: colors.textSecondary }]}>
                • {rec}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Refresh Analysis Button */}
      <TouchableOpacity
        style={[styles.refreshAnalysisButton, { backgroundColor: colors.primary }]}
        onPress={onRefreshAnalysis}
        disabled={isLoadingAnalysis}
      >
        {isLoadingAnalysis ? (
          <ActivityIndicator size="small" color={colors.white} />
        ) : (
          <Text style={[styles.refreshAnalysisText, { color: colors.white }]}>
            🔄 {t('refreshAnalysis')}
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  aiInsightsContainer: {
    marginTop: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  sectionSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  aiStatusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  aiStatusItem: {
    flex: 1,
    alignItems: 'center',
  },
  aiStatusLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  aiStatusValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  aiMetricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
  },
  aiMetric: {
    alignItems: 'center',
  },
  aiMetricLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  aiMetricValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  aiAlertsContainer: {
    marginBottom: 16,
  },
  aiAlertsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  aiAlert: {
    padding: 8,
    borderRadius: 6,
    marginBottom: 4,
  },
  aiAlertText: {
    fontSize: 12,
    fontWeight: '500',
  },
  aiRecommendationsContainer: {
    marginBottom: 16,
  },
  aiRecommendationsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  aiRecommendation: {
    marginBottom: 4,
  },
  aiRecommendationText: {
    fontSize: 12,
    lineHeight: 16,
  },
  refreshAnalysisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 6,
  },
  refreshAnalysisText: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default DehydrationAIInsights;