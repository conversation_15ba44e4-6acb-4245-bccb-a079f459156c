import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';
import { useLanguage } from '../../../contexts/LanguageContext';
import { DehydrationReading } from '../../../store/dehydrationStore';
import { format } from 'date-fns';
import DehydrationStatus from './DehydrationStatus';

interface DehydrationRecentReadingsProps {
  readings: DehydrationReading[];
  animalId: string;
  showDetails: boolean;
}

const DehydrationRecentReadings: React.FC<DehydrationRecentReadingsProps> = ({
  readings,
  animalId,
  showDetails
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  if (!showDetails || readings.length === 0) return null;

  const recentReadings = readings.filter(r => r.animal_id === animalId).slice(0, 5);

  const getHydrationColor = (status: string, level: number) => {
    switch (status) {
      case 'optimal':
        return colors.success;
      case 'mild_dehydration':
        return colors.warning;
      case 'moderate_dehydration':
        return colors.error;
      case 'severe_dehydration':
        return '#8B0000'; // Dark red
      default:
        return colors.textSecondary;
    }
  };

  const formatHydrationStatus = (status: string) => {
    switch (status) {
      case 'optimal':
        return t('optimal');
      case 'mild_dehydration':
        return t('mildDehydration');
      case 'moderate_dehydration':
        return t('moderateDehydration');
      case 'severe_dehydration':
        return t('severeDehydration');
      default:
        return t('unknown');
    }
  };

  return (
    <View style={styles.recentReadingsContainer}>
      <Text style={[styles.sectionSubtitle, { color: colors.text }]}>
        {t('recentReadings')}
      </Text>
      {recentReadings.map(reading => {
        const hydrationColor = getHydrationColor(reading.hydration_status, reading.hydration_level);
        return (
          <View key={reading.id} style={styles.recentReading}>
            <View style={styles.recentReadingLeft}>
              <Text style={[styles.recentReadingLevel, { color: hydrationColor }]}>
                {reading.hydration_level.toFixed(1)}%
              </Text>
              <Text style={[styles.recentReadingStatus, { color: colors.textSecondary }]}>
                {formatHydrationStatus(reading.hydration_status)}
              </Text>
            </View>
            <Text style={[styles.recentReadingTime, { color: colors.textSecondary }]}>
              {format(new Date(reading.created_at), 'MMM dd, HH:mm')}
            </Text>
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  recentReadingsContainer: {
    marginTop: 8,
  },
  sectionSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  recentReading: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  recentReadingLeft: {
    flex: 1,
  },
  recentReadingLevel: {
    fontSize: 16,
    fontWeight: '600',
  },
  recentReadingStatus: {
    fontSize: 12,
  },
  recentReadingTime: {
    fontSize: 12,
  },
});

export default DehydrationRecentReadings;