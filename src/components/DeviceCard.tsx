
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Battery, Bluetooth, Clock, AlertTriangle } from 'lucide-react-native';
import { Device } from '../mocks/devices';
import { useTheme } from '../contexts/ThemeContext';

interface DeviceCardProps {
  device: Device;
  onPress?: () => void;
  onPair?: () => void;
  onUnpair?: () => void;
}

const DeviceCard: React.FC<DeviceCardProps> = ({ 
  device, 
  onPress, 
  onPair, 
  onUnpair 
}) => {
  const { colors } = useTheme();
  // Defensive validation for device data
  if (!device || typeof device !== 'object') {
    return null;
  }
  
  const formatTime = (timestamp?: number) => {
    if (!timestamp) return 'Never';
    
    const date = new Date(timestamp);
    return date.toLocaleString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };
  
  // Handle both batteryLevel and battery_level field names
  const batteryLevel = device.batteryLevel ?? device.battery_level ?? 0;
  
  const getBatteryColor = () => {
    if (batteryLevel <= 20) return colors.error;
    if (batteryLevel <= 40) return colors.warning;
    return colors.success;
  };
  
  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.card,
      borderRadius: 16,
      padding: 16,
      marginBottom: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    name: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginLeft: 8,
    },
    batteryContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    batteryText: {
      fontSize: 14,
      fontWeight: '500',
      marginLeft: 4,
    },
    infoContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    type: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statusIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginRight: 6,
    },
    connected: {
      backgroundColor: colors.success,
    },
    disconnected: {
      backgroundColor: colors.textSecondary,
    },
    lowBattery: {
      backgroundColor: colors.warning,
    },
    statusText: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    syncContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    syncText: {
      fontSize: 14,
      color: colors.textSecondary,
      marginLeft: 6,
    },
    actionContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
    },
    pairButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
    },
    pairButtonText: {
      color: colors.white,
      fontWeight: '500',
      fontSize: 14,
    },
    unpairButton: {
      backgroundColor: colors.background,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.border,
    },
    unpairButtonText: {
      color: colors.textSecondary,
      fontWeight: '500',
      fontSize: 14,
    },
  });

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={!onPress}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Bluetooth 
            size={18} 
            color={
              device.status === 'connected' ? colors.success :
              device.status === 'low_battery' ? colors.warning :
              colors.textSecondary
            } 
          />
          <Text style={styles.name}>{device.name}</Text>
        </View>
        <View style={styles.batteryContainer}>
          <Battery size={16} color={getBatteryColor()} />
          <Text style={[styles.batteryText, { color: getBatteryColor() }]}>
            {device.batteryLevel}%
          </Text>
        </View>
      </View>
      
      <View style={styles.infoContainer}>
        <Text style={styles.type}>{device.type}</Text>
        
        <View style={styles.statusContainer}>
          <View style={[
            styles.statusIndicator,
            device.status === 'connected' ? styles.connected :
            device.status === 'low_battery' ? styles.lowBattery :
            styles.disconnected
          ]} />
          <Text style={styles.statusText}>
            {device.status === 'connected' ? 'Connected' :
             device.status === 'low_battery' ? 'Low Battery' :
             device.status === 'pairing' ? 'Pairing...' :
             'Disconnected'}
          </Text>
        </View>
      </View>
      
      {device.lastSyncTime && (
        <View style={styles.syncContainer}>
          <Clock size={14} color={colors.textSecondary} />
          <Text style={styles.syncText}>
            Last sync: {formatTime(device.lastSyncTime)}
          </Text>
        </View>
      )}
      
      <View style={styles.actionContainer}>
        {device.status === 'disconnected' && onPair && (
          <TouchableOpacity 
            style={styles.pairButton}
            onPress={onPair}
          >
            <Text style={styles.pairButtonText}>Pair Device</Text>
          </TouchableOpacity>
        )}
        
        {(device.status === 'connected' || device.status === 'low_battery') && onUnpair && (
          <TouchableOpacity 
            style={styles.unpairButton}
            onPress={onUnpair}
          >
            <Text style={styles.unpairButtonText}>Unpair</Text>
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
};



export default DeviceCard;
