import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Gauge, RefreshCw } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useLanguage } from '../../contexts/LanguageContext';
import { SpeedDisplayData } from '../../hooks/useSpeedDisplay';

interface FullSpeedViewProps {
  speedData: SpeedDisplayData;
  colors: any;
  isDarkMode: boolean;
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

/**
 * @magic_description Full view for speed monitoring
 * Shows detailed speed information with gradient background
 */
const FullSpeedView: React.FC<FullSpeedViewProps> = ({
  speedData,
  colors,
  isDarkMode,
  onRefresh,
  isRefreshing = false,
}) => {
  const { t } = useLanguage();
  
  const gradientColors = isDarkMode
    ? [colors.card, colors.background]
    : [colors.primary + '10', colors.card];

  return (
    <LinearGradient
      colors={gradientColors}
      style={[styles.container, { borderColor: colors.border }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Gauge size={24} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>{t('currentSpeed')}</Text>
        </View>
        
        {onRefresh && (
          <TouchableOpacity
            style={[styles.refreshButton, { backgroundColor: colors.primary }]}
            onPress={onRefresh}
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <ActivityIndicator size="small" color={colors.background} />
            ) : (
              <View style={styles.refreshContent}>
                <RefreshCw size={16} color={colors.background} />
                <Text style={[styles.refreshText, { color: colors.background }]}>
                  {t('refresh')}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.speedSection}>
        <View style={styles.speedContainer}>
          <Text style={[styles.speed, { color: speedData.speedColor }]}>
            {speedData.formattedSpeed}
          </Text>
          <Text style={[styles.unit, { color: colors.textSecondary }]}>
            {speedData.speedUnit}
          </Text>
        </View>
        
        <View style={styles.statusContainer}>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: speedData.speedColor + '20' }
          ]}>
            <View style={[
              styles.statusDot,
              { backgroundColor: speedData.speedColor }
            ]} />
            <Text style={[styles.statusText, { color: speedData.speedColor }]}>
              {speedData.speedLevel.toUpperCase()}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.footer}>
        <Text style={[styles.lastUpdated, { color: colors.textSecondary }]}>
          {t('lastUpdated')}: {speedData.lastUpdated}
        </Text>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  refreshButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    minWidth: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  refreshContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  refreshText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  speedSection: {
    alignItems: 'center',
    marginBottom: 20,
  },
  speedContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 12,
  },
  speed: {
    fontSize: 48,
    fontWeight: '800',
    lineHeight: 56,
  },
  unit: {
    fontSize: 18,
    fontWeight: '500',
    marginLeft: 8,
  },
  statusContainer: {
    alignItems: 'center',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  footer: {
    alignItems: 'center',
  },
  lastUpdated: {
    fontSize: 12,
    fontStyle: 'italic',
  },
});

export default FullSpeedView;