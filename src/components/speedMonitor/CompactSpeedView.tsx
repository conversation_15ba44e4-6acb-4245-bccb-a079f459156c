import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Gauge, RefreshCw } from 'lucide-react-native';
import { useLanguage } from '../../contexts/LanguageContext';
import { SpeedDisplayData } from '../../hooks/useSpeedDisplay';

interface CompactSpeedViewProps {
  speedData: SpeedDisplayData;
  colors: any;
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

/**
 * @magic_description Compact view for speed monitoring
 * Shows speed with minimal UI for dashboard/list contexts
 */
const CompactSpeedView: React.FC<CompactSpeedViewProps> = ({
  speedData,
  colors,
  onRefresh,
  isRefreshing = false,
}) => {
  const { t } = useLanguage();
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Gauge size={16} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>{t('speed')}</Text>
        </View>
        
        {onRefresh && (
          <TouchableOpacity
            style={[styles.refreshButton, { backgroundColor: colors.primary + '20' }]}
            onPress={onRefresh}
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <RefreshCw size={14} color={colors.primary} />
            )}
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.speedContainer}>
        <Text style={[styles.speed, { color: speedData.speedColor }]}>
          {speedData.formattedSpeed}
        </Text>
        <Text style={[styles.unit, { color: colors.textSecondary }]}>
          {speedData.speedUnit}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  refreshButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  speedContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  speed: {
    fontSize: 24,
    fontWeight: '700',
  },
  unit: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
});

export default CompactSpeedView;