import React, { useState, useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAnimalStore } from '../../store/animalStore';
import { useVitalsStore } from '../../store/vitalsStore';
import { HomeStackParamList } from '../../navigation';
import AlertsSection from './AlertsSection';

type AlertsSectionContainerNavigationProp = NativeStackNavigationProp<HomeStackParamList>;

interface Alert {
  id: string;
  type: 'heartRate' | 'temperature' | 'general' | 'location';
  animalName: string;
  animalId: string;
  message: string;
  timestamp: string;
}

interface VaccineAlert {
  id: string;
  animalId: string;
  animalName: string;
  vaccineName: string;
  expiryDate: string;
  daysRemaining: number;
}

/**
 * @magic_description Container component for AlertsSection that handles alert generation and navigation
 * Processes vitals data to generate health alerts and manages vaccine alerts
 */
const AlertsSectionContainer: React.FC = () => {
  const navigation = useNavigation<AlertsSectionContainerNavigationProp>();
  
  const { animals } = useAnimalStore();
  const { vitals } = useVitalsStore();
  
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [vaccineAlerts, setVaccineAlerts] = useState<VaccineAlert[]>([]);
  
  // Get latest vitals for alert checking
  const latestVitals = animals.map(animal => {
    const animalVitals = vitals
      .filter(v => v.animalId === animal.id)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    return {
      animal,
      vital: animalVitals[0] || null,
    };
  });
  
  const abnormalVitals = latestVitals.filter(({ vital }) => {
    if (!vital) return false;
    
    const isTemperatureAbnormal = vital.temperature < 37.2 || vital.temperature > 38.5;
    const isHeartRateAbnormal = vital.heartRate ? (vital.heartRate < 30 || vital.heartRate > 50) : false;
    const isRespirationAbnormal = vital.respirationRate ? (vital.respirationRate < 8 || vital.respirationRate > 20) : false;
    
    return isTemperatureAbnormal || isHeartRateAbnormal || isRespirationAbnormal;
  });
  
  useEffect(() => {
    // Generate health alerts
    const newAlerts = abnormalVitals.map(({ animal, vital }) => {
      let type: 'heartRate' | 'temperature' | 'general' = 'general';
      let message = 'Abnormal vital signs detected';
      
      if (vital?.temperature && (vital.temperature < 37.2 || vital.temperature > 38.5)) {
        type = 'temperature';
        message = `Temperature is ${vital.temperature.toFixed(1)}°C (normal: 37.2-38.5°C)`;
      } else if (vital?.heartRate && (vital.heartRate < 30 || vital.heartRate > 50)) {
        type = 'heartRate';
        message = `Heart rate is ${vital.heartRate} bpm (normal: 30-50 bpm)`;
      }
      
      return {
        id: vital?.id || `alert-${animal.id}`,
        type,
        animalName: animal.name,
        animalId: animal.id,
        message,
        timestamp: new Date(vital?.timestamp || '').toLocaleString(),
      };
    });
    
    // Generate location alerts
    const locationAlerts = animals
      .filter(animal => animal.location && Date.now() - animal.location.timestamp > 86400000)
      .map(animal => ({
        id: `loc-${animal.id}`,
        type: 'location' as const,
        animalName: animal.name,
        animalId: animal.id,
        message: 'Location data is outdated (more than 24 hours old)',
        timestamp: new Date(animal.location?.timestamp || 0).toLocaleString(),
      }));
    
    setAlerts([...newAlerts, ...locationAlerts]);
    
    // Generate vaccine alerts
    const today = new Date();
    const vaccineData = animals.flatMap(animal => {
      if (!animal.microchipId) return [];
      
      return [
        {
          id: `vac-${animal.id}-1`,
          animalId: animal.id,
          animalName: animal.name,
          vaccineName: 'Equine Influenza',
          expiryDate: new Date(today.getTime() + 15 * 24 * 60 * 60 * 1000),
        },
        {
          id: `vac-${animal.id}-2`,
          animalId: animal.id,
          animalName: animal.name,
          vaccineName: 'Tetanus',
          expiryDate: new Date(today.getTime() + 45 * 24 * 60 * 60 * 1000),
        }
      ];
    });
    
    const vaccineAlerts = vaccineData
      .filter(vaccine => {
        const daysRemaining = Math.ceil((vaccine.expiryDate.getTime() - today.getTime()) / (24 * 60 * 60 * 1000));
        return daysRemaining <= 60;
      })
      .map(vaccine => {
        const daysRemaining = Math.ceil((vaccine.expiryDate.getTime() - today.getTime()) / (24 * 60 * 60 * 1000));
        return {
          ...vaccine,
          daysRemaining,
          expiryDate: vaccine.expiryDate.toLocaleDateString()
        };
      });
    
    setVaccineAlerts(vaccineAlerts);
  }, [abnormalVitals, animals]);
  
  // Navigation handler
  const handleAlertPress = (animalId: string) => {
    navigation.navigate('AnimalDetail', { id: animalId });
  };
  
  // Don't render if no alerts
  if (alerts.length === 0 && vaccineAlerts.length === 0) {
    return null;
  }
  
  return (
    <AlertsSection
      alerts={alerts}
      vaccineAlerts={vaccineAlerts}
      onAlertPress={handleAlertPress}
    />
  );
};

export default AlertsSectionContainer;