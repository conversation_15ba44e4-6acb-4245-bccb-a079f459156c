import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Heart, Brain, Moon, Droplets, TrendingUp, TrendingDown, AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAIHealthStore } from '../../store/aiHealthStore';
import { useBehavioralStore } from '../../store/behavioralStore';
import { useDehydrationStore } from '../../store/dehydrationStore';
import Card from '../ui/Card';
import { Animal } from '../../mocks/animals';

interface HealthSnapshotProps {
  animal: Animal;
  onCardPress?: (type: 'health' | 'stress' | 'sleep' | 'dehydration') => void;
}

const HealthSnapshotFixed: React.FC<HealthSnapshotProps> = ({ animal, onCardPress }) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  const { currentHealthScore } = useAIHealthStore();
  const { getLatestStressAnalysis, getLatestSleepAnalysis } = useBehavioralStore();
  const { getLatestReading } = useDehydrationStore();
  
  const latestStress = getLatestStressAnalysis(animal.id);
  const latestSleep = getLatestSleepAnalysis(animal.id);
  const latestDehydration = getLatestReading(animal.id);
  
  const getHealthScoreColor = (score?: number) => {
    if (!score) return colors.textSecondary;
    if (score >= 80) return colors.success;
    if (score >= 60) return colors.warning;
    return colors.error;
  };
  
  const getStressLevelColor = (level?: string) => {
    switch (level) {
      case 'very_low':
      case 'low':
        return colors.success;
      case 'moderate':
        return colors.warning;
      case 'high':
      case 'very_high':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };
  
  const getSleepQualityColor = (score?: number) => {
    if (!score) return colors.textSecondary;
    if (score >= 80) return colors.success;
    if (score >= 60) return colors.warning;
    return colors.error;
  };
  
  const getDehydrationColor = (status?: string) => {
    switch (status) {
      case 'optimal':
        return colors.success;
      case 'mild_dehydration':
        return colors.warning;
      case 'moderate_dehydration':
      case 'severe_dehydration':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };
  
  const formatStressLevel = (level?: string) => {
    if (!level) return 'No Data';
    return level.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };
  
  const formatSleepQuality = (score?: number) => {
    if (!score) return 'No Data';
    if (score >= 80) return 'Excellent';
    if (score >= 70) return 'Good';
    if (score >= 60) return 'Fair';
    return 'Poor';
  };
  
  const formatDehydrationStatus = (status?: string) => {
    if (!status) return 'No Data';
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };
  
  const healthCards = [
    {
      id: 'health',
      title: 'Health Score',
      value: currentHealthScore?.overall_score ? `${Math.round(currentHealthScore.overall_score)}%` : 'No Data',
      subtitle: currentHealthScore ? 'Latest Assessment' : 'Tap to Calculate',
      icon: Heart,
      color: getHealthScoreColor(currentHealthScore?.overall_score),
      trend: currentHealthScore?.overall_score ? (currentHealthScore.overall_score >= 70 ? 'up' : 'down') : null,
    },
    {
      id: 'stress',
      title: 'Stress Level',
      value: formatStressLevel(latestStress?.stress_level),
      subtitle: latestStress ? `Score: ${Math.round(latestStress.stress_score)}` : 'Tap to Analyze',
      icon: Brain,
      color: getStressLevelColor(latestStress?.stress_level),
      trend: latestStress?.stress_level ? (latestStress.stress_level === 'low' || latestStress.stress_level === 'very_low' ? 'up' : 'down') : null,
    },
    {
      id: 'sleep',
      title: 'Sleep Quality',
      value: formatSleepQuality(latestSleep?.sleep_quality_score),
      subtitle: latestSleep ? `${Math.round(latestSleep.total_sleep_duration_minutes / 60)}h sleep` : 'Tap to Monitor',
      icon: Moon,
      color: getSleepQualityColor(latestSleep?.sleep_quality_score),
      trend: latestSleep?.sleep_quality_score ? (latestSleep.sleep_quality_score >= 70 ? 'up' : 'down') : null,
    },
    {
      id: 'dehydration',
      title: 'Hydration',
      value: formatDehydrationStatus(latestDehydration?.hydration_status),
      subtitle: latestDehydration ? `${latestDehydration.hydration_level.toFixed(1)}% level` : 'Tap to Check',
      icon: Droplets,
      color: getDehydrationColor(latestDehydration?.hydration_status),
      trend: latestDehydration?.hydration_status ? (latestDehydration.hydration_status === 'optimal' ? 'up' : 'down') : null,
    },
  ];
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>Health Snapshot</Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {animal.name}'s latest health metrics
        </Text>
      </View>
      
      <View style={styles.cardsGrid}>
        {healthCards.map((card) => {
          const IconComponent = card.icon;
          const TrendIcon = card.trend === 'up' ? TrendingUp : card.trend === 'down' ? TrendingDown : null;
          const hasAlert = (card.id === 'stress' && latestStress?.stress_level === 'high') ||
                          (card.id === 'dehydration' && latestDehydration?.hydration_status === 'severe_dehydration');
          
          return (
            <TouchableOpacity
              key={card.id}
              style={styles.cardContainer}
              onPress={() => onCardPress?.(card.id as any)}
              activeOpacity={0.7}
            >
              <Card style={[styles.card, hasAlert && { borderColor: colors.error, borderWidth: 2 }]} elevation={2}>
                <View style={styles.cardHeader}>
                  <View style={[styles.iconContainer, { backgroundColor: card.color + '20' }]}>
                    <IconComponent size={20} color={card.color} />
                    {hasAlert && (
                      <View style={styles.alertBadge}>
                        <AlertTriangle size={12} color={colors.white} />
                      </View>
                    )}
                  </View>
                  {TrendIcon && (
                    <TrendIcon size={16} color={card.color} />
                  )}
                </View>
                
                <Text style={[styles.cardTitle, { color: colors.text }]}>
                  {card.title}
                </Text>
                
                <Text style={[styles.cardValue, { color: card.color }]}>
                  {card.value}
                </Text>
                
                <Text style={[styles.cardSubtitle, { color: colors.textSecondary }]}>
                  {card.subtitle}
                </Text>
              </Card>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  cardsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  cardContainer: {
    width: '48%',
  },
  card: {
    padding: 16,
    minHeight: 120,
    justifyContent: 'space-between',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  alertBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#EF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  cardValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 11,
    lineHeight: 14,
  },
});

export default HealthSnapshotFixed;