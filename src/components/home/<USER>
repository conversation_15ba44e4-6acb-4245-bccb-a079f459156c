
import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Image,
  Dimensions 
} from 'react-native';
import { Plus, ChevronRight } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Animal } from '../../mocks/animals';
import AnimalCard from '../AnimalCard';

interface AnimalsOverviewProps {
  animals: Animal[];
  onSeeAll: () => void;
  onAddAnimal: () => void;
  onAnimalPress?: (animalId: string) => void;
}

/**
 * @magic_description Animals overview component for the home screen
 * Shows a horizontal list of animals with add animal option
 */
const AnimalsOverview: React.FC<AnimalsOverviewProps> = ({
  animals,
  onSeeAll,
  onAddAnimal,
  onAnimalPress
}) => {
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();

  return (
    <View style={styles.container}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('myAnimals')}</Text>
        <TouchableOpacity onPress={onSeeAll} style={styles.seeAllButton}>
          <Text style={[styles.seeAll, { color: colors.primary }]}>{t('seeAll')}</Text>
          <ChevronRight size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      {animals.length > 0 ? (
        <ScrollView 
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.animalsContainer}
        >
          {animals.map(animal => (
            <AnimalCard 
              key={animal.id} 
              animal={animal} 
              compact 
              onPress={() => onAnimalPress?.(animal.id)}
            />
          ))}
          
          <TouchableOpacity 
            style={[styles.addAnimalCard, { 
              borderColor: colors.border,
              backgroundColor: isDarkMode ? 'rgba(61, 140, 145, 0.1)' : 'rgba(255, 255, 255, 0.8)'
            }]}
            onPress={onAddAnimal}
          >
            <View style={[styles.addIconCircle, { backgroundColor: colors.primary }]}>
              <Plus size={20} color="#FFF" />
            </View>
            <Text style={[styles.addAnimalText, { color: colors.primary }]}>{t('addAnimal')}</Text>
          </TouchableOpacity>
        </ScrollView>
      ) : (
        <View style={[styles.emptyState, { backgroundColor: colors.card }]}>
          <Text style={[styles.emptyStateText, { color: colors.textLight }]}>
            {t('noAnimalsYet')}
          </Text>
          <TouchableOpacity 
            style={[styles.emptyStateButton, { backgroundColor: colors.primary }]}
            onPress={onAddAnimal}
          >
            <Text style={[styles.emptyStateButtonText, { color: colors.card }]}>
              {t('addFirstAnimal')}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAll: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  animalsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 8,
  },
  addAnimalCard: {
    width: width * 0.28,
    height: width * 0.28 + 60,
    borderRadius: 16,
    borderWidth: 1,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  addIconCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  addAnimalText: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 20,
    marginHorizontal: 20,
    borderRadius: 16,
  },
  emptyStateText: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyStateButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    fontWeight: '600',
    fontSize: 14,
  },
});

export default AnimalsOverview;
