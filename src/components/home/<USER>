import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, FlatList, Image } from 'react-native';
import { ChevronDown, Check } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Animal } from '../../mocks/animals';
import Card from '../ui/Card';

interface AnimalSelectorProps {
  animals: Animal[];
  selectedAnimal: Animal | null;
  onAnimalSelect: (animal: Animal) => void;
}

/**
 * @magic_description Animal selector dropdown for choosing which animal's health data to display
 * Shows animal list with photos and allows selection for health snapshot
 */
const AnimalSelector: React.FC<AnimalSelectorProps> = ({
  animals,
  selectedAnimal,
  onAnimalSelect,
}) => {
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  const [isModalVisible, setIsModalVisible] = useState(false);
  
  const handleAnimalSelect = (animal: Animal) => {
    onAnimalSelect(animal);
    setIsModalVisible(false);
  };
  
  const styles = StyleSheet.create({
    container: {
      marginBottom: 16,
    },
    selector: {
      marginBottom: 8,
    },
    selectorCard: {
      padding: 0,
    },
    selectorContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
    },
    selectedAnimal: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    selectedAnimalImage: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: 12,
    },
    selectedAnimalInfo: {
      flex: 1,
    },
    selectedAnimalName: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 2,
    },
    selectedAnimalBreed: {
      fontSize: 14,
    },
    placeholderContent: {
      flex: 1,
    },
    placeholderText: {
      fontSize: 16,
    },
    modal: {
      flex: 1,
      paddingTop: 60,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingBottom: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '700',
    },
    closeButton: {
      padding: 8,
    },
    closeButtonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    animalList: {
      padding: 20,
    },
    animalItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 12,
      paddingHorizontal: 16,
      marginBottom: 8,
      backgroundColor: colors.card,
      borderRadius: 12,
      shadowColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    animalInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    animalImage: {
      width: 50,
      height: 50,
      borderRadius: 25,
      marginRight: 12,
    },
    animalDetails: {
      flex: 1,
    },
    animalName: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    animalBreed: {
      fontSize: 14,
    },
    checkIcon: {
      width: 24,
      height: 24,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyCard: {
      padding: 20,
      alignItems: 'center',
    },
    emptyText: {
      fontSize: 16,
      textAlign: 'center',
    },
  });

  const renderAnimalItem = ({ item: animal }: { item: Animal }) => (
    <TouchableOpacity
      style={styles.animalItem}
      onPress={() => handleAnimalSelect(animal)}
      activeOpacity={0.7}
    >
      <View style={styles.animalInfo}>
        <Image
          source={{ uri: animal.imageUrl }}
          style={styles.animalImage}
          resizeMode="cover"
        />
        <View style={styles.animalDetails}>
          <Text style={[styles.animalName, { color: colors.text }]}>
            {animal.name}
          </Text>
          <Text style={[styles.animalBreed, { color: colors.textSecondary }]}>
            {animal.breed} • {animal.age} {animal.age === 1 ? 'year' : 'years'}
          </Text>
        </View>
      </View>
      
      {selectedAnimal?.id === animal.id && (
        <View style={[styles.checkIcon, { backgroundColor: colors.primary }]}>
          <Check size={16} color={colors.white} />
        </View>
      )}
    </TouchableOpacity>
  );
  
  if (animals.length === 0) {
    return (
      <View style={styles.container}>
        <Card style={styles.emptyCard}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No animals added yet. Add your first animal to see health data.
          </Text>
        </Card>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.selector}
        onPress={() => setIsModalVisible(true)}
        activeOpacity={0.7}
      >
        <Card style={styles.selectorCard} elevation={1}>
          <View style={styles.selectorContent}>
            {selectedAnimal ? (
              <View style={styles.selectedAnimal}>
                <Image
                  source={{ uri: selectedAnimal.imageUrl }}
                  style={styles.selectedAnimalImage}
                  resizeMode="cover"
                />
                <View style={styles.selectedAnimalInfo}>
                  <Text style={[styles.selectedAnimalName, { color: colors.text }]}>
                    {selectedAnimal.name}
                  </Text>
                  <Text style={[styles.selectedAnimalBreed, { color: colors.textSecondary }]}>
                    {selectedAnimal.breed}
                  </Text>
                </View>
              </View>
            ) : (
              <View style={styles.placeholderContent}>
                <Text style={[styles.placeholderText, { color: colors.textSecondary }]}>
                  Select an animal
                </Text>
              </View>
            )}
            
            <ChevronDown size={20} color={colors.textSecondary} />
          </View>
        </Card>
      </TouchableOpacity>
      
      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={[styles.modal, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Select Animal
            </Text>
            <TouchableOpacity
              onPress={() => setIsModalVisible(false)}
              style={styles.closeButton}
            >
              <Text style={[styles.closeButtonText, { color: colors.primary }]}>
                Done
              </Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={animals}
            renderItem={renderAnimalItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.animalList}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </Modal>
    </View>
  );
};



export default AnimalSelector;