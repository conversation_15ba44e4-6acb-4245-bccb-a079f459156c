
import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Image 
} from 'react-native';
import { Coffee, Syringe, ChevronRight, Clock } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Animal } from '../../mocks/animals';
import Card from '../ui/Card';

interface Task {
  type: 'feeding' | 'medication';
  animal: Animal;
  item: any;
  time: string;
}

interface UpcomingTasksProps {
  tasks: Task[];
  onTaskPress: (animalId: string) => void;
}

/**
 * @magic_description Upcoming tasks component for the home screen
 * Shows today's feeding and medication schedules
 */
const UpcomingTasks: React.FC<UpcomingTasksProps> = ({
  tasks,
  onTaskPress
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  if (tasks.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('upcomingTasks')}</Text>
        </View>
        
        <Card style={[styles.emptyState, { backgroundColor: colors.card }]}>
          <Clock size={32} color={colors.textLight} />
          <Text style={[styles.emptyStateText, { color: colors.textLight }]}>
            {t('noTasksToday')}
          </Text>
          <Text style={[styles.emptyStateSubtext, { color: colors.textLight }]}>
            {t('allCaughtUp')}
          </Text>
        </Card>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('upcomingTasks')}</Text>
        <View style={[styles.taskCount, { backgroundColor: colors.primary + '20' }]}>
          <Text style={[styles.taskCountText, { color: colors.primary }]}>
            {tasks.length}
          </Text>
        </View>
      </View>
      
      {tasks.slice(0, 3).map((task, index) => {
        const IconComponent = task.type === 'feeding' ? Coffee : Syringe;
        const iconColor = task.type === 'feeding' ? colors.accent1 : colors.secondary;
        
        return (
          <Card
            key={`${task.type}-${task.item.id}-${index}`}
            style={styles.taskCard}
            onPress={() => onTaskPress(task.animal.id)}
            elevation={1}
          >
            <View style={[styles.taskIcon, { backgroundColor: iconColor + '20' }]}>
              <IconComponent size={20} color={iconColor} />
            </View>
            
            <Image 
              source={{ uri: task.animal.imageUrl }} 
              style={styles.taskAnimalImage} 
            />
            
            <View style={styles.taskContent}>
              <Text style={[styles.taskAnimalName, { color: colors.text }]}>
                {task.animal.name}
              </Text>
              <Text style={[styles.taskDescription, { color: colors.textLight }]}>
                {task.type === 'feeding' 
                  ? t('feedingTask', { feedType: task.item.feedType })
                  : t('medicationTask', { medicationName: task.item.medicationName })}
              </Text>
              {task.type === 'feeding' && task.item.amount && (
                <Text style={[styles.taskAmount, { color: colors.textLight }]}>
                  {t('amount')}: {task.item.amount}
                </Text>
              )}
              {task.type === 'medication' && task.item.dosage && (
                <Text style={[styles.taskAmount, { color: colors.textLight }]}>
                  {t('dosage')}: {task.item.dosage} {task.item.dosageUnit}
                </Text>
              )}
            </View>
            
            <View style={styles.taskTime}>
              <Text style={[styles.taskTimeText, { color: iconColor }]}>
                {task.time.split(':').map(Number).map(n => n.toString().padStart(2, '0')).join(':')}
              </Text>
              <ChevronRight size={16} color={colors.textLight} />
            </View>
          </Card>
        );
      })}
      
      {tasks.length > 3 && (
        <Text style={[styles.moreTasksText, { color: colors.textLight }]}>
          {t('moreTasksToday', { count: tasks.length - 3 })}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  taskCount: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 24,
    alignItems: 'center',
  },
  taskCountText: {
    fontSize: 12,
    fontWeight: '600',
  },
  taskCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 12,
  },
  taskIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  taskAnimalImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  taskContent: {
    flex: 1,
    marginRight: 8,
  },
  taskAnimalName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  taskDescription: {
    fontSize: 12,
    marginBottom: 2,
  },
  taskAmount: {
    fontSize: 11,
  },
  taskTime: {
    alignItems: 'flex-end',
  },
  taskTimeText: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  moreTasksText: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 12,
    marginBottom: 4,
  },
  emptyStateSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default UpcomingTasks;
