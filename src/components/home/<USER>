import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useAnimalStore } from '../../store/animalStore';
import { useTheme } from '../../contexts/ThemeContext';
import { toast } from 'sonner-native';
import SpeedMonitor from '../SpeedMonitor';

/**
 * @magic_description Container component for SpeedMonitor that handles data fetching and speed updates
 * Manages active animal speed monitoring and refresh functionality
 */
const SpeedMonitorContainer: React.FC = () => {
  const { colors } = useTheme();
  const { getActiveAnimal, updateAnimalSpeed } = useAnimalStore();
  
  const [isRefreshingSpeed, setIsRefreshingSpeed] = useState(false);
  
  const activeAnimal = getActiveAnimal();
  
  const handleRefreshSpeed = async () => {
    if (!activeAnimal) return;
    
    setIsRefreshingSpeed(true);
    
    try {
      const newSpeed = Math.random() * 30;
      await updateAnimalSpeed(activeAnimal.id, newSpeed);
      toast.success(`${activeAnimal.name}'s speed updated`);
    } catch (error) {
      console.error('Error updating speed:', error);
      toast.error('Failed to update speed');
    } finally {
      setIsRefreshingSpeed(false);
    }
  };
  
  // Don't render if no active animal
  if (!activeAnimal) {
    return null;
  }
  
  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {activeAnimal.name}'s Speed
        </Text>
      </View>
      
      <SpeedMonitor 
        animal={activeAnimal} 
        onRefresh={handleRefreshSpeed}
        isRefreshing={isRefreshingSpeed}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
});

export default SpeedMonitorContainer;