import React from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAnimalStore } from '../../store/animalStore';
import { HomeStackParamList } from '../../navigation';
import AnimalsOverview from './AnimalsOverview';

type AnimalsOverviewContainerNavigationProp = NativeStackNavigationProp<HomeStackParamList>;

/**
 * @magic_description Container component for AnimalsOverview that handles data fetching and navigation
 * Manages animal data and navigation to animal-related screens
 */
const AnimalsOverviewContainer: React.FC = () => {
  const navigation = useNavigation<AnimalsOverviewContainerNavigationProp>();
  
  const { animals } = useAnimalStore();
  
  // Navigation handlers
  const handleSeeAll = () => {
    navigation.navigate('Animals');
  };
  
  const handleAddAnimal = () => {
    navigation.navigate('AnimalsStack' as never, { screen: 'AddAnimal' } as never);
  };
  
  const handleAnimalPress = (id: string) => {
    navigation.navigate('AnimalDetail', { id });
  };
  
  return (
    <AnimalsOverview
      animals={animals}
      onSeeAll={handleSeeAll}
      onAddAnimal={handleAddAnimal}
      onAnimalPress={handleAnimalPress}
    />
  );
};

export default AnimalsOverviewContainer;