import React from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAnimalStore } from '../../store/animalStore';
import { useFeedingStore } from '../../store/feedingStore';
import { useMedicationStore } from '../../store/medicationStore';
import { HomeStackParamList } from '../../navigation';
import QuickStats from './QuickStats';

type QuickStatsContainerNavigationProp = NativeStackNavigationProp<HomeStackParamList>;

/**
 * @magic_description Container component for QuickStats that handles data fetching and navigation
 * Calculates stats from various stores and manages navigation logic
 */
const QuickStatsContainer: React.FC = () => {
  const navigation = useNavigation<QuickStatsContainerNavigationProp>();
  
  const { animals } = useAnimalStore();
  const { getTodayFeedings } = useFeedingStore();
  const { getTodayMedications } = useMedicationStore();
  
  // Calculate stats
  const totalAnimals = animals.length;
  const activeDevices = animals.filter(animal => animal.deviceStatus === 'connected').length;
  const todayFeedings = animals.reduce((total, animal) => 
    total + getTodayFeedings(animal.id).length, 0
  );
  const todayMedications = animals.reduce((total, animal) => 
    total + getTodayMedications(animal.id).length, 0
  );
  
  // Navigation handlers
  const handleStatsPress = (type: 'animals' | 'devices' | 'feedings' | 'medications') => {
    switch (type) {
      case 'animals':
        navigation.navigate('Animals');
        break;
      case 'devices':
        navigation.navigate('Devices');
        break;
      default:
        // For feedings and medications, go to the first animal's detail
        if (animals.length > 0) {
          navigation.navigate('AnimalDetail', { id: animals[0].id });
        }
        break;
    }
  };
  
  return (
    <QuickStats
      totalAnimals={totalAnimals}
      activeDevices={activeDevices}
      todayFeedings={todayFeedings}
      todayMedications={todayMedications}
      onStatsPress={handleStatsPress}
    />
  );
};

export default QuickStatsContainer;