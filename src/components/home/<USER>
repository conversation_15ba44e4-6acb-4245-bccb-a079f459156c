import React from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useUserStore } from '../../store/userStore';
import { HomeStackParamList } from '../../navigation';
import PremiumBanner from '../PremiumBanner';

type PremiumBannerContainerNavigationProp = NativeStackNavigationProp<HomeStackParamList>;

/**
 * @magic_description Container component for PremiumBanner that handles user subscription status and navigation
 * Conditionally renders premium banner based on user subscription status
 */
const PremiumBannerContainer: React.FC = () => {
  const navigation = useNavigation<PremiumBannerContainerNavigationProp>();
  
  const { user } = useUserStore();
  
  const isPremium = user?.isPremium || false;
  
  // Navigation handler
  const handleSubscription = () => {
    navigation.navigate('Subscription');
  };
  
  // Don't render if user is premium
  if (isPremium) {
    return null;
  }
  
  return (
    <PremiumBanner onPress={handleSubscription} />
  );
};

export default PremiumBannerContainer;