import React, { useMemo } from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useAnimalStore } from '../../store/animalStore';
import { useFeedingStore } from '../../store/feedingStore';
import { useMedicationStore } from '../../store/medicationStore';
import { HomeStackParamList } from '../../navigation';
import { Animal } from '../../mocks/animals';
import UpcomingTasks from './UpcomingTasks';

type UpcomingTasksContainerNavigationProp = NativeStackNavigationProp<HomeStackParamList>;

interface Task {
  type: 'feeding' | 'medication';
  animal: Animal;
  item: any;
  time: string;
}

/**
 * @magic_description Container component for UpcomingTasks that handles data fetching and navigation
 * Processes feeding and medication data to generate today's task list
 */
const UpcomingTasksContainer: React.FC = () => {
  const navigation = useNavigation<UpcomingTasksContainerNavigationProp>();
  
  const { animals } = useAnimalStore();
  const { getTodayFeedings } = useFeedingStore();
  const { getTodayMedications } = useMedicationStore();
  
  // Generate upcoming tasks
  const upcomingTasks = useMemo(() => {
    return animals.flatMap(animal => {
      const feedings = getTodayFeedings(animal.id).map(feeding => ({
        type: 'feeding' as const,
        animal,
        item: feeding,
        time: feeding.time,
      }));
      
      const medications = getTodayMedications(animal.id).map(medication => ({
        type: 'medication' as const,
        animal,
        item: medication,
        time: medication.time,
      }));
      
      return [...feedings, ...medications];
    }).sort((a, b) => {
      const timeA = parseInt(a.time.replace(':', ''));
      const timeB = parseInt(b.time.replace(':', ''));
      return timeA - timeB;
    });
  }, [animals, getTodayFeedings, getTodayMedications]);
  
  // Navigation handler
  const handleTaskPress = (animalId: string) => {
    navigation.navigate('AnimalDetail', { id: animalId });
  };
  
  return (
    <UpcomingTasks
      tasks={upcomingTasks}
      onTaskPress={handleTaskPress}
    />
  );
};

export default UpcomingTasksContainer;