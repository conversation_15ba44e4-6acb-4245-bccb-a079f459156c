
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import AlertBanner from '../AlertBanner';
import VaccineAlertBanner from '../VaccineAlertBanner';

interface Alert {
  id: string;
  type: 'heartRate' | 'temperature' | 'general' | 'location';
  animalName: string;
  animalId: string;
  message: string;
  timestamp: string;
}

interface VaccineAlert {
  id: string;
  animalId: string;
  animalName: string;
  vaccineName: string;
  expiryDate: string;
  daysRemaining: number;
}

interface AlertsSectionProps {
  alerts: Alert[];
  vaccineAlerts: VaccineAlert[];
  onAlertPress: (animalId: string) => void;
}

/**
 * @magic_description Alerts section component for the home screen
 * Displays health alerts and vaccine expiry notifications
 */
const AlertsSection: React.FC<AlertsSectionProps> = ({
  alerts,
  vaccineAlerts,
  onAlertPress
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  if (alerts.length === 0 && vaccineAlerts.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('healthAlerts')}</Text>
        <View style={[styles.alertCount, { backgroundColor: colors.error + '20' }]}>
          <Text style={[styles.alertCountText, { color: colors.error }]}>
            {alerts.length + vaccineAlerts.length}
          </Text>
        </View>
      </View>
      
      {alerts.map(alert => (
        <AlertBanner
          key={alert.id}
          type={alert.type}
          animalName={alert.animalName}
          message={alert.message}
          timestamp={alert.timestamp}
          onPress={() => onAlertPress(alert.animalId)}
        />
      ))}
      
      {vaccineAlerts.map(alert => (
        <VaccineAlertBanner
          key={alert.id}
          animalName={alert.animalName}
          vaccineName={alert.vaccineName}
          expiryDate={alert.expiryDate}
          daysRemaining={alert.daysRemaining}
          onPress={() => onAlertPress(alert.animalId)}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  alertCount: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 24,
    alignItems: 'center',
  },
  alertCountText: {
    fontSize: 12,
    fontWeight: '600',
  },
});

export default AlertsSection;
