
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Heart, MapPin, Coffee, Syringe } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface QuickStatsProps {
  totalAnimals: number;
  activeDevices: number;
  todayFeedings: number;
  todayMedications: number;
  onStatsPress?: (type: 'animals' | 'devices' | 'feedings' | 'medications') => void;
}

/**
 * @magic_description Quick stats overview component for the home screen
 * Shows key metrics like total animals, active devices, and today's tasks
 */
const QuickStats: React.FC<QuickStatsProps> = ({
  totalAnimals,
  activeDevices,
  todayFeedings,
  todayMedications,
  onStatsPress
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const stats = [
    {
      id: 'animals',
      title: t('animals'),
      value: totalAnimals,
      icon: Heart,
      color: colors.primary,
    },
    {
      id: 'devices',
      title: t('activeDevices'),
      value: activeDevices,
      icon: MapPin,
      color: colors.secondary,
    },
    {
      id: 'feedings',
      title: t('todaysFeedings'),
      value: todayFeedings,
      icon: Coffee,
      color: colors.accent1,
    },
    {
      id: 'medications',
      title: t('medications'),
      value: todayMedications,
      icon: Syringe,
      color: colors.accent3,
    },
  ];

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>{t('quickOverview')}</Text>
      
      <View style={styles.statsGrid}>
        {stats.map((stat) => {
          const IconComponent = stat.icon;
          return (
            <TouchableOpacity
              key={stat.id}
              style={styles.statItem}
              onPress={() => onStatsPress?.(stat.id as any)}
            >
              <Card style={styles.statCard} elevation={1}>
                <View style={[styles.iconContainer, { backgroundColor: stat.color + '20' }]}>
                  <IconComponent size={20} color={stat.color} />
                </View>
                <Text style={[styles.statValue, { color: colors.text }]}>
                  {stat.value}
                </Text>
                <Text style={[styles.statLabel, { color: colors.textLight }]}>
                  {stat.title}
                </Text>
              </Card>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    marginBottom: 12,
  },
  statCard: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default QuickStats;
