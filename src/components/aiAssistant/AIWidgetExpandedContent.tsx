import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { Target, Brain, Lightbulb, AlertTriangle, X } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

interface AIInsight {
  id: string;
  type: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
}

interface AIWidgetExpandedContentProps {
  insights: AIInsight[];
  readinessScore?: number;
  isLoading: boolean;
  onClose: () => void;
  onViewAll: () => void;
  onInsightPress: (insight: AIInsight) => void;
}

const AIWidgetExpandedContent: React.FC<AIWidgetExpandedContentProps> = ({
  insights,
  readinessScore,
  isLoading,
  onClose,
  onViewAll,
  onInsightPress
}) => {
  const { theme } = useTheme();
  const { t } = useLanguage();

  const styles = StyleSheet.create({
    container: {
      position: 'absolute',
      bottom: 90,
      right: 20,
      width: 320,
      maxHeight: 400,
      backgroundColor: theme.surface,
      borderRadius: 16,
      padding: 20,
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.3,
      shadowRadius: 16,
      elevation: 12,
      zIndex: 999
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 16
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    icon: {
      marginRight: 8
    },
    title: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.text
    },
    closeButton: {
      padding: 4,
      borderRadius: 4
    },
    readinessContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.background,
      borderRadius: 8,
      padding: 12,
      marginBottom: 16
    },
    readinessIcon: {
      marginRight: 12
    },
    readinessContent: {
      flex: 1
    },
    readinessTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.text,
      marginBottom: 2
    },
    readinessScore: {
      fontSize: 12,
      color: theme.textSecondary
    },
    insightsList: {
      marginBottom: 16
    },
    insightItem: {
      backgroundColor: theme.background,
      borderRadius: 8,
      padding: 12,
      marginBottom: 8,
      borderLeftWidth: 3,
      borderLeftColor: getPriorityColor('medium')
    },
    insightHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 4
    },
    insightType: {
      fontSize: 10,
      color: theme.textSecondary,
      textTransform: 'uppercase',
      fontWeight: '600'
    },
    insightConfidence: {
      fontSize: 10,
      color: theme.textSecondary
    },
    insightTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.text,
      marginBottom: 2
    },
    insightDescription: {
      fontSize: 12,
      color: theme.textSecondary,
      lineHeight: 16
    },
    viewAllButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 10,
      paddingHorizontal: 16,
      backgroundColor: theme.primary,
      borderRadius: 8
    },
    viewAllText: {
      fontSize: 14,
      color: '#FFFFFF',
      fontWeight: '600',
      marginRight: 8
    },
    loadingContainer: {
      alignItems: 'center',
      paddingVertical: 20
    },
    loadingText: {
      fontSize: 14,
      color: theme.textSecondary,
      marginTop: 8
    },
    emptyContainer: {
      alignItems: 'center',
      paddingVertical: 20
    },
    emptyText: {
      fontSize: 14,
      color: theme.textSecondary,
      textAlign: 'center',
      marginTop: 8
    }
  });

  function getPriorityColor(priority: string): string {
    switch (priority) {
      case 'critical': return '#DC2626';
      case 'high': return '#EF4444';
      case 'medium': return '#F59E0B';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'alert':
        return <AlertTriangle size={12} color="#EF4444" />;
      case 'recommendation':
        return <Target size={12} color="#10B981" />;
      default:
        return <Lightbulb size={12} color={theme.primary} />;
    }
  };

  const getReadinessColor = (score?: number) => {
    if (!score) return theme.textSecondary;
    if (score >= 80) return '#10B981';
    if (score >= 60) return '#F59E0B';
    return '#EF4444';
  };

  const renderInsight = (insight: AIInsight) => (
    <TouchableOpacity
      key={insight.id}
      style={[
        styles.insightItem,
        { borderLeftColor: getPriorityColor(insight.priority) }
      ]}
      onPress={() => onInsightPress(insight)}
    >
      <View style={styles.insightHeader}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {getInsightIcon(insight.type)}
          <Text style={[styles.insightType, { marginLeft: 4 }]}>
            {insight.type}
          </Text>
        </View>
        <Text style={styles.insightConfidence}>
          {Math.round(insight.confidence * 100)}%
        </Text>
      </View>
      <Text style={styles.insightTitle} numberOfLines={1}>
        {insight.title}
      </Text>
      <Text style={styles.insightDescription} numberOfLines={2}>
        {insight.description}
      </Text>
    </TouchableOpacity>
  );

  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={theme.primary} />
          <Text style={styles.loadingText}>
            {t('aiAssistant.widget.loading', 'Analyzing data...')}
          </Text>
        </View>
      );
    }

    if (insights.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Brain size={32} color={theme.textSecondary} />
          <Text style={styles.emptyText}>
            {t('aiAssistant.widget.empty', 'No insights available.\nAdd more health data for analysis.')}
          </Text>
        </View>
      );
    }

    return (
      <>
        {readinessScore !== undefined && (
          <View style={styles.readinessContainer}>
            <View style={styles.readinessIcon}>
              <Target size={20} color={getReadinessColor(readinessScore)} />
            </View>
            <View style={styles.readinessContent}>
              <Text style={styles.readinessTitle}>
                Data Readiness: {readinessScore}%
              </Text>
              <Text style={styles.readinessScore}>
                {readinessScore >= 80 ? 'Excellent' : readinessScore >= 60 ? 'Good' : 'Limited'} data coverage
              </Text>
            </View>
          </View>
        )}
        
        <View style={styles.insightsList}>
          {insights.slice(0, 3).map(renderInsight)}
        </View>
        
        <TouchableOpacity style={styles.viewAllButton} onPress={onViewAll}>
          <Text style={styles.viewAllText}>
            {t('aiAssistant.widget.viewAll', 'View All Insights')}
          </Text>
          <Brain size={16} color="#FFFFFF" />
        </TouchableOpacity>
      </>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={styles.icon}>
            <Brain size={20} color={theme.primary} />
          </View>
          <Text style={styles.title}>
            {t('aiAssistant.widget.title', 'AI Assistant')}
          </Text>
        </View>
        
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <X size={20} color={theme.textSecondary} />
        </TouchableOpacity>
      </View>
      
      {renderContent()}
    </View>
  );
};

export default AIWidgetExpandedContent;