import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Bot, AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';

interface AIWidgetButtonProps {
  hasAlerts: boolean;
  alertCount: number;
  onPress: () => void;
  isLoading: boolean;
}

const AIWidgetButton: React.FC<AIWidgetButtonProps> = ({
  hasAlerts,
  alertCount,
  onPress,
  isLoading
}) => {
  const { theme } = useTheme();
  const { t } = useLanguage();

  const styles = StyleSheet.create({
    container: {
      position: 'absolute',
      bottom: 20,
      right: 20,
      zIndex: 1000
    },
    button: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: theme.primary,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8
    },
    alertButton: {
      backgroundColor: '#EF4444'
    },
    loadingButton: {
      backgroundColor: theme.border
    },
    alertBadge: {
      position: 'absolute',
      top: -5,
      right: -5,
      backgroundColor: '#DC2626',
      borderRadius: 10,
      minWidth: 20,
      height: 20,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: '#FFFFFF'
    },
    alertText: {
      fontSize: 10,
      fontWeight: 'bold',
      color: '#FFFFFF'
    },
    pulseAnimation: {
      // Add pulse animation styles if needed
    }
  });

  const getButtonStyle = () => {
    if (isLoading) return [styles.button, styles.loadingButton];
    if (hasAlerts) return [styles.button, styles.alertButton];
    return styles.button;
  };

  const getIconColor = () => {
    if (isLoading) return theme.textSecondary;
    return '#FFFFFF';
  };

  const getIcon = () => {
    if (hasAlerts) {
      return <AlertTriangle size={24} color={getIconColor()} />;
    }
    return <Bot size={24} color={getIconColor()} />;
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={getButtonStyle()}
        onPress={onPress}
        disabled={isLoading}
        activeOpacity={0.8}
      >
        {getIcon()}
        
        {hasAlerts && alertCount > 0 && (
          <View style={styles.alertBadge}>
            <Text style={styles.alertText}>
              {alertCount > 9 ? '9+' : alertCount.toString()}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default AIWidgetButton;