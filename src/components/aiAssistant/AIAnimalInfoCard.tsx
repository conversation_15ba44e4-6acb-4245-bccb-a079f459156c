/**
 * @magic_description AI Assistant Animal Info Card Component
 * Displays animal information and analysis request button
 * Extracted from AIAssistantScreen for better maintainability
 */

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Brain } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Animal } from '../../mocks/animals';
import Card from '../ui/Card';

interface AIAnimalInfoCardProps {
  animal: Animal;
  isLoadingAnalysis: boolean;
  onRequestAnalysis: () => void;
}

const AIAnimalInfoCard: React.FC<AIAnimalInfoCardProps> = ({
  animal,
  isLoadingAnalysis,
  onRequestAnalysis
}) => {
  const { colors } = useTheme();
  
  return (
    <Card style={styles.animalCard}>
      <View style={styles.animalInfo}>
        <Text style={[styles.animalName, { color: colors.text }]}>{animal.name}</Text>
        <Text style={[styles.animalDetails, { color: colors.textLight }]}>
          {animal.breed} • {animal.age} years old
        </Text>
      </View>
      
      <TouchableOpacity
        style={[
          styles.analysisButton,
          { backgroundColor: colors.primary },
          isLoadingAnalysis && styles.analysisButtonDisabled
        ]}
        onPress={onRequestAnalysis}
        disabled={isLoadingAnalysis}
      >
        {isLoadingAnalysis ? (
          <ActivityIndicator color="#FFFFFF" size="small" />
        ) : (
          <Brain size={20} color="#FFFFFF" />
        )}
        <Text style={styles.analysisButtonText}>
          {isLoadingAnalysis ? 'Analyzing...' : 'Get AI Analysis'}
        </Text>
      </TouchableOpacity>
    </Card>
  );
};

const styles = StyleSheet.create({
  animalCard: {
    marginBottom: 16,
  },
  animalInfo: {
    marginBottom: 16,
  },
  animalName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  animalDetails: {
    fontSize: 16,
  },
  analysisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  analysisButtonDisabled: {
    opacity: 0.6,
  },
  analysisButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AIAnimalInfoCard;