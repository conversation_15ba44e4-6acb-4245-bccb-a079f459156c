import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Lightbulb, Clock } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface CoachingTip {
  tip_id: string;
  category: string;
  priority: string;
  tip_text: string;
  generated_at: string;
}

interface AICoachingTipsCardProps {
  tips: CoachingTip[];
  onMarkAsRead: (tipId: string) => void;
}

const AICoachingTipsCard: React.FC<AICoachingTipsCardProps> = ({ tips, onMarkAsRead }) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return '#EF4444';
      case 'medium':
        return '#F59E0B';
      case 'low':
      case 'normal':
        return '#10B981';
      default:
        return colors.textLight;
    }
  };

  if (tips.length === 0) {
    return null;
  }

  return (
    <Card style={styles.section}>
      <View style={styles.sectionHeader}>
        <Lightbulb size={24} color={colors.primary} />
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('coachingTips') || 'Coaching Tips'}
        </Text>
        <View style={[styles.countBadge, { backgroundColor: colors.primary }]}>
          <Text style={styles.countText}>{tips.length}</Text>
        </View>
      </View>
      
      {tips.slice(0, 3).map((tip) => (
        <TouchableOpacity
          key={tip.tip_id}
          style={[styles.tipItem, { borderBottomColor: colors.border }]}
          onPress={() => onMarkAsRead(tip.tip_id)}
        >
          <View style={styles.tipHeader}>
            <View style={[
              styles.priorityDot,
              { backgroundColor: getPriorityColor(tip.priority) }
            ]} />
            <Text style={[styles.tipCategory, { color: colors.textLight }]}>
              {tip.category}
            </Text>
            <Clock size={12} color={colors.textLight} />
            <Text style={[styles.tipDate, { color: colors.textLight }]}>
              {format(new Date(tip.generated_at), 'MMM d')}
            </Text>
          </View>
          
          <Text style={[styles.tipText, { color: colors.text }]}>
            {tip.tip_text}
          </Text>
        </TouchableOpacity>
      ))}
      
      {tips.length > 3 && (
        <Text style={[styles.moreTips, { color: colors.textLight }]}>
          +{tips.length - 3} more tips
        </Text>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  countBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 24,
    alignItems: 'center',
  },
  countText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  tipItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  tipHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 6,
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  tipCategory: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
    flex: 1,
  },
  tipDate: {
    fontSize: 12,
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
  },
  moreTips: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
});

export default AICoachingTipsCard;