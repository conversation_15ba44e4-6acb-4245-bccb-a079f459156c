import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { Brain, RefreshCw } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Animal } from '../../mocks/animals';

interface AIAssistantHeaderProps {
  animal: Animal;
  isLoading: boolean;
  onRefresh: () => void;
}

const AIAssistantHeader: React.FC<AIAssistantHeaderProps> = ({
  animal,
  isLoading,
  onRefresh
}) => {
  const { colors } = useTheme();





  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Brain size={24} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>
            AI Assistant
          </Text>
        </View>
        
        <TouchableOpacity
          style={[styles.refreshButton, { backgroundColor: colors.primary }]}
          onPress={onRefresh}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <RefreshCw size={16} color={colors.white} />
          )}
        </TouchableOpacity>
      </View>
      
      <View style={styles.animalInfo}>
        <Text style={[styles.animalName, { color: colors.text }]}>
          {animal.name}
        </Text>
        <Text style={[styles.animalDetails, { color: colors.textLight }]}>
          {animal.breed} • {animal.age} years old
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    margin: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  refreshButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  animalInfo: {
    alignItems: 'center',
  },
  animalName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  animalDetails: {
    fontSize: 14,
  },
});

export default AIAssistantHeader;