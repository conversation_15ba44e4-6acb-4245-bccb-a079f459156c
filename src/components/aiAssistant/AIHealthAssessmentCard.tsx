import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Heart } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface HealthAssessment {
  severity_level: string;
  assessment_text: string;
  generated_at: string;
}

interface AIHealthAssessmentCardProps {
  healthAssessment: HealthAssessment;
}

const AIHealthAssessmentCard: React.FC<AIHealthAssessmentCardProps> = ({ healthAssessment }) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'high':
      case 'critical':
        return '#EF4444';
      case 'medium':
      case 'moderate':
        return '#F59E0B';
      case 'low':
      case 'normal':
        return '#10B981';
      default:
        return colors.textLight;
    }
  };

  return (
    <Card style={styles.section}>
      <View style={styles.sectionHeader}>
        <Heart size={24} color={getSeverityColor(healthAssessment.severity_level)} />
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('healthAssessment') || 'Health Assessment'}
        </Text>
        <View style={[
          styles.severityBadge,
          { backgroundColor: getSeverityColor(healthAssessment.severity_level) }
        ]}>
          <Text style={styles.severityText}>
            {healthAssessment.severity_level}
          </Text>
        </View>
      </View>
      
      <Text style={[styles.assessmentText, { color: colors.text }]}>
        {healthAssessment.assessment_text}
      </Text>
      
      <Text style={[styles.timestamp, { color: colors.textLight }]}>
        {format(new Date(healthAssessment.generated_at), 'MMM d, yyyy • h:mm a')}
      </Text>
    </Card>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  severityText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  assessmentText: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 12,
  },
  timestamp: {
    fontSize: 12,
  },
});

export default AIHealthAssessmentCard;