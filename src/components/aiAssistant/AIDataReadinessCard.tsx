import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Database, CheckCircle, AlertTriangle, ChevronRight } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface DataReadinessInfo {
  hasVitalsData: boolean;
  hasFeedingData: boolean;
  hasMedicationData: boolean;
  dataQualityScore: number;
  recentDataCounts: {
    vitals: number;
    feeding: number;
    medications: number;
  };
  recommendations: string[];
}

interface AIDataReadinessCardProps {
  dataInfo: DataReadinessInfo;
  onNavigateToData?: () => void;
}

const AIDataReadinessCard: React.FC<AIDataReadinessCardProps> = ({ 
  dataInfo, 
  onNavigateToData 
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const getDataStatusIcon = (hasData: boolean) => {
    return hasData ? (
      <CheckCircle size={16} color={colors.success} />
    ) : (
      <AlertTriangle size={16} color={colors.warning} />
    );
  };

  const getQualityColor = (score: number) => {
    if (score >= 80) return colors.success;
    if (score >= 60) return colors.warning;
    return colors.error;
  };

  return (
    <Card style={styles.section}>
      <View style={styles.sectionHeader}>
        <Database size={24} color={colors.primary} />
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('dataReadiness') || 'Data Readiness'}
        </Text>
        <View style={[
          styles.scoreBadge,
          { backgroundColor: getQualityColor(dataInfo.dataQualityScore) }
        ]}>
          <Text style={styles.scoreText}>{dataInfo.dataQualityScore}%</Text>
        </View>
      </View>
      
      <View style={styles.dataStatusGrid}>
        <View style={styles.dataStatusItem}>
          {getDataStatusIcon(dataInfo.hasVitalsData)}
          <Text style={[styles.dataStatusLabel, { color: colors.text }]}>Vitals</Text>
          <Text style={[styles.dataStatusCount, { color: colors.textLight }]}>
            {dataInfo.recentDataCounts.vitals} recent
          </Text>
        </View>
        
        <View style={styles.dataStatusItem}>
          {getDataStatusIcon(dataInfo.hasFeedingData)}
          <Text style={[styles.dataStatusLabel, { color: colors.text }]}>Feeding</Text>
          <Text style={[styles.dataStatusCount, { color: colors.textLight }]}>
            {dataInfo.recentDataCounts.feeding} recent
          </Text>
        </View>
        
        <View style={styles.dataStatusItem}>
          {getDataStatusIcon(dataInfo.hasMedicationData)}
          <Text style={[styles.dataStatusLabel, { color: colors.text }]}>Medications</Text>
          <Text style={[styles.dataStatusCount, { color: colors.textLight }]}>
            {dataInfo.recentDataCounts.medications} recent
          </Text>
        </View>
      </View>
      
      {dataInfo.recommendations.length > 0 && (
        <View style={styles.recommendationsSection}>
          <Text style={[styles.recommendationsTitle, { color: colors.text }]}>Recommendations:</Text>
          {dataInfo.recommendations.slice(0, 2).map((rec, index) => (
            <Text key={index} style={[styles.recommendationText, { color: colors.textLight }]}>
              • {rec}
            </Text>
          ))}
        </View>
      )}
      
      {onNavigateToData && (
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={onNavigateToData}
        >
          <Text style={[styles.actionButtonText, { color: colors.primary }]}>
            {t('improveDataQuality') || 'Improve Data Quality'}
          </Text>
          <ChevronRight size={16} color={colors.primary} />
        </TouchableOpacity>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  scoreBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  scoreText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  dataStatusGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  dataStatusItem: {
    alignItems: 'center',
    flex: 1,
    gap: 4,
  },
  dataStatusLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  dataStatusCount: {
    fontSize: 10,
  },
  recommendationsSection: {
    marginBottom: 12,
  },
  recommendationsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  recommendationText: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 4,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    gap: 4,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default AIDataReadinessCard;