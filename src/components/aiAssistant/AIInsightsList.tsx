import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList
} from 'react-native';
import { Lightbulb, TrendingUp, AlertTriangle, Target, ChevronRight } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface AIInsight {
  id: string;
  type: 'health' | 'training' | 'nutrition' | 'behavior';
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  generated_at: string;
  actionable: boolean;
}

interface AIInsightsListProps {
  insights: AIInsight[];
  onInsightPress?: (insight: AIInsight) => void;
}

const AIInsightsList: React.FC<AIInsightsListProps> = ({ insights, onInsightPress }) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'health':
        return <AlertTriangle size={20} color={colors.error} />;
      case 'training':
        return <TrendingUp size={20} color={colors.primary} />;
      case 'nutrition':
        return <Target size={20} color={colors.success} />;
      case 'behavior':
        return <Lightbulb size={20} color={colors.warning} />;
      default:
        return <Lightbulb size={20} color={colors.textLight} />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return colors.error;
      case 'medium':
        return colors.warning;
      case 'low':
        return colors.success;
      default:
        return colors.textLight;
    }
  };

  const renderInsight = ({ item }: { item: AIInsight }) => (
    <TouchableOpacity
      style={[styles.insightItem, { borderBottomColor: colors.border }]}
      onPress={() => onInsightPress?.(item)}
      disabled={!onInsightPress}
    >
      <View style={styles.insightHeader}>
        {getInsightIcon(item.type)}
        <View style={styles.insightTitleContainer}>
          <Text style={[styles.insightTitle, { color: colors.text }]}>
            {item.title}
          </Text>
          <Text style={[styles.insightType, { color: colors.textLight }]}>
            {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
          </Text>
        </View>
        <View style={[
          styles.priorityBadge,
          { backgroundColor: getPriorityColor(item.priority) }
        ]}>
          <Text style={styles.priorityText}>{item.priority}</Text>
        </View>
        {onInsightPress && <ChevronRight size={16} color={colors.textLight} />}
      </View>
      
      <Text style={[styles.insightDescription, { color: colors.textLight }]}>
        {item.description}
      </Text>
      
      <View style={styles.insightFooter}>
        <Text style={[styles.insightDate, { color: colors.textLight }]}>
          {format(new Date(item.generated_at), 'MMM d, yyyy')}
        </Text>
        {item.actionable && (
          <View style={[styles.actionableBadge, { backgroundColor: colors.primary + '20' }]}>
            <Text style={[styles.actionableText, { color: colors.primary }]}>Actionable</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  if (insights.length === 0) {
    return null;
  }

  return (
    <Card style={styles.section}>
      <View style={styles.sectionHeader}>
        <Lightbulb size={24} color={colors.primary} />
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('aiInsights') || 'AI Insights'}
        </Text>
        <View style={[styles.countBadge, { backgroundColor: colors.primary }]}>
          <Text style={styles.countText}>{insights.length}</Text>
        </View>
      </View>
      
      <FlatList
        data={insights.slice(0, 5)} // Show max 5 insights
        renderItem={renderInsight}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
      />
      
      {insights.length > 5 && (
        <Text style={[styles.moreInsights, { color: colors.textLight }]}>
          +{insights.length - 5} more insights
        </Text>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  countBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 24,
    alignItems: 'center',
  },
  countText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  insightItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  insightTitleContainer: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  insightType: {
    fontSize: 12,
    textTransform: 'capitalize',
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  priorityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  insightDescription: {
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 8,
  },
  insightFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  insightDate: {
    fontSize: 11,
  },
  actionableBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  actionableText: {
    fontSize: 10,
    fontWeight: '500',
  },
  moreInsights: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
});

export default AIInsightsList;