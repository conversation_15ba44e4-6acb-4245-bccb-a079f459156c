import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Target } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface ReadinessScore {
  score_value: number;
  score_category: string;
  generated_at: string;
}

interface AIReadinessScoreCardProps {
  readinessScore: ReadinessScore;
}

const AIReadinessScoreCard: React.FC<AIReadinessScoreCardProps> = ({ readinessScore }) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  return (
    <Card style={styles.section}>
      <View style={styles.sectionHeader}>
        <Target size={24} color={colors.primary} />
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {t('readinessScore') || 'Readiness Score'}
        </Text>
      </View>
      
      <View style={styles.readinessContainer}>
        <View style={styles.scoreCircle}>
          <Text style={[styles.scoreValue, { color: colors.primary }]}>
            {Math.round(readinessScore.score_value * 100)}
          </Text>
          <Text style={[styles.scoreLabel, { color: colors.textLight }]}>%</Text>
        </View>
        
        <View style={styles.scoreDetails}>
          <Text style={[styles.scoreCategory, { color: colors.text }]}>
            {readinessScore.score_category}
          </Text>
          <Text style={[styles.scoreDate, { color: colors.textLight }]}>
            {format(new Date(readinessScore.generated_at), 'MMM d, yyyy')}
          </Text>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  readinessContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  scoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#3B82F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scoreValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 12,
    marginTop: -4,
  },
  scoreDetails: {
    flex: 1,
  },
  scoreCategory: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  scoreDate: {
    fontSize: 14,
  },
});

export default AIReadinessScoreCard;