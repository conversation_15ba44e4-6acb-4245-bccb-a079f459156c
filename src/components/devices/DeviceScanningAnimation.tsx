import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions
} from 'react-native';
import { Bluetooth, Wifi, Search, Zap } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface DeviceScanningAnimationProps {
  isScanning: boolean;
  scanProgress?: number;
  devicesFound?: number;
  scanDuration?: number;
}

const DeviceScanningAnimation: React.FC<DeviceScanningAnimationProps> = ({
  isScanning,
  scanProgress = 0,
  devicesFound = 0,
  scanDuration = 3000
}) => {
  const { colors } = useTheme();
  const screenWidth = Dimensions.get('window').width;
  
  // Animation values
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const waveAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const bounceAnim = useRef(new Animated.Value(0)).current;
  
  // Icon animations
  const iconScale1 = useRef(new Animated.Value(0.8)).current;
  const iconScale2 = useRef(new Animated.Value(0.8)).current;
  const iconScale3 = useRef(new Animated.Value(0.8)).current;
  const iconOpacity1 = useRef(new Animated.Value(0.3)).current;
  const iconOpacity2 = useRef(new Animated.Value(0.3)).current;
  const iconOpacity3 = useRef(new Animated.Value(0.3)).current;
  
  useEffect(() => {
    if (isScanning) {
      // Start all animations
      startScanningAnimations();
    } else {
      // Stop all animations
      stopScanningAnimations();
    }
  }, [isScanning]);
  
  useEffect(() => {
    // Update progress animation
    Animated.timing(progressAnim, {
      toValue: scanProgress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [scanProgress]);
  
  useEffect(() => {
    // Bounce animation when devices are found
    if (devicesFound > 0) {
      Animated.sequence([
        Animated.timing(bounceAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(bounceAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [devicesFound]);
  
  const startScanningAnimations = () => {
    // Fade in
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
    
    // Pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    // Rotation animation
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      })
    ).start();
    
    // Wave animation
    Animated.loop(
      Animated.timing(waveAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      })
    ).start();
    
    // Staggered icon animations
    const iconAnimations = [
      { scale: iconScale1, opacity: iconOpacity1, delay: 0 },
      { scale: iconScale2, opacity: iconOpacity2, delay: 500 },
      { scale: iconScale3, opacity: iconOpacity3, delay: 1000 },
    ];
    
    iconAnimations.forEach(({ scale, opacity, delay }) => {
      setTimeout(() => {
        Animated.loop(
          Animated.sequence([
            Animated.parallel([
              Animated.timing(scale, {
                toValue: 1.1,
                duration: 600,
                useNativeDriver: true,
              }),
              Animated.timing(opacity, {
                toValue: 1,
                duration: 600,
                useNativeDriver: true,
              }),
            ]),
            Animated.parallel([
              Animated.timing(scale, {
                toValue: 0.8,
                duration: 600,
                useNativeDriver: true,
              }),
              Animated.timing(opacity, {
                toValue: 0.3,
                duration: 600,
                useNativeDriver: true,
              }),
            ]),
          ])
        ).start();
      }, delay);
    });
  };
  
  const stopScanningAnimations = () => {
    // Fade out
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
    
    // Reset all animations
    pulseAnim.setValue(1);
    rotateAnim.setValue(0);
    waveAnim.setValue(0);
    progressAnim.setValue(0);
    bounceAnim.setValue(0);
    
    // Reset icon animations
    iconScale1.setValue(0.8);
    iconScale2.setValue(0.8);
    iconScale3.setValue(0.8);
    iconOpacity1.setValue(0.3);
    iconOpacity2.setValue(0.3);
    iconOpacity3.setValue(0.3);
  };
  
  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  
  const waveScale = waveAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 2.5],
  });
  
  const waveOpacity = waveAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.8, 0.3, 0],
  });
  
  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, screenWidth - 64],
  });
  
  const bounceScale = bounceAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.1],
  });
  
  if (!isScanning) {
    return null;
  }
  
  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      {/* Central scanning animation */}
      <View style={styles.scanningCenter}>
        {/* Pulse waves */}
        <Animated.View
          style={[
            styles.pulseWave,
            {
              transform: [{ scale: waveScale }],
              opacity: waveOpacity,
              borderColor: colors.primary,
            },
          ]}
        />
        <Animated.View
          style={[
            styles.pulseWave,
            styles.pulseWaveDelay,
            {
              transform: [{ scale: waveScale }],
              opacity: waveOpacity,
              borderColor: colors.primary,
            },
          ]}
        />
        
        {/* Central search icon */}
        <Animated.View
          style={[
            styles.centralIcon,
            {
              transform: [{ scale: pulseAnim }, { rotate: spin }],
              backgroundColor: colors.primary,
            },
          ]}
        >
          <Search size={24} color={colors.card} />
        </Animated.View>
      </View>
      
      {/* Floating device icons */}
      <View style={styles.floatingIcons}>
        <Animated.View
          style={[
            styles.floatingIcon,
            styles.icon1,
            {
              transform: [{ scale: iconScale1 }],
              opacity: iconOpacity1,
            },
          ]}
        >
          <Bluetooth size={20} color={colors.primary} />
        </Animated.View>
        
        <Animated.View
          style={[
            styles.floatingIcon,
            styles.icon2,
            {
              transform: [{ scale: iconScale2 }],
              opacity: iconOpacity2,
            },
          ]}
        >
          <Wifi size={20} color={colors.primary} />
        </Animated.View>
        
        <Animated.View
          style={[
            styles.floatingIcon,
            styles.icon3,
            {
              transform: [{ scale: iconScale3 }],
              opacity: iconOpacity3,
            },
          ]}
        >
          <Zap size={20} color={colors.primary} />
        </Animated.View>
      </View>
      
      {/* Progress bar */}
      <View style={styles.progressContainer}>
        <View style={[styles.progressTrack, { backgroundColor: colors.border }]}>
          <Animated.View
            style={[
              styles.progressBar,
              {
                width: progressWidth,
                backgroundColor: colors.primary,
              },
            ]}
          />
        </View>
      </View>
      
      {/* Scanning text */}
      <Animated.View
        style={[
          styles.textContainer,
          {
            transform: [{ scale: bounceScale }],
          },
        ]}
      >
        <Text style={[styles.scanningText, { color: colors.text }]}>
          Scanning for devices...
        </Text>
        {devicesFound > 0 && (
          <Text style={[styles.devicesFoundText, { color: colors.primary }]}>
            {devicesFound} device{devicesFound !== 1 ? 's' : ''} found
          </Text>
        )}
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  scanningCenter: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  pulseWave: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 2,
  },
  pulseWaveDelay: {
    animationDelay: '0.5s',
  },
  centralIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  floatingIcons: {
    position: 'absolute',
    width: 200,
    height: 200,
    top: 0,
  },
  floatingIcon: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2.62,
  },
  icon1: {
    top: 20,
    left: 80,
  },
  icon2: {
    top: 80,
    right: 20,
  },
  icon3: {
    bottom: 20,
    left: 20,
  },
  progressContainer: {
    width: '100%',
    marginBottom: 16,
  },
  progressTrack: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  textContainer: {
    alignItems: 'center',
  },
  scanningText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  devicesFoundText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default DeviceScanningAnimation;