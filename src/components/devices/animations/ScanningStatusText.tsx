/**
 * @magic_description Scanning Status Text Component
 * Handles the animated status text and devices found counter
 * Extracted from DeviceScanningAnimation for better maintainability
 */

import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';

interface ScanningStatusTextProps {
  devicesFound: number;
}

const ScanningStatusText: React.FC<ScanningStatusTextProps> = ({ devicesFound }) => {
  const { colors } = useTheme();
  const bounceAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    // Bounce animation when devices are found
    if (devicesFound > 0) {
      Animated.sequence([
        Animated.timing(bounceAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(bounceAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [devicesFound]);
  
  const bounceScale = bounceAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 1.1],
  });
  
  return (
    <Animated.View
      style={[
        styles.textContainer,
        {
          transform: [{ scale: bounceScale }],
        },
      ]}
    >
      <Text style={[styles.scanningText, { color: colors.text }]}>
        Scanning for devices...
      </Text>
      {devicesFound > 0 && (
        <Text style={[styles.devicesFoundText, { color: colors.primary }]}>
          {devicesFound} device{devicesFound !== 1 ? 's' : ''} found
        </Text>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  textContainer: {
    alignItems: 'center',
  },
  scanningText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  devicesFoundText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default ScanningStatusText;