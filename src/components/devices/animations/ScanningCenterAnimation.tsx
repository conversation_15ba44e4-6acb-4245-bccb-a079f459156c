/**
 * @magic_description Scanning Center Animation Component
 * Handles the central pulse waves and rotating search icon
 * Extracted from DeviceScanningAnimation for better maintainability
 */

import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { Search } from 'lucide-react-native';
import { useTheme } from '../../../contexts/ThemeContext';

interface ScanningCenterAnimationProps {
  isScanning: boolean;
}

const ScanningCenterAnimation: React.FC<ScanningCenterAnimationProps> = ({ isScanning }) => {
  const { colors } = useTheme();
  
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const waveAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    if (isScanning) {
      startAnimations();
    } else {
      stopAnimations();
    }
  }, [isScanning]);
  
  const startAnimations = () => {
    // Pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();
    
    // Rotation animation
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      })
    ).start();
    
    // Wave animation
    Animated.loop(
      Animated.timing(waveAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      })
    ).start();
  };
  
  const stopAnimations = () => {
    pulseAnim.setValue(1);
    rotateAnim.setValue(0);
    waveAnim.setValue(0);
  };
  
  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  
  const waveScale = waveAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 2.5],
  });
  
  const waveOpacity = waveAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.8, 0.3, 0],
  });
  
  return (
    <View style={styles.scanningCenter}>
      {/* Pulse waves */}
      <Animated.View
        style={[
          styles.pulseWave,
          {
            transform: [{ scale: waveScale }],
            opacity: waveOpacity,
            borderColor: colors.primary,
          },
        ]}
      />
      <Animated.View
        style={[
          styles.pulseWave,
          styles.pulseWaveDelay,
          {
            transform: [{ scale: waveScale }],
            opacity: waveOpacity,
            borderColor: colors.primary,
          },
        ]}
      />
      
      {/* Central search icon */}
      <Animated.View
        style={[
          styles.centralIcon,
          {
            transform: [{ scale: pulseAnim }, { rotate: spin }],
            backgroundColor: colors.primary,
          },
        ]}
      >
        <Search size={24} color={colors.card} />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  scanningCenter: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 40,
  },
  pulseWave: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 2,
  },
  pulseWaveDelay: {
    width: 160,
    height: 160,
    borderRadius: 80,
  },
  centralIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

export default ScanningCenterAnimation;