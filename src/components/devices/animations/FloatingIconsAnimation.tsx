/**
 * @magic_description Floating Icons Animation Component
 * Handles the animated floating device icons (Bluetooth, Wifi, Zap)
 * Extracted from DeviceScanningAnimation for better maintainability
 */

import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { Bluetooth, Wifi, Zap } from 'lucide-react-native';
import { useTheme } from '../../../contexts/ThemeContext';

interface FloatingIconsAnimationProps {
  isScanning: boolean;
}

const FloatingIconsAnimation: React.FC<FloatingIconsAnimationProps> = ({ isScanning }) => {
  const { colors } = useTheme();
  
  // Icon animations
  const iconScale1 = useRef(new Animated.Value(0.8)).current;
  const iconScale2 = useRef(new Animated.Value(0.8)).current;
  const iconScale3 = useRef(new Animated.Value(0.8)).current;
  const iconOpacity1 = useRef(new Animated.Value(0.3)).current;
  const iconOpacity2 = useRef(new Animated.Value(0.3)).current;
  const iconOpacity3 = useRef(new Animated.Value(0.3)).current;
  
  useEffect(() => {
    if (isScanning) {
      startIconAnimations();
    } else {
      stopIconAnimations();
    }
  }, [isScanning]);
  
  const startIconAnimations = () => {
    // Staggered icon animations
    const iconAnimations = [
      { scale: iconScale1, opacity: iconOpacity1, delay: 0 },
      { scale: iconScale2, opacity: iconOpacity2, delay: 500 },
      { scale: iconScale3, opacity: iconOpacity3, delay: 1000 },
    ];
    
    iconAnimations.forEach(({ scale, opacity, delay }) => {
      setTimeout(() => {
        Animated.loop(
          Animated.sequence([
            Animated.parallel([
              Animated.timing(scale, {
                toValue: 1.1,
                duration: 600,
                useNativeDriver: true,
              }),
              Animated.timing(opacity, {
                toValue: 1,
                duration: 600,
                useNativeDriver: true,
              }),
            ]),
            Animated.parallel([
              Animated.timing(scale, {
                toValue: 0.8,
                duration: 600,
                useNativeDriver: true,
              }),
              Animated.timing(opacity, {
                toValue: 0.3,
                duration: 600,
                useNativeDriver: true,
              }),
            ]),
          ])
        ).start();
      }, delay);
    });
  };
  
  const stopIconAnimations = () => {
    // Reset icon animations
    iconScale1.setValue(0.8);
    iconScale2.setValue(0.8);
    iconScale3.setValue(0.8);
    iconOpacity1.setValue(0.3);
    iconOpacity2.setValue(0.3);
    iconOpacity3.setValue(0.3);
  };
  
  return (
    <View style={styles.floatingIcons}>
      <Animated.View
        style={[
          styles.floatingIcon,
          styles.icon1,
          {
            transform: [{ scale: iconScale1 }],
            opacity: iconOpacity1,
          },
        ]}
      >
        <Bluetooth size={20} color={colors.primary} />
      </Animated.View>
      
      <Animated.View
        style={[
          styles.floatingIcon,
          styles.icon2,
          {
            transform: [{ scale: iconScale2 }],
            opacity: iconOpacity2,
          },
        ]}
      >
        <Wifi size={20} color={colors.primary} />
      </Animated.View>
      
      <Animated.View
        style={[
          styles.floatingIcon,
          styles.icon3,
          {
            transform: [{ scale: iconScale3 }],
            opacity: iconOpacity3,
          },
        ]}
      >
        <Zap size={20} color={colors.primary} />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  floatingIcons: {
    position: 'absolute',
    width: '100%',
    height: 200,
    top: 0,
  },
  floatingIcon: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  icon1: {
    top: 20,
    left: 40,
  },
  icon2: {
    top: 60,
    right: 30,
  },
  icon3: {
    top: 120,
    left: 60,
  },
});

export default FloatingIconsAnimation;