/**
 * @magic_description Scanning Progress Bar Component
 * Handles the animated progress bar during device scanning
 * Extracted from DeviceScanningAnimation for better maintainability
 */

import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { useTheme } from '../../../contexts/ThemeContext';

interface ScanningProgressBarProps {
  scanProgress: number;
}

const ScanningProgressBar: React.FC<ScanningProgressBarProps> = ({ scanProgress }) => {
  const { colors } = useTheme();
  const screenWidth = Dimensions.get('window').width;
  const progressAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    // Update progress animation
    Animated.timing(progressAnim, {
      toValue: scanProgress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [scanProgress]);
  
  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, screenWidth - 64],
  });
  
  return (
    <View style={styles.progressContainer}>
      <View style={[styles.progressTrack, { backgroundColor: colors.border }]}>
        <Animated.View
          style={[
            styles.progressBar,
            {
              width: progressWidth,
              backgroundColor: colors.primary,
            },
          ]}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  progressContainer: {
    paddingHorizontal: 32,
    marginBottom: 20,
  },
  progressTrack: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
});

export default ScanningProgressBar;