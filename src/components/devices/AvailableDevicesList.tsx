import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Search, Plus, Bluetooth, Battery } from 'lucide-react-native';
import { Device } from '../../mocks/devices';
import { COLORS } from '../../constants/colors';
import { useLanguage } from '../../contexts/LanguageContext';

// Component for displaying scanned/available devices that can be added
interface AvailableDeviceCardProps {
  device: Device;
  onAdd?: () => Promise<any>;
  onPair?: () => void;
  colors: {
    text: string;
    textLight: string;
  };
}

const AvailableDeviceCard: React.FC<AvailableDeviceCardProps> = ({ 
  device, 
  onAdd, 
  onPair,
  colors 
}) => {
  const { t } = useLanguage();
  const [isAdding, setIsAdding] = useState(false);
  
  const handleAddDevice = async () => {
    if (!onAdd) return;
    
    try {
      setIsAdding(true);
      const result = await onAdd();
      if (result && onPair) {
        // Optionally pair after adding
        onPair();
      }
    } catch (error) {
      console.error('Error adding device:', error);
    } finally {
      setIsAdding(false);
    }
  };
  
  const getBatteryColor = () => {
    const level = device.battery_level || device.batteryLevel || 0;
    if (level <= 20) return COLORS.error;
    if (level <= 40) return COLORS.warning;
    return COLORS.success;
  };
  
  return (
    <View style={availableDeviceStyles.container}>
      <View style={availableDeviceStyles.header}>
        <View style={availableDeviceStyles.titleContainer}>
          <Bluetooth size={18} color={COLORS.textLight} />
          <Text style={[availableDeviceStyles.name, { color: colors.text }]}>{device.name}</Text>
        </View>
        <View style={availableDeviceStyles.batteryContainer}>
          <Battery size={16} color={getBatteryColor()} />
          <Text style={[availableDeviceStyles.batteryText, { color: getBatteryColor() }]}>
            {device.battery_level || device.batteryLevel || 0}%
          </Text>
        </View>
      </View>
      
      <View style={availableDeviceStyles.infoContainer}>
        <Text style={[availableDeviceStyles.type, { color: colors.textLight }]}>{device.type}</Text>
        <Text style={[availableDeviceStyles.statusText, { color: colors.textLight }]}>{t('availableToAdd')}</Text>
      </View>
      
      <View style={availableDeviceStyles.actionContainer}>
        <TouchableOpacity 
          style={[availableDeviceStyles.addButton, { backgroundColor: COLORS.primary }]}
          onPress={handleAddDevice}
          disabled={isAdding}
        >
          {isAdding ? (
            <ActivityIndicator size="small" color="#FFF" />
          ) : (
            <>
              <Plus size={16} color="#FFF" />
              <Text style={availableDeviceStyles.addButtonText}>{t('addDevice')}</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

interface AvailableDevicesListProps {
  availableDevices: Device[];
  isScanning: boolean;
  onPairDevice: (deviceId: string) => void;
  onAddDevice?: (deviceData: any) => Promise<any>;
  colors: {
    text: string;
    textLight: string;
  };
}

const AvailableDevicesList: React.FC<AvailableDevicesListProps> = ({
  availableDevices,
  isScanning,
  onPairDevice,
  onAddDevice,
  colors
}) => {
  const { t } = useLanguage();
  if (isScanning) {
    return null; // Scanning indicator is handled by DeviceDiscoveryControls
  }

  if (availableDevices.length > 0) {
    return (
      <FlatList
        data={availableDevices}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <AvailableDeviceCard 
            device={item} 
            onAdd={onAddDevice ? () => onAddDevice(item) : undefined}
            onPair={() => onPairDevice(item.id)}
            colors={colors}
          />
        )}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    );
  }

  return (
    <View style={styles.emptyContainer}>
      <Search size={48} color={colors.textLight} />
      <Text style={[styles.emptyText, { color: colors.text }]}>{t('noDevicesFound')}</Text>
      <Text style={[styles.emptySubtext, { color: colors.textLight }]}>
        {t('scanToFindDevicesPrompt')}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  listContent: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

// Styles for AvailableDeviceCard component
const availableDeviceStyles = StyleSheet.create({
  container: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  batteryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  batteryText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  infoContainer: {
    marginBottom: 16,
  },
  type: {
    fontSize: 14,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
});

export default AvailableDevicesList;