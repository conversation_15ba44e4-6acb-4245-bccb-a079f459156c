import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Bluetooth } from 'lucide-react-native';
import { useLanguage } from '../../contexts/LanguageContext';

interface BluetoothUnavailableViewProps {
  colors: {
    error: string;
    text: string;
    textLight: string;
  };
}

const BluetoothUnavailableView: React.FC<BluetoothUnavailableViewProps> = ({ colors }) => {
  const { t } = useLanguage();
  
  return (
    <View style={styles.emptyContainer}>
      <Bluetooth size={48} color={colors.error} />
      <Text style={[styles.emptyText, { color: colors.text }]}>{t('bluetoothUnavailable')}</Text>
      <Text style={[styles.emptySubtext, { color: colors.textLight }]}>
        {t('enableBluetoothPrompt')}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default BluetoothUnavailableView;