import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, ActivityIndicator, Animated } from 'react-native';
import { RefreshCw, <PERSON>, Bluetooth, Zap } from 'lucide-react-native';
import DeviceScanningAnimation from './DeviceScanningAnimation';
import { useLanguage } from '../../contexts/LanguageContext';

interface DeviceDiscoveryControlsProps {
  isScanning: boolean;
  onStartScan: () => void;
  onStopScan: () => void;
  onScanBarcode: () => void;
  showBarcodeInput: boolean;
  barcodeInput: string;
  onBarcodeInputChange: (text: string) => void;
  onAddDeviceByBarcode: () => void;
  scanProgress?: number;
  devicesFound?: number;
  colors: {
    primary: string;
    secondary: string;
    card: string;
    text: string;
    textLight: string;
    border: string;
  };
}

const DeviceDiscoveryControls: React.FC<DeviceDiscoveryControlsProps> = ({
  isScanning,
  onStartScan,
  onStopScan,
  onScanBarcode,
  showBarcodeInput,
  barcodeInput,
  onBarcodeInputChange,
  onAddDeviceByBarcode,
  scanProgress = 0,
  devicesFound = 0,
  colors
}) => {
  const { t } = useLanguage();
  // Animation values
  const buttonScale = useRef(new Animated.Value(1)).current;
  const iconRotation = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  
  useEffect(() => {
    if (isScanning) {
      // Start button animations
      Animated.loop(
        Animated.sequence([
          Animated.timing(iconRotation, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(iconRotation, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }),
        ])
      ).start();
      
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      // Reset animations
      iconRotation.setValue(0);
      pulseAnim.setValue(1);
    }
  }, [isScanning]);
  
  const handleScanPress = () => {
    // Button press animation
    Animated.sequence([
      Animated.timing(buttonScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    
    if (isScanning) {
      onStopScan();
    } else {
      onStartScan();
    }
  };
  
  const spin = iconRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  return (
    <View style={styles.scanContainer}>
      <View style={styles.scanButtonsRow}>
        <Animated.View
          style={[
            { flex: 1, marginRight: 8 },
            {
              transform: [{ scale: buttonScale }],
            },
          ]}
        >
          <TouchableOpacity 
            style={[
              styles.scanButton, 
              { 
                backgroundColor: isScanning ? colors.secondary : colors.primary,
                borderWidth: isScanning ? 2 : 0,
                borderColor: colors.primary,
              }
            ]}
            onPress={handleScanPress}
            activeOpacity={0.8}
          >
            {isScanning ? (
              <Animated.View
                style={[
                  styles.scanningButtonContent,
                  {
                    transform: [{ scale: pulseAnim }],
                  },
                ]}
              >
                <Animated.View
                  style={{
                    transform: [{ rotate: spin }],
                    marginRight: 8,
                  }}
                >
                  <Bluetooth size={16} color={colors.primary} />
                </Animated.View>
                <Text style={[styles.scanButtonText, { color: colors.primary }]}>{t('scanning')}</Text>
                <View style={styles.scanningIndicator}>
                  <ActivityIndicator size="small" color={colors.primary} />
                </View>
              </Animated.View>
            ) : (
              <>
                <RefreshCw size={16} color={colors.card} style={{ marginRight: 8 }} />
                <Text style={[styles.scanButtonText, { color: colors.card }]}>{t('scanForDevices')}</Text>
              </>
            )}
          </TouchableOpacity>
        </Animated.View>
        
        <TouchableOpacity 
          style={[styles.scanButton, { backgroundColor: colors.secondary, width: 50 }]}
          onPress={onScanBarcode}
          activeOpacity={0.8}
        >
          <Camera size={20} color={colors.card} />
        </TouchableOpacity>
      </View>
      
      {showBarcodeInput && (
        <View style={styles.barcodeInputContainer}>
          <TextInput
            style={[styles.barcodeInput, { backgroundColor: colors.card, color: colors.text, borderColor: colors.border }]}
            value={barcodeInput}
            onChangeText={onBarcodeInputChange}
            placeholder={t('deviceIdPlaceholder')}
            placeholderTextColor={colors.textLight}
            autoFocus
          />
          <TouchableOpacity 
            style={[styles.barcodeButton, { backgroundColor: colors.primary }]}
            onPress={onAddDeviceByBarcode}
            activeOpacity={0.8}
          >
            <Text style={[styles.barcodeButtonText, { color: colors.card }]}>{t('add')}</Text>
          </TouchableOpacity>
        </View>
      )}
      
      {/* Enhanced scanning animation */}
      {isScanning && (
        <DeviceScanningAnimation
          isScanning={isScanning}
          scanProgress={scanProgress}
          devicesFound={devicesFound}
          scanDuration={3000}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  scanContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  scanButtonsRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  scanButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  scanButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  scanningButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanningIndicator: {
    marginLeft: 8,
  },
  barcodeInputContainer: {
    flexDirection: 'row',
    marginTop: 8,
  },
  barcodeInput: {
    flex: 1,
    height: 40,
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    marginRight: 8,
  },
  barcodeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  barcodeButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default DeviceDiscoveryControls;