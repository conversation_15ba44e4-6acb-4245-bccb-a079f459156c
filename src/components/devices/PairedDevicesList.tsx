import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Bluetooth } from 'lucide-react-native';
import DeviceCard from '../DeviceCard';
import { Device } from '../../mocks/devices';
import { useLanguage } from '../../contexts/LanguageContext';

interface PairedDevicesListProps {
  pairedDevices: Device[];
  onUnpairDevice: (deviceId: string) => void;
  onFindDevicesPress: () => void;
  colors: {
    text: string;
    textLight: string;
    primary: string;
    card: string;
  };
}

const PairedDevicesList: React.FC<PairedDevicesListProps> = ({
  pairedDevices,
  onUnpairDevice,
  onFindDevicesPress,
  colors
}) => {
  const { t } = useLanguage();
  if (pairedDevices.length > 0) {
    return (
      <View style={styles.devicesContainer}>
        <FlatList
          data={pairedDevices}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <DeviceCard 
              device={item} 
              onUnpair={() => onUnpairDevice(item.id)}
            />
          )}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      </View>
    );
  }

  return (
    <View style={styles.devicesContainer}>
      <View style={styles.emptyContainer}>
        <Bluetooth size={48} color={colors.textLight} />
        <Text style={[styles.emptyText, { color: colors.text }]}>{t('noPairedDevices')}</Text>
        <Text style={[styles.emptySubtext, { color: colors.textLight }]}>
          {t('pairDevicePrompt')}
        </Text>
        <TouchableOpacity 
          style={[styles.scanButton, { backgroundColor: colors.primary }]}
          onPress={onFindDevicesPress}
        >
          <Text style={[styles.scanButtonText, { color: colors.card }]}>{t('findDevices')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  devicesContainer: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  scanButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  scanButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PairedDevicesList;