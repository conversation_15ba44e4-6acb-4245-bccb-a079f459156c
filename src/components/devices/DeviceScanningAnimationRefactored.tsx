/**
 * @magic_description Refactored Device Scanning Animation
 * Significantly reduced in size by extracting animation components
 * Now focuses on coordination between animation components
 */

import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import ScanningCenterAnimation from './animations/ScanningCenterAnimation';
import FloatingIconsAnimation from './animations/FloatingIconsAnimation';
import ScanningProgressBar from './animations/ScanningProgressBar';
import ScanningStatusText from './animations/ScanningStatusText';

interface DeviceScanningAnimationProps {
  isScanning: boolean;
  scanProgress?: number;
  devicesFound?: number;
  scanDuration?: number;
}

const DeviceScanningAnimation: React.FC<DeviceScanningAnimationProps> = ({
  isScanning,
  scanProgress = 0,
  devicesFound = 0,
  scanDuration = 3000
}) => {
  const { colors } = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    if (isScanning) {
      // Fade in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      // Fade out
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isScanning]);
  
  if (!isScanning) {
    return null;
  }
  
  return (
    <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
      {/* Central scanning animation */}
      <ScanningCenterAnimation isScanning={isScanning} />
      
      {/* Floating device icons */}
      <FloatingIconsAnimation isScanning={isScanning} />
      
      {/* Progress bar */}
      <ScanningProgressBar scanProgress={scanProgress} />
      
      {/* Scanning text */}
      <ScanningStatusText devicesFound={devicesFound} />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
    minHeight: 300,
  },
});

export default DeviceScanningAnimation;