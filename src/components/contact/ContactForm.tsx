import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Platform
} from 'react-native';
import { Send } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { toast } from 'sonner-native';
import ValidatedInput from '../ui/ValidatedInput';
import { useFormValidation } from '../../hooks/useFormValidation';
import { 
  validateRequired, 
  validateEmail, 
  validateLength 
} from '../../utils/validation';

export interface ContactFormProps {
  initialName?: string;
  initialEmail?: string;
  onSubmit: (name: string, email: string, message: string) => Promise<boolean>;
  isLoading: boolean;
}

/**
 * @magic_description Contact form component for sending email messages to support
 * Handles form validation and submission with loading states
 */
const ContactForm: React.FC<ContactFormProps> = ({
  initialName = '',
  initialEmail = '',
  onSubmit,
  isLoading
}) => {
  const { colors, isDarkMode } = useTheme();
  const validation = useFormValidation();
  
  const [name, setName] = useState(initialName);
  const [email, setEmail] = useState(initialEmail);
  const [message, setMessage] = useState('');
  
  // Update form fields when initial values change
  useEffect(() => {
    setName(initialName);
    setEmail(initialEmail);
    validation.clearAllErrors();
  }, [initialName, initialEmail, validation]);
  
  const handleSubmit = async () => {
    // Comprehensive validation
    const validations = {
      name: validateRequired(name, 'Name'),
      email: validateEmail(email),
      message: validateLength(message, 'Message', 10, 1000)
    };

    const isFormValid = validation.validateAllFields(validations);
    
    if (!isFormValid) {
      const firstError = Object.values(validation.errors)[0];
      toast.error(firstError || 'Please fix the errors before sending');
      return;
    }
    
    const success = await onSubmit(name.trim(), email.trim(), message.trim());
    
    // Clear message field and validation errors on successful submission
    if (success) {
      setMessage('');
      validation.clearAllErrors();
    }
  };
  
  return (
    <View style={styles.formSection}>
      <ValidatedInput
        label="Name"
        value={name}
        onChangeText={setName}
        placeholder="Your name"
        required
        maxLength={100}
        error={validation.errors.name}
        editable={!isLoading}
      />
      
      <ValidatedInput
        label="Email"
        value={email}
        onChangeText={setEmail}
        placeholder="Your email address"
        keyboardType="email-address"
        autoCapitalize="none"
        required
        maxLength={100}
        error={validation.errors.email}
        editable={!isLoading}
      />
      
      <ValidatedInput
        label="Message"
        value={message}
        onChangeText={setMessage}
        placeholder="How can we help you? (minimum 10 characters)"
        multiline
        numberOfLines={6}
        required
        maxLength={1000}
        error={validation.errors.message}
        editable={!isLoading}
      />
      
      <TouchableOpacity 
        style={[
          styles.sendButton, 
          { backgroundColor: (isLoading || !validation.isValid) ? colors.textLight : colors.primary }
        ]}
        onPress={handleSubmit}
        disabled={isLoading || !validation.isValid}
        activeOpacity={0.8}
      >
        {isLoading ? (
          <ActivityIndicator color={colors.card} size="small" />
        ) : (
          <>
            <Send size={20} color={colors.card} />
            <Text style={[styles.sendButtonText, { color: colors.card }]}>Send Email to Support</Text>
          </>
        )}
      </TouchableOpacity>
      
      <Text style={[styles.emailNote, { color: colors.textLight }]}>
        Your message will be <NAME_EMAIL>
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  formSection: {
    marginBottom: 32,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  messageInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 120,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  sendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    marginBottom: 12,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  sendButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  emailNote: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default ContactForm;