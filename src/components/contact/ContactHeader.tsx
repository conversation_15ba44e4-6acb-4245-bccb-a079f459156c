import React from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import { Mail, CheckCircle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface ContactHeaderProps {
  emailSent: boolean;
}

/**
 * @magic_description Contact header component with title, subtitle and success banner
 * Displays contact introduction and email sent confirmation
 */
const ContactHeader: React.FC<ContactHeaderProps> = ({ emailSent }) => {
  const { colors } = useTheme();
  
  return (
    <>
      <View style={styles.iconContainer}>
        <Mail size={32} color={colors.primary} />
      </View>
      
      <Text style={[styles.title, { color: colors.text }]}>Get in Touch</Text>
      <Text style={[styles.subtitle, { color: colors.textLight }]}>
        We're here to help! Send us a message or start a live chat.
      </Text>
      
      {emailSent && (
        <View style={[styles.successBanner, { backgroundColor: colors.success + '20' }]}>
          <CheckCircle size={20} color={colors.success} />
          <Text style={[styles.successText, { color: colors.success }]}>
            Your message has been <NAME_EMAIL>
          </Text>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  successBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  successText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
});

export default ContactHeader;