import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { MessageSquare } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface ContactOptionsProps {
  onOpenChat: () => void;
}

/**
 * @magic_description Contact options component for alternative contact methods
 * Displays divider and live chat option
 */
const ContactOptions: React.FC<ContactOptionsProps> = ({ onOpenChat }) => {
  const { colors } = useTheme();
  
  return (
    <>
      <View style={styles.divider}>
        <View style={[styles.dividerLine, { backgroundColor: colors.border }]} />
        <Text style={[styles.dividerText, { color: colors.textLight, backgroundColor: colors.background }]}>OR</Text>
        <View style={[styles.dividerLine, { backgroundColor: colors.border }]} />
      </View>
      
      <TouchableOpacity 
        style={[styles.chatButton, { backgroundColor: colors.secondary }]}
        onPress={onOpenChat}
        activeOpacity={0.8}
      >
        <MessageSquare size={20} color={colors.card} />
        <Text style={[styles.chatButtonText, { color: colors.card }]}>Start Live Chat</Text>
      </TouchableOpacity>
    </>
  );
};

const styles = StyleSheet.create({
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 32,
  },
  dividerLine: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    paddingHorizontal: 16,
    fontSize: 14,
    fontWeight: '500',
  },
  chatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  chatButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default ContactOptions;