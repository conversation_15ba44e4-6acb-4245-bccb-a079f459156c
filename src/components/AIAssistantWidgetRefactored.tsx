import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  Animated
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAIStore } from '../store/aiStore';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { HomeStackParamList } from '../navigation';
import AIWidgetExpandedContent from './aiAssistant/AIWidgetExpandedContent';
import AIWidgetButton from './aiAssistant/AIWidgetButton';

interface AIAssistantWidgetRefactoredProps {
  animalId?: string;
  style?: any;
}

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

const AIAssistantWidgetRefactored: React.FC<AIAssistantWidgetRefactoredProps> = ({
  animalId,
  style
}) => {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const {
    readinessScore,
    healthAssessment,
    coachingTips,
    isLoading,
    fetchReadinessScore,
    fetchHealthAssessment,
    fetchCoachingTips
  } = useAIStore();
  
  const [isExpanded, setIsExpanded] = useState(false);
  const [animatedValue] = useState(new Animated.Value(0));
  
  // Mock insights data
  const [insights] = useState([
    {
      id: '1',
      type: 'alert',
      title: 'Heart Rate Variation Detected',
      description: 'Slight increase in resting heart rate. Monitor closely and consult vet if it persists.',
      priority: 'high' as const,
      confidence: 0.92
    },
    {
      id: '2',
      type: 'recommendation',
      title: 'Optimal Exercise Schedule',
      description: 'Based on activity patterns, morning walks between 7-9 AM show best results.',
      priority: 'medium' as const,
      confidence: 0.78
    },
    {
      id: '3',
      type: 'insight',
      title: 'Improved Sleep Quality',
      description: 'Sleep patterns have improved by 15% over the past week.',
      priority: 'low' as const,
      confidence: 0.85
    }
  ]);
  
  const hasAlerts = insights.some(insight => 
    insight.priority === 'high' || insight.priority === 'critical'
  );
  
  const alertCount = insights.filter(insight => 
    insight.priority === 'high' || insight.priority === 'critical'
  ).length;
  
  const handleToggleExpanded = useCallback(() => {
    const toValue = isExpanded ? 0 : 1;
    
    Animated.spring(animatedValue, {
      toValue,
      useNativeDriver: true,
      tension: 100,
      friction: 8
    }).start();
    
    setIsExpanded(!isExpanded);
  }, [isExpanded, animatedValue]);
  
  const handleClose = useCallback(() => {
    Animated.spring(animatedValue, {
      toValue: 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8
    }).start(() => {
      setIsExpanded(false);
    });
  }, [animatedValue]);
  
  const handleViewAll = useCallback(() => {
    handleClose();
    
    if (animalId) {
      navigation.navigate('AIAssistant', { animalId });
    } else {
      navigation.navigate('AIAssistant');
    }
  }, [animalId, navigation, handleClose]);
  
  const handleInsightPress = useCallback((insight: any) => {
    handleClose();
    // Navigate to insight detail or show modal
    console.log('Insight pressed:', insight);
  }, [handleClose]);
  
  useEffect(() => {
    if (animalId) {
      fetchReadinessScore(animalId);
      fetchHealthAssessment(animalId);
      fetchCoachingTips(animalId);
    }
  }, [animalId, fetchReadinessScore, fetchHealthAssessment, fetchCoachingTips]);
  
  const styles = StyleSheet.create({
    container: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      zIndex: 1000
    }
  });
  
  const expandedContentStyle = {
    opacity: animatedValue,
    transform: [
      {
        scale: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1]
        })
      },
      {
        translateY: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [20, 0]
        })
      }
    ]
  };
  
  return (
    <View style={[styles.container, style]}>
      {isExpanded && (
        <Animated.View style={expandedContentStyle}>
          <AIWidgetExpandedContent
            insights={insights}
            readinessScore={readinessScore?.score}
            isLoading={isLoading}
            onClose={handleClose}
            onViewAll={handleViewAll}
            onInsightPress={handleInsightPress}
          />
        </Animated.View>
      )}
      
      <AIWidgetButton
        hasAlerts={hasAlerts}
        alertCount={alertCount}
        onPress={handleToggleExpanded}
        isLoading={isLoading}
      />
    </View>
  );
};

export default AIAssistantWidgetRefactored;