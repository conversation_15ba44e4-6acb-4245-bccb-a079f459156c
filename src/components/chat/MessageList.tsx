import React, { useRef, useEffect } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  RefreshControl,
  Text,
  TouchableOpacity
} from 'react-native';
import { MessageSquare } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import ChatMessage from './ChatMessage';

interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  attachments?: {
    type: 'image' | 'file';
    url: string;
    name: string;
  }[];
  isLoading?: boolean;
}

interface MessageListProps {
  messages: ChatMessage[];
  isLoading?: boolean;
  onRefresh?: () => void;
  onImagePress?: (url: string) => void;
  onStartNewChat?: () => void;
  userProfileImage?: string;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  isLoading = false,
  onRefresh,
  onImagePress,
  onStartNewChat,
  userProfileImage
}) => {
  const { colors } = useTheme();
  const flatListRef = useRef<FlatList>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length]);

  const renderMessage = ({ item }: { item: ChatMessage }) => (
    <ChatMessage
      message={item}
      onImagePress={onImagePress}
      userProfileImage={userProfileImage}
    />
  );

  const renderEmptyState = () => (
    <View style={[styles.emptyContainer, { backgroundColor: colors.background }]}>
      <View style={[styles.emptyIconContainer, { backgroundColor: colors.primary + '20' }]}>
        <MessageSquare size={48} color={colors.primary} />
      </View>
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        Start a Conversation
      </Text>
      <Text style={[styles.emptyDescription, { color: colors.textSecondary }]}>
        Ask me anything about your animal's health, training, or behavior. I can analyze images and provide personalized insights!
      </Text>
      
      <View style={styles.suggestionsContainer}>
        <Text style={[styles.suggestionsTitle, { color: colors.text }]}>
          Try asking:
        </Text>
        {[
          "How is my dog's training progress?",
          "Analyze this photo of my pet",
          "What should I feed my cat?",
          "Create a workout plan for my horse"
        ].map((suggestion, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.suggestionItem, { backgroundColor: colors.card, borderColor: colors.border }]}
            onPress={() => onStartNewChat?.()}
          >
            <Text style={[styles.suggestionText, { color: colors.textSecondary }]}>
              "{suggestion}"
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderHeader = () => {
    if (messages.length === 0) return null;
    
    return (
      <View style={styles.headerContainer}>
        <Text style={[styles.headerText, { color: colors.textSecondary }]}>
          AI Assistant Chat
        </Text>
      </View>
    );
  };

  if (messages.length === 0) {
    return renderEmptyState();
  }

  return (
    <FlatList
      ref={flatListRef}
      data={messages}
      renderItem={renderMessage}
      keyExtractor={(item) => item.id}
      style={[styles.container, { backgroundColor: colors.background }]}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={isLoading}
            onRefresh={onRefresh}
            tintColor={colors.primary}
          />
        ) : undefined
      }
      ListHeaderComponent={renderHeader}
      maintainVisibleContentPosition={{
        minIndexForVisible: 0,
        autoscrollToTopThreshold: 10,
      }}
      onContentSizeChange={() => {
        // Auto-scroll to bottom when content size changes
        flatListRef.current?.scrollToEnd({ animated: true });
      }}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingVertical: 8,
    flexGrow: 1,
  },
  headerContainer: {
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  headerText: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 32,
  },
  suggestionsContainer: {
    width: '100%',
  },
  suggestionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  suggestionItem: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  suggestionText: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
  },
});

export default MessageList;