import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { <PERSON><PERSON>, User, Copy, Share2 } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { format } from 'date-fns';
import * as Clipboard from 'expo-clipboard';
import { Share } from 'react-native';
import { toast } from 'sonner-native';

interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  attachments?: {
    type: 'image' | 'file';
    url: string;
    name: string;
  }[];
  isLoading?: boolean;
  isError?: boolean;
}

interface ChatMessageProps {
  message: ChatMessage;
  onImagePress?: (url: string) => void;
  userProfileImage?: string;
}

const ChatMessageComponent: React.FC<ChatMessageProps> = ({
  message,
  onImagePress,
  userProfileImage
}) => {
  const { colors } = useTheme();
  const isUser = message.role === 'user';
  const isError = message.isError || false;
  const screenWidth = Dimensions.get('window').width;

  const handleCopyMessage = async () => {
    try {
      await Clipboard.setStringAsync(message.content);
      toast.success('Message copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy message');
    }
  };

  const handleShareMessage = async () => {
    try {
      await Share.share({
        message: message.content,
        title: 'AI Assistant Message'
      });
    } catch (error) {
      toast.error('Failed to share message');
    }
  };

  const renderAttachments = () => {
    if (!message.attachments || message.attachments.length === 0) return null;

    return (
      <View style={styles.attachmentsContainer}>
        {message.attachments.map((attachment, index) => {
          if (attachment.type === 'image') {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => onImagePress?.(attachment.url)}
                style={styles.imageContainer}
              >
                <Image
                  source={{ uri: attachment.url }}
                  style={styles.attachmentImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            );
          }
          return (
            <View key={index} style={[styles.fileContainer, { backgroundColor: colors.border }]}>
              <Text style={[styles.fileName, { color: colors.text }]}>
                📎 {attachment.name}
              </Text>
            </View>
          );
        })}
      </View>
    );
  };

  const renderMessageActions = () => {
    if (isUser || message.isLoading) return null;

    return (
      <View style={styles.messageActions}>
        <TouchableOpacity
          onPress={handleCopyMessage}
          style={[styles.actionButton, { backgroundColor: colors.border }]}
        >
          <Copy size={14} color={colors.textSecondary} />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleShareMessage}
          style={[styles.actionButton, { backgroundColor: colors.border }]}
        >
          <Share2 size={14} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={[
      styles.messageContainer,
      isUser ? styles.userMessageContainer : styles.assistantMessageContainer
    ]}>
      {/* Avatar */}
      <View style={[
        styles.avatar,
        { backgroundColor: isUser ? colors.primary : colors.secondary }
      ]}>
        {isUser ? (
          userProfileImage ? (
            <Image
              source={{ uri: userProfileImage }}
              style={styles.avatarImage}
              resizeMode="cover"
            />
          ) : (
            <User size={16} color={colors.white} />
          )
        ) : (
          <Image
            source={{ uri: 'https://magically.life/api/media/image?query=beautiful%20race%20horse%20head%20portrait%20elegant%20thoroughbred' }}
            style={styles.avatarImage}
            resizeMode="cover"
          />
        )}
      </View>

      {/* Message Content */}
      <View style={[
        styles.messageContent,
        isError && styles.errorMessage,
        {
          backgroundColor: isError 
            ? '#FEF2F2' 
            : isUser 
              ? colors.primary 
              : colors.card,
          borderColor: isError ? '#FCA5A5' : 'transparent',
          borderWidth: isError ? 1 : 0,
          maxWidth: screenWidth * 0.75
        }
      ]}>
        {/* Attachments */}
        {renderAttachments()}

        {/* Text Content */}
        {message.content && (
          <Text style={[
            styles.messageText,
            { 
              color: isError 
                ? '#DC2626' 
                : isUser 
                  ? colors.white 
                  : colors.text 
            }
          ]}>
            {message.content}
          </Text>
        )}

        {/* Loading indicator for AI responses */}
        {message.isLoading && (
          <View style={styles.loadingContainer}>
            <View style={[styles.loadingDot, { backgroundColor: colors.textSecondary }]} />
            <View style={[styles.loadingDot, { backgroundColor: colors.textSecondary }]} />
            <View style={[styles.loadingDot, { backgroundColor: colors.textSecondary }]} />
          </View>
        )}

        {/* Timestamp */}
        <Text style={[
          styles.timestamp,
          { color: isUser ? colors.white + '80' : colors.textSecondary }
        ]}>
          {format(new Date(message.timestamp), 'HH:mm')}
        </Text>
      </View>

      {/* Message Actions */}
      {renderMessageActions()}
    </View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    flexDirection: 'row',
    marginVertical: 4,
    paddingHorizontal: 16,
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  assistantMessageContainer: {
    justifyContent: 'flex-start',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
    overflow: 'hidden',
  },
  avatarImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  messageContent: {
    borderRadius: 16,
    padding: 12,
    minWidth: 60,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  timestamp: {
    fontSize: 11,
    marginTop: 4,
    textAlign: 'right',
  },
  attachmentsContainer: {
    marginBottom: 8,
  },
  imageContainer: {
    marginBottom: 4,
  },
  attachmentImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
  },
  fileContainer: {
    padding: 8,
    borderRadius: 8,
    marginBottom: 4,
  },
  fileName: {
    fontSize: 14,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  loadingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginHorizontal: 2,
    opacity: 0.6,
  },
  errorMessage: {
    shadowColor: '#EF4444',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageActions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 2,
  },
});

export default ChatMessageComponent;