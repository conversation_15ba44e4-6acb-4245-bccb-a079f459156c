import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  Text
} from 'react-native';
import { Send, Paperclip, Camera, Image as ImageIcon, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { useTheme } from '../../contexts/ThemeContext';
import { toast } from 'sonner-native';

interface ChatInputProps {
  onSendMessage: (message: string, attachments?: File[]) => void;
  isLoading?: boolean;
  placeholder?: string;
}

interface AttachmentPreview {
  id: string;
  uri: string;
  type: 'image' | 'file';
  name: string;
  size?: number;
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  isLoading = false,
  placeholder = "Ask me anything about your pet..."
}) => {
  const { colors } = useTheme();
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<AttachmentPreview[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const inputRef = useRef<TextInput>(null);

  const handleSend = () => {
    if ((!message.trim() && attachments.length === 0) || isLoading || isUploading) {
      return;
    }

    // Convert attachments to File objects (simplified for demo)
    const files = attachments.map(attachment => ({
      uri: attachment.uri,
      type: attachment.type,
      name: attachment.name
    }));

    onSendMessage(message.trim(), files as any);
    setMessage('');
    setAttachments([]);
    inputRef.current?.blur();
  };

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant permission to access your photo library to upload images.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const handleImagePicker = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      setIsUploading(true);
      
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const newAttachment: AttachmentPreview = {
          id: Date.now().toString(),
          uri: asset.uri,
          type: 'image',
          name: asset.fileName || `image_${Date.now()}.jpg`,
          size: asset.fileSize
        };
        
        setAttachments(prev => [...prev, newAttachment]);
        toast.success('Image added successfully');
      }
    } catch (error) {
      console.error('Error picking image:', error);
      toast.error('Failed to pick image');
    } finally {
      setIsUploading(false);
    }
  };

  const handleCameraPicker = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant permission to access your camera to take photos.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      setIsUploading(true);
      
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const newAttachment: AttachmentPreview = {
          id: Date.now().toString(),
          uri: asset.uri,
          type: 'image',
          name: `photo_${Date.now()}.jpg`,
          size: asset.fileSize
        };
        
        setAttachments(prev => [...prev, newAttachment]);
        toast.success('Photo captured successfully');
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      toast.error('Failed to take photo');
    } finally {
      setIsUploading(false);
    }
  };

  const showAttachmentOptions = () => {
    Alert.alert(
      'Add Attachment',
      'Choose how you want to add an image',
      [
        { text: 'Camera', onPress: handleCameraPicker },
        { text: 'Photo Library', onPress: handleImagePicker },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const removeAttachment = (id: string) => {
    setAttachments(prev => prev.filter(att => att.id !== id));
  };

  const renderAttachmentPreviews = () => {
    if (attachments.length === 0) return null;

    return (
      <View style={styles.attachmentPreviewContainer}>
        {attachments.map(attachment => (
          <View key={attachment.id} style={[styles.attachmentPreview, { backgroundColor: colors.card }]}>
            <View style={styles.attachmentInfo}>
              <ImageIcon size={16} color={colors.primary} />
              <Text style={[styles.attachmentName, { color: colors.text }]} numberOfLines={1}>
                {attachment.name}
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => removeAttachment(attachment.id)}
              style={[styles.removeButton, { backgroundColor: colors.error }]}
            >
              <X size={12} color={colors.white} />
            </TouchableOpacity>
          </View>
        ))}
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      {renderAttachmentPreviews()}
      
      <View style={[styles.inputContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
        {/* Attachment Button */}
        <TouchableOpacity
          onPress={showAttachmentOptions}
          style={[styles.attachmentButton, { backgroundColor: colors.background }]}
          disabled={isLoading || isUploading}
        >
          {isUploading ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <Paperclip size={20} color={colors.textSecondary} />
          )}
        </TouchableOpacity>

        {/* Text Input */}
        <TextInput
          ref={inputRef}
          style={[styles.textInput, { color: colors.text }]}
          value={message}
          onChangeText={setMessage}
          placeholder={placeholder}
          placeholderTextColor={colors.textSecondary}
          multiline
          maxLength={1000}
          editable={!isLoading && !isUploading}
          onSubmitEditing={handleSend}
          blurOnSubmit={false}
        />

        {/* Send Button */}
        <TouchableOpacity
          onPress={handleSend}
          style={[
            styles.sendButton,
            {
              backgroundColor: (message.trim() || attachments.length > 0) && !isLoading && !isUploading
                ? colors.primary
                : colors.border
            }
          ]}
          disabled={(!message.trim() && attachments.length === 0) || isLoading || isUploading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <Send
              size={20}
              color={(message.trim() || attachments.length > 0) && !isLoading && !isUploading
                ? colors.white
                : colors.textSecondary
              }
            />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  attachmentPreviewContainer: {
    marginBottom: 8,
  },
  attachmentPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 8,
    borderRadius: 8,
    marginBottom: 4,
  },
  attachmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  attachmentName: {
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  removeButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderWidth: 1,
    borderRadius: 24,
    paddingHorizontal: 4,
    paddingVertical: 4,
    minHeight: 48,
  },
  attachmentButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    lineHeight: 20,
    maxHeight: 100,
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
});

export default ChatInput;