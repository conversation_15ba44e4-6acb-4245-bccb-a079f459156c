import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { PrintType } from '../../hooks/usePrintScreenLogic';
import FeedingPrintItem, { FeedingItem } from './FeedingPrintItem';
import MedicationPrintItem, { MedicationItem } from './MedicationPrintItem';

export interface PrintPreviewContentProps {
  type: PrintType;
  animal: any;
  data: any[];
  title: string;
}

const PrintPreviewContent: React.FC<PrintPreviewContentProps> = ({ type, animal, data, title }) => {
  const { colors } = useTheme();
  
  const renderDataItems = () => {
    if (data.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            No {type === 'feeding' ? 'feeding schedules' : type === 'medication' ? 'medications' : 'vaccinations'} found for today
          </Text>
        </View>
      );
    }
    
    switch (type) {
      case 'feeding':
        return (
          <View style={styles.dataContainer}>
            {data.map((item: FeedingItem, index: number) => (
              <FeedingPrintItem key={item.id} item={item} index={index} />
            ))}
          </View>
        );
      
      case 'medication':
        return (
          <View style={styles.dataContainer}>
            {data.map((item: MedicationItem, index: number) => (
              <MedicationPrintItem key={item.id} item={item} index={index} />
            ))}
          </View>
        );
      
      case 'vaccination':
        // Future implementation for vaccination items
        return (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: colors.textLight }]}>
              Vaccination records display coming soon
            </Text>
          </View>
        );
      
      default:
        return (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: colors.textLight }]}>
              Unknown data type
            </Text>
          </View>
        );
    }
  };
  
  return (
    <View style={[styles.previewCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <Text style={[styles.previewTitle, { color: colors.text }]}>
        {title} for {animal.name}
      </Text>
      <Text style={[styles.previewDate, { color: colors.textLight }]}>
        Date: {new Date().toLocaleDateString()}
      </Text>
      
      <View style={[styles.divider, { backgroundColor: colors.border }]} />
      
      {renderDataItems()}
    </View>
  );
};

const styles = StyleSheet.create({
  previewCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    marginBottom: 24,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  previewDate: {
    fontSize: 14,
    marginBottom: 16,
  },
  divider: {
    height: 1,
    marginBottom: 16,
  },
  dataContainer: {
    gap: 16,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default PrintPreviewContent;