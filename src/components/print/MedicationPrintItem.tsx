import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface MedicationItem {
  id: string;
  medicationName: string;
  dosage: string;
  dosageUnit: string;
  time: string;
  notes?: string;
}

export interface MedicationPrintItemProps {
  item: MedicationItem;
  index: number;
}

const MedicationPrintItem: React.FC<MedicationPrintItemProps> = ({ item, index }) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.dataItem}>
      <View style={styles.dataHeader}>
        <Text style={[styles.dataTitle, { color: colors.text }]}>
          {index + 1}. {item.medicationName}
        </Text>
        <Text style={[styles.dataTime, { color: colors.primary }]}>
          {item.time}
        </Text>
      </View>
      <Text style={[styles.dataSubtitle, { color: colors.textLight }]}>
        Dosage: {item.dosage}{item.dosageUnit}
      </Text>
      {item.notes && (
        <Text style={[styles.dataNotes, { color: colors.textLight }]}>
          Notes: {item.notes}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  dataItem: {
    marginBottom: 16,
  },
  dataHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  dataTitle: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  dataTime: {
    fontSize: 14,
    fontWeight: '500',
  },
  dataSubtitle: {
    fontSize: 14,
    marginBottom: 2,
  },
  dataNotes: {
    fontSize: 14,
    fontStyle: 'italic',
  },
});

export default MedicationPrintItem;