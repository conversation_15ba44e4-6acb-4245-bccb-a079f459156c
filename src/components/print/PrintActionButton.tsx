import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { LucideIcon } from 'lucide-react-native';

export interface PrintActionButtonProps {
  onPress: () => void;
  isLoading: boolean;
  isCompleted: boolean;
  label: string;
  completedLabel: string;
  icon: LucideIcon;
  completedIcon: LucideIcon;
  backgroundColor: string;
  disabled?: boolean;
}

const PrintActionButton: React.FC<PrintActionButtonProps> = ({
  onPress,
  isLoading,
  isCompleted,
  label,
  completedLabel,
  icon: Icon,
  completedIcon: CompletedIcon,
  backgroundColor,
  disabled = false
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.actionButton,
        { backgroundColor },
        (isLoading || disabled) && styles.disabled
      ]}
      onPress={onPress}
      disabled={isLoading || disabled}
    >
      {isLoading ? (
        <ActivityIndicator color="#FFF" size="small" />
      ) : (
        <>
          {isCompleted ? (
            <CompletedIcon size={20} color="#FFF" />
          ) : (
            <Icon size={20} color="#FFF" />
          )}
          <Text style={styles.actionButtonText}>
            {isCompleted ? completedLabel : label}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  disabled: {
    opacity: 0.7,
  },
  actionButtonText: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default PrintActionButton;