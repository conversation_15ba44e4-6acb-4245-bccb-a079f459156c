
import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Dimensions 
} from 'react-native';
import { Crown, Star, Zap } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

interface PremiumBannerProps {
  onPress: () => void;
  compact?: boolean;
}

const PremiumBanner: React.FC<PremiumBannerProps> = ({ 
  onPress,
  compact = false
}) => {
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();
  
  if (compact) {
    return (
      <TouchableOpacity 
        style={styles.compactContainer}
        onPress={onPress}
        activeOpacity={0.9}
      >
        <LinearGradient
          colors={['#F9C74F', '#E07A5F']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.compactGradient}
        >
          <Crown size={14} color="#FFF" />
          <Text style={styles.compactText}>{t('premium')}</Text>
        </LinearGradient>
      </TouchableOpacity>
    );
  }
  
  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.95}
    >
      <LinearGradient
        colors={['#F9C74F', '#E07A5F']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Crown size={22} color="#FFF" />
            <Text style={styles.title}>{t('upgradeToPremium')}</Text>
          </View>
          
          <View style={styles.features}>
            <View style={styles.featureItem}>
              <Star size={16} color="#FFF" />
              <Text style={styles.featureText}>{t('unlimitedAnimals')}</Text>
            </View>
            
            <View style={styles.featureItem}>
              <Zap size={16} color="#FFF" />
              <Text style={styles.featureText}>{t('advancedAnalytics')}</Text>
            </View>
          </View>
          
          <View style={styles.buttonContainer}>
            <Text style={styles.buttonText}>{t('upgradeNow')}</Text>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  gradient: {
    borderRadius: 16,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFF',
    marginLeft: 8,
  },
  features: {
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    color: '#FFF',
    marginLeft: 8,
    fontSize: 14,
  },
  buttonContainer: {
    backgroundColor: '#FFF',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  buttonText: {
    color: '#E07A5F',
    fontWeight: '600',
  },
  compactContainer: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  compactGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  compactText: {
    color: '#FFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
});

export default PremiumBanner;
