import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList
} from 'react-native';
import { 
  Shield, 
  Clock, 
  Calendar, 
  CheckCircle,
  AlertTriangle,
  ArrowRight,
  Target
} from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface PreventiveAction {
  id: string;
  action: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  estimated_cost?: string;
  time_to_implement?: string;
  expected_benefit?: string;
  difficulty: 'easy' | 'moderate' | 'difficult';
  deadline?: string;
  status?: 'pending' | 'in_progress' | 'completed';
}

interface PreventiveActionsCardProps {
  actions: PreventiveAction[];
  onActionPress?: (action: PreventiveAction) => void;
  showAll?: boolean;
}

const PreventiveActionsCard: React.FC<PreventiveActionsCardProps> = ({
  actions,
  onActionPress,
  showAll = false
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#EF4444';
      case 'high': return '#F59E0B';
      case 'medium': return '#3B82F6';
      case 'low': return '#10B981';
      default: return colors.textLight;
    }
  };

  const getPriorityIcon = (priority: string) => {
    const color = getPriorityColor(priority);
    switch (priority) {
      case 'urgent':
        return <AlertTriangle size={16} color={color} />;
      case 'high':
        return <Target size={16} color={color} />;
      case 'medium':
        return <Clock size={16} color={color} />;
      case 'low':
        return <CheckCircle size={16} color={color} />;
      default:
        return <Shield size={16} color={color} />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#10B981';
      case 'moderate': return '#F59E0B';
      case 'difficult': return '#EF4444';
      default: return colors.textLight;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#10B981';
      case 'in_progress': return '#3B82F6';
      case 'pending': return '#6B7280';
      default: return colors.textLight;
    }
  };

  const renderAction = ({ item }: { item: PreventiveAction }) => (
    <TouchableOpacity
      style={[
        styles.actionItem,
        {
          backgroundColor: colors.card,
          borderLeftColor: getPriorityColor(item.priority)
        }
      ]}
      onPress={() => onActionPress?.(item)}
      activeOpacity={0.7}
    >
      <View style={styles.actionHeader}>
        <View style={styles.actionTitleContainer}>
          {getPriorityIcon(item.priority)}
          <Text style={[styles.actionTitle, { color: colors.text }]}>
            {item.action}
          </Text>
        </View>
        
        <View style={styles.actionMeta}>
          <View style={[
            styles.priorityBadge,
            { backgroundColor: getPriorityColor(item.priority) }
          ]}>
            <Text style={styles.priorityText}>
              {item.priority.toUpperCase()}
            </Text>
          </View>
        </View>
      </View>
      
      <Text style={[styles.actionDescription, { color: colors.textLight }]}>
        {item.description}
      </Text>
      
      {/* Action Details */}
      <View style={styles.actionDetails}>
        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.textLight }]}>
            Category:
          </Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {item.category}
          </Text>
        </View>
        
        {item.time_to_implement && (
          <View style={styles.detailRow}>
            <Clock size={12} color={colors.textLight} />
            <Text style={[styles.detailValue, { color: colors.textLight }]}>
              {item.time_to_implement}
            </Text>
          </View>
        )}
        
        {item.estimated_cost && (
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textLight }]}>
              Cost:
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {item.estimated_cost}
            </Text>
          </View>
        )}
        
        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.textLight }]}>
            Difficulty:
          </Text>
          <Text style={[
            styles.detailValue,
            { color: getDifficultyColor(item.difficulty) }
          ]}>
            {item.difficulty.charAt(0).toUpperCase() + item.difficulty.slice(1)}
          </Text>
        </View>
      </View>
      
      {/* Expected Benefit */}
      {item.expected_benefit && (
        <View style={[
          styles.benefitContainer,
          { backgroundColor: colors.surface }
        ]}>
          <Text style={[styles.benefitLabel, { color: colors.textLight }]}>
            Expected Benefit:
          </Text>
          <Text style={[styles.benefitText, { color: colors.text }]}>
            {item.expected_benefit}
          </Text>
        </View>
      )}
      
      {/* Deadline */}
      {item.deadline && (
        <View style={styles.deadlineContainer}>
          <Calendar size={12} color={colors.textLight} />
          <Text style={[styles.deadlineText, { color: colors.textLight }]}>
            Deadline: {format(new Date(item.deadline), 'MMM dd, yyyy')}
          </Text>
        </View>
      )}
      
      {/* Status */}
      {item.status && (
        <View style={styles.statusContainer}>
          <View style={[
            styles.statusDot,
            { backgroundColor: getStatusColor(item.status) }
          ]} />
          <Text style={[
            styles.statusText,
            { color: getStatusColor(item.status) }
          ]}>
            {item.status.replace('_', ' ').toUpperCase()}
          </Text>
        </View>
      )}
      
      <View style={styles.actionFooter}>
        <ArrowRight size={16} color={colors.primary} />
      </View>
    </TouchableOpacity>
  );

  if (!actions || actions.length === 0) {
    return null;
  }

  const displayActions = showAll ? actions : actions.slice(0, 3);
  const urgentActions = actions.filter(a => a.priority === 'urgent').length;
  const highPriorityActions = actions.filter(a => a.priority === 'high').length;

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Shield size={20} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>
            🛷 Preventive Actions
          </Text>
        </View>
        
        <View style={styles.summaryContainer}>
          {urgentActions > 0 && (
            <View style={[styles.summaryBadge, { backgroundColor: '#EF4444' }]}>
              <Text style={styles.summaryText}>
                {urgentActions} Urgent
              </Text>
            </View>
          )}
          {highPriorityActions > 0 && (
            <View style={[styles.summaryBadge, { backgroundColor: '#F59E0B' }]}>
              <Text style={styles.summaryText}>
                {highPriorityActions} High
              </Text>
            </View>
          )}
        </View>
      </View>
      
      <FlatList
        data={displayActions}
        renderItem={renderAction}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
      
      {!showAll && actions.length > 3 && (
        <TouchableOpacity
          style={[styles.showMoreButton, { borderColor: colors.border }]}
          onPress={() => {
            // Could expand or navigate to full list
          }}
        >
          <Text style={[styles.showMoreText, { color: colors.primary }]}>
            +{actions.length - 3} more actions
          </Text>
        </TouchableOpacity>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  summaryContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  summaryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  summaryText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  actionItem: {
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
  },
  actionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  actionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  actionMeta: {
    alignItems: 'flex-end',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  actionDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  actionDetails: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  detailLabel: {
    fontSize: 12,
    marginRight: 8,
    minWidth: 60,
  },
  detailValue: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  benefitContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  benefitLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  benefitText: {
    fontSize: 13,
    fontWeight: '500',
  },
  deadlineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  deadlineText: {
    fontSize: 12,
    marginLeft: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 11,
    fontWeight: '600',
  },
  actionFooter: {
    alignItems: 'flex-end',
  },
  separator: {
    height: 12,
  },
  showMoreButton: {
    marginTop: 12,
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  showMoreText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default PreventiveActionsCard;