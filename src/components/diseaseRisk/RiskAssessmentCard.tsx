import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Shield, AlertTriangle, Info, ChevronRight } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { DiseaseRiskAssessment } from '../../store/aiHealthStore';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface RiskAssessmentCardProps {
  assessment: DiseaseRiskAssessment;
  onViewDetails?: (assessment: DiseaseRiskAssessment) => void;
  showDetails?: boolean;
}

const RiskAssessmentCard: React.FC<RiskAssessmentCardProps> = ({
  assessment,
  onViewDetails,
  showDetails = false
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return '#EF4444';
      case 'high': return '#F59E0B';
      case 'medium': return '#3B82F6';
      case 'low': return '#10B981';
      default: return colors.textLight;
    }
  };

  const getRiskLevelIcon = (level: string) => {
    const color = getRiskLevelColor(level);
    switch (level) {
      case 'critical':
      case 'high':
        return <AlertTriangle size={20} color={color} />;
      case 'medium':
        return <Info size={20} color={color} />;
      case 'low':
        return <Shield size={20} color={color} />;
      default:
        return <Shield size={20} color={color} />;
    }
  };

  const getRiskLevelDescription = (level: string) => {
    switch (level) {
      case 'critical': return t('criticalRiskDescription');
      case 'high': return t('highRiskDescription');
      case 'medium': return t('mediumRiskDescription');
      case 'low': return t('lowRiskDescription');
      default: return '';
    }
  };

  const getConfidenceLevel = (confidence: number) => {
    if (confidence >= 0.8) return t('highConfidence');
    if (confidence >= 0.6) return t('mediumConfidence');
    return t('lowConfidence');
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#10B981';
    if (confidence >= 0.6) return '#F59E0B';
    return '#EF4444';
  };

  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(assessment);
    }
  };

  return (
    <Card style={[
      styles.container,
      {
        borderLeftWidth: 4,
        borderLeftColor: getRiskLevelColor(assessment.risk_level)
      }
    ]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          {getRiskLevelIcon(assessment.risk_level)}
          <View style={styles.titleContent}>
            <Text style={[styles.category, { color: colors.text }]}>
              {assessment.disease_category.charAt(0).toUpperCase() + assessment.disease_category.slice(1)} {t('disease')}
            </Text>
            <Text style={[styles.riskDescription, { color: colors.textLight }]}>
              {getRiskLevelDescription(assessment.risk_level)}
            </Text>
          </View>
        </View>
        
        <View style={styles.headerRight}>
          <View style={[
            styles.riskBadge,
            { backgroundColor: getRiskLevelColor(assessment.risk_level) }
          ]}>
            <Text style={styles.riskText}>
              {assessment.risk_level.toUpperCase()}
            </Text>
          </View>
        </View>
      </View>

      {/* Risk Score */}
      <View style={styles.scoreContainer}>
        <View style={styles.scoreCircle}>
          <Text style={[
            styles.scoreNumber,
            { color: getRiskLevelColor(assessment.risk_level) }
          ]}>
            {assessment.risk_score.toFixed(0)}
          </Text>
          <Text style={[styles.scoreLabel, { color: colors.textLight }]}>
            % {t('risk')}
          </Text>
        </View>
        
        <View style={styles.scoreDetails}>
          <View style={styles.confidenceContainer}>
            <Text style={[styles.confidenceLabel, { color: colors.textLight }]}>
              {t('confidence')}:
            </Text>
            <Text style={[
              styles.confidenceValue,
              { color: getConfidenceColor(assessment.confidence_level) }
            ]}>
              {getConfidenceLevel(assessment.confidence_level)}
            </Text>
          </View>
          
          <Text style={[styles.assessmentDate, { color: colors.textLight }]}>
            {t('assessed')}: {format(new Date(assessment.assessment_date), 'MMM dd, yyyy')}
          </Text>
        </View>
      </View>

      {/* Contributing Factors */}
      {assessment.contributing_factors && Object.keys(assessment.contributing_factors).length > 0 && (
        <View style={styles.factorsContainer}>
          <Text style={[styles.factorsTitle, { color: colors.text }]}>
            {t('contributingFactors')}:
          </Text>
          <View style={styles.factorsList}>
            {Object.entries(assessment.contributing_factors).map(([key, value], index) => (
              <View key={index} style={styles.factorItem}>
                <Text style={[styles.factorBullet, { color: colors.primary }]}>
                  •
                </Text>
                <Text style={[styles.factorText, { color: colors.textLight }]}>
                  {key.replace(/_/g, ' ').charAt(0).toUpperCase() + key.replace(/_/g, ' ').slice(1)}: {String(value)}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Recommendations */}
      {assessment.recommendations && (
        <View style={styles.recommendationsContainer}>
          <Text style={[styles.recommendationsTitle, { color: colors.text }]}>
            {t('recommendations')}:
          </Text>
          <Text style={[styles.recommendationsText, { color: colors.textLight }]}>
            {assessment.recommendations}
          </Text>
        </View>
      )}

      {/* View Details Button */}
      {onViewDetails && (
        <TouchableOpacity
          style={[styles.detailsButton, { borderColor: colors.border }]}
          onPress={handleViewDetails}
        >
          <Text style={[styles.detailsButtonText, { color: colors.primary }]}>
            {t('viewDetails')}
          </Text>
          <ChevronRight size={16} color={colors.primary} />
        </TouchableOpacity>
      )}

      {/* Risk Level Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
          <View style={[
            styles.progressFill,
            {
              width: `${assessment.risk_score}%`,
              backgroundColor: getRiskLevelColor(assessment.risk_level)
            }
          ]} />
        </View>
        <View style={styles.progressLabels}>
          <Text style={[styles.progressLabel, { color: colors.textLight }]}>
            {t('low')}
          </Text>
          <Text style={[styles.progressLabel, { color: colors.textLight }]}>
            {t('high')}
          </Text>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginVertical: 8
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
    gap: 12
  },
  titleContent: {
    flex: 1
  },
  category: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4
  },
  riskDescription: {
    fontSize: 12,
    lineHeight: 16
  },
  headerRight: {
    alignItems: 'flex-end'
  },
  riskBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6
  },
  riskText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold'
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  scoreCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16
  },
  scoreNumber: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  scoreLabel: {
    fontSize: 10,
    marginTop: -2
  },
  scoreDetails: {
    flex: 1
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4
  },
  confidenceLabel: {
    fontSize: 12,
    marginRight: 4
  },
  confidenceValue: {
    fontSize: 12,
    fontWeight: '500'
  },
  assessmentDate: {
    fontSize: 11
  },
  factorsContainer: {
    marginBottom: 16
  },
  factorsTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8
  },
  factorsList: {
    gap: 4
  },
  factorItem: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  factorBullet: {
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 6,
    marginTop: 2
  },
  factorText: {
    fontSize: 12,
    flex: 1,
    lineHeight: 16
  },
  recommendationsContainer: {
    marginBottom: 16
  },
  recommendationsTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8
  },
  recommendationsText: {
    fontSize: 13,
    lineHeight: 18
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderRadius: 6,
    marginBottom: 16,
    gap: 8
  },
  detailsButtonText: {
    fontSize: 14,
    fontWeight: '500'
  },
  progressContainer: {
    marginTop: 8
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden'
  },
  progressFill: {
    height: '100%',
    borderRadius: 2
  },
  progressLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4
  },
  progressLabel: {
    fontSize: 10
  }
});

export default RiskAssessmentCard;