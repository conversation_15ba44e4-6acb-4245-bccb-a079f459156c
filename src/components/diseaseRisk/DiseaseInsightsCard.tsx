import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList
} from 'react-native';
import { Brain, Lightbulb, AlertTriangle, TrendingUp, ChevronRight } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface AIInsight {
  id: string;
  type: 'risk_factor' | 'prevention' | 'recommendation' | 'warning';
  title: string;
  description: string;
  confidence: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  created_at: string;
}

interface DiseaseInsightsCardProps {
  insights: AIInsight[];
  explanation?: string;
  onViewDetails?: () => void;
}

const DiseaseInsightsCard: React.FC<DiseaseInsightsCardProps> = ({
  insights,
  explanation,
  onViewDetails
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'risk_factor':
        return <AlertTriangle size={16} color="#F59E0B" />;
      case 'prevention':
        return <Lightbulb size={16} color="#10B981" />;
      case 'recommendation':
        return <TrendingUp size={16} color="#3B82F6" />;
      case 'warning':
        return <AlertTriangle size={16} color="#EF4444" />;
      default:
        return <Brain size={16} color={colors.primary} />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return '#EF4444';
      case 'high': return '#F59E0B';
      case 'medium': return '#3B82F6';
      case 'low': return '#10B981';
      default: return colors.textLight;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#10B981';
    if (confidence >= 0.6) return '#F59E0B';
    return '#EF4444';
  };

  const renderInsight = ({ item }: { item: AIInsight }) => (
    <View style={[
      styles.insightItem,
      {
        backgroundColor: colors.card,
        borderLeftColor: getSeverityColor(item.severity)
      }
    ]}>
      <View style={styles.insightHeader}>
        <View style={styles.insightTitleContainer}>
          {getInsightIcon(item.type)}
          <Text style={[styles.insightTitle, { color: colors.text }]}>
            {item.title}
          </Text>
        </View>
        
        <View style={styles.insightMeta}>
          <View style={[
            styles.severityBadge,
            { backgroundColor: getSeverityColor(item.severity) }
          ]}>
            <Text style={styles.severityText}>
              {item.severity.toUpperCase()}
            </Text>
          </View>
        </View>
      </View>
      
      <Text style={[styles.insightDescription, { color: colors.textLight }]}>
        {item.description}
      </Text>
      
      <View style={styles.insightFooter}>
        <Text style={[styles.insightCategory, { color: colors.textLight }]}>
          {item.category}
        </Text>
        
        <View style={styles.confidenceContainer}>
          <Text style={[styles.confidenceLabel, { color: colors.textLight }]}>
            Confidence:
          </Text>
          <Text style={[
            styles.confidenceValue,
            { color: getConfidenceColor(item.confidence) }
          ]}>
            {Math.round(item.confidence * 100)}%
          </Text>
        </View>
      </View>
    </View>
  );

  if (!insights || insights.length === 0) {
    return null;
  }

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Brain size={20} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>
            🤖 AI Disease Risk Insights
          </Text>
        </View>
        
        {onViewDetails && (
          <TouchableOpacity
            style={styles.viewDetailsButton}
            onPress={onViewDetails}
          >
            <Text style={[styles.viewDetailsText, { color: colors.primary }]}>
              View All
            </Text>
            <ChevronRight size={16} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>
      
      {explanation && (
        <View style={[styles.explanationContainer, { backgroundColor: colors.surface }]}>
          <Text style={[styles.explanationText, { color: colors.text }]}>
            {explanation}
          </Text>
        </View>
      )}
      
      <FlatList
        data={insights.slice(0, 3)} // Show top 3 insights
        renderItem={renderInsight}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
      
      {insights.length > 3 && (
        <TouchableOpacity
          style={[styles.showMoreButton, { borderColor: colors.border }]}
          onPress={onViewDetails}
        >
          <Text style={[styles.showMoreText, { color: colors.primary }]}>
            +{insights.length - 3} more insights
          </Text>
        </TouchableOpacity>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewDetailsText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  explanationContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  explanationText: {
    fontSize: 14,
    lineHeight: 20,
  },
  insightItem: {
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
  },
  insightHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  insightTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  insightMeta: {
    alignItems: 'flex-end',
  },
  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  severityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  insightDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  insightFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  insightCategory: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  confidenceLabel: {
    fontSize: 12,
    marginRight: 4,
  },
  confidenceValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  separator: {
    height: 12,
  },
  showMoreButton: {
    marginTop: 12,
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  showMoreText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default DiseaseInsightsCard;