import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { CheckCircle, AlertCircle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface DataReadinessSummaryProps {
  allReady: boolean;
}

/**
 * @magic_description Summary component for data readiness status
 * Shows overall readiness with appropriate styling and messaging
 */
const DataReadinessSummary: React.FC<DataReadinessSummaryProps> = ({
  allReady,
}) => {
  const { colors } = useTheme();

  const summaryStyle = {
    backgroundColor: allReady ? colors.success + '20' : colors.warning + '20',
    borderColor: allReady ? colors.success : colors.warning,
  };

  const iconColor = allReady ? colors.success : colors.warning;
  const Icon = allReady ? CheckCircle : AlertCircle;

  return (
    <View style={[styles.summary, summaryStyle]}>
      <View style={styles.summaryContent}>
        <Icon size={24} color={iconColor} />
        <View style={styles.summaryText}>
          <Text style={[styles.summaryTitle, { color: colors.text }]}>
            {allReady ? 'All Systems Ready' : 'System Check Required'}
          </Text>
          <Text style={[styles.summaryMessage, { color: colors.textSecondary }]}>
            {allReady
              ? 'All data sources are connected and ready for monitoring.'
              : 'Some systems need attention before data collection can begin.'}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  summary: {
    marginTop: 20,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  summaryContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  summaryText: {
    marginLeft: 12,
    flex: 1,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  summaryMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default DataReadinessSummary;