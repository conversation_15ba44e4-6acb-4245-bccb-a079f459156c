import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { RefreshCw } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface DataReadinessHeaderProps {
  isRefreshing: boolean;
  onRefresh: () => void;
}

/**
 * @magic_description Header component for data readiness check screen
 * Displays title and refresh button with loading state
 */
const DataReadinessHeader: React.FC<DataReadinessHeaderProps> = ({
  isRefreshing,
  onRefresh,
}) => {
  const { colors } = useTheme();

  return (
    <View style={styles.header}>
      <Text style={[styles.title, { color: colors.text }]}>
        Data Readiness Check
      </Text>
      <TouchableOpacity
        style={[styles.refreshButton, { backgroundColor: colors.primary }]}
        onPress={onRefresh}
        disabled={isRefreshing}
      >
        {isRefreshing ? (
          <ActivityIndicator size="small" color={colors.background} />
        ) : (
          <RefreshCw size={20} color={colors.background} />
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default DataReadinessHeader;