import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { 
  Brain, 
  Activity, 
  Heart, 
  Coffee, 
  ChevronRight,
  Lightbulb
} from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { HomeStackParamList } from '../../navigation';
import Card from '../ui/Card';
import { Animal } from '../../mocks/animals';

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

interface AIGuidanceComponentProps {
  animal: Animal;
  missingDataTypes: string[];
}

const AIGuidanceComponent: React.FC<AIGuidanceComponentProps> = ({
  animal,
  missingDataTypes
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  const navigation = useNavigation<NavigationProp>();

  const getGuidanceActions = () => {
    const actions = [];
    
    if (missingDataTypes.includes('training')) {
      actions.push({
        id: 'training',
        title: t('logTrainingSession'),
        description: t('recordTrainingActivities'),
        icon: Activity,
        color: colors.primary,
        onPress: () => navigation.navigate('LogTrainingSession', { animalId: animal.id })
      });
    }
    
    if (missingDataTypes.includes('vitals')) {
      actions.push({
        id: 'vitals',
        title: t('recordVitals'),
        description: t('logHealthMeasurements'),
        icon: Heart,
        color: '#EF4444',
        onPress: () => navigation.navigate('RecordVitals', { animalId: animal.id })
      });
    }
    
    if (missingDataTypes.includes('feeding')) {
      actions.push({
        id: 'feeding',
        title: t('updateFeedingSchedule'),
        description: t('setFeedingTimes'),
        icon: Coffee,
        color: '#F59E0B',
        onPress: () => navigation.navigate('FeedSchedule', { animalId: animal.id })
      });
    }
    
    return actions;
  };

  const actions = getGuidanceActions();

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
          <Brain size={32} color={colors.primary} />
        </View>
        <View style={styles.headerText}>
          <Text style={[styles.title, { color: colors.text }]}>
            {t('aiInsightsFor', { name: animal.name })}
          </Text>
          <Text style={[styles.subtitle, { color: colors.textLight }]}>
            {t('helpMeLearnAbout', { name: animal.name })}
          </Text>
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.explanationContainer}>
          <Lightbulb size={20} color={colors.primary} />
          <Text style={[styles.explanation, { color: colors.text }]}>
            {t('needMoreInformation', { name: animal.name })}
          </Text>
        </View>

        <View style={styles.actionsContainer}>
          {actions.map((action) => {
            const IconComponent = action.icon;
            return (
              <TouchableOpacity
                key={action.id}
                style={[
                  styles.actionButton,
                  { 
                    backgroundColor: colors.card,
                    borderColor: colors.border
                  }
                ]}
                onPress={action.onPress}
                activeOpacity={0.7}
              >
                <View style={styles.actionContent}>
                  <View style={[
                    styles.actionIcon,
                    { backgroundColor: action.color + '20' }
                  ]}>
                    <IconComponent size={20} color={action.color} />
                  </View>
                  
                  <View style={styles.actionText}>
                    <Text style={[styles.actionTitle, { color: colors.text }]}>
                      {action.title}
                    </Text>
                    <Text style={[styles.actionDescription, { color: colors.textLight }]}>
                      {action.description}
                    </Text>
                  </View>
                  
                  <ChevronRight size={20} color={colors.textLight} />
                </View>
              </TouchableOpacity>
            );
          })}
        </View>

        <View style={styles.benefitsContainer}>
          <Text style={[styles.benefitsTitle, { color: colors.text }]}>
            What you'll get:
          </Text>
          <View style={styles.benefitsList}>
            <Text style={[styles.benefitItem, { color: colors.textLight }]}>
              • Personalized health assessments
            </Text>
            <Text style={[styles.benefitItem, { color: colors.textLight }]}>
              • Training performance insights
            </Text>
            <Text style={[styles.benefitItem, { color: colors.textLight }]}>
              • Nutrition and care recommendations
            </Text>
            <Text style={[styles.benefitItem, { color: colors.textLight }]}>
              • Early health alerts and warnings
            </Text>
          </View>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  content: {
    gap: 20,
  },
  explanationContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    padding: 16,
    backgroundColor: '#F0F9FF',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#0EA5E9',
  },
  explanation: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  actionsContainer: {
    gap: 12,
  },
  actionButton: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  actionText: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  benefitsContainer: {
    padding: 16,
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
  },
  benefitsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  benefitsList: {
    gap: 4,
  },
  benefitItem: {
    fontSize: 13,
    lineHeight: 18,
  },
});

export default AIGuidanceComponent;