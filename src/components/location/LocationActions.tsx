import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { RefreshCw, Navigation, Bluetooth } from 'lucide-react-native';

interface LocationActionsProps {
  onUpdateLocation: () => void;
  onOpenInMaps: () => void;
  onConnectDevice: () => void;
  isUpdating: boolean;
  isLoading: boolean;
  hasLocation: boolean;
  connectedDevice: string | null;
  colors: {
    primary: string;
    secondary: string;
    accent3: string;
  };
}

const LocationActions: React.FC<LocationActionsProps> = ({
  onUpdateLocation,
  onOpenInMaps,
  onConnectDevice,
  isUpdating,
  isLoading,
  hasLocation,
  connectedDevice,
  colors
}) => {
  return (
    <View style={styles.actionsContainer}>
      <TouchableOpacity 
        style={[styles.actionButton, { backgroundColor: colors.primary }]}
        onPress={onUpdateLocation}
        disabled={isUpdating}
      >
        {isUpdating ? (
          <ActivityIndicator color="#FFF" size="small" />
        ) : (
          <>
            <RefreshCw size={20} color="#FFF" />
            <Text style={styles.actionButtonText}>Update Location</Text>
          </>
        )}
      </TouchableOpacity>
      
      {hasLocation && (
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: colors.secondary }]}
          onPress={onOpenInMaps}
        >
          <Navigation size={20} color="#FFF" />
          <Text style={styles.actionButtonText}>Open in Maps</Text>
        </TouchableOpacity>
      )}
      
      <TouchableOpacity 
        style={[styles.actionButton, { backgroundColor: colors.accent3 }]}
        onPress={onConnectDevice}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#FFF" size="small" />
        ) : (
          <>
            <Bluetooth size={20} color="#FFF" />
            <Text style={styles.actionButtonText}>
              {connectedDevice ? 'Change Device' : 'Connect Device'}
            </Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  actionsContainer: {
    gap: 12,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    padding: 14,
    gap: 8,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFF',
  },
});

export default LocationActions;