import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface AnimalLocationInfoProps {
  lastUpdated: string;
  connectedDevice: string | null;
  coordinates: {
    latitude: number;
    longitude: number;
  } | null;
  colors: {
    card: string;
    text: string;
    textLight: string;
  };
}

const AnimalLocationInfo: React.FC<AnimalLocationInfoProps> = ({
  lastUpdated,
  connectedDevice,
  coordinates,
  colors
}) => {
  return (
    <View style={styles.infoContainer}>
      <View style={[styles.infoCard, { backgroundColor: colors.card }]}>
        <Text style={[styles.infoLabel, { color: colors.textLight }]}>Last Updated</Text>
        <Text style={[styles.infoValue, { color: colors.text }]}>{lastUpdated}</Text>
      </View>
      
      <View style={[styles.infoCard, { backgroundColor: colors.card }]}>
        <Text style={[styles.infoLabel, { color: colors.textLight }]}>Connected Device</Text>
        <Text style={[styles.infoValue, { color: colors.text }]}>
          {connectedDevice || 'None'}
        </Text>
      </View>
      
      {coordinates && (
        <View style={[styles.infoCard, { backgroundColor: colors.card }]}>
          <Text style={[styles.infoLabel, { color: colors.textLight }]}>Coordinates</Text>
          <Text style={[styles.infoValue, { color: colors.text }]}>
            {coordinates.latitude.toFixed(6)}, {coordinates.longitude.toFixed(6)}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  infoContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginBottom: 16,
  },
  infoCard: {
    width: '48%',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default AnimalLocationInfo;