import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Image,
  Platform,
  Dimensions
} from 'react-native';
import { WebView } from 'react-native-webview';
import { MapPin } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

const { width, height } = Dimensions.get('window');

interface AnimalLocation {
  latitude: number;
  longitude: number;
  timestamp?: number | string;
}

interface MapViewComponentProps {
  animalLocation: AnimalLocation | null | undefined;
  animalName: string;
  colors: {
    background: string;
    text: string;
    textLight: string;
    card: string;
    border: string;
  };
  isDarkMode: boolean;
  onLocationUpdate?: (location: AnimalLocation) => void;
}

// Supabase project configuration
const SUPABASE_PROJECT_REF = 'hfqhqymuenbuzndkdcqf';
const EDGE_FUNCTION_URL = `https://${SUPABASE_PROJECT_REF}.supabase.co/functions/v1/secure-map-proxy`;

// Fallback map configuration
const FALLBACK_MAP_ENABLED = true;
const OPENSTREETMAP_TILE_URL = 'https://tile.openstreetmap.org';

// Map configuration
const MAP_CONFIG = {
  defaultZoom: 15,
  defaultWidth: 400,
  defaultHeight: 300,
  maxRetries: 2,
  retryDelay: 1000,
};

const MapViewComponent: React.FC<MapViewComponentProps> = ({
  animalLocation,
  animalName,
  colors,
  isDarkMode,
  onLocationUpdate
}) => {
  const webViewRef = useRef<WebView>(null);
  const [isMapLoading, setIsMapLoading] = useState(true);
  const [mapError, setMapError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Update map marker when location changes
  useEffect(() => {
    if (animalLocation && webViewRef.current && Platform.OS !== 'web') {
      const updateScript = `
        if (window.updateMarker) {
          window.updateMarker(${animalLocation.latitude}, ${animalLocation.longitude});
        }
      `;
      webViewRef.current.injectJavaScript(updateScript);
    }
  }, [animalLocation]);

  const getSecureMapUrl = (location: AnimalLocation): string => {
    const { latitude, longitude } = location;
    const zoom = MAP_CONFIG.defaultZoom;
    const width = MAP_CONFIG.defaultWidth;
    const height = MAP_CONFIG.defaultHeight;
    const mapType = isDarkMode ? 'dark' : 'roadmap';
    
    // Construct URL for secure Edge Function proxy
    const params = new URLSearchParams({
      latitude: latitude.toString(),
      longitude: longitude.toString(),
      zoom: zoom.toString(),
      width: width.toString(),
      height: height.toString(),
      maptype: mapType,
      animalName: animalName
    });
    
    return `${EDGE_FUNCTION_URL}?${params.toString()}`;
  };

  const getFallbackMapUrl = (location: AnimalLocation): string => {
    const { latitude, longitude } = location;
    const zoom = MAP_CONFIG.defaultZoom;
    
    // Use OpenStreetMap as fallback
    // This creates a simple static map using OSM tiles
    const tileX = Math.floor((longitude + 180) / 360 * Math.pow(2, zoom));
    const tileY = Math.floor((1 - Math.log(Math.tan(latitude * Math.PI / 180) + 1 / Math.cos(latitude * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, zoom));
    
    return `${OPENSTREETMAP_TILE_URL}/${zoom}/${tileX}/${tileY}.png`;
  };

  const handleMapError = (error: string) => {
    console.error('Map loading error:', error);
    setMapError(error);
    
    // Retry logic
    if (retryCount < MAP_CONFIG.maxRetries) {
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        setMapError(null);
      }, MAP_CONFIG.retryDelay);
    }
  };

  const generateSecureMapHTML = (location: AnimalLocation): string => {
    const { latitude, longitude } = location;
    const primaryMapUrl = getSecureMapUrl(location);
    const fallbackMapUrl = FALLBACK_MAP_ENABLED ? getFallbackMapUrl(location) : null;
    
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body, html {
              margin: 0;
              padding: 0;
              height: 100%;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background-color: ${colors.background};
            }
            .map-container {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
            }
            .map-image {
              max-width: 100%;
              max-height: 100%;
              border-radius: 8px;
              box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            .location-info {
              position: absolute;
              bottom: 10px;
              left: 10px;
              background: rgba(0,0,0,0.8);
              color: white;
              padding: 8px 12px;
              border-radius: 6px;
              font-size: 12px;
              backdrop-filter: blur(4px);
            }
            .animal-name {
              font-weight: bold;
              margin-bottom: 2px;
            }
            .coordinates {
              opacity: 0.9;
            }
            .error-message {
              color: ${colors.text};
              text-align: center;
              padding: 20px;
              background-color: ${colors.card};
              border-radius: 8px;
              margin: 20px;
            }
            .fallback-notice {
              position: absolute;
              top: 10px;
              right: 10px;
              background: rgba(255, 165, 0, 0.9);
              color: white;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 10px;
            }
          </style>
        </head>
        <body>
          <div class="map-container">
            <img 
              class="map-image" 
              src="${primaryMapUrl}" 
              alt="Map showing ${animalName}'s location"
              onerror="handleMapError(this)"
            />
            <div class="location-info">
              <div class="animal-name">${animalName}</div>
              <div class="coordinates">${latitude.toFixed(6)}, ${longitude.toFixed(6)}</div>
            </div>
          </div>
          <script>
            let fallbackUsed = false;
            
            function handleMapError(img) {
              console.log('Primary map failed, trying fallback...');
              
              if (!fallbackUsed && ${fallbackMapUrl ? 'true' : 'false'}) {
                fallbackUsed = true;
                img.src = '${fallbackMapUrl}';
                
                // Add fallback notice
                const container = img.parentElement;
                const notice = document.createElement('div');
                notice.className = 'fallback-notice';
                notice.textContent = 'Basic Map';
                container.appendChild(notice);
                
                // If fallback also fails, show error
                img.onerror = function() {
                  this.parentElement.innerHTML = '<div class="error-message">📍 Location: ${latitude.toFixed(4)}, ${longitude.toFixed(4)}<br/>Map temporarily unavailable</div>';
                };
              } else {
                img.parentElement.innerHTML = '<div class="error-message">📍 Location: ${latitude.toFixed(4)}, ${longitude.toFixed(4)}<br/>Map temporarily unavailable</div>';
              }
            }
            
            // Function to update map image (called from React Native)
            window.updateMarker = function(lat, lng) {
              const mapImage = document.querySelector('.map-image');
              const locationInfo = document.querySelector('.location-info');
              
              if (mapImage && locationInfo) {
                // Reset fallback state
                fallbackUsed = false;
                
                // Update image source with new coordinates
                const params = new URLSearchParams({
                  latitude: lat.toString(),
                  longitude: lng.toString(),
                  zoom: '${MAP_CONFIG.defaultZoom}',
                  width: '${MAP_CONFIG.defaultWidth}',
                  height: '${MAP_CONFIG.defaultHeight}',
                  maptype: '${isDarkMode ? 'dark' : 'roadmap'}',
                  animalName: '${animalName}'
                });
                
                mapImage.src = '${EDGE_FUNCTION_URL}?' + params.toString();
                mapImage.onerror = function() { handleMapError(this); };
                
                // Update location info
                locationInfo.innerHTML = \`
                  <div class="animal-name">${animalName}</div>
                  <div class="coordinates">\${lat.toFixed(6)}, \${lng.toFixed(6)}</div>
                \`;
              }
            };
          </script>
        </body>
      </html>
    `;
  };

  if (!animalLocation) {
    return (
      <View style={[styles.mapContainer, { backgroundColor: colors.card }]}>
        <View style={styles.noLocationContainer}>
          <MapPin size={48} color={colors.textLight} />
          <Text style={[styles.noLocationText, { color: colors.text }]}>No location data available</Text>
          <Text style={[styles.noLocationSubtext, { color: colors.textLight }]}>
            Connect a GPS device to track {animalName}'s location
          </Text>
        </View>
      </View>
    );
  }

  // Render error state if map failed and retries exhausted
  if (mapError && retryCount >= MAP_CONFIG.maxRetries) {
    return (
      <View style={[styles.mapContainer, { backgroundColor: colors.card }]}>
        <View style={styles.errorContainer}>
          <MapPin size={48} color={colors.error} />
          <Text style={[styles.errorTitle, { color: colors.error }]}>Map Unavailable</Text>
          <Text style={[styles.errorText, { color: colors.text }]}>
            📍 {animalLocation.latitude.toFixed(4)}, {animalLocation.longitude.toFixed(4)}
          </Text>
          <Text style={[styles.errorSubtext, { color: colors.textLight }]}>
            Location coordinates are available, but map visualization is temporarily unavailable.
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              setMapError(null);
              setRetryCount(0);
            }}
          >
            <Text style={[styles.retryButtonText, { color: colors.white }]}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.mapContainer, { backgroundColor: colors.card }]}>
      {Platform.OS === 'web' ? (
        <Image
          source={{ uri: getSecureMapUrl(animalLocation) }}
          style={styles.map}
          resizeMode="cover"
          onError={() => {
            handleMapError('Failed to load secure map image');
          }}
        />
      ) : (
        <WebView
          ref={webViewRef}
          source={{ html: generateSecureMapHTML(animalLocation) }}
          style={styles.map}
          onLoadEnd={() => setIsMapLoading(false)}
          renderLoading={() => (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.text} />
              <Text style={[styles.loadingText, { color: colors.text }]}>Loading map...</Text>
            </View>
          )}
          startInLoadingState={true}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          onError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            handleMapError(`WebView error: ${nativeEvent.description || 'Unknown error'}`);
          }}
        />
      )}
      
      {/* Show retry indicator */}
      {retryCount > 0 && retryCount < MAP_CONFIG.maxRetries && (
        <View style={styles.retryIndicator}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.retryText, { color: colors.primary }]}>Retrying... ({retryCount}/{MAP_CONFIG.maxRetries})</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  mapContainer: {
    height: height * 0.4,
    borderRadius: 12,
    overflow: 'hidden',
    marginHorizontal: 16,
    marginBottom: 16,
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  noLocationContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  noLocationText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  noLocationSubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  errorSubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  retryIndicator: {
    position: 'absolute',
    top: 10,
    right: 10,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  retryText: {
    marginLeft: 6,
    fontSize: 12,
    fontWeight: '500',
  },
});

export default MapViewComponent;