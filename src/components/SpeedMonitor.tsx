
import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { Animal } from '../mocks/animals';
import { useSpeedDisplay } from '../hooks/useSpeedDisplay';
import CompactSpeedView from './speedMonitor/CompactSpeedView';
import FullSpeedView from './speedMonitor/FullSpeedView';

interface SpeedMonitorProps {
  animal: Animal;
  compact?: boolean;
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

/**
 * SpeedMonitor component displays the current speed of an animal
 * @param animal - The animal object containing speed data
 * @param compact - Whether to display in compact mode (for home screen)
 * @param onRefresh - Function to refresh the speed data
 * @param isRefreshing - Whether the speed data is currently refreshing
 */
/**
 * @magic_description Speed monitoring component with compact and full display modes
 * Uses specialized hook for data processing and sub-components for rendering
 */
const SpeedMonitor: React.FC<SpeedMonitorProps> = ({ 
  animal, 
  compact = false,
  onRefresh,
  isRefreshing = false
}) => {
  const { colors, isDarkMode } = useTheme();
  
  // Get processed speed data from custom hook
  const speedData = useSpeedDisplay(animal, colors);
  
  // Render appropriate view based on compact prop
  if (compact) {
    return (
      <CompactSpeedView
        speedData={speedData}
        colors={colors}
        onRefresh={onRefresh}
        isRefreshing={isRefreshing}
      />
    );
  }
  
  return (
    <FullSpeedView
      speedData={speedData}
      colors={colors}
      isDarkMode={isDarkMode}
      onRefresh={onRefresh}
      isRefreshing={isRefreshing}
    />
  );
};



export default SpeedMonitor;
