
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Clock, Bell, Pill, Check, X } from 'lucide-react-native';
import { MedicationRecord } from '../mocks/medications';
import { useTheme } from '../contexts/ThemeContext';

interface MedicationCardProps {
  medication: MedicationRecord;
  onPress?: () => void;
  onToggleComplete?: (completed: boolean) => void;
}

const MedicationCard: React.FC<MedicationCardProps> = ({ 
  medication, 
  onPress,
  onToggleComplete
}) => {
  const { colors, isDarkMode } = useTheme();
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };
  
  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    
    return date.toLocaleString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };
  
  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.card,
      borderRadius: 16,
      padding: 16,
      marginBottom: 16,
      shadowColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    completedContainer: {
      opacity: 0.7,
      backgroundColor: colors.successLight,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    name: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginLeft: 8,
      flex: 1,
    },
    completedText: {
      textDecorationLine: 'line-through',
      color: colors.textSecondary,
    },
    completeButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
    },
    completedButton: {
      backgroundColor: colors.success,
    },
    incompleteButton: {
      backgroundColor: colors.background,
      borderWidth: 1,
      borderColor: colors.border,
    },
    detailsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    dosageContainer: {
      flex: 1,
    },
    dosageLabel: {
      fontSize: 12,
      color: colors.textSecondary,
      marginBottom: 2,
    },
    dosage: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
    },
    frequencyContainer: {
      flex: 1,
    },
    frequencyLabel: {
      fontSize: 12,
      color: colors.textSecondary,
      marginBottom: 2,
    },
    frequency: {
      fontSize: 14,
      fontWeight: '500',
      color: colors.text,
    },
    timeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    time: {
      fontSize: 14,
      color: colors.primary,
      marginLeft: 6,
      fontWeight: '500',
    },
    reminderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.secondaryLight,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginLeft: 12,
    },
    reminderText: {
      fontSize: 12,
      color: colors.secondary,
      marginLeft: 4,
    },
    dateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    dateLabel: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    date: {
      fontSize: 12,
      color: colors.text,
      fontWeight: '500',
    },
    notes: {
      fontSize: 14,
      color: colors.textSecondary,
      fontStyle: 'italic',
      marginTop: 8,
    },
  });

  return (
    <TouchableOpacity 
      style={[
        styles.container,
        medication.completed && styles.completedContainer
      ]}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={!onPress}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Pill 
            size={18} 
            color={medication.completed ? colors.success : colors.primary} 
          />
          <Text style={[
            styles.name,
            medication.completed && styles.completedText
          ]}>
            {medication.medicationName}
          </Text>
        </View>
        
        {onToggleComplete && (
          <TouchableOpacity
            style={[
              styles.completeButton,
              medication.completed ? styles.completedButton : styles.incompleteButton
            ]}
            onPress={() => onToggleComplete(!medication.completed)}
          >
            {medication.completed ? (
              <Check size={16} color={colors.white} />
            ) : (
              <X size={16} color={colors.textSecondary} />
            )}
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.detailsContainer}>
        <View style={styles.dosageContainer}>
          <Text style={styles.dosageLabel}>Dosage:</Text>
          <Text style={styles.dosage}>
            {medication.dosage} {medication.dosageUnit}
          </Text>
        </View>
        
        <View style={styles.frequencyContainer}>
          <Text style={styles.frequencyLabel}>Frequency:</Text>
          <Text style={styles.frequency}>{medication.frequency}</Text>
        </View>
      </View>
      
      <View style={styles.timeContainer}>
        <Clock size={14} color={colors.textSecondary} />
        <Text style={styles.time}>{formatTime(medication.time)}</Text>
        
        {medication.reminder && (
          <View style={styles.reminderContainer}>
            <Bell size={12} color={colors.secondary} />
            <Text style={styles.reminderText}>
              Reminder set
            </Text>
          </View>
        )}
      </View>
      
      <View style={styles.dateContainer}>
        <Text style={styles.dateLabel}>Start: </Text>
        <Text style={styles.date}>{formatDate(medication.startDate)}</Text>
        
        {medication.endDate && (
          <>
            <Text style={styles.dateLabel}> End: </Text>
            <Text style={styles.date}>{formatDate(medication.endDate)}</Text>
          </>
        )}
      </View>
      
      {medication.notes && (
        <Text style={styles.notes}>{medication.notes}</Text>
      )}
    </TouchableOpacity>
  );
};



export default MedicationCard;
