import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList
} from 'react-native';
import {
  Lightbulb,
  AlertTriangle,
  TrendingUp,
  ChevronRight,
  Clock
} from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface HealthInsight {
  id: string;
  type: 'recommendation' | 'alert' | 'trend';
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  created_at: string;
  action_required: boolean;
}

interface AIHealthInsightsListProps {
  insights: HealthInsight[];
  onInsightPress: (insight: HealthInsight) => void;
}

const AIHealthInsightsList: React.FC<AIHealthInsightsListProps> = ({
  insights,
  onInsightPress
}) => {
  const { theme } = useTheme();
  const { t } = useLanguage();

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'recommendation':
        return Lightbulb;
      case 'alert':
        return AlertTriangle;
      case 'trend':
        return TrendingUp;
      default:
        return Lightbulb;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return '#EF4444';
      case 'high':
        return '#F59E0B';
      case 'medium':
        return '#3B82F6';
      case 'low':
        return '#10B981';
      default:
        return theme.textSecondary;
    }
  };

  const renderInsight = ({ item }: { item: HealthInsight }) => {
    const IconComponent = getInsightIcon(item.type);
    const severityColor = getSeverityColor(item.severity);

    return (
      <TouchableOpacity
        style={[styles.insightCard, { backgroundColor: theme.surface }]}
        onPress={() => onInsightPress(item)}
      >
        <View style={styles.insightHeader}>
          <View style={styles.insightIconContainer}>
            <IconComponent size={20} color={severityColor} />
          </View>
          <View style={styles.insightContent}>
            <Text style={[styles.insightTitle, { color: theme.text }]}>
              {item.title}
            </Text>
            <Text style={[styles.insightDescription, { color: theme.textSecondary }]}>
              {item.description}
            </Text>
            <View style={styles.insightMeta}>
              <Clock size={12} color={theme.textSecondary} />
              <Text style={[styles.insightTime, { color: theme.textSecondary }]}>
                {format(new Date(item.created_at), 'MMM d, h:mm a')}
              </Text>
              {item.action_required && (
                <View style={[styles.actionBadge, { backgroundColor: severityColor }]}>
                  <Text style={styles.actionBadgeText}>Action Required</Text>
                </View>
              )}
            </View>
          </View>
          <ChevronRight size={16} color={theme.textSecondary} />
        </View>
      </TouchableOpacity>
    );
  };

  if (insights.length === 0) {
    return (
      <Card style={styles.emptyContainer}>
        <Lightbulb size={48} color={theme.textSecondary} />
        <Text style={[styles.emptyTitle, { color: theme.text }]}>
          No Insights Available
        </Text>
        <Text style={[styles.emptyDescription, { color: theme.textSecondary }]}>
          AI insights will appear here as we analyze your animal's health data.
        </Text>
      </Card>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={[styles.sectionTitle, { color: theme.text }]}>
        Recent Insights
      </Text>
      <FlatList
        data={insights}
        renderItem={renderInsight}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  insightCard: {
    borderRadius: 12,
    marginBottom: 8,
    padding: 16,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  insightIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  insightDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  insightMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  insightTime: {
    fontSize: 12,
    marginLeft: 4,
    marginRight: 8,
  },
  actionBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  actionBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default AIHealthInsightsList;