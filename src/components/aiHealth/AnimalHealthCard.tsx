import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { Brain, AlertTriangle, Shield, Activity } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Animal } from '../../mocks/animals';
import Card from '../ui/Card';

interface AnimalHealthCardProps {
  animal: Animal;
  healthScore?: { overall_score: number };
  alertsCount: number;
  criticalAlertsCount: number;
  highRisksCount: number;
  trendsCount: number;
  onViewDetails: (animalId: string) => void;
  onViewAIInsights: (animalId: string) => void;
}

const AnimalHealthCard: React.FC<AnimalHealthCardProps> = ({
  animal,
  healthScore,
  alertsCount,
  criticalAlertsCount,
  highRisksCount,
  trendsCount,
  onViewDetails,
  onViewAIInsights
}) => {
  const { colors } = useTheme();
  const screenWidth = Dimensions.get('window').width;

  const getScoreStatusColor = (score: number) => {
    if (score >= 85) return '#10B981';
    if (score >= 70) return '#F59E0B';
    if (score >= 50) return '#EF4444';
    return '#6B7280';
  };

  const renderHealthScoreGauge = (score: number, size: number = 50) => {
    return (
      <View style={[styles.gaugeContainer, { width: size, height: size }]}>
        <View style={styles.gaugeBackground}>
          <Text style={[styles.gaugeScore, { color: getScoreStatusColor(score) }]}>
            {score}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <Card style={[styles.animalCard, { width: screenWidth * 0.85 }]}>
      <TouchableOpacity 
        style={styles.animalCardContent}
        onPress={() => onViewDetails(animal.id)}
      >
        {/* Animal Header */}
        <View style={styles.animalHeader}>
          <View style={styles.animalInfo}>
            <Text style={[styles.animalName, { color: colors.text }]}>
              {animal.name}
            </Text>
            <Text style={[styles.animalBreed, { color: colors.textLight }]}>
              {animal.breed} • {animal.age} years
            </Text>
          </View>
          
          {/* Health Score Gauge */}
          {healthScore && renderHealthScoreGauge(healthScore.overall_score, 50)}
        </View>
        
        {/* Quick Stats Row */}
        <View style={styles.quickStatsRow}>
          <View style={styles.statItem}>
            <AlertTriangle 
              size={16} 
              color={criticalAlertsCount > 0 ? '#EF4444' : colors.textLight} 
            />
            <Text style={[styles.statNumber, { color: colors.text }]}>
              {alertsCount}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textLight }]}>
              Alerts
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Shield 
              size={16} 
              color={highRisksCount > 0 ? '#F59E0B' : colors.textLight} 
            />
            <Text style={[styles.statNumber, { color: colors.text }]}>
              {highRisksCount}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textLight }]}>
              Risks
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Activity 
              size={16} 
              color={trendsCount > 0 ? colors.primary : colors.textLight} 
            />
            <Text style={[styles.statNumber, { color: colors.text }]}>
              {trendsCount}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textLight }]}>
              Trends
            </Text>
          </View>
          
          <TouchableOpacity 
            style={styles.aiInsightsButton}
            onPress={() => onViewAIInsights(animal.id)}
          >
            <Brain size={16} color={colors.primary} />
            <Text style={[styles.aiInsightsText, { color: colors.primary }]}>
              AI
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Status Indicators */}
        <View style={styles.statusIndicators}>
          {criticalAlertsCount > 0 && (
            <View style={[styles.statusBadge, { backgroundColor: '#EF4444' }]}>
              <Text style={[styles.statusBadgeText, { color: colors.white }]}>Critical Alert</Text>
            </View>
          )}
          {highRisksCount > 0 && (
            <View style={[styles.statusBadge, { backgroundColor: '#F59E0B' }]}>
              <Text style={[styles.statusBadgeText, { color: colors.white }]}>High Risk</Text>
            </View>
          )}
          {healthScore && healthScore.overall_score >= 85 && alertsCount === 0 && (
            <View style={[styles.statusBadge, { backgroundColor: '#10B981' }]}>
              <Text style={[styles.statusBadgeText, { color: colors.white }]}>Excellent Health</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Card>
  );
};

const styles = StyleSheet.create({
  animalCard: {
    padding: 16,
    marginBottom: 8,
  },
  animalCardContent: {
    flex: 1,
  },
  animalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  animalInfo: {
    flex: 1,
  },
  animalName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  animalBreed: {
    fontSize: 12,
  },
  gaugeContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  gaugeBackground: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  gaugeScore: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  quickStatsRow: {
    flexDirection: 'row',
    marginVertical: 8,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 4,
  },
  statLabel: {
    fontSize: 10,
    marginTop: 2,
  },
  aiInsightsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#F0F9FF',
  },
  aiInsightsText: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  statusIndicators: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginTop: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
});

export default AnimalHealthCard;