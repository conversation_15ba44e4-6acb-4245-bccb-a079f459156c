import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { AlertTriangle, ChevronRight } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface PriorityAlertsCardProps {
  criticalAlertsCount: number;
  onReviewAlerts: () => void;
}

const PriorityAlertsCard: React.FC<PriorityAlertsCardProps> = ({
  criticalAlertsCount,
  onReviewAlerts
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  if (criticalAlertsCount === 0) {
    return null;
  }

  return (
    <Card style={[styles.priorityAlertsCard, { borderLeftColor: '#EF4444' }]}>
      <View style={styles.priorityHeader}>
        <AlertTriangle size={20} color="#EF4444" />
        <Text style={[styles.priorityTitle, { color: '#EF4444' }]}>
          {t('criticalAlertsRequireAttention')}
        </Text>
      </View>
      <Text style={[styles.priorityDescription, { color: colors.textLight }]}>
        {criticalAlertsCount} {t('criticalAlertsNeedImmediate')}
      </Text>
      <TouchableOpacity style={styles.priorityAction} onPress={onReviewAlerts}>
        <Text style={[styles.priorityActionText, { color: colors.primary }]}>
          {t('reviewAlerts')}
        </Text>
        <ChevronRight size={16} color={colors.primary} />
      </TouchableOpacity>
    </Card>
  );
};

const styles = StyleSheet.create({
  priorityAlertsCard: {
    marginBottom: 16,
    padding: 16,
    borderLeftWidth: 4,
  },
  priorityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  priorityTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  priorityDescription: {
    fontSize: 12,
    marginBottom: 12,
  },
  priorityAction: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  priorityActionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default PriorityAlertsCard;