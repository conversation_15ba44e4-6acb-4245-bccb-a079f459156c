import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Heart, RefreshCw, Brain, TrendingUp, Shield, Activity, BarChart3, AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { HealthStackParamList } from '../../navigation';
import Card from '../ui/Card';

interface QuickActionsGridProps {
  onRefreshData: () => void;
  isRefreshing?: boolean;
}

type HealthNavigationProp = NativeStackNavigationProp<HealthStackParamList>;

const QuickActionsGrid: React.FC<QuickActionsGridProps> = ({
  onRefreshData,
  isRefreshing = false
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  const navigation = useNavigation<HealthNavigationProp>();

  const quickActions = [
    {
      id: 'refresh',
      title: isRefreshing ? t('common.refreshing') : t('common.refresh'),
      icon: RefreshCw,
      color: '#10B981',
      onPress: onRefreshData,
      disabled: isRefreshing,
    },
    {
      id: 'health_trends',
      title: t('ai.healthTrends'),
      icon: TrendingUp,
      color: '#45B7D1',
      onPress: () => navigation.navigate('HealthTrends'),
    },
    {
      id: 'disease_risk',
      title: t('ai.diseaseRisk'),
      icon: Shield,
      color: '#FF6B6B',
      onPress: () => navigation.navigate('DiseaseRisk'),
    },
    {
      id: 'stress_analysis',
      title: t('ai.stressAnalysis'),
      icon: Brain,
      color: '#9B59B6',
      onPress: () => navigation.navigate('StressAnalysis'),
    },
    {
      id: 'sleep_monitoring',
      title: t('ai.sleepMonitoring'),
      icon: Activity,
      color: '#4ECDC4',
      onPress: () => navigation.navigate('SleepMonitoring'),
    },
    {
      id: 'environmental',
      title: t('ai.environmental'),
      icon: BarChart3,
      color: '#F39C12',
      onPress: () => navigation.navigate('EnvironmentalAnalysis'),
    },
    {
      id: 'predictive',
      title: t('ai.predictiveInsights'),
      icon: AlertTriangle,
      color: '#E74C3C',
      onPress: () => navigation.navigate('PredictiveInsights'),
    },
    {
      id: 'ai_assistant',
      title: t('ai.assistant'),
      icon: Brain,
      color: '#8E44AD',
      onPress: () => navigation.navigate('AIAssistant'),
    },
  ];

  return (
    <Card style={styles.sectionCard}>
      <Text style={[styles.sectionTitle, { color: colors.text, marginBottom: 16 }]}>
        {t('ai.quickActions')}
      </Text>
      
      <View style={styles.quickActionsGrid}>
        {quickActions.map((action) => (
          <TouchableOpacity
            key={action.id}
            style={[
              styles.quickActionButton, 
              { 
                backgroundColor: action.color,
                opacity: action.disabled ? 0.7 : 1
              }
            ]}
            onPress={action.onPress}
            disabled={action.disabled}
            activeOpacity={0.8}
          >
            <action.icon size={18} color="#FFFFFF" />
            <Text style={[styles.quickActionText, { color: colors.white }]} numberOfLines={2}>
              {action.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  sectionCard: {
    padding: 16,
    marginVertical: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickActionButton: {
    flex: 1,
    minWidth: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderRadius: 8,
    gap: 6,
    minHeight: 44,
  },
  quickActionText: {
    fontSize: 11,
    fontWeight: '600',
    textAlign: 'center',
    flex: 1,
  },
});

export default QuickActionsGrid;