import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Eye, Heart, Shield } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface HealthInsightsCardProps {
  averageHealthScore: number;
  highRiskFactorsCount: number;
  onViewDetails: () => void;
}

const HealthInsightsCard: React.FC<HealthInsightsCardProps> = ({
  averageHealthScore,
  highRiskFactorsCount,
  onViewDetails
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  return (
    <Card style={styles.sectionCard}>
      <View style={styles.sectionHeader}>
        <View style={styles.sectionTitleContainer}>
          <Eye size={20} color={colors.primary} />
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('healthInsights')}
          </Text>
        </View>
        <TouchableOpacity onPress={onViewDetails}>
          <Text style={[styles.viewAllLink, { color: colors.primary }]}>
            {t('viewDetails')}
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.insightsGrid}>
        {/* Health Score Distribution */}
        <View style={styles.insightCard}>
          <View style={styles.insightHeader}>
            <Heart size={16} color="#10B981" />
            <Text style={[styles.insightTitle, { color: colors.text }]}>
              {t('avgHealthScore')}
            </Text>
          </View>
          <Text style={[styles.insightValue, { color: '#10B981' }]}>
            {averageHealthScore > 0 ? averageHealthScore : '--'}
          </Text>
          <Text style={[styles.insightSubtitle, { color: colors.textLight }]}>
            {t('acrossAllAnimals')}
          </Text>
        </View>
        
        {/* Risk Assessment */}
        <View style={styles.insightCard}>
          <View style={styles.insightHeader}>
            <Shield size={16} color="#F59E0B" />
            <Text style={[styles.insightTitle, { color: colors.text }]}>
              {t('riskFactors')}
            </Text>
          </View>
          <Text style={[styles.insightValue, { color: '#F59E0B' }]}>
            {highRiskFactorsCount}
          </Text>
          <Text style={[styles.insightSubtitle, { color: colors.textLight }]}>
            {t('highRiskFactors')}
          </Text>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  sectionCard: {
    padding: 16,
    marginVertical: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  viewAllLink: {
    fontSize: 14,
    fontWeight: '500',
  },
  insightsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  insightCard: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  insightTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 6,
  },
  insightValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  insightSubtitle: {
    fontSize: 10,
  },
});

export default HealthInsightsCard;