import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { Brain, Heart, AlertTriangle, Zap } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface OverallHealthSummaryProps {
  stableHealth: { healthy: number; total: number };
  totalAlerts: number;
  criticalAlerts: number;
  totalAnimals: number;
  isLoading?: boolean;
}

const OverallHealthSummary: React.FC<OverallHealthSummaryProps> = ({
  stableHealth,
  totalAlerts,
  criticalAlerts,
  totalAnimals,
  isLoading = false
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  return (
    <Card style={styles.summaryCard}>
      <View style={styles.summaryHeader}>
        <Brain size={24} color={colors.primary} />
        <Text style={[styles.summaryTitle, { color: colors.text }]}>
          {t('stableOverview')}
        </Text>
        {isLoading && (
          <ActivityIndicator 
            size="small" 
            color={colors.primary} 
            style={styles.loadingIndicator} 
          />
        )}
      </View>
      
      <View style={styles.summaryStats}>
        <View style={styles.summaryStatItem}>
          <View style={[styles.summaryStatCircle, { backgroundColor: '#10B981' }]}>
            <Heart size={20} color="#FFFFFF" />
          </View>
          <Text style={[styles.summaryStatNumber, { color: colors.text }]}>
            {stableHealth.healthy}/{stableHealth.total}
          </Text>
          <Text style={[styles.summaryStatLabel, { color: colors.textLight }]}>
            {t('healthyAnimals')}
          </Text>
        </View>
        
        <View style={styles.summaryStatItem}>
          <View style={[styles.summaryStatCircle, { backgroundColor: criticalAlerts > 0 ? '#EF4444' : '#F59E0B' }]}>
            <AlertTriangle size={20} color="#FFFFFF" />
          </View>
          <Text style={[styles.summaryStatNumber, { color: colors.text }]}>
            {totalAlerts}
          </Text>
          <Text style={[styles.summaryStatLabel, { color: colors.textLight }]}>
            {t('activeAlerts')}
          </Text>
        </View>
        
        <View style={styles.summaryStatItem}>
          <View style={[styles.summaryStatCircle, { backgroundColor: colors.primary }]}>
            <Zap size={20} color="#FFFFFF" />
          </View>
          <Text style={[styles.summaryStatNumber, { color: colors.text }]}>
            {totalAnimals}
          </Text>
          <Text style={[styles.summaryStatLabel, { color: colors.textLight }]}>
            {t('totalAnimals')}
          </Text>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    marginBottom: 20,
    padding: 20,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 8,
    flex: 1,
  },
  loadingIndicator: {
    marginLeft: 8,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryStatItem: {
    alignItems: 'center',
    flex: 1,
  },
  summaryStatCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryStatNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  summaryStatLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default OverallHealthSummary;