import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator
} from 'react-native';
import { Activity, Heart, Brain, TrendingUp } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import Card from '../ui/Card';

interface RecentActivityCardProps {
  healthScoresCount: number;
  assessmentsCount: number;
  trendsCount: number;
  isLoading?: boolean;
}

const RecentActivityCard: React.FC<RecentActivityCardProps> = ({
  healthScoresCount,
  assessmentsCount,
  trendsCount,
  isLoading = false
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  return (
    <Card style={styles.sectionCard}>
      <View style={styles.sectionHeader}>
        <View style={styles.sectionTitleContainer}>
          <Activity size={20} color={colors.primary} />
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('recentActivity')}
          </Text>
        </View>
        {isLoading && <ActivityIndicator size="small" color={colors.primary} />}
      </View>
      
      {/* Activity Summary */}
      <View style={styles.activitySummary}>
        <View style={styles.activityItem}>
          <View style={[styles.activityIcon, { backgroundColor: '#10B981' }]}>
            <Heart size={16} color="#FFFFFF" />
          </View>
          <View style={styles.activityContent}>
            <Text style={[styles.activityTitle, { color: colors.text }]}>
              {t('vitalsRecorded')}
            </Text>
            <Text style={[styles.activitySubtitle, { color: colors.textLight }]}>
              {healthScoresCount} {t('recordsToday')}
            </Text>
          </View>
        </View>
        
        <View style={styles.activityItem}>
          <View style={[styles.activityIcon, { backgroundColor: colors.primary }]}>
            <Brain size={16} color="#FFFFFF" />
          </View>
          <View style={styles.activityContent}>
            <Text style={[styles.activityTitle, { color: colors.text }]}>
              {t('aiAnalysisCompleted')}
            </Text>
            <Text style={[styles.activitySubtitle, { color: colors.textLight }]}>
              {assessmentsCount} {t('assessments')}
            </Text>
          </View>
        </View>
        
        <View style={styles.activityItem}>
          <View style={[styles.activityIcon, { backgroundColor: '#F59E0B' }]}>
            <TrendingUp size={16} color="#FFFFFF" />
          </View>
          <View style={styles.activityContent}>
            <Text style={[styles.activityTitle, { color: colors.text }]}>
              {t('trendsIdentified')}
            </Text>
            <Text style={[styles.activitySubtitle, { color: colors.textLight }]}>
              {trendsCount} {t('patterns')}
            </Text>
          </View>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  sectionCard: {
    padding: 16,
    marginVertical: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  activitySummary: {
    gap: 12,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  activitySubtitle: {
    fontSize: 12,
  },
});

export default RecentActivityCard;