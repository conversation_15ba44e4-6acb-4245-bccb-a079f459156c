import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image
} from 'react-native';
import {
  Heart,
  AlertTriangle,
  TrendingUp,
  Brain,
  ChevronRight
} from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Animal } from '../../mocks/animals';
import { HealthScore, SmartHealthAlert } from '../../store/aiHealthStore';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface AnimalHealthSummaryCardProps {
  animal: Animal;
  healthScore?: HealthScore;
  alertsCount: number;
  criticalAlertsCount: number;
  onPress: () => void;
}

const AnimalHealthSummaryCard: React.FC<AnimalHealthSummaryCardProps> = ({
  animal,
  healthScore,
  alertsCount,
  criticalAlertsCount,
  onPress
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  const getHealthStatusColor = (score: number) => {
    if (score >= 90) return '#10B981'; // Green
    if (score >= 75) return '#3B82F6'; // Blue
    if (score >= 60) return '#F59E0B'; // Yellow
    if (score >= 40) return '#EF4444'; // Red
    return '#DC2626'; // Dark Red
  };
  
  const getHealthStatusText = (score: number) => {
    if (score >= 90) return t('excellent');
    if (score >= 75) return t('good');
    if (score >= 60) return t('fair');
    if (score >= 40) return t('poor');
    return t('critical');
  };
  
  const score = healthScore?.overall_score || 0;
  const statusColor = getHealthStatusColor(score);
  const statusText = getHealthStatusText(score);
  
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      <Card style={[styles.container, { backgroundColor: colors.card }]}>
        <View style={styles.header}>
          {/* Animal Image */}
          <View style={styles.imageContainer}>
            {animal.imageUrl ? (
              <Image
                source={{ uri: animal.imageUrl }}
                style={styles.animalImage}
                resizeMode="cover"
              />
            ) : (
              <View style={[styles.placeholderImage, { backgroundColor: colors.border }]}>
                <Heart size={20} color={colors.textLight} />
              </View>
            )}
          </View>
          
          {/* Animal Info */}
          <View style={styles.animalInfo}>
            <Text style={[styles.animalName, { color: colors.text }]} numberOfLines={1}>
              {animal.name}
            </Text>
            <Text style={[styles.animalBreed, { color: colors.textLight }]} numberOfLines={1}>
              {animal.breed} • {animal.age} years
            </Text>
            
            {/* Health Status */}
            <View style={styles.statusContainer}>
              <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
              <Text style={[styles.statusText, { color: statusColor }]}>
                {statusText}
              </Text>
            </View>
          </View>
          
          {/* Health Score */}
          <View style={styles.scoreContainer}>
            <View style={[styles.scoreCircle, { borderColor: statusColor }]}>
              <Text style={[styles.scoreValue, { color: statusColor }]}>
                {score}
              </Text>
            </View>
            <Text style={[styles.scoreLabel, { color: colors.textLight }]}>{t('score')}</Text>
          </View>
          
          {/* Navigation Arrow */}
          <ChevronRight size={20} color={colors.textLight} style={styles.arrow} />
        </View>
        
        {/* Health Metrics */}
        <View style={styles.metricsContainer}>
          <View style={styles.metricItem}>
            <Brain size={16} color={colors.primary} />
            <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('aiAnalysis')}</Text>
            <Text style={[styles.metricValue, { color: colors.text }]}>{t('active')}</Text>
          </View>
          
          <View style={styles.metricItem}>
            <AlertTriangle size={16} color={alertsCount > 0 ? '#EF4444' : colors.textLight} />
            <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('alerts')}</Text>
            <Text style={[styles.metricValue, { color: alertsCount > 0 ? '#EF4444' : colors.text }]}>
              {alertsCount}
            </Text>
          </View>
          
          <View style={styles.metricItem}>
            <TrendingUp size={16} color={colors.success} />
            <Text style={[styles.metricLabel, { color: colors.textLight }]}>{t('trends')}</Text>
            <Text style={[styles.metricValue, { color: colors.text }]}>{t('tracked')}</Text>
          </View>
        </View>
        
        {/* Critical Alerts Banner */}
        {criticalAlertsCount > 0 && (
          <View style={[styles.criticalBanner, { backgroundColor: '#FEF2F2', borderColor: '#EF4444' }]}>
            <AlertTriangle size={14} color="#EF4444" />
            <Text style={[styles.criticalText, { color: '#EF4444' }]}>
              {criticalAlertsCount} {t('criticalAlertsRequireAttention')}
            </Text>
          </View>
        )}
        
        {/* Last Updated */}
        {healthScore && (
          <Text style={[styles.lastUpdated, { color: colors.textLight }]}>
            {t('lastUpdated')} {format(new Date(healthScore.created_at), 'MMM d, h:mm a')}
          </Text>
        )}
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 16
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  imageContainer: {
    marginRight: 12
  },
  animalImage: {
    width: 48,
    height: 48,
    borderRadius: 24
  },
  placeholderImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center'
  },
  animalInfo: {
    flex: 1
  },
  animalName: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 2
  },
  animalBreed: {
    fontSize: 12,
    marginBottom: 6
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 6
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600'
  },
  scoreContainer: {
    alignItems: 'center',
    marginRight: 12
  },
  scoreCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4
  },
  scoreValue: {
    fontSize: 14,
    fontWeight: '700'
  },
  scoreLabel: {
    fontSize: 10
  },
  arrow: {
    marginLeft: 8
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    marginBottom: 12
  },
  metricItem: {
    alignItems: 'center',
    flex: 1
  },
  metricLabel: {
    fontSize: 10,
    marginTop: 4,
    marginBottom: 2
  },
  metricValue: {
    fontSize: 12,
    fontWeight: '600'
  },
  criticalBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8
  },
  criticalText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6
  },
  lastUpdated: {
    fontSize: 10,
    textAlign: 'center'
  }
});

export default AnimalHealthSummaryCard;