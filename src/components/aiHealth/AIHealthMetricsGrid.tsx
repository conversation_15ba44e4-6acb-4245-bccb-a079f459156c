import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList
} from 'react-native';
import {
  Heart,
  AlertTriangle,
  TrendingUp,
  Shield,
  Activity,
  Eye,
  ChevronRight
} from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { HealthScore, SmartHealthAlert, DiseaseRiskAssessment } from '../../store/aiHealthStore';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface AIHealthMetricsGridProps {
  healthScore: HealthScore | null;
  alerts: SmartHealthAlert[];
  riskAssessments: DiseaseRiskAssessment[];
  onViewHealthScore: () => void;
  onViewAlerts: () => void;
  onViewRisks: () => void;
}

const AIHealthMetricsGrid: React.FC<AIHealthMetricsGridProps> = ({
  healthScore,
  alerts,
  riskAssessments,
  onViewHealthScore,
  onViewAlerts,
  onViewRisks
}) => {
  const { theme } = useTheme();
  const { t } = useLanguage();

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#10B981';
    if (score >= 60) return '#F59E0B';
    return '#EF4444';
  };

  const getScoreStatus = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    return 'Needs Attention';
  };

  const criticalAlerts = alerts.filter(alert => alert.severity === 'critical').length;
  const highRisks = riskAssessments.filter(risk => risk.risk_level === 'high').length;

  const metrics = [
    {
      id: 'health_score',
      title: 'Health Score',
      value: healthScore?.overall_score || 0,
      subtitle: healthScore ? getScoreStatus(healthScore.overall_score) : 'No data',
      icon: Heart,
      color: healthScore ? getScoreColor(healthScore.overall_score) : theme.textSecondary,
      onPress: onViewHealthScore,
    },
    {
      id: 'alerts',
      title: 'Active Alerts',
      value: alerts.length,
      subtitle: criticalAlerts > 0 ? `${criticalAlerts} critical` : 'All clear',
      icon: AlertTriangle,
      color: criticalAlerts > 0 ? '#EF4444' : '#10B981',
      onPress: onViewAlerts,
    },
    {
      id: 'risks',
      title: 'Risk Assessments',
      value: riskAssessments.length,
      subtitle: highRisks > 0 ? `${highRisks} high risk` : 'Low risk',
      icon: Shield,
      color: highRisks > 0 ? '#F59E0B' : '#10B981',
      onPress: onViewRisks,
    },
    {
      id: 'trends',
      title: 'Health Trends',
      value: '7d',
      subtitle: 'Improving',
      icon: TrendingUp,
      color: '#10B981',
      onPress: () => {}, // TODO: Implement trends view
    },
  ];

  const renderMetricCard = ({ item }: { item: typeof metrics[0] }) => (
    <TouchableOpacity
      style={[styles.metricCard, { backgroundColor: theme.surface }]}
      onPress={item.onPress}
    >
      <View style={styles.metricHeader}>
        <item.icon size={24} color={item.color} />
        <ChevronRight size={16} color={theme.textSecondary} />
      </View>
      
      <View style={styles.metricContent}>
        <Text style={[styles.metricValue, { color: item.color }]}>
          {typeof item.value === 'number' && item.id === 'health_score' 
            ? `${item.value}%` 
            : item.value}
        </Text>
        <Text style={[styles.metricTitle, { color: theme.text }]}>
          {item.title}
        </Text>
        <Text style={[styles.metricSubtitle, { color: theme.textSecondary }]}>
          {item.subtitle}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={metrics}
        renderItem={renderMetricCard}
        keyExtractor={(item) => item.id}
        numColumns={2}
        columnWrapperStyle={styles.row}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  metricCard: {
    flex: 0.48,
    padding: 16,
    borderRadius: 12,
    minHeight: 120,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  metricContent: {
    flex: 1,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  metricTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  metricSubtitle: {
    fontSize: 12,
  },
});

export default AIHealthMetricsGrid;