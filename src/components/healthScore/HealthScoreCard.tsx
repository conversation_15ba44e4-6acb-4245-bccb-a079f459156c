import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { Heart, TrendingUp, RefreshCw, ChevronRight } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { HealthScore } from '../../store/aiHealthStore';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface HealthScoreCardProps {
  healthScore: HealthScore | null;
  isLoading: boolean;
  onRefresh: () => void;
  onViewDetails: () => void;
  animalName?: string;
}

const HealthScoreCard: React.FC<HealthScoreCardProps> = ({
  healthScore,
  isLoading,
  onRefresh,
  onViewDetails,
  animalName
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const getScoreColor = (score: number) => {
    if (score >= 85) return '#10B981'; // Green
    if (score >= 70) return '#F59E0B'; // Yellow
    if (score >= 50) return '#EF4444'; // Red
    return '#6B7280'; // Gray
  };

  const getScoreStatus = (score: number) => {
    if (score >= 85) return t('excellent');
    if (score >= 70) return t('good');
    if (score >= 50) return t('concerning');
    return t('critical');
  };

  return (
    <Card style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Heart size={20} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>
            {t('aiHealthScore')}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <RefreshCw size={16} color={colors.primary} />
          )}
        </TouchableOpacity>
      </View>

      {/* Content */}
      {healthScore ? (
        <>
          {/* Main Score Display */}
          <View style={styles.scoreContainer}>
            <View style={styles.scoreCircle}>
              <Text style={[
                styles.scoreNumber,
                { color: getScoreColor(healthScore.overall_score) }
              ]}>
                {healthScore.overall_score}
              </Text>
              <Text style={[styles.scoreOutOf, { color: colors.textLight }]}>
                /100
              </Text>
            </View>
            <View style={styles.scoreInfo}>
              <Text style={[
                styles.scoreStatus,
                { color: getScoreColor(healthScore.overall_score) }
              ]}>
                {getScoreStatus(healthScore.overall_score)}
              </Text>
              <Text style={[styles.scoreDate, { color: colors.textLight }]}>
                {format(new Date(healthScore.score_date), 'MMM dd, yyyy')}
              </Text>
              {animalName && (
                <Text style={[styles.animalName, { color: colors.text }]}>
                  {animalName}
                </Text>
              )}
            </View>
          </View>

          {/* Component Scores */}
          <View style={styles.componentScores}>
            <View style={styles.componentRow}>
              <ScoreComponent
                label={t('vitals')}
                score={healthScore.vitals_score}
                colors={colors}
              />
              <ScoreComponent
                label={t('activity')}
                score={healthScore.activity_score}
                colors={colors}
              />
            </View>
            <View style={styles.componentRow}>
              <ScoreComponent
                label={t('feeding')}
                score={healthScore.feeding_score}
                colors={colors}
              />
              <ScoreComponent
                label={t('medication')}
                score={healthScore.medication_score}
                colors={colors}
              />
            </View>
          </View>

          {/* Explanation */}
          {healthScore.score_explanation && (
            <View style={styles.explanationContainer}>
              <Text style={[styles.explanation, { color: colors.textLight }]} numberOfLines={3}>
                {healthScore.score_explanation}
              </Text>
            </View>
          )}

          {/* View Details Button */}
          <TouchableOpacity
            style={[styles.detailsButton, { borderColor: colors.border }]}
            onPress={onViewDetails}
          >
            <Text style={[styles.detailsButtonText, { color: colors.primary }]}>
              {t('viewDetails')}
            </Text>
            <ChevronRight size={16} color={colors.primary} />
          </TouchableOpacity>
        </>
      ) : (
        /* No Data State */
        <View style={styles.noDataContainer}>
          <Heart size={32} color={colors.textLight} />
          <Text style={[styles.noDataTitle, { color: colors.text }]}>
            {t('noHealthScore')}
          </Text>
          <Text style={[styles.noDataSubtitle, { color: colors.textLight }]}>
            {t('calculateFirstScore')}
          </Text>
          <TouchableOpacity
            style={[styles.calculateButton, { backgroundColor: colors.primary }]}
            onPress={onRefresh}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <TrendingUp size={16} color="#FFFFFF" />
            )}
            <Text style={styles.calculateButtonText}>
              {isLoading ? t('calculating') : t('calculateScore')}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </Card>
  );
};

// Component Score Display
interface ScoreComponentProps {
  label: string;
  score: number;
  colors: any;
}

const ScoreComponent: React.FC<ScoreComponentProps> = ({ label, score, colors }) => {
  const getScoreColor = (score: number) => {
    if (score >= 85) return '#10B981';
    if (score >= 70) return '#F59E0B';
    if (score >= 50) return '#EF4444';
    return '#6B7280';
  };

  return (
    <View style={styles.componentContainer}>
      <Text style={[styles.componentLabel, { color: colors.textLight }]}>
        {label}
      </Text>
      <Text style={[
        styles.componentScore,
        { color: getScoreColor(score) }
      ]}>
        {score}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginVertical: 8
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  title: {
    fontSize: 18,
    fontWeight: '600'
  },
  refreshButton: {
    padding: 4
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20
  },
  scoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16
  },
  scoreNumber: {
    fontSize: 24,
    fontWeight: 'bold'
  },
  scoreOutOf: {
    fontSize: 12,
    marginTop: -4
  },
  scoreInfo: {
    flex: 1
  },
  scoreStatus: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4
  },
  scoreDate: {
    fontSize: 12,
    marginBottom: 2
  },
  animalName: {
    fontSize: 14,
    fontWeight: '500'
  },
  componentScores: {
    marginBottom: 16
  },
  componentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8
  },
  componentContainer: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8
  },
  componentLabel: {
    fontSize: 12,
    marginBottom: 4
  },
  componentScore: {
    fontSize: 16,
    fontWeight: '600'
  },
  explanationContainer: {
    marginBottom: 16
  },
  explanation: {
    fontSize: 14,
    lineHeight: 20
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderRadius: 8,
    gap: 8
  },
  detailsButtonText: {
    fontSize: 14,
    fontWeight: '500'
  },
  noDataContainer: {
    alignItems: 'center',
    paddingVertical: 24
  },
  noDataTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 12,
    marginBottom: 4
  },
  noDataSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20
  },
  calculateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8
  },
  calculateButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500'
  }
});

export default HealthScoreCard;