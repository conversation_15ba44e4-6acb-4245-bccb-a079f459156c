import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import { Brain, AlertTriangle, Lightbulb, Target, X, Zap, Bot } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAIStore } from '../store/aiStore';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { HomeStackParamList } from '../navigation';
import { format } from 'date-fns';

interface AIAssistantWidgetProps {
  animalId: string;
  animalName: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  onNavigateToAI?: () => void;
}

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const AIAssistantWidget: React.FC<AIAssistantWidgetProps> = ({
  animalId,
  animalName,
  position = 'bottom-right',
  onNavigateToAI
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  const navigation = useNavigation<NavigationProp>();
  const [isExpanded, setIsExpanded] = useState(false);
  const [animatedValue] = useState(new Animated.Value(0));
  
  const {
    isLoadingAnalysis,
    isLoadingRealtimeAnalysis,
    requestAIAnalysis,
    requestRealtimeAnalysis,
    getLatestHealthAssessment,
    getLatestReadinessScore,
    getLatestRealtimeAnalysis,
    getUnreadTips,
    fetchHealthAssessments,
    fetchReadinessScores,
    fetchCoachingTips
  } = useAIStore();
  
  const latestHealthAssessment = getLatestHealthAssessment(animalId);
  const latestReadinessScore = getLatestReadinessScore(animalId);
  const latestRealtimeAnalysis = getLatestRealtimeAnalysis(animalId);
  const unreadTips = getUnreadTips(animalId);
  
  useEffect(() => {
    // Load AI data when animal changes
    const loadAIData = async () => {
      await Promise.all([
        fetchHealthAssessments(animalId),
        fetchReadinessScores(animalId),
        fetchCoachingTips(animalId),
      ]);
    };
    
    loadAIData();
  }, [animalId]);
  
  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: isExpanded ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [isExpanded]);
  
  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'high':
      case 'critical':
        return '#EF4444';
      case 'medium':
      case 'moderate':
        return '#F59E0B';
      case 'low':
      case 'normal':
        return '#10B981';
      default:
        return colors.primary;
    }
  };
  
  const getPositionStyles = () => {
    const baseStyles = {
      position: 'absolute' as const,
      zIndex: 1000,
    };
    
    switch (position) {
      case 'bottom-right':
        return { ...baseStyles, bottom: 20, right: 20 };
      case 'bottom-left':
        return { ...baseStyles, bottom: 20, left: 20 };
      case 'top-right':
        return { ...baseStyles, top: 100, right: 20 };
      case 'top-left':
        return { ...baseStyles, top: 100, left: 20 };
      default:
        return { ...baseStyles, bottom: 20, right: 20 };
    }
  };
  
  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };
  
  const handleNavigateToAI = () => {
    setIsExpanded(false);
    if (onNavigateToAI) {
      onNavigateToAI();
    } else {
      navigation.navigate('AIAssistant', { animalId });
    }
  };
  
  const handleRequestAnalysis = async () => {
    try {
      await requestAIAnalysis(animalId);
      setIsExpanded(false);
    } catch (error) {
      // Error is handled in the store
    }
  };

  const handleRequestRealtimeAnalysis = async () => {
    try {
      await requestRealtimeAnalysis(animalId);
      setIsExpanded(false);
    } catch (error) {
      // Error is handled in the store
    }
  };
  
  const handleNavigateToChat = () => {
    setIsExpanded(false);
    navigation.navigate('AIChat', { animalId });
  };
  
  const hasAlerts = latestHealthAssessment?.severity_level === 'high' || 
                   latestHealthAssessment?.severity_level === 'critical';
  const hasNewTips = unreadTips.length > 0;
  const hasAnyNotifications = hasAlerts || hasNewTips;
  const hasAnyData = latestHealthAssessment || latestReadinessScore || unreadTips.length > 0;
  
  const expandedHeight = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 200],
  });
  
  const expandedOpacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });
  
  return (
    <View style={[styles.container, getPositionStyles()]}>
      {/* Expanded Content */}
      <Animated.View
        style={[
          styles.expandedContainer,
          {
            backgroundColor: colors.card,
            borderColor: colors.border,
            height: expandedHeight,
            opacity: expandedOpacity,
          },
        ]}
        pointerEvents={isExpanded ? 'auto' : 'none'}
      >
        <View style={styles.expandedContent}>
          <View style={styles.expandedHeader}>
            <Text style={[styles.expandedTitle, { color: colors.text }]}>
              {t('aiWidgetInsights')} - {animalName}
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleToggleExpanded}
            >
              <X size={16} color={colors.textLight} />
            </TouchableOpacity>
          </View>
          
          {/* Readiness Score */}
          {latestReadinessScore ? (
            <View style={styles.insightRow}>
              <Target size={16} color={colors.primary} />
              <Text style={[styles.insightLabel, { color: colors.text }]}>{t('aiWidgetReadiness')}</Text>
              <Text style={[styles.insightValue, { color: colors.primary }]}>
                {Math.round(latestReadinessScore.score_value * 100)}%
              </Text>
            </View>
          ) : !hasAnyData && (
            <View style={styles.insightRow}>
              <Brain size={16} color={colors.textLight} />
              <Text style={[styles.insightLabel, { color: colors.textLight }]}>{t('aiWidgetNoAIData')}</Text>
              <Text style={[styles.insightValue, { color: colors.textLight }]}>{t('aiWidgetLogTraining')}</Text>
            </View>
          )}
          
          {/* Real-time Status or Health Alert */}
          {latestRealtimeAnalysis ? (
            <View style={styles.insightRow}>
              <Lightbulb size={16} color={colors.primary} />
              <Text style={[styles.insightLabel, { color: colors.text }]}>{t('aiWidgetLiveStatus')}</Text>
              <Text style={[styles.insightValue, { color: colors.primary }]}>{t('aiWidgetAvailable')}</Text>
            </View>
          ) : latestHealthAssessment && (
            <View style={styles.insightRow}>
              <AlertTriangle 
                size={16} 
                color={getSeverityColor(latestHealthAssessment.severity_level)} 
              />
              <Text style={[styles.insightLabel, { color: colors.text }]}>{t('aiWidgetHealth')}</Text>
              <Text style={[
                styles.insightValue,
                { color: getSeverityColor(latestHealthAssessment.severity_level) }
              ]}>
                {latestHealthAssessment.severity_level}
              </Text>
            </View>
          )}
          
          {/* Latest Tip */}
          {unreadTips.length > 0 && (
            <View style={styles.tipContainer}>
              <View style={styles.tipHeader}>
                <Lightbulb size={14} color={colors.primary} />
                <Text style={[styles.tipLabel, { color: colors.text }]}>{t('aiWidgetLatestTip')}</Text>
              </View>
              <Text style={[styles.tipText, { color: colors.textLight }]} numberOfLines={2}>
                {unreadTips[0].tip_text}
              </Text>
            </View>
          )}
          
          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary }]}
              onPress={handleRequestRealtimeAnalysis}
              disabled={isLoadingRealtimeAnalysis}
            >
              {isLoadingRealtimeAnalysis ? (
                <ActivityIndicator color="#FFFFFF" size="small" />
              ) : (
                <Lightbulb size={14} color="#FFFFFF" />
              )}
              <Text style={styles.actionButtonText}>
                {isLoadingRealtimeAnalysis ? t('aiWidgetChecking') : t('aiWidgetLiveCheckIn')}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.chatButton, { backgroundColor: colors.secondary }]}
              onPress={handleNavigateToChat}
            >
              <Text style={[styles.chatButtonText, { color: colors.card }]}>💬 Chat</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryButton, { borderColor: colors.border }]}
              onPress={handleNavigateToAI}
            >
              <Text style={[styles.secondaryButtonText, { color: colors.text }]}>{t('aiWidgetViewAll')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Animated.View>
      
      {/* Main Widget Button */}
      <TouchableOpacity
        style={[
          styles.widgetButton,
          {
            backgroundColor: colors.primary,
            shadowColor: colors.text,
          },
        ]}
        onPress={handleToggleExpanded}
        activeOpacity={0.8}
      >
        <Bot size={24} color="#FFFFFF" />
        
        {/* Notification Badges */}
        {hasAnyNotifications && (
          <View style={styles.notificationContainer}>
            {hasAlerts && (
              <View style={[styles.alertBadge, { backgroundColor: '#EF4444' }]}>
                <AlertTriangle size={8} color="#FFFFFF" />
              </View>
            )}
            {hasNewTips && (
              <View style={[styles.tipBadge, { backgroundColor: '#F59E0B' }]}>
                <Text style={styles.badgeText}>{unreadTips.length}</Text>
              </View>
            )}
          </View>
        )}
        
        {/* Readiness Score Overlay or New Badge */}
        {latestReadinessScore ? (
          <View style={styles.scoreOverlay}>
            <Text style={styles.scoreText}>
              {Math.round(latestReadinessScore.score_value * 100)}
            </Text>
          </View>
        ) : !hasAnyData && (
          <View style={styles.newBadge}>
            <Text style={styles.newBadgeText}>{t('aiWidgetNew')}</Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-end',
  },
  expandedContainer: {
    width: 280,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  expandedContent: {
    padding: 12,
    flex: 1,
  },
  expandedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  expandedTitle: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },
  insightRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  insightLabel: {
    fontSize: 12,
    marginLeft: 6,
    flex: 1,
  },
  insightValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  tipContainer: {
    marginBottom: 12,
  },
  tipHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  tipLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  tipText: {
    fontSize: 11,
    lineHeight: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 4,
    marginTop: 'auto',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 6,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  secondaryButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  chatButton: {
    flex: 0.8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 6,
  },
  chatButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  widgetButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  notificationContainer: {
    position: 'absolute',
    top: -2,
    right: -2,
    flexDirection: 'row',
    gap: 2,
  },
  alertBadge: {
    width: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tipBadge: {
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  scoreOverlay: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#10B981',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  scoreText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  newBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#F59E0B',
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  newBadgeText: {
    color: '#FFFFFF',
    fontSize: 8,
    fontWeight: 'bold',
  },
});

export default AIAssistantWidget;