import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface FeedAmountInputProps {
  amount: string;
  onAmountChange: (text: string) => void;
}

/**
 * @magic_description Feed amount input component for entering feeding amounts
 * Handles amount input with proper styling and placeholder
 */
const FeedAmountInput: React.FC<FeedAmountInputProps> = ({
  amount,
  onAmountChange
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.formGroup}>
      <Text style={[styles.label, { color: colors.textLight }]}>Amount *</Text>
      <TextInput
        style={[styles.input, { 
          backgroundColor: colors.background, 
          color: colors.text, 
          borderColor: colors.border 
        }]}
        value={amount}
        onChangeText={onAmountChange}
        placeholder="e.g., 2 kg"
        placeholderTextColor={colors.textLight}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
});

export default FeedAmountInput;