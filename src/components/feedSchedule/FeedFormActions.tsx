import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Save } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface FeedFormActionsProps {
  onCancel: () => void;
  onSave: () => void;
}

/**
 * @magic_description Feed form actions component for cancel and save buttons
 * Handles form submission and cancellation actions
 */
const FeedFormActions: React.FC<FeedFormActionsProps> = ({
  onCancel,
  onSave
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.formButtons}>
      <TouchableOpacity 
        style={[styles.cancelButton, { borderColor: colors.border }]}
        onPress={onCancel}
      >
        <Text style={[styles.cancelButtonText, { color: colors.text }]}>Cancel</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={[styles.saveButton, { backgroundColor: colors.primary }]}
        onPress={onSave}
      >
        <Save size={18} color={colors.card} />
        <Text style={[styles.saveButtonText, { color: colors.card }]}>Save</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  formButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default FeedFormActions;