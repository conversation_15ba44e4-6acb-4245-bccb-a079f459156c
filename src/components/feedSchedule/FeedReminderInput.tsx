import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import FeedTimeInput from './FeedTimeInput';

export interface FeedReminderInputProps {
  reminder: boolean;
  onReminderToggle: (value: boolean) => void;
  reminderTime: string;
  onReminderTimeChange: (text: string) => void;
}

/**
 * @magic_description Feed reminder input component for setting feeding reminders
 * Handles reminder toggle and conditional reminder time input
 */
const FeedReminderInput: React.FC<FeedReminderInputProps> = ({
  reminder,
  onReminderToggle,
  reminderTime,
  onReminderTimeChange
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.reminderContainer}>
      <View style={styles.reminderHeader}>
        <Text style={[styles.reminderLabel, { color: colors.text }]}>Set Reminder</Text>
        <Switch
          value={reminder}
          onValueChange={onReminderToggle}
          trackColor={{ false: '#E2E8F0', true: colors.primaryLight }}
          thumbColor={reminder ? colors.primary : '#f4f3f4'}
        />
      </View>
      
      {reminder && (
        <View style={styles.reminderTimeContainer}>
          <FeedTimeInput
            label="Reminder Time:"
            time={reminderTime}
            onTimeChange={onReminderTimeChange}
            placeholder="HH:MM"
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  reminderContainer: {
    marginBottom: 20,
  },
  reminderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  reminderLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  reminderTimeContainer: {
    marginTop: 8,
  },
});

export default FeedReminderInput;