import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Clock, Bell, Coffee, Trash2 } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface FeedingEntry {
  id: string;
  feedType: string;
  amount: string;
  time: string;
  notes?: string;
  reminder: boolean;
  reminderTime?: string;
}

interface FeedScheduleListItemProps {
  feeding: FeedingEntry;
  onDelete: (id: string) => void;
}

const FeedScheduleListItem: React.FC<FeedScheduleListItemProps> = ({
  feeding,
  onDelete
}) => {
  const { colors } = useTheme();

  return (
    <View 
      style={[styles.feedingCard, { backgroundColor: colors.card }]}
    >
      <View style={styles.feedingHeader}>
        <View style={styles.feedingTime}>
          <Clock size={16} color={colors.primary} />
          <Text style={[styles.feedingTimeText, { color: colors.text }]}>{feeding.time}</Text>
        </View>
        
        {feeding.reminder && (
          <View style={[styles.reminderBadge, { backgroundColor: colors.primaryLight }]}>
            <Bell size={12} color={colors.primary} />
            <Text style={[styles.reminderBadgeText, { color: colors.primary }]}>
              {feeding.reminderTime || 'Reminder On'}
            </Text>
          </View>
        )}
      </View>
      
      <View style={styles.feedingContent}>
        <View style={[styles.feedingIconContainer, { backgroundColor: colors.primaryLight }]}>
          <Coffee size={20} color={colors.primary} />
        </View>
        
        <View style={styles.feedingDetails}>
          <Text style={[styles.feedingType, { color: colors.text }]}>{feeding.feedType}</Text>
          <Text style={[styles.feedingAmount, { color: colors.textLight }]}>{feeding.amount}</Text>
          
          {feeding.notes && (
            <Text style={[styles.feedingNotes, { color: colors.textLight }]}>{feeding.notes}</Text>
          )}
        </View>
      </View>
      
      <TouchableOpacity 
        style={styles.deleteButton}
        onPress={() => onDelete(feeding.id)}
      >
        <Trash2 size={18} color={colors.error} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  feedingCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  feedingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  feedingTime: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  feedingTimeText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  reminderBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  reminderBadgeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  feedingContent: {
    flexDirection: 'row',
  },
  feedingIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  feedingDetails: {
    flex: 1,
  },
  feedingType: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  feedingAmount: {
    fontSize: 14,
    marginBottom: 4,
  },
  feedingNotes: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  deleteButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default FeedScheduleListItem;