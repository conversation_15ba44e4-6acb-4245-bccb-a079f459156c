import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface FeedNotesInputProps {
  notes: string;
  onNotesChange: (text: string) => void;
}

/**
 * @magic_description Feed notes input component for additional feeding information
 * Handles multiline notes input with proper styling
 */
const FeedNotesInput: React.FC<FeedNotesInputProps> = ({
  notes,
  onNotesChange
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.formGroup}>
      <Text style={[styles.label, { color: colors.textLight }]}>Notes</Text>
      <TextInput
        style={[styles.textArea, { 
          backgroundColor: colors.background, 
          color: colors.text, 
          borderColor: colors.border 
        }]}
        value={notes}
        onChangeText={onNotesChange}
        placeholder="Additional notes"
        placeholderTextColor={colors.textLight}
        multiline
        numberOfLines={3}
        textAlignVertical="top"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 80,
  },
});

export default FeedNotesInput;