import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { toast } from 'sonner-native';
import FeedTypeInput from './FeedTypeInput';
import FeedAmountInput from './FeedAmountInput';
import FeedTimeInput from './FeedTimeInput';
import FeedNotesInput from './FeedNotesInput';
import FeedReminderInput from './FeedReminderInput';
import FeedFormActions from './FeedFormActions';
import { useFormValidation } from '../../hooks/useFormValidation';
import { 
  validateRequired, 
  validateNumber, 
  validateLength 
} from '../../utils/validation';

interface FeedScheduleFormProps {
  animalId: string;
  onFormSubmit: () => void;
  onFormCancel: () => void;
  isPremium: boolean;
  currentEntryCount: number;
  maxFreeEntries: number;
  addFeedingEntry: (feeding: any) => void;
}

const FeedScheduleForm: React.FC<FeedScheduleFormProps> = ({
  animalId,
  onFormSubmit,
  onFormCancel,
  isPremium,
  currentEntryCount,
  maxFreeEntries,
  addFeedingEntry
}) => {
  const { colors } = useTheme();
  
  // Form state
  const [feedType, setFeedType] = useState('');
  const [amount, setAmount] = useState('');
  const [time, setTime] = useState('08:00');
  const [notes, setNotes] = useState('');
  const [reminder, setReminder] = useState(true);
  const [reminderTime, setReminderTime] = useState('07:45');
  const [customFeedType, setCustomFeedType] = useState('');

  const validation = useFormValidation();

  const handleSubmit = () => {
    const finalFeedType = feedType === 'Other' ? customFeedType : feedType;
    
    // Comprehensive validation
    const validations = {
      feedType: validateRequired(finalFeedType, 'Feed type'),
      amount: validateNumber(amount, 'Amount', 0.1, 1000, true),
      notes: validateLength(notes, 'Notes', undefined, 200)
    };

    const isFormValid = validation.validateAllFields(validations);
    
    if (!isFormValid) {
      const firstError = Object.values(validation.errors)[0];
      toast.error(firstError || 'Please fix the errors before saving');
      return;
    }
    
    // Check if user has reached the free limit
    if (!isPremium && currentEntryCount >= maxFreeEntries) {
      toast.error(`Free plan limited to ${maxFreeEntries} feeding schedules. Upgrade to Premium for unlimited schedules.`);
      return;
    }
    
    const newFeeding = {
      animalId,
      feedType: finalFeedType.trim(),
      amount: amount.trim(),
      time,
      notes: notes.trim() || undefined,
      reminder,
      reminderTime: reminder ? reminderTime : undefined
    };
    
    addFeedingEntry(newFeeding);
    toast.success('Feeding schedule added successfully');
    
    // Reset form and validation
    resetForm();
    validation.resetForm();
    onFormSubmit();
  };

  const resetForm = () => {
    setFeedType('');
    setAmount('');
    setTime('08:00');
    setNotes('');
    setReminder(true);
    setReminderTime('07:45');
    setCustomFeedType('');
  };

  const handleCancel = () => {
    resetForm();
    onFormCancel();
  };

  const handleFeedTypeSelect = (type: string) => {
    setFeedType(type);
    if (type !== 'Other') {
      setCustomFeedType('');
    }
  };

  const feedTypes = ['Hay', 'Grain', 'Vitamin', 'Supplement', 'Other'];

  return (
    <View style={[styles.formCard, { backgroundColor: colors.card }]}>
      <Text style={[styles.formTitle, { color: colors.text }]}>Add Feeding Schedule</Text>
      
      <FeedTypeInput
        feedType={feedType}
        customFeedType={customFeedType}
        onFeedTypeSelect={handleFeedTypeSelect}
        onCustomFeedTypeChange={setCustomFeedType}
      />
      
      <FeedAmountInput
        amount={amount}
        onAmountChange={setAmount}
      />
      
      <FeedTimeInput
        label="Time *"
        time={time}
        onTimeChange={setTime}
      />
      
      <FeedNotesInput
        notes={notes}
        onNotesChange={setNotes}
      />
      
      <FeedReminderInput
        reminder={reminder}
        onReminderToggle={setReminder}
        reminderTime={reminderTime}
        onReminderTimeChange={setReminderTime}
      />
      
      <FeedFormActions
        onCancel={handleCancel}
        onSave={handleSubmit}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  formCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
});

export default FeedScheduleForm;