import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface FeedTypeInputProps {
  feedType: string;
  customFeedType: string;
  onFeedTypeSelect: (type: string) => void;
  onCustomFeedTypeChange: (text: string) => void;
}

/**
 * @magic_description Feed type input component for selecting predefined or custom feed types
 * Handles feed type selection with buttons and custom input for "Other" option
 */
const FeedTypeInput: React.FC<FeedTypeInputProps> = ({
  feedType,
  customFeedType,
  onFeedTypeSelect,
  onCustomFeedTypeChange
}) => {
  const { colors } = useTheme();
  
  const feedTypes = ['Hay', 'Grain', 'Vitamin', 'Supplement', 'Other'];
  
  return (
    <View style={styles.formGroup}>
      <Text style={[styles.label, { color: colors.textLight }]}>Feed Type *</Text>
      <View style={styles.feedTypeSelector}>
        {feedTypes.map((type) => (
          <TouchableOpacity
            key={type}
            style={[
              styles.feedTypeButton,
              feedType === type && [styles.feedTypeButtonActive, { backgroundColor: colors.primary }],
              { borderColor: colors.border }
            ]}
            onPress={() => onFeedTypeSelect(type)}
          >
            <Text style={[
              styles.feedTypeButtonText,
              feedType === type && [styles.feedTypeButtonTextActive, { color: colors.card }],
              { color: colors.text }
            ]}>{type}</Text>
          </TouchableOpacity>
        ))}
      </View>
      
      {feedType === 'Other' && (
        <TextInput
          style={[styles.input, { 
            backgroundColor: colors.background, 
            color: colors.text, 
            borderColor: colors.border, 
            marginTop: 8 
          }]}
          value={customFeedType}
          onChangeText={onCustomFeedTypeChange}
          placeholder="Enter custom feed type"
          placeholderTextColor={colors.textLight}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  feedTypeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  feedTypeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8,
  },
  feedTypeButtonActive: {
    borderWidth: 0,
  },
  feedTypeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  feedTypeButtonTextActive: {
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
});

export default FeedTypeInput;