import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

export interface FeedTimeInputProps {
  label: string;
  time: string;
  onTimeChange: (text: string) => void;
  placeholder?: string;
}

/**
 * @magic_description Reusable time input component for feeding schedules
 * Handles time input with HH:MM format for both main time and reminder time
 */
const FeedTimeInput: React.FC<FeedTimeInputProps> = ({
  label,
  time,
  onTimeChange,
  placeholder = "HH:MM"
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.formGroup}>
      <Text style={[styles.label, { color: colors.textLight }]}>{label}</Text>
      <TextInput
        style={[styles.input, { 
          backgroundColor: colors.background, 
          color: colors.text, 
          borderColor: colors.border 
        }]}
        value={time}
        onChangeText={onTimeChange}
        placeholder={placeholder}
        placeholderTextColor={colors.textLight}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
});

export default FeedTimeInput;