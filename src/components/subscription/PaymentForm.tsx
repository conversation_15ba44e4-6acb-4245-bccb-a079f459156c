import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, ScrollView } from 'react-native';
import { Lock } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Header from '../Header';
import PaymentMethodSelector from './PaymentMethodSelector';
import PaymentCardForm, { CardData } from './PaymentCardForm';
import { usePaymentProcessing } from '../../hooks/usePaymentProcessing';
import { useSubscriptionValidation } from '../../hooks/useSubscriptionValidation';

type PaymentMethod = 'card' | 'apple' | 'google' | 'paypal';
type PlanType = 'monthly' | 'yearly';

interface PaymentFormProps {
  selectedPlan: PlanType;
  onCancel: () => void;
  onPaymentSuccess: () => void;
}

/**
 * @magic_description Payment form container component managing payment flow
 * Combines payment method selection, card form, and payment processing
 */
const PaymentForm: React.FC<PaymentFormProps> = ({ selectedPlan, onCancel, onPaymentSuccess }) => {
  const { colors, isDarkMode } = useTheme();
  
  // Payment method state
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('card');
  
  // Card data state
  const [cardData, setCardData] = useState<CardData>({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: ''
  });
  
  // Custom hooks
  const {
    isProcessing,
    processCardPayment,
    processApplePay,
    processGooglePay,
    processPayPal
  } = usePaymentProcessing(onPaymentSuccess);
  
  const { validatePayment } = useSubscriptionValidation();
  
  const handleCardDataChange = (field: keyof CardData, value: string) => {
    setCardData(prev => ({ ...prev, [field]: value }));
  };
  
  const handlePayment = async () => {
    // Validate payment data
    if (!validatePayment(selectedPaymentMethod, selectedPaymentMethod === 'card' ? cardData : undefined)) {
      return;
    }
    
    // Process payment based on selected method
    switch (selectedPaymentMethod) {
      case 'card':
        await processCardPayment(selectedPlan);
        break;
      case 'apple':
        await processApplePay(selectedPlan);
        break;
      case 'google':
        await processGooglePay(selectedPlan);
        break;
      case 'paypal':
        await processPayPal(selectedPlan);
        break;
    }
  };
  
  const getPlanPrice = () => {
    return selectedPlan === 'monthly' ? '$14.99' : '$143.90';
  };
  
  return (
    <View style={[styles.paymentFormContainer, { backgroundColor: colors.card }]}>
            <View style={styles.paymentFormHeader}>
              <Text style={[styles.paymentFormTitle, { color: colors.text }]}>Payment Details</Text>
              <TouchableOpacity onPress={onCancel}>
                <Text style={[styles.paymentFormCancel, { color: colors.textLight }]}>Cancel</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.securePaymentInfo}>
              <Lock size={16} color={colors.success} />
              <Text style={[styles.securePaymentText, { color: colors.success }]}>Secure Payment</Text>
            </View>
            
            <View style={styles.planSummary}>
              <Text style={[styles.planSummaryTitle, { color: colors.text }]}>
                {selectedPlan === 'monthly' ? 'Monthly Plan' : 'Annual Plan'}
              </Text>
              <Text style={[styles.planSummaryPrice, { color: colors.primary }]}>
                {selectedPlan === 'monthly' ? '$14.99/month' : '$143.90/year'}
              </Text>
            </View>
            
            <PaymentMethodSelector
              selectedMethod={selectedPaymentMethod}
              onMethodSelect={setSelectedPaymentMethod}
            />
            
            {selectedPaymentMethod === 'card' && (
              <PaymentCardForm
                cardData={cardData}
                onCardDataChange={handleCardDataChange}
              />
            )}
            
            <TouchableOpacity 
              style={[
                styles.payButton, 
                { backgroundColor: isProcessing ? colors.textLight : colors.primary }
              ]}
              onPress={handlePayment}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator color={colors.card} size="small" />
              ) : (
                <Text style={[styles.payButtonText, { color: colors.card }]}>
                  {`Pay ${getPlanPrice()}`}
                </Text>
              )}
            </TouchableOpacity>
            
            <Text style={[styles.securityNote, { color: colors.textLight }]}>
              Your payment information is securely processed. We do not store your full card details.
            </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  paymentFormContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  paymentFormHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  paymentFormTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  paymentFormCancel: {
    fontSize: 14,
  },
  securePaymentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  securePaymentText: {
    fontSize: 14,
    marginLeft: 8,
  },
  planSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(61, 140, 145, 0.1)',
    borderRadius: 8,
    marginBottom: 20,
  },
  planSummaryTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  planSummaryPrice: {
    fontSize: 16,
    fontWeight: '600',
  },
  payButton: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    padding: 16,
    marginTop: 8,
    flexDirection: 'row',
  },
  payButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  securityNote: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 18,
  },
});

export default PaymentForm;