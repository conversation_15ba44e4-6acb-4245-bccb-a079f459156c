import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

const { width } = Dimensions.get('window');

interface PricingPlansProps {
  selectedPlan: 'monthly' | 'yearly' | null;
  onPlanSelect: (plan: 'monthly' | 'yearly') => void;
}

/**
 * @magic_description Pricing plans component displaying monthly and yearly subscription options
 * Shows pricing cards with selection state and savings badge for yearly plan
 */
const PricingPlans: React.FC<PricingPlansProps> = ({ selectedPlan, onPlanSelect }) => {
  const { colors } = useTheme();
  
  return (
    <View style={styles.plansContainer}>
      <TouchableOpacity 
        style={[
          styles.planCard, 
          selectedPlan === 'monthly' && styles.selectedPlanCard,
          { 
            backgroundColor: colors.card,
            borderColor: selectedPlan === 'monthly' ? colors.primary : colors.border
          }
        ]}
        onPress={() => onPlanSelect('monthly')}
      >
        <Text style={[styles.planTitle, { color: colors.text }]}>Monthly</Text>
        <Text style={[styles.planPrice, { color: colors.primary }]}>$14.99</Text>
        <Text style={[styles.planPeriod, { color: colors.textLight }]}>per month</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={[
          styles.planCard, 
          selectedPlan === 'yearly' && styles.selectedPlanCard,
          { 
            backgroundColor: colors.card,
            borderColor: selectedPlan === 'yearly' ? colors.primary : colors.border
          }
        ]}
        onPress={() => onPlanSelect('yearly')}
      >
        <View style={[styles.saveBadge, { backgroundColor: colors.primary }]}>
          <Text style={[styles.saveText, { color: colors.card }]}>Save 20%</Text>
        </View>
        <Text style={[styles.planTitle, { color: colors.text }]}>Yearly</Text>
        <Text style={[styles.planPrice, { color: colors.primary }]}>$143.90</Text>
        <Text style={[styles.planPeriod, { color: colors.textLight }]}>per year</Text>
        <Text style={[styles.monthlyEquivalent, { color: colors.success }]}>
          Only $11.99/month
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  plansContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  planCard: {
    width: (width - 48) / 2,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    borderWidth: 2,
    position: 'relative',
  },
  selectedPlanCard: {
    borderWidth: 2,
  },
  saveBadge: {
    position: 'absolute',
    top: -10,
    right: -10,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  saveText: {
    fontSize: 12,
    fontWeight: '600',
  },
  planTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  planPrice: {
    fontSize: 24,
    fontWeight: '700',
  },
  planPeriod: {
    fontSize: 14,
  },
  monthlyEquivalent: {
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
});

export default PricingPlans;