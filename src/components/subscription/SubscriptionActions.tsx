import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { CreditCard } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface SubscriptionActionsProps {
  selectedPlan: 'monthly' | 'yearly' | null;
  onSubscribe: () => void;
}

/**
 * @magic_description Subscription actions component with subscribe button and terms text
 * Displays the main subscribe button and legal terms information
 */
const SubscriptionActions: React.FC<SubscriptionActionsProps> = ({
  selectedPlan,
  onSubscribe
}) => {
  const { colors } = useTheme();
  
  const getButtonText = () => {
    if (selectedPlan) {
      return `Subscribe Now: ${selectedPlan === 'monthly' ? '$14.99/month' : '$143.90/year'}`;
    }
    return 'Select a Plan';
  };
  
  return (
    <>
      <TouchableOpacity 
        style={[
          styles.subscribeButton, 
          { 
            backgroundColor: selectedPlan ? colors.primary : colors.textLight 
          }
        ]}
        onPress={onSubscribe}
        disabled={!selectedPlan}
      >
        <CreditCard size={20} color={colors.card} />
        <Text style={[styles.subscribeButtonText, { color: colors.card }]}>
          {getButtonText()}
        </Text>
      </TouchableOpacity>
      
      <Text style={[styles.termsText, { color: colors.textLight }]}>
        By subscribing, you agree to our Terms of Service and Privacy Policy. 
        Subscription automatically renews unless auto-renew is turned off at least 24 hours before the end of the current period.
        Monthly subscription: $14.99/month. Annual subscription: $143.90/year (save 20%).
      </Text>
    </>
  );
};

const styles = StyleSheet.create({
  subscribeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  subscribeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  termsText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default SubscriptionActions;