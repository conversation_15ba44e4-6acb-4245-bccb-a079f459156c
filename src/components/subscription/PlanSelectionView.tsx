import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  StatusBar
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Header from '../Header';
import SubscriptionHeader from './SubscriptionHeader';
import PricingPlans from './PricingPlans';
import FeaturesList from './FeaturesList';
import SubscriptionActions from './SubscriptionActions';

export interface PlanSelectionViewProps {
  selectedPlan: 'monthly' | 'yearly' | null;
  onPlanSelect: (plan: 'monthly' | 'yearly') => void;
  onSubscribe: () => void;
}

/**
 * @magic_description Plan selection view component for subscription screen
 * Displays subscription plans, features, and subscription actions
 */
const PlanSelectionView: React.FC<PlanSelectionViewProps> = ({
  selectedPlan,
  onPlanSelect,
  onSubscribe
}) => {
  const { colors, isDarkMode } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <Header 
        title="Premium Subscription" 
        showBackButton
      />
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <SubscriptionHeader />
          
          <PricingPlans 
            selectedPlan={selectedPlan} 
            onPlanSelect={onPlanSelect} 
          />
          
          <FeaturesList />
          
          <SubscriptionActions 
            selectedPlan={selectedPlan} 
            onSubscribe={onSubscribe} 
          />
          
          <Text style={[styles.termsText, { color: colors.textLight }]}>
            By subscribing, you agree to our Terms of Service and Privacy Policy. 
            Subscription automatically renews unless auto-renew is turned off at least 24 hours before the end of the current period.
            Monthly subscription: $14.99/month. Annual subscription: $143.90/year (save 20%).
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  termsText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default PlanSelectionView;