import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { CreditCard, Check, Apple } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

type PaymentMethod = 'card' | 'apple' | 'google' | 'paypal';

interface PaymentMethodSelectorProps {
  selectedMethod: PaymentMethod;
  onMethodSelect: (method: PaymentMethod) => void;
}

/**
 * @magic_description Payment method selector component with multiple payment options
 * Displays credit card, Apple Pay, Google Pay, and PayPal options with selection state
 */
const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  selectedMethod,
  onMethodSelect
}) => {
  const { colors } = useTheme();
  
  const renderSelectedIndicator = (isSelected: boolean) => {
    if (!isSelected) return null;
    
    return (
      <View style={[styles.selectedIndicator, { backgroundColor: colors.primary }]}>
        <Check size={16} color="#FFF" />
      </View>
    );
  };
  
  return (
    <View style={styles.paymentMethodSelector}>
      <Text style={[styles.paymentMethodTitle, { color: colors.text }]}>Select Payment Method</Text>
      
      <TouchableOpacity
        style={[
          styles.paymentMethodOption,
          selectedMethod === 'card' && [styles.selectedPaymentMethod, { borderColor: colors.primary }],
          { backgroundColor: colors.card }
        ]}
        onPress={() => onMethodSelect('card')}
      >
        <CreditCard size={24} color={selectedMethod === 'card' ? colors.primary : colors.textLight} />
        <Text style={[
          styles.paymentMethodText, 
          { color: selectedMethod === 'card' ? colors.primary : colors.text }
        ]}>
          Credit / Debit Card
        </Text>
        {renderSelectedIndicator(selectedMethod === 'card')}
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.paymentMethodOption,
          selectedMethod === 'apple' && [styles.selectedPaymentMethod, { borderColor: colors.primary }],
          { backgroundColor: colors.card }
        ]}
        onPress={() => onMethodSelect('apple')}
      >
        <Apple size={24} color={selectedMethod === 'apple' ? colors.primary : colors.textLight} />
        <Text style={[
          styles.paymentMethodText, 
          { color: selectedMethod === 'apple' ? colors.primary : colors.text }
        ]}>
          Apple Pay
        </Text>
        {renderSelectedIndicator(selectedMethod === 'apple')}
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.paymentMethodOption,
          selectedMethod === 'google' && [styles.selectedPaymentMethod, { borderColor: colors.primary }],
          { backgroundColor: colors.card }
        ]}
        onPress={() => onMethodSelect('google')}
      >
        <Image 
          source={{ uri: 'https://magically.life/api/media/image?query=google%20pay%20official%20logo%20transparent%20background' }}
          style={styles.googlePayIcon}
        />
        <Text style={[
          styles.paymentMethodText, 
          { color: selectedMethod === 'google' ? colors.primary : colors.text }
        ]}>
          Google Pay
        </Text>
        {renderSelectedIndicator(selectedMethod === 'google')}
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.paymentMethodOption,
          selectedMethod === 'paypal' && [styles.selectedPaymentMethod, { borderColor: colors.primary }],
          { backgroundColor: colors.card }
        ]}
        onPress={() => onMethodSelect('paypal')}
      >
        <Image 
          source={{ uri: 'https://magically.life/api/media/image?query=paypal%20official%20logo%20transparent%20background' }}
          style={styles.paypalIcon}
        />
        <Text style={[
          styles.paymentMethodText, 
          { color: selectedMethod === 'paypal' ? colors.primary : colors.text }
        ]}>
          PayPal
        </Text>
        {renderSelectedIndicator(selectedMethod === 'paypal')}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  paymentMethodSelector: {
    marginBottom: 20,
  },
  paymentMethodTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  paymentMethodOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedPaymentMethod: {
    borderWidth: 1,
  },
  paymentMethodText: {
    fontSize: 16,
    marginLeft: 12,
    flex: 1,
  },
  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  googlePayIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  paypalIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
});

export default PaymentMethodSelector;