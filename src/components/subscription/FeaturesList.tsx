import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Check } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface FeaturesListProps {
  // No props needed - features are static
}

/**
 * @magic_description Features list component showing premium vs free feature comparison
 * Displays comprehensive list of premium features and limited free features
 */
const FeaturesList: React.FC<FeaturesListProps> = () => {
  const { colors } = useTheme();
  
  const renderFeatureItem = (text: string, isPremium: boolean = true) => (
    <View style={styles.featureItem}>
      <Check size={18} color={isPremium ? colors.primary : colors.textLight} />
      <Text style={[styles.featureText, { color: isPremium ? colors.text : colors.textLight }]}>
        {text}
      </Text>
    </View>
  );
  
  return (
    <View style={[styles.featuresCard, { backgroundColor: colors.card }]}>
      <Text style={[styles.featuresTitle, { color: colors.text }]}>Premium Features</Text>
      
      {renderFeatureItem('Unlimited animals (Free plan: max 3)')}
      {renderFeatureItem('Unlimited feeding schedules (Free plan: max 3)')}
      {renderFeatureItem('Unlimited medication records (Free plan: max 3)')}
      {renderFeatureItem('Unlimited vital records (Free plan: max 3)')}
      {renderFeatureItem('Real-time monitoring via Bluetooth/WiFi')}
      {renderFeatureItem('Cloud data synchronization')}
      {renderFeatureItem('Vaccination tracking with alerts')}
      {renderFeatureItem('Advanced health analytics')}
      {renderFeatureItem('Print & export reports')}
      {renderFeatureItem('Priority support')}
      
      <View style={[styles.divider, { backgroundColor: colors.border }]} />
      
      <Text style={[styles.freeTitle, { color: colors.text }]}>Free Features</Text>
      
      {renderFeatureItem('Up to 3 animals', false)}
      {renderFeatureItem('Basic health tracking', false)}
      {renderFeatureItem('Limited records', false)}
    </View>
  );
};

const styles = StyleSheet.create({
  featuresCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  featuresTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 16,
    marginLeft: 12,
  },
  divider: {
    height: 1,
    marginVertical: 16,
  },
  freeTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
});

export default FeaturesList;