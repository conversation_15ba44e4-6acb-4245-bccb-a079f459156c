import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Header from '../Header';
import PaymentForm from './PaymentForm';

export interface PaymentProcessingViewProps {
  selectedPlan: 'monthly' | 'yearly' | null;
  onCancel: () => void;
  onPaymentSuccess: () => void;
}

/**
 * @magic_description Payment processing view component for subscription screen
 * Displays payment form and handles payment flow
 */
const PaymentProcessingView: React.FC<PaymentProcessingViewProps> = ({
  selectedPlan,
  onCancel,
  onPaymentSuccess
}) => {
  const { colors, isDarkMode } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={isDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background} 
      />
      
      <Header 
        title="Payment Details" 
        showBackButton
      />
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <PaymentForm 
            selectedPlan={selectedPlan}
            onCancel={onCancel}
            onPaymentSuccess={onPaymentSuccess}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
});

export default PaymentProcessingView;