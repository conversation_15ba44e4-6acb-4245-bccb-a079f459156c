import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ChevronsUp } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface SubscriptionHeaderProps {
  // No props needed - this is a static header
}

/**
 * @magic_description Subscription screen header component with premium upgrade messaging
 * Displays the main call-to-action header encouraging users to upgrade to premium
 */
const SubscriptionHeader: React.FC<SubscriptionHeaderProps> = () => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.headerCard, { backgroundColor: colors.primary }]}>
      <ChevronsUp size={32} color={colors.card} />
      <Text style={[styles.headerTitle, { color: colors.card }]}>Upgrade to Premium</Text>
      <Text style={[styles.headerSubtitle, { color: colors.card }]}>
        Unlock all features and get the most out of HoofBeat
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  headerCard: {
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: 16,
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default SubscriptionHeader;