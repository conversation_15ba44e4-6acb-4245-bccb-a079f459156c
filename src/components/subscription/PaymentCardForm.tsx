import React from 'react';
import { View, Text, StyleSheet, TextInput } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface CardData {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
}

interface PaymentCardFormProps {
  cardData: CardData;
  onCardDataChange: (field: keyof CardData, value: string) => void;
}

/**
 * @magic_description Payment card form component with credit card input fields
 * Handles card number, expiry date, CVV, and cardholder name with proper formatting
 */
const PaymentCardForm: React.FC<PaymentCardFormProps> = ({ cardData, onCardDataChange }) => {
  const { colors } = useTheme();
  
  const formatCardNumber = (text: string) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, '');
    // Add space after every 4 digits
    const formatted = cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
    return formatted.substring(0, 19); // Limit to 16 digits + 3 spaces
  };
  
  const formatExpiryDate = (text: string) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, '');
    // Format as MM/YY
    if (cleaned.length > 2) {
      return `${cleaned.substring(0, 2)}/${cleaned.substring(2, 4)}`;
    }
    return cleaned;
  };
  
  const handleCardNumberChange = (text: string) => {
    onCardDataChange('cardNumber', formatCardNumber(text));
  };
  
  const handleExpiryDateChange = (text: string) => {
    onCardDataChange('expiryDate', formatExpiryDate(text));
  };
  
  return (
    <>
      <View style={styles.formGroup}>
        <Text style={[styles.formLabel, { color: colors.textLight }]}>Card Number</Text>
        <TextInput
          style={[styles.formInput, { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }]}
          placeholder="1234 5678 9012 3456"
          placeholderTextColor={colors.textLight}
          value={cardData.cardNumber}
          onChangeText={handleCardNumberChange}
          keyboardType="number-pad"
          maxLength={19}
        />
      </View>
      
      <View style={styles.formRow}>
        <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
          <Text style={[styles.formLabel, { color: colors.textLight }]}>Expiry Date</Text>
          <TextInput
            style={[styles.formInput, { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }]}
            placeholder="MM/YY"
            placeholderTextColor={colors.textLight}
            value={cardData.expiryDate}
            onChangeText={handleExpiryDateChange}
            keyboardType="number-pad"
            maxLength={5}
          />
        </View>
        
        <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
          <Text style={[styles.formLabel, { color: colors.textLight }]}>CVV</Text>
          <TextInput
            style={[styles.formInput, { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }]}
            placeholder="123"
            placeholderTextColor={colors.textLight}
            value={cardData.cvv}
            onChangeText={(text) => onCardDataChange('cvv', text)}
            keyboardType="number-pad"
            maxLength={4}
            secureTextEntry
          />
        </View>
      </View>
      
      <View style={styles.formGroup}>
        <Text style={[styles.formLabel, { color: colors.textLight }]}>Cardholder Name</Text>
        <TextInput
          style={[styles.formInput, { backgroundColor: colors.background, color: colors.text, borderColor: colors.border }]}
          placeholder="John Doe"
          placeholderTextColor={colors.textLight}
          value={cardData.cardholderName}
          onChangeText={(text) => onCardDataChange('cardholderName', text)}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
});

export default PaymentCardForm;
export type { CardData };