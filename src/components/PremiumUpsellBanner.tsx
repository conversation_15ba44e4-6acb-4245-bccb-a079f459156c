import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Lock } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';

interface PremiumUpsellBannerProps {
  message: string;
  buttonText?: string;
  onUpgradePress: () => void;
  icon?: React.ReactNode;
}

const PremiumUpsellBanner: React.FC<PremiumUpsellBannerProps> = ({
  message,
  buttonText = 'Upgrade',
  onUpgradePress,
  icon
}) => {
  const { colors } = useTheme();

  return (
    <View style={[styles.premiumBanner, { backgroundColor: colors.secondaryLight }]}>
      {icon || <Lock size={20} color={colors.secondary} />}
      <Text style={[styles.premiumBannerText, { color: colors.secondary }]}>
        {message}
      </Text>
      <TouchableOpacity 
        style={[styles.upgradeButton, { backgroundColor: colors.secondary }]}
        onPress={onUpgradePress}
      >
        <Text style={[styles.upgradeButtonText, { color: colors.card }]}>{buttonText}</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  premiumBanner: {
    flexDirection: 'column',
    alignItems: 'center',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  premiumBannerText: {
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 8,
  },
  upgradeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 8,
  },
  upgradeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default PremiumUpsellBanner;