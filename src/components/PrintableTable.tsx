
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

interface PrintableTableProps {
  title: string;
  headers: string[];
  data: Record<string, string>[];
}

const PrintableTable: React.FC<PrintableTableProps> = ({ title, headers, data }) => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
      
      <View style={[styles.table, { borderColor: colors.border }]}>
        <View style={[styles.headerRow, { backgroundColor: colors.primary, borderColor: colors.border }]}>
          {headers.map((header, index) => (
            <View 
              key={index} 
              style={[
                styles.headerCell, 
                index < headers.length - 1 && { borderRightWidth: 1, borderRightColor: colors.border }
              ]}
            >
              <Text style={[styles.headerText, { color: colors.card }]}>{header}</Text>
            </View>
          ))}
        </View>
        
        {data.map((row, rowIndex) => (
          <View 
            key={rowIndex} 
            style={[
              styles.dataRow, 
              { borderColor: colors.border },
              rowIndex % 2 === 1 && { backgroundColor: colors.background }
            ]}
          >
            {headers.map((header, cellIndex) => {
              const key = header.toLowerCase().replace(/\s/g, '_');
              return (
                <View 
                  key={cellIndex} 
                  style={[
                    styles.dataCell, 
                    cellIndex < headers.length - 1 && { borderRightWidth: 1, borderRightColor: colors.border }
                  ]}
                >
                  <Text style={[styles.dataText, { color: colors.text }]}>{row[key] || '-'}</Text>
                </View>
              );
            })}
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  table: {
    borderWidth: 1,
    borderRadius: 4,
    overflow: 'hidden',
  },
  headerRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  headerCell: {
    flex: 1,
    padding: 8,
    alignItems: 'center',
  },
  headerText: {
    fontWeight: '600',
    fontSize: 14,
  },
  dataRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  dataCell: {
    flex: 1,
    padding: 8,
    alignItems: 'center',
  },
  dataText: {
    fontSize: 14,
  },
});

export default PrintableTable;
