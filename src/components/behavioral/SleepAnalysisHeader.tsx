import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { Moon } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { Animal } from '../../mocks/animals';
import Card from '../ui/Card';

interface SleepAnalysisHeaderProps {
  animal: Animal;
  isLoading: boolean;
  onAnalyze: () => void;
}

const SleepAnalysisHeader: React.FC<SleepAnalysisHeaderProps> = ({
  animal,
  isLoading,
  onAnalyze
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  return (
    <Card style={styles.container}>
      <View style={styles.animalInfo}>
        <Text style={[styles.animalName, { color: colors.text }]}>
          {animal.name}
        </Text>
        <Text style={[styles.animalDetails, { color: colors.textLight }]}>
          {animal.breed} • {animal.age} {t('ageYears')}
        </Text>
      </View>
      
      <TouchableOpacity
        style={[
          styles.analysisButton,
          { backgroundColor: colors.primary },
          isLoading && styles.analysisButtonDisabled
        ]}
        onPress={onAnalyze}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#FFFFFF" size="small" />
        ) : (
          <Moon size={20} color="#FFFFFF" />
        )}
        <Text style={styles.analysisButtonText}>
          {isLoading ? t('analyzing') : t('analyzeSleep')}
        </Text>
      </TouchableOpacity>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  animalInfo: {
    marginBottom: 16,
  },
  animalName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  animalDetails: {
    fontSize: 16,
    fontWeight: '500',
  },
  analysisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  analysisButtonDisabled: {
    opacity: 0.6,
  },
  analysisButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default SleepAnalysisHeader;