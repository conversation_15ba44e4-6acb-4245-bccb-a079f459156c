
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Syringe, AlertTriangle } from 'lucide-react-native';
import { COLORS } from '../constants/colors';
import { useTheme } from '../contexts/ThemeContext';

interface VaccineAlertBannerProps {
  animalName: string;
  vaccineName: string;
  expiryDate: string;
  daysRemaining: number;
  onPress?: () => void;
}

const VaccineAlertBanner: React.FC<VaccineAlertBannerProps> = ({
  animalName,
  vaccineName,
  expiryDate,
  daysRemaining,
  onPress
}) => {
  const { colors } = useTheme();
  
  const getAlertColor = () => {
    if (daysRemaining <= 7) return colors.error;
    if (daysRemaining <= 30) return colors.warning;
    return colors.primary;
  };
  
  const alertColor = getAlertColor();
  
  return (
    <TouchableOpacity 
      style={[styles.container, { backgroundColor: `${alertColor}20` }]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.iconContainer, { backgroundColor: `${alertColor}30` }]}>
        <Syringe size={20} color={alertColor} />
      </View>
      
      <View style={styles.content}>
        <Text style={[styles.title, { color: alertColor }]}>Vaccine Expiring Soon</Text>
        <Text style={[styles.animalName, { color: colors.text }]}>{animalName}</Text>
        <Text style={[styles.message, { color: colors.text }]}>
          {vaccineName} expires on {expiryDate}
        </Text>
        <Text style={[styles.daysRemaining, { color: alertColor }]}>
          {daysRemaining} days remaining
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  animalName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    marginBottom: 4,
  },
  daysRemaining: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default VaccineAlertBanner;
