
import React from 'react';
import { 
  View, 
  StyleSheet, 
  TouchableOpacity, 
  StyleProp, 
  ViewStyle,
  Platform 
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface CardProps {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  onPress?: () => void;
  elevation?: number; // 0-3
  padding?: number;
  noBorder?: boolean;
}

const Card: React.FC<CardProps> = ({ 
  children, 
  style, 
  onPress, 
  elevation = 1,
  padding = 16,
  noBorder = false
}) => {
  const { colors, isDarkMode } = useTheme();
  
  // Calculate shadow based on elevation
  const shadowOpacity = isDarkMode ? 0.4 : 0.1;
  const shadowRadius = elevation * 2;
  const shadowOffset = { width: 0, height: elevation };
  const elevationValue = elevation * 2;
  
  const cardStyle = [
    styles.card,
    {
      backgroundColor: colors.card,
      padding,
      borderWidth: noBorder ? 0 : StyleSheet.hairlineWidth,
      borderColor: colors.border,
      shadowColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : '#000',
      shadowOffset,
      shadowOpacity,
      shadowRadius,
      elevation: elevationValue,
    },
    style,
  ];
  
  if (onPress) {
    return (
      <TouchableOpacity 
        style={cardStyle} 
        onPress={onPress}
        activeOpacity={0.9}
      >
        {children}
      </TouchableOpacity>
    );
  }
  
  return <View style={cardStyle}>{children}</View>;
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 16,
    overflow: Platform.OS === 'android' ? 'hidden' : 'visible',
  },
});

export default Card;
