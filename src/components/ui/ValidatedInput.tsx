import React from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TextInputProps,
  KeyboardTypeOptions
} from 'react-native';
import { AlertCircle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface ValidatedInputProps extends Omit<TextInputProps, 'style'> {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  required?: boolean;
  keyboardType?: KeyboardTypeOptions;
  multiline?: boolean;
  numberOfLines?: number;
  placeholder?: string;
  maxLength?: number;
  containerStyle?: any;
  inputStyle?: any;
  labelStyle?: any;
  errorStyle?: any;
  showErrorIcon?: boolean;
  onBlur?: () => void;
  onFocus?: () => void;
}

const ValidatedInput: React.FC<ValidatedInputProps> = ({
  label,
  value,
  onChangeText,
  error,
  required = false,
  keyboardType = 'default',
  multiline = false,
  numberOfLines = 1,
  placeholder,
  maxLength,
  containerStyle,
  inputStyle,
  labelStyle,
  errorStyle,
  showErrorIcon = true,
  onBlur,
  onFocus,
  ...textInputProps
}) => {
  const { colors, isDarkMode } = useTheme();
  const hasError = !!error;

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Label */}
      <View style={styles.labelContainer}>
        <Text style={[
          styles.label,
          { color: colors.textLight },
          labelStyle
        ]}>
          {label}
          {required && (
            <Text style={[styles.required, { color: colors.error }]}> *</Text>
          )}
        </Text>
        {maxLength && (
          <Text style={[styles.charCount, { color: colors.textLight }]}>
            {value.length}/{maxLength}
          </Text>
        )}
      </View>

      {/* Input */}
      <TextInput
        style={[
          styles.input,
          {
            backgroundColor: isDarkMode ? colors.card : colors.background,
            color: colors.text,
            borderColor: hasError ? colors.error : colors.border,
            borderWidth: hasError ? 2 : 1,
          },
          multiline && styles.multilineInput,
          inputStyle
        ]}
        value={value}
        onChangeText={onChangeText}
        onBlur={onBlur}
        onFocus={onFocus}
        placeholder={placeholder}
        placeholderTextColor={colors.textLight}
        keyboardType={keyboardType}
        multiline={multiline}
        numberOfLines={multiline ? numberOfLines : 1}
        textAlignVertical={multiline ? 'top' : 'center'}
        maxLength={maxLength}
        {...textInputProps}
      />

      {/* Error Message */}
      {hasError && (
        <View style={styles.errorContainer}>
          {showErrorIcon && (
            <AlertCircle size={16} color={colors.error} style={styles.errorIcon} />
          )}
          <Text style={[
            styles.errorText,
            { color: colors.error },
            errorStyle
          ]}>
            {error}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  labelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  required: {
    fontSize: 16,
    fontWeight: '600',
  },
  charCount: {
    fontSize: 12,
  },
  input: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 48,
  },
  multilineInput: {
    minHeight: 100,
    paddingTop: 12,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  errorIcon: {
    marginRight: 6,
  },
  errorText: {
    fontSize: 14,
    flex: 1,
  },
});

export default ValidatedInput;