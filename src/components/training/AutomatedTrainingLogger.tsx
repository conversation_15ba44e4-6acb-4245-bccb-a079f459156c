import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch
} from 'react-native';
import { Activity, RotateCcw, Settings, Pause, AlertTriangle } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import AutoPauseManager from './AutoPauseManager';
import AutoPauseSettings from './AutoPauseSettings';

interface AutomatedTrainingLoggerProps {
  isEnabled: boolean;
  isActive: boolean;
  isPaused?: boolean;
  isAutoPaused?: boolean;
  currentSpeed?: number;
  autoPauseSettings?: {
    enabled: boolean;
    inactivityThreshold: number;
    speedThreshold: number;
  };
  onToggleEnabled: (enabled: boolean) => void;
  onStartLogging: () => void;
  onStopLogging: () => void;
  onPauseLogging?: () => void;
  onResumeLogging?: () => void;
  onAutoPause?: () => void;
  onResetLogger: () => void;
  onUpdateAutoPauseSettings?: (settings: any) => void;
  sessionCount: number;
  lastActivity?: string;
}

const AutomatedTrainingLogger: React.FC<AutomatedTrainingLoggerProps> = ({
  isEnabled,
  isActive,
  isPaused = false,
  isAutoPaused = false,
  currentSpeed = 0,
  autoPauseSettings = { enabled: true, inactivityThreshold: 5, speedThreshold: 0.5 },
  onToggleEnabled,
  onStartLogging,
  onStopLogging,
  onPauseLogging,
  onResumeLogging,
  onAutoPause,
  onResetLogger,
  onUpdateAutoPauseSettings,
  sessionCount,
  lastActivity
}) => {
  const { theme } = useTheme();
  const [showAutoPauseSettings, setShowAutoPauseSettings] = React.useState(false);

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.surface,
      borderRadius: 12,
      padding: 20,
      marginBottom: 20,
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 16
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.text
    },
    enableSwitch: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8
    },
    switchLabel: {
      fontSize: 14,
      color: theme.textSecondary
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
      padding: 12,
      backgroundColor: theme.background,
      borderRadius: 8
    },
    statusIcon: {
      marginRight: 12
    },
    statusText: {
      flex: 1
    },
    statusTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.text,
      marginBottom: 4
    },
    statusSubtitle: {
      fontSize: 14,
      color: theme.textSecondary
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 16
    },
    statItem: {
      alignItems: 'center'
    },
    statValue: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.text,
      marginBottom: 4
    },
    statLabel: {
      fontSize: 12,
      color: theme.textSecondary
    },
    controlsContainer: {
      flexDirection: 'row',
      gap: 12
    },
    controlButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      gap: 8
    },
    startButton: {
      backgroundColor: '#10B981'
    },
    pauseButton: {
      backgroundColor: '#F59E0B'
    },
    stopButton: {
      backgroundColor: '#EF4444'
    },
    resetButton: {
      backgroundColor: theme.border
    },
    settingsButton: {
      backgroundColor: theme.surface,
      borderWidth: 1,
      borderColor: theme.border
    },
    disabledButton: {
      backgroundColor: theme.border,
      opacity: 0.6
    },
    buttonText: {
      fontSize: 14,
      fontWeight: '600'
    },
    startButtonText: {
      color: '#FFFFFF'
    },
    pauseButtonText: {
      color: '#FFFFFF'
    },
    stopButtonText: {
      color: '#FFFFFF'
    },
    resetButtonText: {
      color: theme.text
    },
    settingsButtonText: {
      color: theme.text
    },
    disabledButtonText: {
      color: theme.textSecondary
    }
  });

  const getStatusColor = () => {
    if (!isEnabled) return theme.textSecondary;
    if (isAutoPaused) return '#F59E0B';
    if (isPaused) return '#F59E0B';
    if (isActive) return '#10B981';
    return '#F59E0B';
  };

  const getStatusText = () => {
    if (!isEnabled) return 'Disabled';
    if (isAutoPaused) return 'Auto-Paused';
    if (isPaused) return 'Paused';
    if (isActive) return 'Active - Monitoring';
    return 'Ready to Start';
  };

  const getStatusSubtitle = () => {
    if (!isEnabled) return 'Enable automated logging to track training sessions';
    if (isAutoPaused) return `Auto-paused due to ${autoPauseSettings.inactivityThreshold}min inactivity`;
    if (isPaused) return 'Session paused - tap resume to continue';
    if (isActive) return `Monitoring activity (Speed: ${currentSpeed.toFixed(1)} km/h)`;
    if (lastActivity) return `Last activity: ${lastActivity}`;
    return 'Waiting for training activity to begin';
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Automated Training Logger</Text>
        <View style={styles.enableSwitch}>
          <Text style={styles.switchLabel}>Enable</Text>
          <Switch
            value={isEnabled}
            onValueChange={onToggleEnabled}
            trackColor={{ false: theme.border, true: theme.primary }}
            thumbColor={isEnabled ? '#FFFFFF' : theme.textSecondary}
          />
        </View>
      </View>

      <View style={styles.statusContainer}>
        <View style={styles.statusIcon}>
          <Activity size={24} color={getStatusColor()} />
        </View>
        <View style={styles.statusText}>
          <Text style={styles.statusTitle}>{getStatusText()}</Text>
          <Text style={styles.statusSubtitle}>{getStatusSubtitle()}</Text>
        </View>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{sessionCount}</Text>
          <Text style={styles.statLabel}>Sessions Logged</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{isActive ? 'ON' : 'OFF'}</Text>
          <Text style={styles.statLabel}>Status</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{isEnabled ? 'YES' : 'NO'}</Text>
          <Text style={styles.statLabel}>Auto-Detection</Text>
        </View>
      </View>

      <View style={styles.controlsContainer}>
        {!isActive ? (
          <TouchableOpacity
            style={[
              styles.controlButton,
              isEnabled ? styles.startButton : styles.disabledButton
            ]}
            onPress={onStartLogging}
            disabled={!isEnabled}
          >
            <Activity size={16} color={isEnabled ? '#FFFFFF' : theme.textSecondary} />
            <Text style={[
              styles.buttonText,
              isEnabled ? styles.startButtonText : styles.disabledButtonText
            ]}>
              Start Logging
            </Text>
          </TouchableOpacity>
        ) : isPaused ? (
          <TouchableOpacity
            style={[styles.controlButton, styles.startButton]}
            onPress={onResumeLogging}
          >
            <Activity size={16} color="#FFFFFF" />
            <Text style={[styles.buttonText, styles.startButtonText]}>
              Resume
            </Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.controlButton, styles.pauseButton]}
            onPress={onPauseLogging}
          >
            <Pause size={16} color="#FFFFFF" />
            <Text style={[styles.buttonText, styles.pauseButtonText]}>
              Pause
            </Text>
          </TouchableOpacity>
        )}

        {isActive && (
          <TouchableOpacity
            style={[styles.controlButton, styles.stopButton]}
            onPress={onStopLogging}
          >
            <Activity size={16} color="#FFFFFF" />
            <Text style={[styles.buttonText, styles.stopButtonText]}>
              Stop
            </Text>
          </TouchableOpacity>
        )}

        {!isActive && (
          <TouchableOpacity
            style={[styles.controlButton, styles.resetButton]}
            onPress={onResetLogger}
          >
            <RotateCcw size={16} color={theme.text} />
            <Text style={[styles.buttonText, styles.resetButtonText]}>
              Reset
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.controlButton, styles.settingsButton]}
          onPress={() => setShowAutoPauseSettings(true)}
        >
          <Settings size={16} color={theme.text} />
          <Text style={[styles.buttonText, styles.settingsButtonText]}>
            Auto-Pause
          </Text>
        </TouchableOpacity>
      </View>

      {/* Auto-Pause Manager */}
      {onAutoPause && onUpdateAutoPauseSettings && (
        <AutoPauseManager
          isRecording={isActive}
          isPaused={isPaused}
          isAutoPaused={isAutoPaused}
          currentSpeed={currentSpeed}
          settings={autoPauseSettings}
          onAutoPause={onAutoPause}
          onResume={onResumeLogging || (() => {})}
          onStop={onStopLogging}
          onUpdateSettings={onUpdateAutoPauseSettings}
        />
      )}

      {/* Auto-Pause Settings Modal */}
      {onUpdateAutoPauseSettings && (
        <AutoPauseSettings
          settings={autoPauseSettings}
          onUpdateSettings={onUpdateAutoPauseSettings}
          visible={showAutoPauseSettings}
          onClose={() => setShowAutoPauseSettings(false)}
        />
      )}
    </View>
  );
};

export default AutomatedTrainingLogger;