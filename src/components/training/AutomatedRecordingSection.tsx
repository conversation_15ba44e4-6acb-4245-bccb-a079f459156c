import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Activity, Play, Pause, Square } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import LiveMetricsDisplay from './LiveMetricsDisplay';

interface AutomatedRecordingSectionProps {
  isRecording: boolean;
  isPaused: boolean;
  currentData: any;
  currentDuration: number;
  automatedError: string | null;
  onStartRecording: () => void;
  onPauseRecording: () => void;
  onResumeRecording: () => void;
  onStopRecording: () => void;
  canStart: boolean;
  canPause: boolean;
  canResume: boolean;
  canStop: boolean;
  formatDuration: (seconds: number) => string;
}

const AutomatedRecordingSection: React.FC<AutomatedRecordingSectionProps> = ({
  isRecording,
  isPaused,
  currentData,
  currentDuration,
  automatedError,
  onStartRecording,
  onPauseRecording,
  onResumeRecording,
  onStopRecording,
  canStart,
  canPause,
  canResume,
  canStop,
  formatDuration
}) => {
  const { colors } = useTheme();

  return (
    <View style={[styles.automatedSection, { backgroundColor: colors.card }]}>
      <View style={styles.automatedHeader}>
        <Activity size={20} color={colors.primary} />
        <Text style={[styles.automatedTitle, { color: colors.text }]}>Automated Recording</Text>
        {isRecording && (
          <View style={[styles.recordingIndicator, { backgroundColor: colors.error }]}>
            <View style={styles.recordingDot} />
          </View>
        )}
      </View>
      
      {/* Live Metrics Display */}
      {isRecording && currentData && (
        <LiveMetricsDisplay 
          currentData={currentData}
          currentDuration={currentDuration}
          formatDuration={formatDuration}
        />
      )}
      
      {/* Recording Controls */}
      <View style={styles.recordingControls}>
        {!isRecording ? (
          <TouchableOpacity
            style={[styles.recordingButton, styles.startButton, { backgroundColor: colors.success }]}
            onPress={onStartRecording}
            disabled={!canStart}
          >
            <Play size={20} color="#FFFFFF" />
            <Text style={styles.recordingButtonText}>Start Recording</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.activeControls}>
            {!isPaused ? (
              <TouchableOpacity
                style={[styles.recordingButton, styles.pauseButton, { backgroundColor: colors.warning }]}
                onPress={onPauseRecording}
                disabled={!canPause}
              >
                <Pause size={20} color="#FFFFFF" />
                <Text style={styles.recordingButtonText}>Pause</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.recordingButton, styles.resumeButton, { backgroundColor: colors.success }]}
                onPress={onResumeRecording}
                disabled={!canResume}
              >
                <Play size={20} color="#FFFFFF" />
                <Text style={styles.recordingButtonText}>Resume</Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              style={[styles.recordingButton, styles.stopButton, { backgroundColor: colors.error }]}
              onPress={onStopRecording}
              disabled={!canStop}
            >
              <Square size={20} color="#FFFFFF" />
              <Text style={styles.recordingButtonText}>Stop & Review</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
      
      {automatedError && (
        <Text style={[styles.errorText, { color: colors.error }]}>
          {automatedError}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  automatedSection: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.2)',
  },
  automatedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  automatedTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  recordingIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  recordingControls: {
    gap: 8,
  },
  activeControls: {
    flexDirection: 'row',
    gap: 8,
  },
  recordingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 6,
  },
  startButton: {
    flex: 1,
  },
  pauseButton: {
    flex: 1,
  },
  resumeButton: {
    flex: 1,
  },
  stopButton: {
    flex: 1,
  },
  recordingButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  errorText: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default AutomatedRecordingSection;