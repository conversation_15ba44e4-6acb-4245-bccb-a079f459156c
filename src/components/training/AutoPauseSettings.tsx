import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Modal,
  TextInput
} from 'react-native';
import { Settings, Clock, Gauge, X } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';

interface AutoPauseSettings {
  enabled: boolean;
  inactivityThreshold: number; // minutes
  speedThreshold: number; // km/h
}

interface AutoPauseSettingsProps {
  settings: AutoPauseSettings;
  onUpdateSettings: (settings: AutoPauseSettings) => void;
  visible: boolean;
  onClose: () => void;
}

export default function AutoPauseSettings({
  settings,
  onUpdateSettings,
  visible,
  onClose,
}: AutoPauseSettingsProps) {
  const { colors } = useTheme();
  const [localSettings, setLocalSettings] = React.useState(settings);

  React.useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  const handleSave = () => {
    onUpdateSettings(localSettings);
    onClose();
  };

  const updateSetting = (key: keyof AutoPauseSettings, value: any) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: colors.text }]}>
              Auto-Pause Settings
            </Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <Card style={styles.settingCard}>
              <View style={styles.settingRow}>
                <View style={styles.settingInfo}>
                  <Settings size={20} color={colors.primary} />
                  <View style={styles.settingText}>
                    <Text style={[styles.settingTitle, { color: colors.text }]}>
                      Enable Auto-Pause
                    </Text>
                    <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                      Automatically pause when animal is inactive
                    </Text>
                  </View>
                </View>
                <Switch
                  value={localSettings.enabled}
                  onValueChange={(value) => updateSetting('enabled', value)}
                  trackColor={{ false: colors.border, true: colors.primary + '40' }}
                  thumbColor={localSettings.enabled ? colors.primary : colors.textSecondary}
                />
              </View>
            </Card>

            {localSettings.enabled && (
              <>
                <Card style={styles.settingCard}>
                  <View style={styles.settingRow}>
                    <View style={styles.settingInfo}>
                      <Clock size={20} color={colors.primary} />
                      <View style={styles.settingText}>
                        <Text style={[styles.settingTitle, { color: colors.text }]}>
                          Inactivity Threshold
                        </Text>
                        <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                          Minutes of inactivity before auto-pause
                        </Text>
                      </View>
                    </View>
                    <View style={styles.inputContainer}>
                      <TextInput
                        style={[styles.input, { 
                          color: colors.text, 
                          borderColor: colors.border,
                          backgroundColor: colors.surface 
                        }]}
                        value={localSettings.inactivityThreshold.toString()}
                        onChangeText={(text) => {
                          const value = parseInt(text) || 1;
                          updateSetting('inactivityThreshold', Math.max(1, Math.min(30, value)));
                        }}
                        keyboardType="numeric"
                        maxLength={2}
                      />
                      <Text style={[styles.unit, { color: colors.textSecondary }]}>min</Text>
                    </View>
                  </View>
                </Card>

                <Card style={styles.settingCard}>
                  <View style={styles.settingRow}>
                    <View style={styles.settingInfo}>
                      <Gauge size={20} color={colors.primary} />
                      <View style={styles.settingText}>
                        <Text style={[styles.settingTitle, { color: colors.text }]}>
                          Speed Threshold
                        </Text>
                        <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                          Speed below which animal is considered inactive
                        </Text>
                      </View>
                    </View>
                    <View style={styles.inputContainer}>
                      <TextInput
                        style={[styles.input, { 
                          color: colors.text, 
                          borderColor: colors.border,
                          backgroundColor: colors.surface 
                        }]}
                        value={localSettings.speedThreshold.toString()}
                        onChangeText={(text) => {
                          const value = parseFloat(text) || 0.1;
                          updateSetting('speedThreshold', Math.max(0.1, Math.min(5, value)));
                        }}
                        keyboardType="decimal-pad"
                        maxLength={3}
                      />
                      <Text style={[styles.unit, { color: colors.textSecondary }]}>km/h</Text>
                    </View>
                  </View>
                </Card>

                <View style={[styles.infoBox, { backgroundColor: colors.primary + '10' }]}>
                  <Text style={[styles.infoText, { color: colors.primary }]}>
                    💡 Auto-pause helps save battery and prevents accidental long recordings when your animal stops moving.
                  </Text>
                </View>
              </>
            )}
          </View>

          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: colors.primary }]}
              onPress={handleSave}
            >
              <Text style={[styles.saveButtonText, { color: colors.white }]}>
                Save Settings
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    padding: 20,
    gap: 16,
  },
  settingCard: {
    padding: 16,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    textAlign: 'center',
    minWidth: 60,
  },
  unit: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoBox: {
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
  actions: {
    padding: 20,
    paddingTop: 0,
  },
  saveButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});