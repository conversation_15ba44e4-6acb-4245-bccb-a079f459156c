import React from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import { Activity, MapPin, Zap, Heart } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';

interface LiveMetrics {
  heartRate?: number;
  speed?: number;
  distance?: number;
  calories?: number;
  intensity?: 'low' | 'medium' | 'high';
}

interface LiveMetricsDisplayProps {
  metrics: LiveMetrics;
  isRecording: boolean;
}

const LiveMetricsDisplay: React.FC<LiveMetricsDisplayProps> = ({
  metrics,
  isRecording
}) => {
  const { theme } = useTheme();

  const getIntensityColor = (intensity?: string) => {
    switch (intensity) {
      case 'high':
        return '#EF4444';
      case 'medium':
        return '#F59E0B';
      case 'low':
        return '#10B981';
      default:
        return theme.textSecondary;
    }
  };

  const metricItems = [
    {
      icon: Heart,
      label: 'Heart Rate',
      value: metrics.heartRate ? `${metrics.heartRate} bpm` : '--',
      color: '#EF4444',
    },
    {
      icon: Zap,
      label: 'Speed',
      value: metrics.speed ? `${metrics.speed.toFixed(1)} mph` : '--',
      color: '#3B82F6',
    },
    {
      icon: MapPin,
      label: 'Distance',
      value: metrics.distance ? `${metrics.distance.toFixed(2)} mi` : '--',
      color: '#10B981',
    },
    {
      icon: Activity,
      label: 'Calories',
      value: metrics.calories ? `${metrics.calories} cal` : '--',
      color: '#F59E0B',
    },
  ];

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.text }]}>Live Metrics</Text>
        <View style={[
          styles.statusIndicator,
          { backgroundColor: isRecording ? '#10B981' : theme.textSecondary }
        ]} />
      </View>

      <View style={styles.metricsGrid}>
        {metricItems.map((item, index) => (
          <View key={index} style={styles.metricItem}>
            <View style={styles.metricHeader}>
              <item.icon size={20} color={item.color} />
              <Text style={[styles.metricLabel, { color: theme.textSecondary }]}>
                {item.label}
              </Text>
            </View>
            <Text style={[styles.metricValue, { color: theme.text }]}>
              {item.value}
            </Text>
          </View>
        ))}
      </View>

      {metrics.intensity && (
        <View style={styles.intensityContainer}>
          <Text style={[styles.intensityLabel, { color: theme.textSecondary }]}>
            Intensity:
          </Text>
          <Text style={[
            styles.intensityValue,
            { color: getIntensityColor(metrics.intensity) }
          ]}>
            {metrics.intensity.charAt(0).toUpperCase() + metrics.intensity.slice(1)}
          </Text>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricItem: {
    width: '48%',
    marginBottom: 16,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    marginLeft: 6,
  },
  metricValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  intensityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  intensityLabel: {
    fontSize: 14,
    marginRight: 8,
  },
  intensityValue: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default LiveMetricsDisplay;