import React from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Animal } from '../../mocks/animals';

interface TrainingSessionHeaderProps {
  animal: Animal;
}

const TrainingSessionHeader: React.FC<TrainingSessionHeaderProps> = ({ animal }) => {
  const { colors } = useTheme();

  return (
    <View style={[styles.animalInfo, { backgroundColor: colors.card }]}>
      <Text style={[styles.animalName, { color: colors.text }]}>{animal.name}</Text>
      <Text style={[styles.animalDetails, { color: colors.textLight }]}>
        {animal.breed} • {animal.age} years old
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  animalInfo: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  animalName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  animalDetails: {
    fontSize: 14,
  },
});

export default TrainingSessionHeader;