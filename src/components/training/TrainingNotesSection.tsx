import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface TrainingNotesSectionProps {
  notes: string;
  onNotesChange: (notes: string) => void;
  editable?: boolean;
}

const TrainingNotesSection: React.FC<TrainingNotesSectionProps> = ({
  notes,
  onNotesChange,
  editable = true
}) => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.surface,
      borderRadius: 12,
      padding: 20,
      marginBottom: 20,
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.text,
      marginBottom: 12
    },
    notesInput: {
      backgroundColor: theme.background,
      borderRadius: 8,
      padding: 16,
      fontSize: 16,
      color: theme.text,
      textAlignVertical: 'top',
      minHeight: 120,
      borderWidth: 1,
      borderColor: theme.border
    },
    notesDisplay: {
      backgroundColor: theme.background,
      borderRadius: 8,
      padding: 16,
      minHeight: 120,
      borderWidth: 1,
      borderColor: theme.border
    },
    notesText: {
      fontSize: 16,
      color: theme.text,
      lineHeight: 24
    },
    placeholder: {
      fontSize: 16,
      color: theme.textSecondary,
      fontStyle: 'italic'
    }
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Training Notes</Text>
      
      {editable ? (
        <TextInput
          style={styles.notesInput}
          value={notes}
          onChangeText={onNotesChange}
          placeholder="Add notes about this training session..."
          placeholderTextColor={theme.textSecondary}
          multiline
          numberOfLines={6}
          textAlignVertical="top"
        />
      ) : (
        <View style={styles.notesDisplay}>
          {notes ? (
            <Text style={styles.notesText}>{notes}</Text>
          ) : (
            <Text style={styles.placeholder}>No notes added for this session</Text>
          )}
        </View>
      )}
    </View>
  );
};

export default TrainingNotesSection;