import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Platform
} from 'react-native';
import { Clock } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../../contexts/ThemeContext';

interface TrainingSessionFormProps {
  sessionDate: Date;
  showDatePicker: boolean;
  distance: string;
  speed: string;
  duration: string;
  restTime: string;
  intensityLabel: string;
  notes: string;
  isRecording: boolean;
  isAutomatedMode: boolean;
  currentData: any;
  currentDuration: number;
  onDatePress: () => void;
  onDateChange: (event: any, selectedDate?: Date) => void;
  onDistanceChange: (value: string) => void;
  onSpeedChange: (value: string) => void;
  onDurationChange: (value: string) => void;
  onRestTimeChange: (value: string) => void;
  onIntensityChange: (intensity: string) => void;
  onNotesChange: (value: string) => void;
  formatDuration: (seconds: number) => string;
}

const TrainingSessionForm: React.FC<TrainingSessionFormProps> = ({
  sessionDate,
  showDatePicker,
  distance,
  speed,
  duration,
  restTime,
  intensityLabel,
  notes,
  isRecording,
  isAutomatedMode,
  currentData,
  currentDuration,
  onDatePress,
  onDateChange,
  onDistanceChange,
  onSpeedChange,
  onDurationChange,
  onRestTimeChange,
  onIntensityChange,
  onNotesChange,
  formatDuration
}) => {
  const { colors } = useTheme();
  
  const intensityOptions = ['Light', 'Moderate', 'Vigorous', 'High Intensity', 'Endurance'];

  return (
    <>
      {/* Session Date */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Session Date & Time</Text>
        <TouchableOpacity
          style={[styles.dateButton, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={onDatePress}
        >
          <Clock size={20} color={colors.primary} />
          <Text style={[styles.dateText, { color: colors.text }]}>
            {sessionDate.toLocaleDateString()} {sessionDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </TouchableOpacity>
      </View>
      
      {showDatePicker && (
        <DateTimePicker
          value={sessionDate}
          mode="datetime"
          display="default"
          onChange={onDateChange}
        />
      )}
      
      {/* Distance */}
      <View style={styles.section}>
        <View style={styles.fieldHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Distance (km) *</Text>
          {isAutomatedMode && currentData.distance && (
            <Text style={[styles.autoFilledLabel, { color: colors.success }]}>Auto-filled</Text>
          )}
        </View>
        <TextInput
          style={[
            styles.input, 
            { 
              backgroundColor: colors.card, 
              borderColor: colors.border, 
              color: colors.text,
              opacity: isRecording ? 0.6 : 1
            }
          ]}
          value={distance}
          onChangeText={onDistanceChange}
          placeholder="e.g., 5.2"
          placeholderTextColor={colors.textLight}
          keyboardType="decimal-pad"
          editable={!isRecording}
        />
      </View>
      
      {/* Speed */}
      <View style={styles.section}>
        <View style={styles.fieldHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Average Speed (km/h) *</Text>
          {isAutomatedMode && currentData.avg_speed && (
            <Text style={[styles.autoFilledLabel, { color: colors.success }]}>Auto-filled</Text>
          )}
        </View>
        <TextInput
          style={[
            styles.input, 
            { 
              backgroundColor: colors.card, 
              borderColor: colors.border, 
              color: colors.text,
              opacity: isRecording ? 0.6 : 1
            }
          ]}
          value={speed}
          onChangeText={onSpeedChange}
          placeholder="e.g., 15.5"
          placeholderTextColor={colors.textLight}
          keyboardType="decimal-pad"
          editable={!isRecording}
        />
        {isAutomatedMode && currentData.max_speed && (
          <Text style={[styles.additionalInfo, { color: colors.textLight }]}>
            Max Speed: {currentData.max_speed.toFixed(1)} km/h
          </Text>
        )}
      </View>
      
      {/* Duration */}
      <View style={styles.section}>
        <View style={styles.fieldHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Duration *</Text>
          {isAutomatedMode && isRecording && (
            <Text style={[styles.liveLabel, { color: colors.primary }]}>Live: {formatDuration(currentDuration)}</Text>
          )}
        </View>
        <TextInput
          style={[
            styles.input, 
            { 
              backgroundColor: colors.card, 
              borderColor: colors.border, 
              color: colors.text,
              opacity: isRecording ? 0.6 : 1
            }
          ]}
          value={duration}
          onChangeText={onDurationChange}
          placeholder="e.g., 25:30 or 25 (minutes)"
          placeholderTextColor={colors.textLight}
          editable={!isRecording}
        />
        <Text style={[styles.helpText, { color: colors.textLight }]}>Format: MM:SS or just minutes</Text>
      </View>
      
      {/* Rest Time */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Rest Time (optional)</Text>
        <TextInput
          style={[
            styles.input, 
            { 
              backgroundColor: colors.card, 
              borderColor: colors.border, 
              color: colors.text,
              opacity: isRecording ? 0.6 : 1
            }
          ]}
          value={restTime}
          onChangeText={onRestTimeChange}
          placeholder="e.g., 10:00 or 10 (minutes)"
          placeholderTextColor={colors.textLight}
          editable={!isRecording}
        />
      </View>
      
      {/* Intensity */}
      <View style={styles.section}>
        <View style={styles.fieldHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Intensity Level</Text>
          {isAutomatedMode && currentData.avg_intensity && (
            <Text style={[styles.autoFilledLabel, { color: colors.success }]}>Auto-detected</Text>
          )}
        </View>
        <View style={styles.intensityContainer}>
          {intensityOptions.map((intensity) => (
            <TouchableOpacity
              key={intensity}
              style={[
                styles.intensityButton,
                {
                  backgroundColor: intensityLabel === intensity ? colors.primary : colors.card,
                  borderColor: colors.border,
                  opacity: isRecording ? 0.6 : 1
                }
              ]}
              onPress={() => onIntensityChange(intensity)}
              disabled={isRecording}
            >
              <Text style={[
                styles.intensityText,
                {
                  color: intensityLabel === intensity ? '#FFFFFF' : colors.text
                }
              ]}>
                {intensity}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        {isAutomatedMode && currentData.avg_intensity && (
          <Text style={[styles.additionalInfo, { color: colors.textLight }]}>
            Current: {currentData.avg_intensity.toFixed(0)}% • Max: {currentData.max_intensity?.toFixed(0) || 0}%
          </Text>
        )}
      </View>
      
      {/* Notes */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Notes (optional)</Text>
        <TextInput
          style={[
            styles.textArea, 
            { 
              backgroundColor: colors.card, 
              borderColor: colors.border, 
              color: colors.text,
              opacity: isRecording ? 0.6 : 1
            }
          ]}
          value={notes}
          onChangeText={onNotesChange}
          placeholder={isAutomatedMode ? "Add notes about the automated session..." : "Add any additional notes about the training session..."}
          placeholderTextColor={colors.textLight}
          multiline
          numberOfLines={4}
          textAlignVertical="top"
          editable={!isRecording}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
  },
  helpText: {
    fontSize: 12,
    marginTop: 4,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
  },
  dateText: {
    fontSize: 16,
    marginLeft: 8,
  },
  intensityContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  intensityButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  intensityText: {
    fontSize: 14,
    fontWeight: '500',
  },
  fieldHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  autoFilledLabel: {
    fontSize: 12,
    fontWeight: '500',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
  },
  liveLabel: {
    fontSize: 12,
    fontWeight: '600',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  additionalInfo: {
    fontSize: 12,
    marginTop: 4,
  },
});

export default TrainingSessionForm;