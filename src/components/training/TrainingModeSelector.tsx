import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Activity } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface TrainingModeSelectorProps {
  isAutomatedMode: boolean;
  onModeChange: (automated: boolean) => void;
  isRecording: boolean;
}

const TrainingModeSelector: React.FC<TrainingModeSelectorProps> = ({
  isAutomatedMode,
  onModeChange,
  isRecording
}) => {
  const { colors } = useTheme();

  return (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>Recording Mode</Text>
      <View style={styles.modeSelector}>
        <TouchableOpacity
          style={[
            styles.modeButton,
            {
              backgroundColor: !isAutomatedMode ? colors.primary : colors.card,
              borderColor: colors.border
            }
          ]}
          onPress={() => onModeChange(false)}
          disabled={isRecording}
        >
          <Text style={[
            styles.modeButtonText,
            {
              color: !isAutomatedMode ? '#FFFFFF' : colors.text
            }
          ]}>
            Manual Entry
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.modeButton,
            {
              backgroundColor: isAutomatedMode ? colors.primary : colors.card,
              borderColor: colors.border
            }
          ]}
          onPress={() => onModeChange(true)}
          disabled={isRecording}
        >
          <Activity size={16} color={isAutomatedMode ? '#FFFFFF' : colors.text} />
          <Text style={[
            styles.modeButtonText,
            {
              color: isAutomatedMode ? '#FFFFFF' : colors.text
            }
          ]}>
            Automated
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  modeSelector: {
    flexDirection: 'row',
    gap: 8,
  },
  modeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    gap: 6,
  },
  modeButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default TrainingModeSelector;