import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Play, Pause, Square, RotateCcw } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface TrainingSessionTimerProps {
  isRunning: boolean;
  duration: number;
  onStart: () => void;
  onPause: () => void;
  onStop: () => void;
  onReset: () => void;
}

const TrainingSessionTimer: React.FC<TrainingSessionTimerProps> = ({
  isRunning,
  duration,
  onStart,
  onPause,
  onStop,
  onReset
}) => {
  const { theme } = useTheme();

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes
        .toString()
        .padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
      .toString()
      .padStart(2, '0')}`;
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.surface }]}>
      <View style={styles.timerDisplay}>
        <Text style={[styles.timeText, { color: theme.text }]}>
          {formatTime(duration)}
        </Text>
        <Text style={[styles.statusText, { color: theme.textSecondary }]}>
          {isRunning ? 'Recording...' : duration > 0 ? 'Paused' : 'Ready to start'}
        </Text>
      </View>

      <View style={styles.controls}>
        {!isRunning ? (
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: theme.primary }]}
            onPress={onStart}
          >
            <Play size={24} color="#FFFFFF" />
            <Text style={styles.buttonText}>Start</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: '#F59E0B' }]}
            onPress={onPause}
          >
            <Pause size={24} color="#FFFFFF" />
            <Text style={styles.buttonText}>Pause</Text>
          </TouchableOpacity>
        )}

        {duration > 0 && (
          <>
            <TouchableOpacity
              style={[styles.secondaryButton, { borderColor: theme.border }]}
              onPress={onStop}
            >
              <Square size={20} color={theme.text} />
              <Text style={[styles.secondaryButtonText, { color: theme.text }]}>
                Stop
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.secondaryButton, { borderColor: theme.border }]}
              onPress={onReset}
            >
              <RotateCcw size={20} color={theme.text} />
              <Text style={[styles.secondaryButtonText, { color: theme.text }]}>
                Reset
              </Text>
            </TouchableOpacity>
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    alignItems: 'center',
  },
  timerDisplay: {
    alignItems: 'center',
    marginBottom: 24,
  },
  timeText: {
    fontSize: 48,
    fontWeight: '700',
    fontFamily: 'monospace',
  },
  statusText: {
    fontSize: 16,
    marginTop: 8,
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    gap: 6,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default TrainingSessionTimer;