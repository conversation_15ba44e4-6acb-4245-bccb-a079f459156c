import React, { useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert
} from 'react-native';
import { Pause, Play, Square, Clock, Activity } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { toast } from 'sonner-native';

interface AutoPauseSettings {
  enabled: boolean;
  inactivityThreshold: number; // minutes
  speedThreshold: number; // km/h
}

interface AutoPauseManagerProps {
  isRecording: boolean;
  isPaused: boolean;
  isAutoPaused: boolean;
  currentSpeed: number;
  settings: AutoPauseSettings;
  onAutoPause: () => void;
  onResume: () => void;
  onStop: () => void;
  onUpdateSettings: (settings: AutoPauseSettings) => void;
}

export default function AutoPauseManager({
  isRecording,
  isPaused,
  isAutoPaused,
  currentSpeed,
  settings,
  onAutoPause,
  onResume,
  onStop,
  onUpdateSettings,
}: AutoPauseManagerProps) {
  const { colors } = useTheme();
  const [inactivityStartTime, setInactivityStartTime] = React.useState<Date | null>(null);
  const [showAutoPauseModal, setShowAutoPauseModal] = React.useState(false);

  // Auto-pause detection logic
  const checkForInactivity = useCallback(() => {
    if (!isRecording || isPaused || !settings.enabled) {
      return;
    }

    const now = new Date();

    // Check if speed is below threshold (indicating inactivity)
    if (currentSpeed < settings.speedThreshold) {
      if (!inactivityStartTime) {
        // Start tracking inactivity
        setInactivityStartTime(now);
      } else {
        // Check if inactivity duration exceeds threshold
        const inactivityDuration = (now.getTime() - inactivityStartTime.getTime()) / (1000 * 60); // minutes
        
        if (inactivityDuration >= settings.inactivityThreshold) {
          // Auto-pause the session
          handleAutoPause();
        }
      }
    } else {
      // Activity detected, reset inactivity tracking
      setInactivityStartTime(null);
    }
  }, [isRecording, isPaused, settings, currentSpeed, inactivityStartTime]);

  // Handle auto-pause
  const handleAutoPause = useCallback(() => {
    onAutoPause();
    setInactivityStartTime(null);
    setShowAutoPauseModal(true);
    
    // Show notification
    toast('Session auto-paused due to inactivity', {
      description: `No movement detected for ${settings.inactivityThreshold} minutes`,
      duration: 8000,
    });
  }, [onAutoPause, settings.inactivityThreshold]);

  // Handle resume from auto-pause
  const handleResume = useCallback(() => {
    onResume();
    setShowAutoPauseModal(false);
    toast('Training session resumed');
  }, [onResume]);

  // Handle stop from auto-pause
  const handleStop = useCallback(() => {
    Alert.alert(
      'Stop Training Session',
      'Are you sure you want to stop this training session?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Stop',
          style: 'destructive',
          onPress: () => {
            onStop();
            setShowAutoPauseModal(false);
          },
        },
      ]
    );
  }, [onStop]);

  // Run inactivity check every 30 seconds
  useEffect(() => {
    if (!settings.enabled) return;

    const interval = setInterval(checkForInactivity, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [checkForInactivity, settings.enabled]);

  // Auto-pause modal
  const renderAutoPauseModal = () => (
    <Modal
      visible={showAutoPauseModal && isAutoPaused}
      transparent
      animationType="fade"
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
          <View style={styles.modalHeader}>
            <Pause size={32} color={colors.warning} />
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Session Auto-Paused
            </Text>
          </View>
          
          <Text style={[styles.modalDescription, { color: colors.textSecondary }]}>
            Your training session was automatically paused due to {settings.inactivityThreshold} minutes of inactivity.
          </Text>
          
          <View style={styles.inactivityInfo}>
            <Clock size={16} color={colors.textSecondary} />
            <Text style={[styles.inactivityText, { color: colors.textSecondary }]}>
              Speed below {settings.speedThreshold} km/h
            </Text>
          </View>
          
          <View style={styles.modalActions}>
            <TouchableOpacity
              style={[styles.actionButton, styles.resumeButton, { backgroundColor: colors.primary }]}
              onPress={handleResume}
            >
              <Play size={20} color={colors.white} />
              <Text style={[styles.actionButtonText, { color: colors.white }]}>
                Resume
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.stopButton, { backgroundColor: colors.error }]}
              onPress={handleStop}
            >
              <Square size={20} color={colors.white} />
              <Text style={[styles.actionButtonText, { color: colors.white }]}>
                Stop & Review
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  // Inactivity indicator (when tracking inactivity)
  const renderInactivityIndicator = () => {
    if (!inactivityStartTime || !settings.enabled || isPaused) return null;

    const elapsed = (Date.now() - inactivityStartTime.getTime()) / (1000 * 60); // minutes
    const remaining = Math.max(0, settings.inactivityThreshold - elapsed);
    
    return (
      <View style={[styles.inactivityIndicator, { backgroundColor: colors.warning + '20' }]}>
        <Activity size={16} color={colors.warning} />
        <Text style={[styles.inactivityText, { color: colors.warning }]}>
          Auto-pause in {Math.ceil(remaining)}m
        </Text>
      </View>
    );
  };

  return (
    <>
      {renderInactivityIndicator()}
      {renderAutoPauseModal()}
    </>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  modalDescription: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 16,
  },
  inactivityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    gap: 8,
  },
  inactivityText: {
    fontSize: 14,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  resumeButton: {
    // Primary color applied via backgroundColor prop
  },
  stopButton: {
    // Error color applied via backgroundColor prop
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  inactivityIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginVertical: 8,
    gap: 8,
  },
});