/**
 * @magic_description Intensity Selector Component
 * Handles the selection of training intensity levels
 * Extracted from LogTrainingSessionScreen for better maintainability
 */

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface IntensitySelectorProps {
  selectedIntensity: string;
  onIntensityChange: (intensity: string) => void;
}

const IntensitySelector: React.FC<IntensitySelectorProps> = ({
  selectedIntensity,
  onIntensityChange
}) => {
  const { colors } = useTheme();
  
  const intensityOptions = ['Light', 'Moderate', 'Vigorous', 'High Intensity', 'Endurance'];
  
  return (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>Intensity Level</Text>
      <View style={styles.intensityContainer}>
        {intensityOptions.map((intensity) => (
          <TouchableOpacity
            key={intensity}
            style={[
              styles.intensityButton,
              {
                backgroundColor: selectedIntensity === intensity ? colors.primary : colors.card,
                borderColor: colors.border
              }
            ]}
            onPress={() => onIntensityChange(intensity)}
          >
            <Text style={[
              styles.intensityText,
              {
                color: selectedIntensity === intensity ? '#FFFFFF' : colors.text
              }
            ]}>
              {intensity}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  intensityContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  intensityButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
  },
  intensityText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default IntensitySelector;