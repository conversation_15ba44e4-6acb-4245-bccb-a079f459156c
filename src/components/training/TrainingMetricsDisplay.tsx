import React from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import { Clock, MapPin, Zap, Activity } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface TrainingMetrics {
  duration: number;
  distance: number;
  intensity: string;
  heartRate?: number;
  caloriesBurned?: number;
  steps?: number;
}

interface TrainingMetricsDisplayProps {
  metrics: TrainingMetrics;
  isRecording: boolean;
}

const TrainingMetricsDisplay: React.FC<TrainingMetricsDisplayProps> = ({
  metrics,
  isRecording
}) => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.surface,
      borderRadius: 12,
      padding: 20,
      marginBottom: 20,
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.text,
      marginBottom: 16
    },
    metricsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 16
    },
    metricCard: {
      flex: 1,
      minWidth: '45%',
      backgroundColor: theme.background,
      borderRadius: 8,
      padding: 16,
      alignItems: 'center'
    },
    metricIcon: {
      marginBottom: 8
    },
    metricValue: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.text,
      marginBottom: 4
    },
    metricLabel: {
      fontSize: 12,
      color: theme.textSecondary,
      textAlign: 'center'
    },
    recordingIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 16,
      paddingVertical: 8,
      paddingHorizontal: 12,
      backgroundColor: '#10B981',
      borderRadius: 20
    },
    recordingDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#FFFFFF',
      marginRight: 8
    },
    recordingText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '600'
    }
  });

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDistance = (meters: number): string => {
    if (meters >= 1000) {
      return `${(meters / 1000).toFixed(1)} km`;
    }
    return `${meters} m`;
  };

  const getIntensityColor = (intensity: string): string => {
    switch (intensity.toLowerCase()) {
      case 'low': return '#10B981';
      case 'medium': return '#F59E0B';
      case 'high': return '#EF4444';
      default: return theme.textSecondary;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Training Metrics</Text>
      
      <View style={styles.metricsGrid}>
        <View style={styles.metricCard}>
          <View style={styles.metricIcon}>
            <Clock size={24} color={theme.primary} />
          </View>
          <Text style={styles.metricValue}>
            {formatDuration(metrics.duration)}
          </Text>
          <Text style={styles.metricLabel}>Duration</Text>
        </View>

        <View style={styles.metricCard}>
          <View style={styles.metricIcon}>
            <MapPin size={24} color={theme.primary} />
          </View>
          <Text style={styles.metricValue}>
            {formatDistance(metrics.distance)}
          </Text>
          <Text style={styles.metricLabel}>Distance</Text>
        </View>

        <View style={styles.metricCard}>
          <View style={styles.metricIcon}>
            <Zap size={24} color={getIntensityColor(metrics.intensity)} />
          </View>
          <Text style={[styles.metricValue, { color: getIntensityColor(metrics.intensity) }]}>
            {metrics.intensity}
          </Text>
          <Text style={styles.metricLabel}>Intensity</Text>
        </View>

        {metrics.heartRate && (
          <View style={styles.metricCard}>
            <View style={styles.metricIcon}>
              <Activity size={24} color="#EF4444" />
            </View>
            <Text style={styles.metricValue}>
              {metrics.heartRate}
            </Text>
            <Text style={styles.metricLabel}>Heart Rate (BPM)</Text>
          </View>
        )}

        {metrics.caloriesBurned && (
          <View style={styles.metricCard}>
            <View style={styles.metricIcon}>
              <Zap size={24} color="#F59E0B" />
            </View>
            <Text style={styles.metricValue}>
              {metrics.caloriesBurned}
            </Text>
            <Text style={styles.metricLabel}>Calories</Text>
          </View>
        )}

        {metrics.steps && (
          <View style={styles.metricCard}>
            <View style={styles.metricIcon}>
              <MapPin size={24} color="#8B5CF6" />
            </View>
            <Text style={styles.metricValue}>
              {metrics.steps.toLocaleString()}
            </Text>
            <Text style={styles.metricLabel}>Steps</Text>
          </View>
        )}
      </View>

      {isRecording && (
        <View style={styles.recordingIndicator}>
          <View style={styles.recordingDot} />
          <Text style={styles.recordingText}>Recording in Progress</Text>
        </View>
      )}
    </View>
  );
};

export default TrainingMetricsDisplay;
export type { TrainingMetrics };