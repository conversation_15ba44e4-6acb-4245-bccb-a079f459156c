import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { Save } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface TrainingSessionActionsProps {
  isLoading: boolean;
  onSave: () => void;
}

const TrainingSessionActions: React.FC<TrainingSessionActionsProps> = ({
  isLoading,
  onSave
}) => {
  const { colors } = useTheme();

  return (
    <>
      <TouchableOpacity
        style={[
          styles.saveButton,
          { backgroundColor: colors.primary },
          isLoading && styles.saveButtonDisabled
        ]}
        onPress={onSave}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="#FFFFFF" />
        ) : (
          <>
            <Save size={20} color="#FFFFFF" />
            <Text style={styles.saveButtonText}>Save Training Session</Text>
          </>
        )}
      </TouchableOpacity>
      
      <View style={styles.bottomPadding} />
    </>
  );
};

const styles = StyleSheet.create({
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginTop: 20,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  bottomPadding: {
    height: 20,
  },
});

export default TrainingSessionActions;