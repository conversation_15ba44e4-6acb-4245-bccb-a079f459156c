import React from 'react';
import {
  View,
  Text,
  StyleSheet
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';

interface AutomatedDataDisplayProps {
  currentData: any;
}

const AutomatedDataDisplay: React.FC<AutomatedDataDisplayProps> = ({ currentData }) => {
  const { colors } = useTheme();

  if (!currentData || Object.keys(currentData).length === 0) {
    return null;
  }

  return (
    <View style={[styles.automatedDataSection, { backgroundColor: colors.card }]}>
      <Text style={[styles.automatedDataTitle, { color: colors.text }]}>Additional Automated Data</Text>
      <View style={styles.automatedDataGrid}>
        {currentData.heart_rate_avg && (
          <View style={styles.automatedDataItem}>
            <Text style={[styles.automatedDataLabel, { color: colors.textLight }]}>Heart Rate</Text>
            <Text style={[styles.automatedDataValue, { color: colors.text }]}>
              Avg: {currentData.heart_rate_avg} BPM
            </Text>
            {currentData.heart_rate_max && (
              <Text style={[styles.automatedDataSubvalue, { color: colors.textLight }]}>
                Max: {currentData.heart_rate_max} BPM
              </Text>
            )}
          </View>
        )}
        
        {currentData.elevation_gain && (
          <View style={styles.automatedDataItem}>
            <Text style={[styles.automatedDataLabel, { color: colors.textLight }]}>Elevation</Text>
            <Text style={[styles.automatedDataValue, { color: colors.text }]}>
              +{currentData.elevation_gain.toFixed(0)}m
            </Text>
          </View>
        )}
        
        {currentData.session_quality && (
          <View style={styles.automatedDataItem}>
            <Text style={[styles.automatedDataLabel, { color: colors.textLight }]}>Session Quality</Text>
            <Text style={[
              styles.automatedDataValue, 
              { 
                color: currentData.session_quality === 'excellent' ? colors.success :
                       currentData.session_quality === 'good' ? colors.primary :
                       currentData.session_quality === 'fair' ? colors.warning : colors.error
              }
            ]}>
              {currentData.session_quality.charAt(0).toUpperCase() + currentData.session_quality.slice(1)}
            </Text>
          </View>
        )}
        
        {currentData.gps_track && currentData.gps_track.length > 0 && (
          <View style={styles.automatedDataItem}>
            <Text style={[styles.automatedDataLabel, { color: colors.textLight }]}>GPS Points</Text>
            <Text style={[styles.automatedDataValue, { color: colors.text }]}>
              {currentData.gps_track.length} recorded
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  automatedDataSection: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(34, 197, 94, 0.2)',
  },
  automatedDataTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  automatedDataGrid: {
    gap: 12,
  },
  automatedDataItem: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  automatedDataLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  automatedDataValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  automatedDataSubvalue: {
    fontSize: 12,
    marginTop: 2,
  },
});

export default AutomatedDataDisplay;