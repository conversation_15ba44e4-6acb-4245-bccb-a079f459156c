
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Thermometer, Heart, Scale, Clock, Smartphone } from 'lucide-react-native';
import { VitalRecord } from '../mocks/vitals';
import { useTheme } from '../contexts/ThemeContext';

interface VitalCardProps {
  vital: VitalRecord;
  onPress?: () => void;
}

const VitalCard: React.FC<VitalCardProps> = ({ vital, onPress }) => {
  const { colors, isDarkMode } = useTheme();
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  };
  
  const isAbnormal = (type: 'temperature' | 'heartRate', value?: number) => {
    if (!value) return false;
    
    switch (type) {
      case 'temperature':
        return value < 37.2 || value > 38.5;
      case 'heartRate':
        return value < 30 || value > 50;
      default:
        return false;
    }
  };
  
  return (
    <TouchableOpacity 
      style={[
        styles.container, 
        { 
          backgroundColor: colors.card,
          shadowColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : '#000'
        }
      ]}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={!onPress}
    >
      <View style={styles.header}>
        <View style={styles.dateContainer}>
          <Clock size={14} color={colors.textLight} />
          <Text style={[styles.date, { color: colors.textLight }]}>{formatDate(vital.timestamp)}</Text>
        </View>
        {vital.isFromDevice && (
          <View style={[styles.sourceContainer, { backgroundColor: colors.primaryLight }]}>
            <Smartphone size={14} color={colors.primary} />
            <Text style={[styles.source, { color: colors.primary }]}>Device</Text>
          </View>
        )}
      </View>
      
      <View style={styles.vitalsContainer}>
        <View style={styles.vitalItem}>
          <Thermometer 
            size={20} 
            color={isAbnormal('temperature', vital.temperature) ? colors.error : colors.primary} 
          />
          <View style={styles.vitalTextContainer}>
            <Text style={[
              styles.vitalValue,
              { color: isAbnormal('temperature', vital.temperature) ? colors.error : colors.text }
            ]}>
              {vital.temperature.toFixed(1)}°C
            </Text>
            <Text style={[styles.vitalLabel, { color: colors.textLight }]}>Temperature</Text>
          </View>
        </View>
        
        {vital.heartRate && (
          <View style={styles.vitalItem}>
            <Heart 
              size={20} 
              color={isAbnormal('heartRate', vital.heartRate) ? colors.error : colors.primary} 
            />
            <View style={styles.vitalTextContainer}>
              <Text style={[
                styles.vitalValue,
                { color: isAbnormal('heartRate', vital.heartRate) ? colors.error : colors.text }
              ]}>
                {vital.heartRate} bpm
              </Text>
              <Text style={[styles.vitalLabel, { color: colors.textLight }]}>Heart Rate</Text>
            </View>
          </View>
        )}
        
        {vital.weight && (
          <View style={styles.vitalItem}>
            <Scale size={20} color={colors.primary} />
            <View style={styles.vitalTextContainer}>
              <Text style={[styles.vitalValue, { color: colors.text }]}>{vital.weight} kg</Text>
              <Text style={[styles.vitalLabel, { color: colors.textLight }]}>Weight</Text>
            </View>
          </View>
        )}
      </View>
      
      {vital.notes && (
        <View style={[styles.notesContainer, { borderTopColor: colors.border }]}>
          <Text style={[styles.notesLabel, { color: colors.text }]}>Notes:</Text>
          <Text style={[styles.notes, { color: colors.textLight }]}>{vital.notes}</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  date: {
    fontSize: 14,
    marginLeft: 4,
  },
  sourceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  source: {
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '500',
  },
  vitalsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  vitalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: 12,
  },
  vitalTextContainer: {
    marginLeft: 8,
  },
  vitalValue: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  vitalLabel: {
    fontSize: 12,
  },
  notesContainer: {
    borderTopWidth: 1,
    paddingTop: 12,
    marginTop: 4,
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  notes: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default VitalCard;
