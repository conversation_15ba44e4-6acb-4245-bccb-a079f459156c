import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { AlertTriangle, CheckCircle, Clock, X } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { SmartHealthAlert } from '../../store/aiHealthStore';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface AlertCardProps {
  alert: SmartHealthAlert;
  onResolve: (alertId: string, notes?: string) => void;
  onDismiss?: (alertId: string) => void;
  showActions?: boolean;
}

const AlertCard: React.FC<AlertCardProps> = ({
  alert,
  onResolve,
  onDismiss,
  showActions = true
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return '#EF4444';
      case 'high': return '#F59E0B';
      case 'medium': return '#3B82F6';
      case 'low': return '#10B981';
      default: return colors.textLight;
    }
  };

  const getPriorityIcon = (priority: string) => {
    const color = getPriorityColor(priority);
    switch (priority) {
      case 'critical':
      case 'high':
        return <AlertTriangle size={20} color={color} />;
      case 'medium':
        return <Clock size={20} color={color} />;
      case 'low':
        return <CheckCircle size={20} color={color} />;
      default:
        return <AlertTriangle size={20} color={color} />;
    }
  };

  const handleResolve = () => {
    onResolve(alert.id, 'Resolved by user');
  };

  const handleDismiss = () => {
    if (onDismiss) {
      onDismiss(alert.id);
    }
  };

  return (
    <Card style={[
      styles.container,
      {
        borderLeftWidth: 4,
        borderLeftColor: getPriorityColor(alert.priority_level)
      }
    ]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          {getPriorityIcon(alert.priority_level)}
          <Text style={[styles.title, { color: colors.text }]} numberOfLines={2}>
            {alert.title}
          </Text>
        </View>
        
        <View style={styles.headerRight}>
          <View style={[
            styles.priorityBadge,
            { backgroundColor: getPriorityColor(alert.priority_level) }
          ]}>
            <Text style={styles.priorityText}>
              {alert.priority_level.toUpperCase()}
            </Text>
          </View>
          
          {showActions && onDismiss && (
            <TouchableOpacity
              style={styles.dismissButton}
              onPress={handleDismiss}
            >
              <X size={16} color={colors.textLight} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Description */}
      <Text style={[styles.description, { color: colors.textLight }]}>
        {alert.description}
      </Text>

      {/* Recommended Actions */}
      {alert.recommended_actions && alert.recommended_actions.length > 0 && (
        <View style={styles.actionsContainer}>
          <Text style={[styles.actionsTitle, { color: colors.text }]}>
            {t('recommendedActions')}:
          </Text>
          {alert.recommended_actions.map((action, index) => (
            <View key={index} style={styles.actionItem}>
              <Text style={[styles.actionBullet, { color: colors.primary }]}>
                •
              </Text>
              <Text style={[styles.actionText, { color: colors.textLight }]}>
                {action}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={[styles.timestamp, { color: colors.textLight }]}>
          {format(new Date(alert.created_at), 'MMM dd, yyyy HH:mm')}
        </Text>
        
        {showActions && (
          <TouchableOpacity
            style={[styles.resolveButton, { backgroundColor: colors.primary }]}
            onPress={handleResolve}
          >
            <CheckCircle size={14} color="#FFFFFF" />
            <Text style={styles.resolveButtonText}>
              {t('markResolved')}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Expiry Warning */}
      {alert.expires_at && (
        <View style={styles.expiryContainer}>
          <Clock size={12} color={colors.textLight} />
          <Text style={[styles.expiryText, { color: colors.textLight }]}>
            {t('expires')}: {format(new Date(alert.expires_at), 'MMM dd, HH:mm')}
          </Text>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginVertical: 8
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
    gap: 8
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    lineHeight: 22
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6
  },
  priorityText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold'
  },
  dismissButton: {
    padding: 4
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16
  },
  actionsContainer: {
    marginBottom: 16
  },
  actionsTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4
  },
  actionBullet: {
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 8,
    marginTop: 2
  },
  actionText: {
    fontSize: 13,
    flex: 1,
    lineHeight: 18
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  timestamp: {
    fontSize: 12
  },
  resolveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4
  },
  resolveButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500'
  },
  expiryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 4
  },
  expiryText: {
    fontSize: 11
  }
});

export default AlertCard;