import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Brain, Zap, Target } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

interface AIOnboardingBannerProps {
  onGetStarted: () => void;
  onDismiss?: () => void;
}

const AIOnboardingBanner: React.FC<AIOnboardingBannerProps> = ({
  onGetStarted,
  onDismiss
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();
  
  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <Brain size={32} color="#FFFFFF" />
          <Text style={styles.title}>{t('meetYourAIAssistant')}</Text>
        </View>
        
        <Text style={styles.description}>
          {t('aiAssistantDescription')}
        </Text>
        
        <View style={styles.features}>
          <View style={styles.feature}>
            <Target size={16} color="#FFFFFF" />
            <Text style={styles.featureText}>{t('readinessScores')}</Text>
          </View>
          <View style={styles.feature}>
            <Zap size={16} color="#FFFFFF" />
            <Text style={styles.featureText}>{t('trainingPlans')}</Text>
          </View>
        </View>
        
        <TouchableOpacity
          style={styles.getStartedButton}
          onPress={onGetStarted}
        >
          <Text style={styles.getStartedText}>{t('logFirstTrainingSession')}</Text>
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    overflow: 'hidden',
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 12,
  },
  description: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    lineHeight: 20,
    marginBottom: 16,
  },
  features: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 20,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureText: {
    fontSize: 12,
    color: '#FFFFFF',
    marginLeft: 6,
    opacity: 0.9,
  },
  getStartedButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  getStartedText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default AIOnboardingBanner;