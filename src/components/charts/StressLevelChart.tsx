import React, { memo, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity
} from 'react-native';
import { Brain, TrendingUp, TrendingDown } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { format } from 'date-fns';

interface StressAnalysis {
  id: string;
  stress_score: number;
  stress_level: string;
  analysis_timestamp: string;
  confidence_level: number;
}

interface StressLevelChartProps {
  data: StressAnalysis[];
  onDataPointPress?: (analysis: StressAnalysis) => void;
  timeRange?: '7d' | '30d' | '90d';
}

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = screenWidth - 40;
const chartHeight = 200;

const StressLevelChart: React.FC<StressLevelChartProps> = memo(({
  data,
  onDataPointPress,
  timeRange = '7d'
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    const sortedData = [...data]
      .sort((a, b) => new Date(a.analysis_timestamp).getTime() - new Date(b.analysis_timestamp).getTime())
      .slice(-20); // Last 20 data points

    return sortedData.map((item, index) => ({
      ...item,
      x: (index / (sortedData.length - 1)) * (chartWidth - 60),
      y: chartHeight - 40 - ((item.stress_score / 100) * (chartHeight - 80))
    }));
  }, [data]);

  const getStressLevelColor = (level: string) => {
    switch (level) {
      case 'very_high': return '#DC2626';
      case 'high': return '#EF4444';
      case 'moderate': return '#F59E0B';
      case 'low': return '#10B981';
      case 'very_low': return '#059669';
      default: return colors.textLight;
    }
  };

  const getStressLevelText = (level: string) => {
    switch (level) {
      case 'very_high': return t('veryHigh');
      case 'high': return t('high');
      case 'moderate': return t('moderate');
      case 'low': return t('low');
      case 'very_low': return t('veryLow');
      default: return level;
    }
  };

  const getTrendDirection = () => {
    if (chartData.length < 2) return null;
    const recent = chartData.slice(-3);
    const avg = recent.reduce((sum, item) => sum + item.stress_score, 0) / recent.length;
    const older = chartData.slice(-6, -3);
    const oldAvg = older.length > 0 ? older.reduce((sum, item) => sum + item.stress_score, 0) / older.length : avg;
    
    if (avg > oldAvg + 5) return 'up';
    if (avg < oldAvg - 5) return 'down';
    return 'stable';
  };

  const trendDirection = getTrendDirection();
  const latestScore = chartData.length > 0 ? chartData[chartData.length - 1].stress_score : 0;
  const latestLevel = chartData.length > 0 ? chartData[chartData.length - 1].stress_level : 'low';

  if (chartData.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: colors.card }]}>
        <View style={styles.header}>
          <Brain size={20} color={colors.textLight} />
          <Text style={[styles.title, { color: colors.text }]}>{t('stressHistory')}</Text>
        </View>
        <View style={styles.emptyState}>
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            {t('noStressAnalysis')}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.header}>
        <Brain size={20} color={getStressLevelColor(latestLevel)} />
        <Text style={[styles.title, { color: colors.text }]}>{t('stressHistory')}</Text>
        <View style={styles.trendContainer}>
          {trendDirection === 'up' && <TrendingUp size={16} color={colors.error} />}
          {trendDirection === 'down' && <TrendingDown size={16} color={colors.success} />}
          <Text style={[styles.currentScore, { color: getStressLevelColor(latestLevel) }]}>
            {latestScore}
          </Text>
        </View>
      </View>

      <View style={styles.chartContainer}>
        <View style={[styles.chart, { height: chartHeight }]}>
          {/* Y-axis labels */}
          <View style={styles.yAxis}>
            {[100, 75, 50, 25, 0].map((value) => (
              <View key={value} style={styles.yAxisLabel}>
                <Text style={[styles.yAxisText, { color: colors.textLight }]}>
                  {value}
                </Text>
              </View>
            ))}
          </View>

          {/* Chart area */}
          <View style={styles.chartArea}>
            {/* Grid lines */}
            {[0, 25, 50, 75, 100].map((value) => {
              const y = chartHeight - 40 - ((value / 100) * (chartHeight - 80));
              return (
                <View
                  key={value}
                  style={[
                    styles.gridLine,
                    { top: y, backgroundColor: colors.border }
                  ]}
                />
              );
            })}

            {/* Data line */}
            {chartData.length > 1 && (
              <View style={styles.lineContainer}>
                {chartData.slice(0, -1).map((point, index) => {
                  const nextPoint = chartData[index + 1];
                  const lineLength = Math.sqrt(
                    Math.pow(nextPoint.x - point.x, 2) + Math.pow(nextPoint.y - point.y, 2)
                  );
                  const angle = Math.atan2(nextPoint.y - point.y, nextPoint.x - point.x) * (180 / Math.PI);
                  
                  return (
                    <View
                      key={index}
                      style={[
                        styles.line,
                        {
                          left: point.x + 30,
                          top: point.y,
                          width: lineLength,
                          transform: [{ rotate: `${angle}deg` }],
                          backgroundColor: getStressLevelColor(point.stress_level)
                        }
                      ]}
                    />
                  );
                })}
              </View>
            )}

            {/* Data points */}
            {chartData.map((point, index) => (
              <TouchableOpacity
                key={point.id}
                style={[
                  styles.dataPoint,
                  {
                    left: point.x + 25,
                    top: point.y - 5,
                    backgroundColor: getStressLevelColor(point.stress_level),
                    borderColor: colors.background
                  }
                ]}
                onPress={() => onDataPointPress?.(point)}
                activeOpacity={0.7}
              >
                <View style={styles.dataPointInner} />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* X-axis labels */}
        <View style={styles.xAxis}>
          {chartData.filter((_, index) => index % Math.ceil(chartData.length / 4) === 0).map((point) => (
            <Text
              key={point.id}
              style={[
                styles.xAxisText,
                { color: colors.textLight, left: point.x + 20 }
              ]}
            >
              {format(new Date(point.analysis_timestamp), 'MMM d')}
            </Text>
          ))}
        </View>
      </View>

      {/* Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendDot, { backgroundColor: getStressLevelColor(latestLevel) }]} />
          <Text style={[styles.legendText, { color: colors.textLight }]}>
            {t('currentStressLevel')}: {getStressLevelText(latestLevel)}
          </Text>
        </View>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentScore: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  chartContainer: {
    marginBottom: 16,
  },
  chart: {
    flexDirection: 'row',
    position: 'relative',
  },
  yAxis: {
    width: 30,
    justifyContent: 'space-between',
    paddingVertical: 20,
  },
  yAxisLabel: {
    alignItems: 'flex-end',
  },
  yAxisText: {
    fontSize: 10,
    fontWeight: '500',
  },
  chartArea: {
    flex: 1,
    position: 'relative',
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    opacity: 0.3,
  },
  lineContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  line: {
    position: 'absolute',
    height: 2,
    opacity: 0.8,
  },
  dataPoint: {
    position: 'absolute',
    width: 10,
    height: 10,
    borderRadius: 5,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dataPointInner: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  xAxis: {
    flexDirection: 'row',
    marginTop: 8,
    position: 'relative',
    height: 20,
  },
  xAxisText: {
    position: 'absolute',
    fontSize: 10,
    fontWeight: '500',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyState: {
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default StressLevelChart;