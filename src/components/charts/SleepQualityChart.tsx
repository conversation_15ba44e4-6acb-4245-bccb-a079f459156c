import React, { memo, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity
} from 'react-native';
import { Moon, Clock, Activity } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { format } from 'date-fns';

interface SleepAnalysis {
  id: string;
  sleep_quality_score: number;
  total_sleep_duration_minutes: number;
  sleep_efficiency_percentage: number;
  sleep_date: string;
  deep_sleep_minutes: number;
  light_sleep_minutes: number;
  rem_sleep_minutes: number;
  wake_episodes: number;
}

interface SleepQualityChartProps {
  data: SleepAnalysis[];
  onDataPointPress?: (analysis: SleepAnalysis) => void;
  chartType?: 'quality' | 'duration' | 'efficiency';
}

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = screenWidth - 40;
const chartHeight = 200;

const SleepQualityChart: React.FC<SleepQualityChartProps> = memo(({
  data,
  onDataPointPress,
  chartType = 'quality'
}) => {
  const { colors } = useTheme();
  const { t } = useLanguage();

  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    const sortedData = [...data]
      .sort((a, b) => new Date(a.sleep_date).getTime() - new Date(b.sleep_date).getTime())
      .slice(-14); // Last 14 days

    return sortedData.map((item, index) => {
      let value: number;
      let maxValue: number;
      
      switch (chartType) {
        case 'duration':
          value = item.total_sleep_duration_minutes;
          maxValue = 600; // 10 hours
          break;
        case 'efficiency':
          value = item.sleep_efficiency_percentage;
          maxValue = 100;
          break;
        default:
          value = item.sleep_quality_score;
          maxValue = 100;
      }

      return {
        ...item,
        value,
        x: (index / (sortedData.length - 1)) * (chartWidth - 60),
        y: chartHeight - 40 - ((value / maxValue) * (chartHeight - 80))
      };
    });
  }, [data, chartType]);

  const getSleepQualityColor = (score: number) => {
    if (score >= 80) return '#10B981';
    if (score >= 60) return '#F59E0B';
    if (score >= 40) return '#EF4444';
    return '#DC2626';
  };

  const getSleepStageColor = (stage: string) => {
    switch (stage) {
      case 'deep': return '#1E40AF';
      case 'light': return '#3B82F6';
      case 'rem': return '#8B5CF6';
      default: return colors.textLight;
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const getChartTitle = () => {
    switch (chartType) {
      case 'duration': return t('sleepDuration');
      case 'efficiency': return t('sleepEfficiency');
      default: return t('sleepQuality');
    }
  };

  const getChartIcon = () => {
    switch (chartType) {
      case 'duration': return <Clock size={20} color={colors.primary} />;
      case 'efficiency': return <Activity size={20} color={colors.primary} />;
      default: return <Moon size={20} color={colors.primary} />;
    }
  };

  const getValueText = (value: number) => {
    switch (chartType) {
      case 'duration': return formatDuration(value);
      case 'efficiency': return `${value.toFixed(1)}%`;
      default: return value.toString();
    }
  };

  const getMaxValue = () => {
    switch (chartType) {
      case 'duration': return 600; // 10 hours
      case 'efficiency': return 100;
      default: return 100;
    }
  };

  const latestData = chartData.length > 0 ? chartData[chartData.length - 1] : null;
  const averageValue = chartData.length > 0 
    ? chartData.reduce((sum, item) => sum + item.value, 0) / chartData.length 
    : 0;

  if (chartData.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: colors.card }]}>
        <View style={styles.header}>
          {getChartIcon()}
          <Text style={[styles.title, { color: colors.text }]}>{getChartTitle()}</Text>
        </View>
        <View style={styles.emptyState}>
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            {t('noSleepAnalysis')}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.header}>
        {getChartIcon()}
        <Text style={[styles.title, { color: colors.text }]}>{getChartTitle()}</Text>
        <View style={styles.statsContainer}>
          <Text style={[styles.currentValue, { color: colors.text }]}>
            {latestData ? getValueText(latestData.value) : '—'}
          </Text>
          <Text style={[styles.averageValue, { color: colors.textLight }]}>
            Avg: {getValueText(averageValue)}
          </Text>
        </View>
      </View>

      <View style={styles.chartContainer}>
        <View style={[styles.chart, { height: chartHeight }]}>
          {/* Y-axis labels */}
          <View style={styles.yAxis}>
            {[100, 75, 50, 25, 0].map((percentage) => {
              const value = (percentage / 100) * getMaxValue();
              return (
                <View key={percentage} style={styles.yAxisLabel}>
                  <Text style={[styles.yAxisText, { color: colors.textLight }]}>
                    {chartType === 'duration' ? formatDuration(value) : 
                     chartType === 'efficiency' ? `${value}%` : value.toString()}
                  </Text>
                </View>
              );
            })}
          </View>

          {/* Chart area */}
          <View style={styles.chartArea}>
            {/* Grid lines */}
            {[0, 25, 50, 75, 100].map((percentage) => {
              const y = chartHeight - 40 - ((percentage / 100) * (chartHeight - 80));
              return (
                <View
                  key={percentage}
                  style={[
                    styles.gridLine,
                    { top: y, backgroundColor: colors.border }
                  ]}
                />
              );
            })}

            {/* Sleep stages bars (for quality chart) */}
            {chartType === 'quality' && chartData.map((point, index) => {
              const barWidth = Math.max(8, (chartWidth - 80) / chartData.length - 4);
              const totalSleep = point.deep_sleep_minutes + point.light_sleep_minutes + point.rem_sleep_minutes;
              
              if (totalSleep === 0) return null;
              
              const deepHeight = (point.deep_sleep_minutes / totalSleep) * (chartHeight - 80);
              const lightHeight = (point.light_sleep_minutes / totalSleep) * (chartHeight - 80);
              const remHeight = (point.rem_sleep_minutes / totalSleep) * (chartHeight - 80);
              
              return (
                <View
                  key={`stages-${point.id}`}
                  style={[
                    styles.stageBar,
                    {
                      left: point.x + 30 - barWidth / 2,
                      bottom: 40,
                      width: barWidth,
                      height: deepHeight + lightHeight + remHeight,
                    }
                  ]}
                >
                  <View style={[
                    styles.stageSegment,
                    {
                      height: deepHeight,
                      backgroundColor: getSleepStageColor('deep'),
                    }
                  ]} />
                  <View style={[
                    styles.stageSegment,
                    {
                      height: lightHeight,
                      backgroundColor: getSleepStageColor('light'),
                    }
                  ]} />
                  <View style={[
                    styles.stageSegment,
                    {
                      height: remHeight,
                      backgroundColor: getSleepStageColor('rem'),
                    }
                  ]} />
                </View>
              );
            })}

            {/* Data line (for duration and efficiency charts) */}
            {chartType !== 'quality' && chartData.length > 1 && (
              <View style={styles.lineContainer}>
                {chartData.slice(0, -1).map((point, index) => {
                  const nextPoint = chartData[index + 1];
                  const lineLength = Math.sqrt(
                    Math.pow(nextPoint.x - point.x, 2) + Math.pow(nextPoint.y - point.y, 2)
                  );
                  const angle = Math.atan2(nextPoint.y - point.y, nextPoint.x - point.x) * (180 / Math.PI);
                  
                  return (
                    <View
                      key={index}
                      style={[
                        styles.line,
                        {
                          left: point.x + 30,
                          top: point.y,
                          width: lineLength,
                          transform: [{ rotate: `${angle}deg` }],
                          backgroundColor: colors.primary
                        }
                      ]}
                    />
                  );
                })}
              </View>
            )}

            {/* Data points */}
            {chartType !== 'quality' && chartData.map((point) => (
              <TouchableOpacity
                key={point.id}
                style={[
                  styles.dataPoint,
                  {
                    left: point.x + 25,
                    top: point.y - 5,
                    backgroundColor: getSleepQualityColor(point.sleep_quality_score),
                    borderColor: colors.background
                  }
                ]}
                onPress={() => onDataPointPress?.(point)}
                activeOpacity={0.7}
              >
                <View style={styles.dataPointInner} />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* X-axis labels */}
        <View style={styles.xAxis}>
          {chartData.filter((_, index) => index % Math.ceil(chartData.length / 5) === 0).map((point) => (
            <Text
              key={point.id}
              style={[
                styles.xAxisText,
                { color: colors.textLight, left: point.x + 20 }
              ]}
            >
              {format(new Date(point.sleep_date), 'MMM d')}
            </Text>
          ))}
        </View>
      </View>

      {/* Legend for sleep stages */}
      {chartType === 'quality' && (
        <View style={styles.legend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: getSleepStageColor('deep') }]} />
            <Text style={[styles.legendText, { color: colors.textLight }]}>{t('deepSleep')}</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: getSleepStageColor('light') }]} />
            <Text style={[styles.legendText, { color: colors.textLight }]}>{t('lightSleep')}</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: getSleepStageColor('rem') }]} />
            <Text style={[styles.legendText, { color: colors.textLight }]}>{t('remSleep')}</Text>
          </View>
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  statsContainer: {
    alignItems: 'flex-end',
  },
  currentValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  averageValue: {
    fontSize: 12,
    fontWeight: '500',
  },
  chartContainer: {
    marginBottom: 16,
  },
  chart: {
    flexDirection: 'row',
    position: 'relative',
  },
  yAxis: {
    width: 50,
    justifyContent: 'space-between',
    paddingVertical: 20,
  },
  yAxisLabel: {
    alignItems: 'flex-end',
  },
  yAxisText: {
    fontSize: 10,
    fontWeight: '500',
  },
  chartArea: {
    flex: 1,
    position: 'relative',
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    opacity: 0.3,
  },
  stageBar: {
    position: 'absolute',
    flexDirection: 'column-reverse',
    borderRadius: 2,
    overflow: 'hidden',
  },
  stageSegment: {
    width: '100%',
  },
  lineContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  line: {
    position: 'absolute',
    height: 2,
    opacity: 0.8,
  },
  dataPoint: {
    position: 'absolute',
    width: 10,
    height: 10,
    borderRadius: 5,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dataPointInner: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  xAxis: {
    flexDirection: 'row',
    marginTop: 8,
    position: 'relative',
    height: 20,
  },
  xAxisText: {
    position: 'absolute',
    fontSize: 10,
    fontWeight: '500',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  legendText: {
    fontSize: 11,
    fontWeight: '500',
  },
  emptyState: {
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default SleepQualityChart;