import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image
} from 'react-native';
import { Camera, Upload, X } from 'lucide-react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useImagePickerActions } from '../hooks/useImagePickerActions';
import ImagePickerModal from './ImagePickerModal';

interface ImagePickerWithOptionsProps {
  label: string;
  imageUrl: string;
  onImageChange: (uri: string) => void;
  placeholder?: string;
  isPassport?: boolean;
}

/**
 * @magic_description Image picker component with multiple input options
 * Supports gallery selection, camera capture, URL input, and image removal
 */
const ImagePickerWithOptions: React.FC<ImagePickerWithOptionsProps> = ({
  label,
  imageUrl,
  onImageChange,
  placeholder,
  isPassport = false
}) => {
  const { colors } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);

  // Get image picker actions from custom hook
  const {
    pickFromGallery<PERSON><PERSON><PERSON>,
    handleImage<PERSON>romUrl<PERSON><PERSON><PERSON>,
    takePhoto<PERSON><PERSON><PERSON>,
  } = useImagePickerActions({
    onImageChange,
    onCloseOptions: () => setModalVisible(false),
  });

  // Handle image removal
  const removeImage = () => {
    onImageChange('');
  };

  // Dynamic styles based on isPassport prop
  const containerStyle = isPassport ? styles.passportContainer : styles.imageContainer;
  const imageStyle = isPassport ? styles.passportImage : styles.image;
  const placeholderStyle = isPassport ? styles.addPassportPlaceholder : styles.addImagePlaceholder;
  const placeholderTextStyle = isPassport ? styles.addPassportText : styles.addImageText;
  const removeButtonStyle = isPassport ? styles.removePassportButton : styles.removeImageButton;

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={[containerStyle, { backgroundColor: colors.card, borderColor: colors.border }]}
        onPress={() => setModalVisible(true)}
      >
        {imageUrl ? (
          <>
            <Image 
              source={{ uri: imageUrl }} 
              style={imageStyle} 
            />
            <TouchableOpacity 
              style={[removeButtonStyle, { backgroundColor: colors.error }]}
              onPress={removeImage}
            >
              <X size={16} color="#FFF" />
            </TouchableOpacity>
          </>
        ) : (
          <View style={placeholderStyle}>
            {isPassport ? (
              <Upload size={32} color={colors.primary} />
            ) : (
              <Camera size={32} color={colors.primary} />
            )}
            <Text style={[placeholderTextStyle, { color: colors.primary }]}>
              {placeholder || `Add ${label}`}
            </Text>
          </View>
        )}
      </TouchableOpacity>
      
      <ImagePickerModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onPickFromGallery={pickFromGalleryHandler}
        onPickFromUrl={handleImageFromUrlHandler}
        onTakePhoto={takePhotoHandler}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  imageContainer: {
    height: 200,
    borderRadius: 12,
    borderWidth: 2,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  passportContainer: {
    height: 150,
    borderRadius: 12,
    borderWidth: 2,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
  passportImage: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
  addImagePlaceholder: {
    alignItems: 'center',
  },
  addPassportPlaceholder: {
    alignItems: 'center',
  },
  addImageText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 8,
  },
  addPassportText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removePassportButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ImagePickerWithOptions;