
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { Heart, Thermometer, Wind, Bluetooth, Wifi } from 'lucide-react-native';
import { COLORS } from '../constants/colors';
import { useAnimalStore } from '../store/animalStore';
import { useLiveVitals } from '../hooks/useLiveVitals';
import LiveMonitorHeader from './liveMonitor/LiveMonitorHeader';
import ConnectionOptionsView from './liveMonitor/ConnectionOptionsView';
import VitalDisplayCard from './liveMonitor/VitalDisplayCard';

interface LiveMonitorProps {
  animalId: string;
  onClose: () => void;
}

const LiveMonitor: React.FC<LiveMonitorProps> = ({ animalId, onClose }) => {
  const { getAnimalById } = useAnimalStore();
  const animal = getAnimalById(animalId);
  
  // Use the live vitals hook
  const {
    heartRate,
    temperature,
    respirationRate,
    elapsed,
    isConnected,
    connectionType,
    isConnecting,
    connectDevice,
    formatTime,
    isAbnormal
  } = useLiveVitals({ animalId });
  
  // Define vital configurations for mapping
  const vitalConfigs = [
    {
      type: 'heartRate' as const,
      value: heartRate,
      label: 'Heart Rate (bpm)',
      icon: Heart,
      formatValue: (value: number) => Math.round(value).toString()
    },
    {
      type: 'temperature' as const,
      value: temperature,
      label: 'Temperature (°C)',
      icon: Thermometer,
      formatValue: (value: number) => value.toFixed(1)
    },
    {
      type: 'respirationRate' as const,
      value: respirationRate,
      label: 'Respiration (bpm)',
      icon: Wind,
      formatValue: (value: number) => Math.round(value).toString()
    }
  ];
  
  return (
    <View style={styles.container}>
      <LiveMonitorHeader 
        animalName={animal?.name || 'Unknown Animal'}
        isConnected={isConnected}
        elapsedTime={elapsed}
        formatTime={formatTime}
        onClose={onClose}
      />
      
      {!isConnected ? (
        <ConnectionOptionsView 
          connectDevice={connectDevice}
          isConnecting={isConnecting}
          connectionType={connectionType}
        />
      ) : (
        <>
          <View style={styles.vitalsContainer}>
            {vitalConfigs.map((config) => (
              <VitalDisplayCard
                key={config.type}
                vitalType={config.type}
                value={config.value}
                label={config.label}
                icon={config.icon}
                isAbnormal={isAbnormal}
                formatValue={config.formatValue}
              />
            ))}
          </View>
          
          <View style={styles.connectionInfo}>
            <Text style={styles.connectionInfoText}>
              Connected via {connectionType === 'bluetooth' ? 'Bluetooth' : 'WiFi'}
            </Text>
            {connectionType === 'bluetooth' ? (
              <Bluetooth size={16} color={COLORS.success} />
            ) : (
              <Wifi size={16} color={COLORS.success} />
            )}
          </View>
          
          <TouchableOpacity 
            style={styles.stopButton}
            onPress={onClose}
          >
            <Text style={styles.stopButtonText}>Stop Monitoring</Text>
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.card,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    width: width - 32,
    alignSelf: 'center',
  },
  vitalsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  connectionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  connectionInfoText: {
    fontSize: 14,
    color: COLORS.text,
    marginRight: 8,
  },
  stopButton: {
    backgroundColor: COLORS.error,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  stopButtonText: {
    color: COLORS.card,
    fontWeight: '600',
    fontSize: 16,
  },
});

export default LiveMonitor;
