import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LucideIcon } from 'lucide-react-native';
import { COLORS } from '../../constants/colors';

export type VitalType = 'temperature' | 'heartRate' | 'respirationRate';

export interface VitalDisplayCardProps {
  vitalType: VitalType;
  value: number;
  label: string;
  icon: LucideIcon;
  isAbnormal: (type: VitalType, value: number) => boolean;
  formatValue?: (value: number) => string;
}

const VitalDisplayCard: React.FC<VitalDisplayCardProps> = ({
  vitalType,
  value,
  label,
  icon: Icon,
  isAbnormal,
  formatValue
}) => {
  const abnormal = isAbnormal(vitalType, value);
  const displayValue = formatValue ? formatValue(value) : Math.round(value).toString();
  
  return (
    <View style={[
      styles.vitalCard,
      abnormal && styles.abnormalCard
    ]}>
      <Icon 
        size={28} 
        color={abnormal ? COLORS.error : COLORS.primary} 
      />
      <Text style={[
        styles.vitalValue,
        abnormal && styles.abnormalValue
      ]}>
        {displayValue}
      </Text>
      <Text style={styles.vitalLabel}>{label}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  vitalCard: {
    width: '30%',
    backgroundColor: COLORS.background,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
  },
  abnormalCard: {
    backgroundColor: 'rgba(229, 62, 62, 0.1)',
  },
  vitalValue: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
    marginVertical: 8,
  },
  abnormalValue: {
    color: COLORS.error,
  },
  vitalLabel: {
    fontSize: 12,
    color: COLORS.textLight,
    textAlign: 'center',
  },
});

export default VitalDisplayCard;