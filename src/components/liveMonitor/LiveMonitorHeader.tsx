import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Activity, X } from 'lucide-react-native';
import { COLORS } from '../../constants/colors';

export interface LiveMonitorHeaderProps {
  animalName: string;
  isConnected: boolean;
  elapsedTime: number;
  formatTime: (seconds: number) => string;
  onClose: () => void;
}

const LiveMonitorHeader: React.FC<LiveMonitorHeaderProps> = ({
  animalName,
  isConnected,
  elapsedTime,
  formatTime,
  onClose
}) => {
  return (
    <View style={styles.header}>
      <View>
        <Text style={styles.title}>Live Monitoring</Text>
        <Text style={styles.animalName}>{animalName}</Text>
      </View>
      
      {isConnected && (
        <View style={styles.timerContainer}>
          <Activity size={16} color={COLORS.primary} />
          <Text style={styles.timer}>{formatTime(elapsedTime)}</Text>
        </View>
      )}
      
      <TouchableOpacity 
        style={styles.closeButton}
        onPress={onClose}
      >
        <X size={20} color={COLORS.text} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  animalName: {
    fontSize: 14,
    color: COLORS.textLight,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primaryLight,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  timer: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.primary,
    marginLeft: 6,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LiveMonitorHeader;