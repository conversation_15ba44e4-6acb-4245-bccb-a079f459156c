import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Bluetooth, Wifi, Activity } from 'lucide-react-native';
import { COLORS } from '../../constants/colors';
import { ConnectionType } from '../../hooks/useLiveVitals';

export interface ConnectionOptionsViewProps {
  connectDevice: (type: 'bluetooth' | 'wifi') => void;
  isConnecting: boolean;
  connectionType: ConnectionType;
}

const ConnectionOptionsView: React.FC<ConnectionOptionsViewProps> = ({
  connectDevice,
  isConnecting,
  connectionType
}) => {
  return (
    <View style={styles.connectionOptions}>
      <Text style={styles.connectionTitle}>Connect to Device</Text>
      <Text style={styles.connectionSubtitle}>Choose a connection method</Text>
      
      <View style={styles.connectionButtons}>
        <TouchableOpacity 
          style={[styles.connectionButton, { backgroundColor: COLORS.primary }]}
          onPress={() => connectDevice('bluetooth')}
          disabled={isConnecting}
        >
          <Bluetooth size={24} color="#FFFFFF" />
          <Text style={styles.connectionButtonText}>Bluetooth</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.connectionButton, { backgroundColor: COLORS.secondary }]}
          onPress={() => connectDevice('wifi')}
          disabled={isConnecting}
        >
          <Wifi size={24} color="#FFFFFF" />
          <Text style={styles.connectionButtonText}>WiFi</Text>
        </TouchableOpacity>
      </View>
      
      {isConnecting && (
        <View style={styles.connectingContainer}>
          <Activity size={24} color={COLORS.primary} />
          <Text style={styles.connectingText}>
            Connecting via {connectionType === 'bluetooth' ? 'Bluetooth' : 'WiFi'}...
          </Text>
        </View>
      )}
      
      <Text style={styles.connectionNote}>
        Note: Premium users can connect via Bluetooth or WiFi to sync data to the cloud in real-time.
      </Text>
      
      <View style={styles.premiumFeatures}>
        <Text style={styles.premiumFeaturesTitle}>Premium Features:</Text>
        <Text style={styles.premiumFeatureItem}>• Real-time vital monitoring</Text>
        <Text style={styles.premiumFeatureItem}>• Bluetooth & WiFi connectivity</Text>
        <Text style={styles.premiumFeatureItem}>• Cloud data synchronization</Text>
        <Text style={styles.premiumFeatureItem}>• Abnormal vitals alerts</Text>
        <Text style={styles.premiumFeatureItem}>• Historical data analysis</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  connectionOptions: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  connectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 8,
  },
  connectionSubtitle: {
    fontSize: 14,
    color: COLORS.textLight,
    marginBottom: 20,
  },
  connectionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 20,
  },
  connectionButton: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    width: '45%',
  },
  connectionButtonText: {
    color: '#FFFFFF',
    fontWeight: '500',
    marginTop: 8,
  },
  connectingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  connectingText: {
    marginLeft: 8,
    color: COLORS.text,
  },
  connectionNote: {
    fontSize: 12,
    color: COLORS.textLight,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 16,
  },
  premiumFeatures: {
    marginTop: 20,
    alignItems: 'center',
  },
  premiumFeaturesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.secondary,
    marginBottom: 8,
  },
  premiumFeatureItem: {
    fontSize: 14,
    color: COLORS.text,
    marginBottom: 4,
  },
});

export default ConnectionOptionsView;