
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Clock, Bell, Coffee } from 'lucide-react-native';
import { FeedingEntry } from '../mocks/feeding';
import { useTheme } from '../contexts/ThemeContext';

interface FeedingScheduleCardProps {
  feeding: FeedingEntry;
  onPress?: () => void;
}

const FeedingScheduleCard: React.FC<FeedingScheduleCardProps> = ({ feeding, onPress }) => {
  const { colors } = useTheme();
  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    
    return date.toLocaleString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };
  
  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.card,
      borderRadius: 16,
      padding: 16,
      marginBottom: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    timeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    time: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginLeft: 8,
    },
    reminderContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.secondaryLight,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginLeft: 12,
    },
    reminderText: {
      fontSize: 12,
      color: colors.secondary,
      marginLeft: 4,
    },
    contentContainer: {
      flexDirection: 'row',
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.primaryLight,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    detailsContainer: {
      flex: 1,
    },
    feedType: {
      fontSize: 16,
      fontWeight: '500',
      color: colors.text,
      marginBottom: 4,
    },
    amount: {
      fontSize: 14,
      color: colors.textSecondary,
      marginBottom: 4,
    },
    notes: {
      fontSize: 14,
      color: colors.textSecondary,
      fontStyle: 'italic',
    },
  });

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={!onPress}
    >
      <View style={styles.timeContainer}>
        <Clock size={16} color={colors.primary} />
        <Text style={styles.time}>{formatTime(feeding.time)}</Text>
        {feeding.reminder && (
          <View style={styles.reminderContainer}>
            <Bell size={12} color={colors.secondary} />
            <Text style={styles.reminderText}>
              Reminder: {feeding.reminderTime ? formatTime(feeding.reminderTime) : 'On'}
            </Text>
          </View>
        )}
      </View>
      
      <View style={styles.contentContainer}>
        <View style={styles.iconContainer}>
          <Coffee size={24} color={colors.primary} />
        </View>
        
        <View style={styles.detailsContainer}>
          <Text style={styles.feedType}>{feeding.feedType}</Text>
          <Text style={styles.amount}>{feeding.amount}</Text>
          
          {feeding.notes && (
            <Text style={styles.notes}>{feeding.notes}</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};



export default FeedingScheduleCard;
