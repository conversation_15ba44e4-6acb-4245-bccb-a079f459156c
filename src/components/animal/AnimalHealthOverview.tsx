import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { Heart, Activity, Thermometer, Weight, TrendingUp } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';

interface HealthMetric {
  id: string;
  label: string;
  value: string;
  unit?: string;
  status: 'normal' | 'warning' | 'critical';
  trend?: 'up' | 'down' | 'stable';
  lastUpdated: string;
}

interface AnimalHealthOverviewProps {
  healthMetrics: HealthMetric[];
  onViewDetails: () => void;
  onAddVitals: () => void;
}

const AnimalHealthOverview: React.FC<AnimalHealthOverviewProps> = ({
  healthMetrics,
  onViewDetails,
  onAddVitals
}) => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 16
    },
    titleContainer: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    icon: {
      marginRight: 12
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.text
    },
    addButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      backgroundColor: theme.primary,
      borderRadius: 6
    },
    addButtonText: {
      fontSize: 12,
      color: '#FFFFFF',
      fontWeight: '600'
    },
    metricsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
      marginBottom: 16
    },
    metricCard: {
      backgroundColor: theme.background,
      borderRadius: 8,
      padding: 16,
      minWidth: '45%',
      flex: 1
    },
    metricHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 8
    },
    metricIcon: {
      marginRight: 8
    },
    metricLabel: {
      fontSize: 12,
      color: theme.textSecondary,
      flex: 1
    },
    trendIcon: {
      marginLeft: 4
    },
    metricValue: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.text,
      marginBottom: 4
    },
    metricUnit: {
      fontSize: 14,
      color: theme.textSecondary
    },
    metricStatus: {
      fontSize: 10,
      fontWeight: '600',
      textTransform: 'uppercase',
      marginTop: 4
    },
    normalStatus: {
      color: '#10B981'
    },
    warningStatus: {
      color: '#F59E0B'
    },
    criticalStatus: {
      color: '#EF4444'
    },
    lastUpdated: {
      fontSize: 10,
      color: theme.textSecondary,
      marginTop: 2
    },
    viewDetailsButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      backgroundColor: theme.background,
      borderRadius: 8
    },
    viewDetailsText: {
      fontSize: 14,
      color: theme.primary,
      fontWeight: '600',
      marginRight: 8
    },
    emptyState: {
      alignItems: 'center',
      paddingVertical: 32
    },
    emptyText: {
      fontSize: 16,
      color: theme.textSecondary,
      textAlign: 'center',
      marginTop: 12
    }
  });

  const getMetricIcon = (label: string) => {
    switch (label.toLowerCase()) {
      case 'heart rate':
        return <Heart size={16} color={theme.primary} />;
      case 'temperature':
        return <Thermometer size={16} color={theme.primary} />;
      case 'weight':
        return <Weight size={16} color={theme.primary} />;
      case 'activity':
        return <Activity size={16} color={theme.primary} />;
      default:
        return <Activity size={16} color={theme.primary} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return '#10B981';
      case 'warning': return '#F59E0B';
      case 'critical': return '#EF4444';
      default: return theme.textSecondary;
    }
  };

  const getTrendIcon = (trend?: string) => {
    if (!trend) return null;
    
    const color = trend === 'up' ? '#10B981' : trend === 'down' ? '#EF4444' : theme.textSecondary;
    return (
      <TrendingUp 
        size={12} 
        color={color}
        style={{
          transform: [{ rotate: trend === 'down' ? '180deg' : '0deg' }]
        }}
      />
    );
  };

  const formatLastUpdated = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  const renderMetricCard = (metric: HealthMetric) => (
    <View key={metric.id} style={styles.metricCard}>
      <View style={styles.metricHeader}>
        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
          <View style={styles.metricIcon}>
            {getMetricIcon(metric.label)}
          </View>
          <Text style={styles.metricLabel}>{metric.label}</Text>
        </View>
        {getTrendIcon(metric.trend) && (
          <View style={styles.trendIcon}>
            {getTrendIcon(metric.trend)}
          </View>
        )}
      </View>
      
      <View style={{ flexDirection: 'row', alignItems: 'baseline' }}>
        <Text style={styles.metricValue}>{metric.value}</Text>
        {metric.unit && (
          <Text style={styles.metricUnit}> {metric.unit}</Text>
        )}
      </View>
      
      <Text style={[
        styles.metricStatus,
        { color: getStatusColor(metric.status) }
      ]}>
        {metric.status}
      </Text>
      
      <Text style={styles.lastUpdated}>
        {formatLastUpdated(metric.lastUpdated)}
      </Text>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Heart size={48} color={theme.textSecondary} />
      <Text style={styles.emptyText}>
        No health data recorded yet.{"\n"}Start tracking your pet's vitals.
      </Text>
    </View>
  );

  return (
    <Card>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={styles.icon}>
            <Heart size={20} color={theme.primary} />
          </View>
          <Text style={styles.title}>Health Overview</Text>
        </View>
        
        <TouchableOpacity style={styles.addButton} onPress={onAddVitals}>
          <Text style={styles.addButtonText}>Add Vitals</Text>
        </TouchableOpacity>
      </View>
      
      {healthMetrics.length === 0 ? (
        renderEmptyState()
      ) : (
        <>
          <View style={styles.metricsGrid}>
            {healthMetrics.map(renderMetricCard)}
          </View>
          
          <TouchableOpacity style={styles.viewDetailsButton} onPress={onViewDetails}>
            <Text style={styles.viewDetailsText}>View Full Health Report</Text>
            <TrendingUp size={16} color={theme.primary} />
          </TouchableOpacity>
        </>
      )}
    </Card>
  );
};

export default AnimalHealthOverview;
export type { HealthMetric };