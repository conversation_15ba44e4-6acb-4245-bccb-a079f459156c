import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity
} from 'react-native';
import { Calendar, Clock, Bell, ChevronRight } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';
import { format, isToday, isTomorrow } from 'date-fns';

interface UpcomingEvent {
  id: string;
  type: 'feeding' | 'medication' | 'vaccination' | 'checkup';
  title: string;
  description: string;
  scheduledTime: string;
  isOverdue?: boolean;
}

interface UpcomingEventsCardProps {
  events: UpcomingEvent[];
  onEventPress?: (event: UpcomingEvent) => void;
}

const UpcomingEventsCard: React.FC<UpcomingEventsCardProps> = ({ 
  events, 
  onEventPress 
}) => {
  const { colors } = useTheme();
  
  const getEventColor = (type: string, isOverdue?: boolean) => {
    if (isOverdue) return '#EF4444';
    
    switch (type) {
      case 'feeding':
        return '#F59E0B';
      case 'medication':
        return '#8B5CF6';
      case 'vaccination':
        return '#10B981';
      case 'checkup':
        return '#3B82F6';
      default:
        return colors.primary;
    }
  };
  
  const formatEventTime = (scheduledTime: string) => {
    const date = new Date(scheduledTime);
    
    if (isToday(date)) {
      return `Today at ${format(date, 'h:mm a')}`;
    } else if (isTomorrow(date)) {
      return `Tomorrow at ${format(date, 'h:mm a')}`;
    } else {
      return format(date, 'MMM d at h:mm a');
    }
  };
  
  const renderEventItem = ({ item }: { item: UpcomingEvent }) => {
    const eventColor = getEventColor(item.type, item.isOverdue);
    
    return (
      <TouchableOpacity
        style={styles.eventItem}
        onPress={() => onEventPress?.(item)}
        disabled={!onEventPress}
      >
        <View style={[styles.eventIndicator, { backgroundColor: eventColor }]} />
        
        <View style={styles.eventContent}>
          <View style={styles.eventHeader}>
            <Text style={[styles.eventTitle, { color: colors.text }]}>
              {item.title}
            </Text>
            {item.isOverdue && (
              <View style={[styles.overdueBadge, { backgroundColor: '#EF4444' }]}>
                <Text style={styles.overdueText}>Overdue</Text>
              </View>
            )}
          </View>
          
          <Text style={[styles.eventDescription, { color: colors.textLight }]}>
            {item.description}
          </Text>
          
          <View style={styles.eventTimeContainer}>
            <Clock size={12} color={colors.textLight} />
            <Text style={[styles.eventTime, { color: colors.textLight }]}>
              {formatEventTime(item.scheduledTime)}
            </Text>
          </View>
        </View>
        
        {onEventPress && (
          <ChevronRight size={16} color={colors.textLight} />
        )}
      </TouchableOpacity>
    );
  };
  
  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        <Calendar size={20} color={colors.primary} />
        <Text style={[styles.title, { color: colors.text }]}>Upcoming Events</Text>
      </View>
      
      {events.length > 0 ? (
        <FlatList
          data={events.slice(0, 5)} // Show next 5 events
          renderItem={renderEventItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyState}>
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            No upcoming events
          </Text>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  eventItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  eventIndicator: {
    width: 4,
    height: '100%',
    borderRadius: 2,
    marginRight: 12,
    minHeight: 40,
  },
  eventContent: {
    flex: 1,
  },
  eventHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  eventTitle: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  overdueBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  overdueText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  eventDescription: {
    fontSize: 12,
    marginBottom: 6,
  },
  eventTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  eventTime: {
    fontSize: 11,
  },
  emptyState: {
    paddingVertical: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
  },
});

export default UpcomingEventsCard;