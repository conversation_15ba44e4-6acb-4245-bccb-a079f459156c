import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image
} from 'react-native';
import { Edit, Share, Heart } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Animal } from '../../mocks/animals';

interface AnimalProfileHeaderProps {
  animal: Animal;
  onEdit: () => void;
  onShare: () => void;
  onBack: () => void;
}

const AnimalProfileHeader: React.FC<AnimalProfileHeaderProps> = ({
  animal,
  onEdit,
  onShare,
  onBack
}) => {
  const { colors } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.imageContainer}>
        <Image
          source={{
            uri: animal.imageUrl || 'https://magically.life/api/media/image?query=livestock%20animal%20portrait'
          }}
          style={styles.animalImage}
          resizeMode="cover"
        />
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(animal.deviceStatus) }]}>
          <Text style={styles.statusText}>{animal.deviceStatus}</Text>
        </View>
      </View>
      
      <View style={styles.infoContainer}>
        <View style={styles.headerRow}>
          <View style={styles.nameContainer}>
            <Text style={[styles.animalName, { color: colors.text }]}>{animal.name}</Text>
            <Text style={[styles.animalBreed, { color: colors.textLight }]}>
              {animal.breed} • {animal.age} years old
            </Text>
          </View>
          
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary }]}
              onPress={onEdit}
            >
              <Edit size={16} color={colors.white} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.secondary }]}
              onPress={onShare}
            >
              <Share size={16} color={colors.white} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Heart size={16} color={colors.primary} />
            <Text style={[styles.statLabel, { color: colors.textLight }]}>Health</Text>
            <Text style={[styles.statValue, { color: colors.text }]}>Good</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: colors.textLight }]}>Weight</Text>
            <Text style={[styles.statValue, { color: colors.text }]}>{animal.weight || 'N/A'}</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statLabel, { color: colors.textLight }]}>Location</Text>
            <Text style={[styles.statValue, { color: colors.text }]}>Pasture A</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'connected':
      return '#10B981';
    case 'paired':
      return '#3B82F6';
    case 'low_battery':
      return '#F59E0B';
    case 'disconnected':
    default:
      return '#6B7280';
  }
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 16,
    margin: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imageContainer: {
    position: 'relative',
    alignSelf: 'center',
    marginBottom: 16,
  },
  animalImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  statusBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  infoContainer: {
    flex: 1,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  nameContainer: {
    flex: 1,
  },
  animalName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  animalBreed: {
    fontSize: 14,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  statItem: {
    alignItems: 'center',
    gap: 4,
  },
  statLabel: {
    fontSize: 12,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default AnimalProfileHeader;