import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList
} from 'react-native';
import { Clock, Activity, Heart, Utensils } from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';
import { format } from 'date-fns';

interface ActivityItem {
  id: string;
  type: 'training' | 'vitals' | 'feeding' | 'medication';
  title: string;
  description: string;
  timestamp: string;
}

interface RecentActivityCardProps {
  activities: ActivityItem[];
}

const RecentActivityCard: React.FC<RecentActivityCardProps> = ({ activities }) => {
  const { colors } = useTheme();
  
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'training':
        return Activity;
      case 'vitals':
        return Heart;
      case 'feeding':
        return Utensils;
      case 'medication':
        return Clock;
      default:
        return Activity;
    }
  };
  
  const getActivityColor = (type: string) => {
    switch (type) {
      case 'training':
        return colors.primary;
      case 'vitals':
        return '#EF4444';
      case 'feeding':
        return '#F59E0B';
      case 'medication':
        return '#8B5CF6';
      default:
        return colors.primary;
    }
  };
  
  const renderActivityItem = ({ item }: { item: ActivityItem }) => {
    const IconComponent = getActivityIcon(item.type);
    const iconColor = getActivityColor(item.type);
    
    return (
      <View style={styles.activityItem}>
        <View style={[styles.iconContainer, { backgroundColor: iconColor + '20' }]}>
          <IconComponent size={16} color={iconColor} />
        </View>
        
        <View style={styles.activityContent}>
          <Text style={[styles.activityTitle, { color: colors.text }]}>
            {item.title}
          </Text>
          <Text style={[styles.activityDescription, { color: colors.textLight }]}>
            {item.description}
          </Text>
          <Text style={[styles.activityTime, { color: colors.textLight }]}>
            {format(new Date(item.timestamp), 'MMM d, h:mm a')}
          </Text>
        </View>
      </View>
    );
  };
  
  return (
    <Card style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>Recent Activity</Text>
      
      {activities.length > 0 ? (
        <FlatList
          data={activities.slice(0, 5)} // Show last 5 activities
          renderItem={renderActivityItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyState}>
          <Text style={[styles.emptyText, { color: colors.textLight }]}>
            No recent activity
          </Text>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  activityDescription: {
    fontSize: 12,
    marginBottom: 4,
  },
  activityTime: {
    fontSize: 11,
  },
  emptyState: {
    paddingVertical: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
  },
});

export default RecentActivityCard;