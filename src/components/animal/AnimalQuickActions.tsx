import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { 
  Activity, 
  Utensils, 
  Pill, 
  Calendar, 
  Camera, 
  MapPin,
  Brain,
  Stethoscope
} from 'lucide-react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Card from '../ui/Card';

interface AnimalQuickActionsProps {
  onLogTraining: () => void;
  onRecordVitals: () => void;
  onViewFeeding: () => void;
  onAddMedication: () => void;
  onViewLocation: () => void;
  onTakePhoto: () => void;
  onAIAnalysis: () => void;
  onHealthDashboard: () => void;
}

const AnimalQuickActions: React.FC<AnimalQuickActionsProps> = ({
  onLogTraining,
  onRecordVitals,
  onViewFeeding,
  onAddMedication,
  onViewLocation,
  onTakePhoto,
  onAIAnalysis,
  onHealthDashboard
}) => {
  const { colors } = useTheme();
  
  const actions = [
    {
      icon: Activity,
      label: 'Log Training',
      onPress: onLogTraining,
      color: colors.primary,
    },
    {
      icon: Stethoscope,
      label: 'Record Vitals',
      onPress: onRecordVitals,
      color: '#EF4444',
    },
    {
      icon: Utensils,
      label: 'Feeding',
      onPress: onViewFeeding,
      color: '#F59E0B',
    },
    {
      icon: Pill,
      label: 'Medication',
      onPress: onAddMedication,
      color: '#8B5CF6',
    },
    {
      icon: MapPin,
      label: 'Location',
      onPress: onViewLocation,
      color: '#10B981',
    },
    {
      icon: Camera,
      label: 'Photo',
      onPress: onTakePhoto,
      color: '#6B7280',
    },
    {
      icon: Brain,
      label: 'AI Analysis',
      onPress: onAIAnalysis,
      color: '#3B82F6',
    },
    {
      icon: Calendar,
      label: 'Health Dashboard',
      onPress: onHealthDashboard,
      color: '#EC4899',
    },
  ];
  
  return (
    <Card style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>Quick Actions</Text>
      
      <View style={styles.actionsGrid}>
        {actions.map((action, index) => {
          const IconComponent = action.icon;
          return (
            <TouchableOpacity
              key={index}
              style={[styles.actionItem, { backgroundColor: colors.background }]}
              onPress={action.onPress}
            >
              <View style={[styles.iconContainer, { backgroundColor: action.color + '20' }]}>
                <IconComponent size={24} color={action.color} />
              </View>
              <Text style={[styles.actionLabel, { color: colors.text }]}>
                {action.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionItem: {
    width: '23%',
    aspectRatio: 1,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionLabel: {
    fontSize: 10,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default AnimalQuickActions;